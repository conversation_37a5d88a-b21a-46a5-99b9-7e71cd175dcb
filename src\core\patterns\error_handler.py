"""
统一错误处理机制 - P3级架构规范化
提供分层的异常处理和错误恢复策略
"""

from typing import Optional, Dict, Any, Callable, Type
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from loguru import logger
import traceback


class ErrorSeverity(Enum):
    """错误严重程度"""
    INFO = "info"           # 信息级
    WARNING = "warning"     # 警告级
    ERROR = "error"         # 错误级
    CRITICAL = "critical"   # 严重级
    FATAL = "fatal"         # 致命级


class ErrorCategory(Enum):
    """错误类别"""
    VALIDATION = "validation"     # 验证错误
    BUSINESS = "business"         # 业务错误
    DATA = "data"                # 数据错误
    NETWORK = "network"          # 网络错误
    SYSTEM = "system"            # 系统错误
    SECURITY = "security"        # 安全错误
    UNKNOWN = "unknown"          # 未知错误


@dataclass
class ErrorContext:
    """错误上下文"""
    error_id: str
    timestamp: datetime
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    details: Optional[Dict[str, Any]] = None
    stack_trace: Optional[str] = None
    user_message: Optional[str] = None
    recovery_action: Optional[str] = None


# 自定义异常层次
class ApplicationException(Exception):
    """应用异常基类"""
    
    def __init__(
        self, 
        message: str,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.ERROR,
        details: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ):
        super().__init__(message)
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.user_message = user_message or message


class ValidationException(ApplicationException):
    """验证异常"""
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.WARNING,
            **kwargs
        )
        if field:
            self.details['field'] = field


class BusinessException(ApplicationException):
    """业务异常"""
    
    def __init__(self, message: str, code: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.BUSINESS,
            severity=ErrorSeverity.ERROR,
            **kwargs
        )
        if code:
            self.details['code'] = code


class DataException(ApplicationException):
    """数据异常"""
    
    def __init__(self, message: str, table: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.DATA,
            severity=ErrorSeverity.ERROR,
            **kwargs
        )
        if table:
            self.details['table'] = table


class SecurityException(ApplicationException):
    """安全异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.SECURITY,
            severity=ErrorSeverity.CRITICAL,
            **kwargs
        )


class ErrorHandler:
    """
    统一错误处理器
    
    提供错误捕获、记录、恢复和通知功能
    """
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.logger = logger
        self._error_handlers: Dict[ErrorCategory, Callable] = {}
        self._recovery_strategies: Dict[ErrorCategory, Callable] = {}
        self._error_history: list = []
        self._max_history = 100
        
        # 注册默认处理器
        self._register_default_handlers()
        
        self.logger.info("ErrorHandler 初始化完成")
    
    def _register_default_handlers(self):
        """注册默认错误处理器"""
        self.register_handler(ErrorCategory.VALIDATION, self._handle_validation_error)
        self.register_handler(ErrorCategory.BUSINESS, self._handle_business_error)
        self.register_handler(ErrorCategory.DATA, self._handle_data_error)
        self.register_handler(ErrorCategory.SYSTEM, self._handle_system_error)
        self.register_handler(ErrorCategory.SECURITY, self._handle_security_error)
    
    def register_handler(self, category: ErrorCategory, handler: Callable):
        """
        注册错误处理器
        
        Args:
            category: 错误类别
            handler: 处理函数
        """
        self._error_handlers[category] = handler
        self.logger.debug(f"注册错误处理器: {category.value}")
    
    def register_recovery(self, category: ErrorCategory, strategy: Callable):
        """
        注册恢复策略
        
        Args:
            category: 错误类别
            strategy: 恢复策略函数
        """
        self._recovery_strategies[category] = strategy
        self.logger.debug(f"注册恢复策略: {category.value}")
    
    def handle_error(self, error: Exception) -> ErrorContext:
        """
        处理错误
        
        Args:
            error: 异常对象
        
        Returns:
            错误上下文
        """
        # 创建错误上下文
        context = self._create_error_context(error)
        
        # 记录错误
        self._log_error(context)
        
        # 保存到历史
        self._save_to_history(context)
        
        # 获取处理器
        handler = self._error_handlers.get(
            context.category,
            self._handle_unknown_error
        )
        
        # 处理错误
        try:
            handler(context)
        except Exception as e:
            self.logger.error(f"错误处理器执行失败: {e}")
        
        # 尝试恢复
        if context.category in self._recovery_strategies:
            try:
                recovery_action = self._recovery_strategies[context.category](context)
                context.recovery_action = recovery_action
            except Exception as e:
                self.logger.error(f"恢复策略执行失败: {e}")
        
        return context
    
    def _create_error_context(self, error: Exception) -> ErrorContext:
        """创建错误上下文"""
        import uuid
        
        # 确定错误类别和严重程度
        if isinstance(error, ApplicationException):
            category = error.category
            severity = error.severity
            message = str(error)
            details = error.details
            user_message = error.user_message
        else:
            category = ErrorCategory.UNKNOWN
            severity = ErrorSeverity.ERROR
            message = str(error)
            details = {'type': type(error).__name__}
            user_message = "系统发生错误，请稍后重试"
        
        # 获取堆栈跟踪
        stack_trace = traceback.format_exc()
        
        return ErrorContext(
            error_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            category=category,
            severity=severity,
            message=message,
            details=details,
            stack_trace=stack_trace,
            user_message=user_message
        )
    
    def _log_error(self, context: ErrorContext):
        """记录错误日志"""
        log_message = f"[{context.error_id}] {context.category.value}: {context.message}"
        
        if context.severity == ErrorSeverity.INFO:
            self.logger.info(log_message)
        elif context.severity == ErrorSeverity.WARNING:
            self.logger.warning(log_message)
        elif context.severity == ErrorSeverity.ERROR:
            self.logger.error(log_message)
        elif context.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif context.severity == ErrorSeverity.FATAL:
            self.logger.critical(f"FATAL: {log_message}")
        
        # 详细信息记录到DEBUG
        if context.details:
            self.logger.debug(f"错误详情: {context.details}")
        if context.stack_trace and context.severity.value in ['error', 'critical', 'fatal']:
            self.logger.debug(f"堆栈跟踪:\n{context.stack_trace}")
    
    def _save_to_history(self, context: ErrorContext):
        """保存到错误历史"""
        self._error_history.append(context)
        
        # 限制历史记录数量
        if len(self._error_history) > self._max_history:
            self._error_history.pop(0)
    
    def _handle_validation_error(self, context: ErrorContext):
        """处理验证错误"""
        self.logger.debug(f"处理验证错误: {context.message}")
        # 验证错误通常不需要特殊处理，直接返回给用户
    
    def _handle_business_error(self, context: ErrorContext):
        """处理业务错误"""
        self.logger.debug(f"处理业务错误: {context.message}")
        # 业务错误可能需要回滚事务
    
    def _handle_data_error(self, context: ErrorContext):
        """处理数据错误"""
        self.logger.debug(f"处理数据错误: {context.message}")
        # 数据错误可能需要数据修复
    
    def _handle_system_error(self, context: ErrorContext):
        """处理系统错误"""
        self.logger.debug(f"处理系统错误: {context.message}")
        # 系统错误可能需要重启服务
    
    def _handle_security_error(self, context: ErrorContext):
        """处理安全错误"""
        self.logger.debug(f"处理安全错误: {context.message}")
        # 安全错误需要记录审计日志
    
    def _handle_unknown_error(self, context: ErrorContext):
        """处理未知错误"""
        self.logger.debug(f"处理未知错误: {context.message}")
    
    def get_error_history(self, category: Optional[ErrorCategory] = None) -> list:
        """
        获取错误历史
        
        Args:
            category: 错误类别（可选）
        
        Returns:
            错误历史列表
        """
        if category:
            return [e for e in self._error_history if e.category == category]
        return self._error_history.copy()
    
    def clear_history(self):
        """清除错误历史"""
        self._error_history.clear()
        self.logger.debug("清除错误历史")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取错误统计
        
        Returns:
            统计信息
        """
        stats = {
            'total': len(self._error_history),
            'by_category': {},
            'by_severity': {}
        }
        
        for error in self._error_history:
            # 按类别统计
            category = error.category.value
            stats['by_category'][category] = stats['by_category'].get(category, 0) + 1
            
            # 按严重程度统计
            severity = error.severity.value
            stats['by_severity'][severity] = stats['by_severity'].get(severity, 0) + 1
        
        return stats


# 错误处理装饰器
def handle_errors(
    category: ErrorCategory = ErrorCategory.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.ERROR,
    user_message: Optional[str] = None,
    reraise: bool = False
):
    """
    错误处理装饰器
    
    Args:
        category: 错误类别
        severity: 严重程度
        user_message: 用户友好的错误信息
        reraise: 是否重新抛出异常
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ApplicationException:
                # 应用异常直接抛出
                raise
            except Exception as e:
                # 包装为应用异常
                app_error = ApplicationException(
                    message=str(e),
                    category=category,
                    severity=severity,
                    user_message=user_message,
                    details={'function': func.__name__}
                )
                
                # 处理错误
                handler = get_error_handler()
                handler.handle_error(app_error)
                
                if reraise:
                    raise app_error
                
                return None
        
        return wrapper
    return decorator


# 全局错误处理器实例
_error_handler = None

def get_error_handler() -> ErrorHandler:
    """获取错误处理器实例"""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler()
    return _error_handler