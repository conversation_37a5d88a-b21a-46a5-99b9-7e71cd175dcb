"""
PyQt5重构高保真原型 - 智能树形展开算法

基于时间衰减和路径预测的智能展开算法，实现用户习惯记忆和智能预测功能。
为4级树形导航提供智能展开策略和用户体验优化。

主要功能:
1. 访问路径记录和权重计算
2. 时间衰减算法优化历史数据
3. 路径模式识别和预测
4. 上下文感知的展开建议
5. 用户习惯学习和适应
"""

import time
import json
import os
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, deque
from PyQt5.QtCore import QObject, pyqtSignal
import logging

from src.utils.log_config import setup_logger


class PathPredictionModel:
    """
    路径预测模型
    
    基于用户访问模式和序列分析的路径预测系统。
    """
    
    def __init__(self):
        self.sequence_patterns = defaultdict(list)
        self.context_patterns = defaultdict(dict)
        self.confidence_threshold = 0.3
        self.logger = setup_logger(__name__ + ".PathPredictionModel")
    
    def learn_sequence(self, path_sequence: List[str], context: Dict[str, Any] = None):
        """
        学习路径序列模式
        
        Args:
            path_sequence: 路径序列
            context: 上下文信息
        """
        if len(path_sequence) < 2:
            return
            
        # 学习序列转换模式
        for i in range(len(path_sequence) - 1):
            current_path = path_sequence[i]
            next_path = path_sequence[i + 1]
            
            self.sequence_patterns[current_path].append(next_path)
            
            # 学习上下文关联
            if context:
                context_key = str(sorted(context.items()))
                if current_path not in self.context_patterns[context_key]:
                    self.context_patterns[context_key][current_path] = 0
                self.context_patterns[context_key][current_path] += 1
    
    def predict_next_paths(self, current_path: str, context: Dict[str, Any] = None) -> List[Tuple[str, float]]:
        """
        预测下一步路径
        
        Args:
            current_path: 当前路径
            context: 当前上下文
            
        Returns:
            预测路径及其置信度列表
        """
        predictions = defaultdict(float)
        
        # 基于序列模式预测
        if current_path in self.sequence_patterns:
            next_paths = self.sequence_patterns[current_path]
            path_counts = defaultdict(int)
            
            for path in next_paths:
                path_counts[path] += 1
            
            total_count = len(next_paths)
            for path, count in path_counts.items():
                confidence = count / total_count
                predictions[path] += confidence * 0.7  # 序列权重70%
        
        # 基于上下文模式预测
        if context:
            context_key = str(sorted(context.items()))
            if context_key in self.context_patterns:
                context_paths = self.context_patterns[context_key]
                total_context_weight = sum(context_paths.values())
                
                for path, weight in context_paths.items():
                    confidence = weight / total_context_weight
                    predictions[path] += confidence * 0.3  # 上下文权重30%
        
        # 过滤低置信度预测
        filtered_predictions = [
            (path, confidence) for path, confidence in predictions.items()
            if confidence >= self.confidence_threshold
        ]
        
        return sorted(filtered_predictions, key=lambda x: x[1], reverse=True)


class SmartTreeExpansion(QObject):
    """
    智能树形展开算法
    
    基于时间衰减和路径预测的智能展开算法，学习用户习惯并提供智能展开建议。
    """
    
    # 展开建议信号
    expansion_suggested = pyqtSignal(list)  # (suggested_paths)
    pattern_learned = pyqtSignal(str, float)  # (pattern, confidence)
    
    def __init__(self, data_file: str = "state/user/tree_expansion_data.json"):
        """
        初始化智能展开算法
        
        Args:
            data_file: 数据持久化文件路径
        """
        super().__init__()
        # 绝对化持久化路径（基于 APP_ROOT）
        app_root = os.environ.get('APP_ROOT') or os.getcwd()
        self.data_file = data_file if os.path.isabs(data_file) else os.path.join(app_root, data_file)
        self.logger = setup_logger(__name__)
        
        # 访问历史记录
        self.access_history = deque(maxlen=1000)  # 限制历史记录数量
        self.path_weights = {}
        self.session_paths = []  # 当前会话路径序列
        
        # 算法参数
        self.time_decay_factor = 0.95  # 时间衰减因子
        self.session_timeout = 1800    # 会话超时时间（30分钟）
        self.min_weight_threshold = 0.1  # 最小权重阈值
        
        # 预测模型
        self.prediction_model = PathPredictionModel()
        
        # 加载历史数据
        self._load_data()
        
        self.logger.info("智能树形展开算法初始化完成")
    
    def record_access(self, path: str, context: Dict[str, Any] = None, timestamp: float = None):
        """
        记录访问路径
        
        Args:
            path: 访问的路径
            context: 访问上下文
            timestamp: 访问时间戳
        """
        if timestamp is None:
            timestamp = time.time()
        
        # 记录访问历史
        access_record = {
            'path': path,
            'timestamp': timestamp,
            'context': context or {}
        }
        self.access_history.append(access_record)
        
        # 更新路径权重
        self._update_path_weights(path, timestamp)
        
        # 更新会话路径序列
        self._update_session_sequence(path, timestamp)
        
        # 学习路径模式
        self._learn_patterns()
        
        self.logger.debug(f"记录访问路径: {path}")
    
    def predict_expansion_paths(self, current_context: Dict[str, Any] = None, max_predictions: int = 5) -> List[Tuple[str, float]]:
        """
        预测需要展开的路径
        
        Args:
            current_context: 当前上下文
            max_predictions: 最大预测数量
            
        Returns:
            预测路径及其置信度列表
        """
        current_time = time.time()
        predictions = {}
        
        # 基于时间衰减的权重预测
        weight_predictions = self._predict_by_weights(current_time)
        
        # 基于路径模式的预测
        pattern_predictions = self._predict_by_patterns(current_context)
        
        # 基于用户序列的预测
        sequence_predictions = self._predict_by_sequence(current_context)
        
        # 合并预测结果
        all_predictions = [weight_predictions, pattern_predictions, sequence_predictions]
        predictions = self._merge_predictions(*all_predictions)
        
        # 排序并限制数量
        sorted_predictions = sorted(predictions.items(), key=lambda x: x[1], reverse=True)
        result = sorted_predictions[:max_predictions]
        
        if result:
            self.expansion_suggested.emit([path for path, _ in result])
            self.logger.info(f"预测展开路径: {[path for path, score in result]}")
        
        return result
    
    def _update_path_weights(self, path: str, timestamp: float):
        """
        更新路径权重
        
        Args:
            path: 路径
            timestamp: 时间戳
        """
        if path not in self.path_weights:
            self.path_weights[path] = {
                'weight': 0.0,
                'last_access': timestamp,
                'access_count': 0
            }
        
        # 应用时间衰减
        time_diff = timestamp - self.path_weights[path]['last_access']
        decay = self.time_decay_factor ** (time_diff / 3600)  # 按小时衰减
        
        # 更新权重
        self.path_weights[path]['weight'] = self.path_weights[path]['weight'] * decay + 1.0
        self.path_weights[path]['last_access'] = timestamp
        self.path_weights[path]['access_count'] += 1
        
        # 清理低权重路径
        self._cleanup_low_weights()
    
    def _update_session_sequence(self, path: str, timestamp: float):
        """
        更新会话路径序列
        
        Args:
            path: 路径
            timestamp: 时间戳
        """
        # 检查会话超时
        if (self.session_paths and 
            timestamp - self.session_paths[-1]['timestamp'] > self.session_timeout):
            # 会话超时，学习当前序列并开始新会话
            self._complete_session()
        
        # 添加到当前会话
        self.session_paths.append({
            'path': path,
            'timestamp': timestamp
        })
    
    def _complete_session(self):
        """
        完成当前会话，学习路径序列
        """
        if len(self.session_paths) >= 2:
            path_sequence = [item['path'] for item in self.session_paths]
            self.prediction_model.learn_sequence(path_sequence)
            
            self.logger.debug(f"完成会话学习，序列长度: {len(path_sequence)}")
        
        self.session_paths.clear()
    
    def _learn_patterns(self):
        """
        学习访问模式
        """
        if len(self.access_history) < 10:
            return
        
        # 提取最近的访问模式
        recent_accesses = list(self.access_history)[-10:]
        
        # 寻找重复模式
        patterns = self._extract_patterns(recent_accesses)
        
        for pattern, confidence in patterns:
            self.pattern_learned.emit(pattern, confidence)
    
    def _predict_by_weights(self, current_time: float) -> Dict[str, float]:
        """
        基于权重预测路径
        
        Args:
            current_time: 当前时间
            
        Returns:
            预测结果字典
        """
        predictions = {}
        
        for path, weight_data in self.path_weights.items():
            time_diff = current_time - weight_data['last_access']
            time_weight = self.time_decay_factor ** (time_diff / 3600)
            
            final_weight = weight_data['weight'] * time_weight
            if final_weight >= self.min_weight_threshold:
                predictions[path] = final_weight
        
        return predictions
    
    def _predict_by_patterns(self, current_context: Dict[str, Any] = None) -> Dict[str, float]:
        """
        基于模式预测路径
        
        Args:
            current_context: 当前上下文
            
        Returns:
            预测结果字典
        """
        predictions = {}
        
        # 基于时间模式
        current_hour = time.localtime().tm_hour
        time_patterns = self._get_time_patterns(current_hour)
        predictions.update(time_patterns)
        
        # 基于上下文模式
        if current_context:
            context_patterns = self._get_context_patterns(current_context)
            predictions.update(context_patterns)
        
        return predictions
    
    def _predict_by_sequence(self, current_context: Dict[str, Any] = None) -> Dict[str, float]:
        """
        基于序列预测路径
        
        Args:
            current_context: 当前上下文
            
        Returns:
            预测结果字典
        """
        predictions = {}
        
        if self.session_paths:
            last_path = self.session_paths[-1]['path']
            sequence_predictions = self.prediction_model.predict_next_paths(last_path, current_context)
            
            for path, confidence in sequence_predictions:
                predictions[path] = confidence
        
        return predictions
    
    def _merge_predictions(self, *prediction_dicts) -> Dict[str, float]:
        """
        合并多个预测结果
        
        Args:
            *prediction_dicts: 预测结果字典列表
            
        Returns:
            合并后的预测结果
        """
        merged = defaultdict(float)
        weights = [0.4, 0.3, 0.3]  # 权重、模式、序列的权重分配
        
        for i, predictions in enumerate(prediction_dicts):
            weight = weights[i] if i < len(weights) else 0.1
            for path, score in predictions.items():
                merged[path] += score * weight
        
        return dict(merged)
    
    def _extract_patterns(self, accesses: List[Dict[str, Any]]) -> List[Tuple[str, float]]:
        """
        提取访问模式
        
        Args:
            accesses: 访问记录列表
            
        Returns:
            模式及其置信度列表
        """
        patterns = []
        
        # 提取路径序列模式
        paths = [access['path'] for access in accesses]
        
        # 寻找重复的子序列
        for length in range(2, min(5, len(paths))):
            for i in range(len(paths) - length + 1):
                subsequence = tuple(paths[i:i+length])
                
                # 计算该子序列的出现频率
                count = 0
                for j in range(len(paths) - length + 1):
                    if tuple(paths[j:j+length]) == subsequence:
                        count += 1
                
                if count >= 2:  # 至少出现2次
                    confidence = count / (len(paths) - length + 1)
                    pattern_str = " -> ".join(subsequence)
                    patterns.append((pattern_str, confidence))
        
        return patterns
    
    def _get_time_patterns(self, current_hour: int) -> Dict[str, float]:
        """
        获取时间模式预测
        
        Args:
            current_hour: 当前小时
            
        Returns:
            时间模式预测结果
        """
        time_predictions = {}
        
        # 分析同一时间段的访问历史
        hour_accesses = [
            access for access in self.access_history
            if time.localtime(access['timestamp']).tm_hour == current_hour
        ]
        
        if hour_accesses:
            path_counts = defaultdict(int)
            for access in hour_accesses:
                path_counts[access['path']] += 1
            
            total_count = len(hour_accesses)
            for path, count in path_counts.items():
                time_predictions[path] = count / total_count
        
        return time_predictions
    
    def _get_context_patterns(self, context: Dict[str, Any]) -> Dict[str, float]:
        """
        获取上下文模式预测
        
        Args:
            context: 上下文信息
            
        Returns:
            上下文模式预测结果
        """
        context_predictions = {}
        
        # 基于相似上下文的历史访问
        similar_accesses = []
        for access in self.access_history:
            similarity = self._calculate_context_similarity(context, access['context'])
            if similarity > 0.5:  # 相似度阈值
                similar_accesses.append(access)
        
        if similar_accesses:
            path_counts = defaultdict(int)
            for access in similar_accesses:
                path_counts[access['path']] += 1
            
            total_count = len(similar_accesses)
            for path, count in path_counts.items():
                context_predictions[path] = count / total_count
        
        return context_predictions
    
    def _calculate_context_similarity(self, context1: Dict[str, Any], context2: Dict[str, Any]) -> float:
        """
        计算上下文相似度
        
        Args:
            context1: 上下文1
            context2: 上下文2
            
        Returns:
            相似度值 (0-1)
        """
        if not context1 or not context2:
            return 0.0
        
        common_keys = set(context1.keys()) & set(context2.keys())
        if not common_keys:
            return 0.0
        
        matches = 0
        for key in common_keys:
            if context1[key] == context2[key]:
                matches += 1
        
        return matches / len(common_keys)
    
    def _cleanup_low_weights(self):
        """
        清理低权重路径
        """
        paths_to_remove = [
            path for path, data in self.path_weights.items()
            if data['weight'] < self.min_weight_threshold
        ]
        
        for path in paths_to_remove:
            del self.path_weights[path]
    
    def _save_data(self):
        """
        保存数据到文件
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
            
            data = {
                'path_weights': self.path_weights,
                'access_history': list(self.access_history),
                'sequence_patterns': dict(self.prediction_model.sequence_patterns),
                'context_patterns': dict(self.prediction_model.context_patterns)
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            self.logger.debug(f"数据保存成功: {self.data_file}")
            
        except Exception as e:
            self.logger.error(f"数据保存失败: {e}")
    
    def _load_data(self):
        """
        从文件加载数据
        """
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.path_weights = data.get('path_weights', {})
                
                # 恢复访问历史
                history_data = data.get('access_history', [])
                self.access_history = deque(history_data, maxlen=1000)
                
                # 恢复预测模型数据
                sequence_data = data.get('sequence_patterns', {})
                for path, sequences in sequence_data.items():
                    self.prediction_model.sequence_patterns[path] = sequences
                
                context_data = data.get('context_patterns', {})
                for context_key, patterns in context_data.items():
                    self.prediction_model.context_patterns[context_key] = patterns
                
                self.logger.info(f"数据加载成功: {self.data_file}")
            
        except Exception as e:
            self.logger.warning(f"数据加载失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'total_accesses': len(self.access_history),
            'tracked_paths': len(self.path_weights),
            'session_length': len(self.session_paths),
            'learned_sequences': len(self.prediction_model.sequence_patterns),
            'context_patterns': len(self.prediction_model.context_patterns)
        }
    
    def clear_path_cache(self):
        """
        清除路径缓存和当前会话
        
        在树形结构重建后调用，确保算法不会尝试访问已删除的项目
        """
        # 清除当前会话路径，因为它们可能指向已删除的项目
        if self.session_paths:
            self._complete_session()  # 先完成当前会话的学习
            
        # 不清除历史权重数据，保留用户偏好
        # 只清除当前会话，避免访问已删除的项目
        self.session_paths.clear()
        
        self.logger.debug("智能展开算法的路径缓存已清除")
    
    def clear_data(self):
        """
        清空所有数据
        """
        self.access_history.clear()
        self.path_weights.clear()
        self.session_paths.clear()
        self.prediction_model.sequence_patterns.clear()
        self.prediction_model.context_patterns.clear()
        
        self.logger.info("智能展开数据已清空")
    
    # ----------------------------
    # PREDICTION BOOSTING UTILITIES
    # ----------------------------
    def boost_path_weight(self, path: str, boost: float = 5.0):
        """
        提升指定路径的权重，用于在自动选择最新路径后，快速引导预测到最新期，
        避免仍旧预测到历史月份。
        
        Args:
            path: 需要提升的完整导航路径
            boost: 提升的权重值（默认5.0）
        """
        try:
            timestamp = time.time()
            if path not in self.path_weights:
                self.path_weights[path] = {
                    'weight': 0.0,
                    'last_access': timestamp,
                    'access_count': 0
                }
            self.path_weights[path]['weight'] += max(0.0, float(boost))
            self.path_weights[path]['last_access'] = timestamp
            self.path_weights[path]['access_count'] += 1
            # 清理过低权重的过期路径
            self._cleanup_low_weights()
            self.logger.debug(f"已提升路径权重: {path}, +{boost}")
        except Exception as e:
            self.logger.debug(f"提升路径权重失败(忽略): {e}")
    
    def __del__(self):
        """
        析构函数，保存数据
        """
        self._save_data() 