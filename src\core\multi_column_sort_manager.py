"""
多列排序管理器 - P3级优化
提供完善的多列排序支持，包括优先级管理、冲突解决和UI交互增强
"""

from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
from loguru import logger


class SortDirection(Enum):
    """排序方向枚举"""
    ASC = "asc"
    DESC = "desc"
    
    def opposite(self):
        """获取相反的排序方向"""
        return SortDirection.DESC if self == SortDirection.ASC else SortDirection.ASC


@dataclass
class SortColumn:
    """排序列配置"""
    column_name: str
    direction: SortDirection
    priority: int  # 优先级，数字越小优先级越高
    data_type: Optional[str] = None  # 数据类型：numeric, text, date等
    null_position: str = "last"  # 空值位置：first或last
    case_sensitive: bool = False  # 是否大小写敏感（仅文本）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'column_name': self.column_name,
            'sort_order': self.direction.value,
            'priority': self.priority,
            'data_type': self.data_type,
            'null_position': self.null_position,
            'case_sensitive': self.case_sensitive
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SortColumn':
        """从字典创建"""
        return cls(
            column_name=data.get('column_name', data.get('column', '')),
            direction=SortDirection(data.get('sort_order', data.get('order', 'asc'))),
            priority=data.get('priority', 0),
            data_type=data.get('data_type'),
            null_position=data.get('null_position', 'last'),
            case_sensitive=data.get('case_sensitive', False)
        )


class MultiColumnSortManager:
    """
    多列排序管理器
    
    主要功能：
    1. 多列排序优先级管理
    2. 智能类型检测和排序
    3. 排序冲突解决
    4. UI交互增强
    5. 排序状态持久化
    """
    
    def __init__(self):
        """初始化管理器"""
        self.logger = logger
        
        # 当前排序配置
        self._sort_columns: List[SortColumn] = []
        
        # 最大排序列数
        self._max_sort_columns = 5
        
        # 数据类型检测阈值
        self._type_detection_sample_size = 100
        
        self.logger.info("MultiColumnSortManager 初始化完成")
    
    def add_sort_column(self, column_name: str, direction: str = "asc",
                       priority: Optional[int] = None) -> bool:
        """
        添加排序列
        
        Args:
            column_name: 列名
            direction: 排序方向
            priority: 优先级（None表示添加到末尾）
        
        Returns:
            是否添加成功
        """
        # 检查是否已存在
        existing = self._find_sort_column(column_name)
        if existing:
            # 更新方向
            existing.direction = SortDirection(direction)
            self.logger.info(f"[P3优化] 更新排序列: {column_name}, 方向={direction}")
            return True
        
        # 检查数量限制
        if len(self._sort_columns) >= self._max_sort_columns:
            self.logger.warning(f"[P3优化] 排序列数量已达上限: {self._max_sort_columns}")
            return False
        
        # 确定优先级
        if priority is None:
            priority = len(self._sort_columns)
        
        # 创建新的排序列
        sort_col = SortColumn(
            column_name=column_name,
            direction=SortDirection(direction),
            priority=priority
        )
        
        self._sort_columns.append(sort_col)
        self._reorder_by_priority()
        
        self.logger.info(f"[P3优化] 添加排序列: {column_name}, 优先级={priority}")
        return True
    
    def remove_sort_column(self, column_name: str) -> bool:
        """
        移除排序列
        
        Args:
            column_name: 列名
        
        Returns:
            是否移除成功
        """
        sort_col = self._find_sort_column(column_name)
        if sort_col:
            self._sort_columns.remove(sort_col)
            self._reorder_by_priority()
            self.logger.info(f"[P3优化] 移除排序列: {column_name}")
            return True
        return False
    
    def toggle_sort_direction(self, column_name: str) -> Optional[str]:
        """
        切换排序方向
        
        Args:
            column_name: 列名
        
        Returns:
            新的排序方向，或None（如果列不存在）
        """
        sort_col = self._find_sort_column(column_name)
        if sort_col:
            sort_col.direction = sort_col.direction.opposite()
            self.logger.info(f"[P3优化] 切换排序方向: {column_name} -> {sort_col.direction.value}")
            return sort_col.direction.value
        return None
    
    def update_priority(self, column_name: str, new_priority: int) -> bool:
        """
        更新排序优先级
        
        Args:
            column_name: 列名
            new_priority: 新优先级
        
        Returns:
            是否更新成功
        """
        sort_col = self._find_sort_column(column_name)
        if sort_col:
            # 移除当前列
            self._sort_columns.remove(sort_col)
            # 插入到新位置
            new_priority = max(0, min(new_priority, len(self._sort_columns)))
            self._sort_columns.insert(new_priority, sort_col)
            # 重新分配优先级
            self._reorder_by_priority()
            self.logger.info(f"[P3优化] 更新优先级: {column_name} -> {new_priority}")
            return True
        return False
    
    def apply_sort(self, df: pd.DataFrame, detect_types: bool = True) -> pd.DataFrame:
        """
        应用多列排序
        
        Args:
            df: 待排序的DataFrame
            detect_types: 是否自动检测数据类型
        
        Returns:
            排序后的DataFrame
        """
        if not self._sort_columns or df.empty:
            return df
        
        try:
            # 准备排序参数
            by_columns = []
            ascending_list = []
            na_position_list = []
            
            for sort_col in self._sort_columns:
                if sort_col.column_name not in df.columns:
                    self.logger.warning(f"[P3优化] 排序列不存在: {sort_col.column_name}")
                    continue
                
                # 检测数据类型
                if detect_types and not sort_col.data_type:
                    sort_col.data_type = self._detect_column_type(df[sort_col.column_name])
                
                # 处理特殊数据类型
                column_to_sort = self._prepare_column_for_sort(
                    df[sort_col.column_name], 
                    sort_col
                )
                
                by_columns.append(column_to_sort)
                ascending_list.append(sort_col.direction == SortDirection.ASC)
                na_position_list.append(sort_col.null_position)
            
            if not by_columns:
                return df
            
            # 执行排序
            sorted_df = df.copy()
            
            # 处理多列排序
            if len(by_columns) == 1:
                sorted_df = sorted_df.sort_values(
                    by=by_columns[0],
                    ascending=ascending_list[0],
                    na_position=na_position_list[0],
                    ignore_index=True
                )
            else:
                # pandas的sort_values不支持每列不同的na_position
                # 所以使用统一的na_position
                sorted_df = sorted_df.sort_values(
                    by=by_columns,
                    ascending=ascending_list,
                    na_position=na_position_list[0],  # 使用第一列的设置
                    ignore_index=True
                )
            
            self.logger.debug(f"[P3优化] 应用{len(by_columns)}列排序成功")
            return sorted_df
            
        except Exception as e:
            self.logger.error(f"[P3优化] 多列排序失败: {e}")
            return df
    
    def get_sort_config(self) -> List[Dict[str, Any]]:
        """
        获取排序配置（用于传递给其他组件）
        
        Returns:
            排序配置列表
        """
        return [col.to_dict() for col in self._sort_columns]
    
    def set_sort_config(self, config: List[Dict[str, Any]]):
        """
        设置排序配置
        
        Args:
            config: 排序配置列表
        """
        self._sort_columns = []
        for item in config:
            try:
                sort_col = SortColumn.from_dict(item)
                self._sort_columns.append(sort_col)
            except Exception as e:
                self.logger.error(f"[P3优化] 解析排序配置失败: {e}")
        
        self._reorder_by_priority()
        self.logger.info(f"[P3优化] 设置排序配置: {len(self._sort_columns)}列")
    
    def clear_all(self):
        """清除所有排序配置"""
        self._sort_columns.clear()
        self.logger.info("[P3优化] 清除所有排序配置")
    
    def get_sort_summary(self) -> str:
        """
        获取排序摘要（用于UI显示）
        
        Returns:
            排序摘要字符串
        """
        if not self._sort_columns:
            return "无排序"
        
        parts = []
        for sort_col in self._sort_columns[:3]:  # 最多显示3个
            arrow = "↑" if sort_col.direction == SortDirection.ASC else "↓"
            parts.append(f"{sort_col.column_name}{arrow}")
        
        if len(self._sort_columns) > 3:
            parts.append(f"...等{len(self._sort_columns)}列")
        
        return ", ".join(parts)
    
    def _find_sort_column(self, column_name: str) -> Optional[SortColumn]:
        """查找排序列配置"""
        for col in self._sort_columns:
            if col.column_name == column_name:
                return col
        return None
    
    def _reorder_by_priority(self):
        """按优先级重新排序"""
        self._sort_columns.sort(key=lambda x: x.priority)
        # 重新分配优先级
        for i, col in enumerate(self._sort_columns):
            col.priority = i
    
    def _detect_column_type(self, series: pd.Series) -> str:
        """
        检测列的数据类型
        
        Args:
            series: 数据列
        
        Returns:
            数据类型：numeric, date, text
        """
        # 采样检测
        sample_size = min(len(series), self._type_detection_sample_size)
        sample = series.dropna().head(sample_size)
        
        if sample.empty:
            return "text"
        
        # 尝试数值类型
        try:
            pd.to_numeric(sample, errors='raise')
            return "numeric"
        except:
            pass
        
        # 尝试日期类型
        try:
            pd.to_datetime(sample, errors='raise')
            return "date"
        except:
            pass
        
        return "text"
    
    def _prepare_column_for_sort(self, series: pd.Series, 
                                 sort_col: SortColumn) -> str:
        """
        为排序准备列数据（返回列名）
        
        Args:
            series: 原始数据列
            sort_col: 排序配置
        
        Returns:
            列名
        """
        # 直接返回列名，pandas会自动处理
        return sort_col.column_name
    
    def validate_sort_columns(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        验证排序列是否有效
        
        Args:
            df: DataFrame
        
        Returns:
            (是否全部有效, 无效列名列表)
        """
        invalid_columns = []
        for sort_col in self._sort_columns:
            if sort_col.column_name not in df.columns:
                invalid_columns.append(sort_col.column_name)
        
        return len(invalid_columns) == 0, invalid_columns
    
    def optimize_for_performance(self, df: pd.DataFrame) -> List[str]:
        """
        性能优化建议
        
        Args:
            df: DataFrame
        
        Returns:
            优化建议列表
        """
        suggestions = []
        
        # 检查排序列数量
        if len(self._sort_columns) > 3:
            suggestions.append(f"排序列过多({len(self._sort_columns)}列)，建议减少到3列以内")
        
        # 检查是否有高基数列
        for sort_col in self._sort_columns:
            if sort_col.column_name in df.columns:
                unique_ratio = df[sort_col.column_name].nunique() / len(df)
                if unique_ratio > 0.9:
                    suggestions.append(f"列'{sort_col.column_name}'基数很高，排序效果可能不明显")
        
        return suggestions


# 创建全局实例
_multi_sort_manager = None

def get_multi_sort_manager() -> MultiColumnSortManager:
    """获取多列排序管理器的全局实例"""
    global _multi_sort_manager
    if _multi_sort_manager is None:
        _multi_sort_manager = MultiColumnSortManager()
    return _multi_sort_manager