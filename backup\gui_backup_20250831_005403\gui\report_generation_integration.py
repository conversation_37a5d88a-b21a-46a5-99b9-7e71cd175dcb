"""
月度工资异动处理系统 - 报告生成集成组件

本模块实现Phase 3的报告生成集成功能，将现代化GUI与报告生成模块深度集成。

主要功能:
1. 报告生成接口适配 (模板选择+参数配置)
2. 报告预览功能 (实时预览+格式检查)
3. 报告生成进度 (进度条+状态更新)
4. 报告输出管理 (文件保存+打开)
5. 批量报告生成
6. 生成历史管理

Phase 3 任务: P3-002-3
创建时间: 2025-01-22
质量标准: 10/10 完美级
"""

import os
import sys
from typing import List, Dict, Any, Optional, Callable
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QProgressBar, QTextEdit, QSplitter, QGroupBox, QFrame,
    QFileDialog, QMessageBox, QTabWidget, QTableWidget,
    QComboBox, QSpinBox, QCheckBox, QLineEdit, QApplication,
    QListWidget, QListWidgetItem, QScrollArea, QGridLayout,
    QButtonGroup, QRadioButton, QSlider, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer, QPropertyAnimation, QEasingCurve, QSize
from PyQt5.QtGui import QFont, QColor, QPalette, QMovie, QIcon, QPixmap

from src.utils.log_config import setup_logger
from src.utils.logging_utils import bind_context, log_throttle, log_sample, redact
from src.core.application_service import ApplicationService, ServiceResult
from src.modules.report_generation import ReportManager, TemplateEngine
from src.gui.modern_table_editor import ModernTableEditor
from src.gui.toast_system import ToastManager


class ReportGenerationWorker(QThread):
    """报告生成工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)          # 进度更新信号
    status_updated = pyqtSignal(str)            # 状态更新信号
    report_generated = pyqtSignal(str, bool)    # 报告生成完成信号 (output_path, success)
    error_occurred = pyqtSignal(str)            # 错误发生信号
    generation_completed = pyqtSignal(bool)     # 生成完成信号
    
    def __init__(self, template_key: str, data: Dict[str, Any], 
                 output_path: str, report_manager: ReportManager):
        """初始化报告生成工作线程"""
        super().__init__()
        self.template_key = template_key
        self.data = data
        self.output_path = output_path
        self.report_manager = report_manager
        base_logger = setup_logger(__name__)
        self.logger = bind_context(base_logger, component="ReportGenerationWorker", template_key=template_key)
        
    def run(self):
        """执行报告生成"""
        try:
            import time as _t, os as _os
            t0 = _t.time()
            # 节流记录开始
            try:
                from src.utils.logging_utils import log_throttle
                if log_throttle('report-gen-start', 2.0):
                    self.logger.info(f"开始报告生成: template={self.template_key}, output={_os.path.basename(self.output_path)}")
            except Exception:
                pass
            self.status_updated.emit("正在准备模板...")
            self.progress_updated.emit(10)
            
            # 获取模板信息
            template_info = self.report_manager.template_engine.get_template_info(self.template_key)
            
            self.status_updated.emit("正在生成报告...")
            self.progress_updated.emit(30)
            
            # 生成报告
            success = self.report_manager._generate_sync(
                template_key=self.template_key,
                data=self.data,
                output_path=self.output_path,
                report_type=template_info['type']
            )
            
            self.progress_updated.emit(80)
            
            if success:
                self.status_updated.emit("报告生成完成")
                self.progress_updated.emit(100)
                self.report_generated.emit(self.output_path, True)
                # 节流记录完成
                try:
                    elapsed_ms = int((_t.time() - t0) * 1000)
                    size_str = None
                    try:
                        sz = _os.path.getsize(self.output_path)
                        size_str = f"{sz/1024:.1f}KB" if sz < 1024*1024 else f"{sz/(1024*1024):.1f}MB"
                    except Exception:
                        pass
                    from src.utils.logging_utils import log_throttle
                    if log_throttle('report-gen-success', 2.0):
                        extra = f", size={size_str}" if size_str else ""
                        self.logger.info(f"报告生成成功: {_os.path.basename(self.output_path)}, {elapsed_ms}ms{extra}")
                except Exception:
                    pass
            else:
                self.status_updated.emit("报告生成失败")
                self.error_occurred.emit("报告生成过程中发生错误")
                self.report_generated.emit(self.output_path, False)
            
            self.generation_completed.emit(success)
            
        except Exception as e:
            self.logger.error(f"报告生成失败: {e}")
            self.error_occurred.emit(str(e))
            self.generation_completed.emit(False)


class TemplateSelectionWidget(QWidget):
    """模板选择组件"""
    
    template_selected = pyqtSignal(str)  # 模板选择信号
    
    def __init__(self, template_engine: TemplateEngine, parent=None):
        """初始化模板选择组件"""
        super().__init__(parent)
        self.template_engine = template_engine
        self._init_ui()
        self._load_templates()
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(12)
        
        # 标题
        title_label = QLabel("选择报告模板")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 模板类型筛选
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("类型筛选:"))
        
        self.type_combo = QComboBox()
        self.type_combo.addItems(["全部", "Word文档", "Excel表格"])
        self.type_combo.currentTextChanged.connect(self._filter_templates)
        filter_layout.addWidget(self.type_combo)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # 模板列表
        self.template_list = QListWidget()
        self.template_list.setMinimumHeight(200)
        self.template_list.itemClicked.connect(self._on_template_selected)
        self.template_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #d0d7de;
                border-radius: 6px;
                background-color: white;
                selection-background-color: #0078d4;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f6f8fa;
            }
            QListWidget::item:hover {
                background-color: #f6f8fa;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
        """)
        layout.addWidget(self.template_list)
        
        # 模板预览区
        preview_group = QGroupBox("模板预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.template_name_label = QLabel("未选择模板")
        self.template_name_label.setFont(QFont("", 12, QFont.Bold))
        preview_layout.addWidget(self.template_name_label)
        
        self.template_description = QTextEdit()
        self.template_description.setMaximumHeight(80)
        self.template_description.setReadOnly(True)
        self.template_description.setPlainText("请选择一个模板查看详细信息")
        preview_layout.addWidget(self.template_description)
        
        layout.addWidget(preview_group)
        
    def _load_templates(self):
        """加载可用模板"""
        try:
            templates = self.template_engine.get_available_templates()
            self.all_templates = templates
            self._display_templates(templates)
        except Exception as e:
            self.logger.error(f"加载模板失败: {e}")
            
    def _display_templates(self, templates: List[Dict[str, Any]]):
        """显示模板列表"""
        self.template_list.clear()
        
        for template in templates:
            item = QListWidgetItem()
            
            # 设置显示文本
            display_text = f"{template['name']}"
            if template['type'] == 'word':
                display_text += " [Word]"
            elif template['type'] == 'excel':
                display_text += " [Excel]"
                
            item.setText(display_text)
            item.setData(Qt.UserRole, template['key'])
            
            # 添加到列表
            self.template_list.addItem(item)
            
    def _filter_templates(self, filter_type: str):
        """根据类型筛选模板"""
        if filter_type == "全部":
            filtered_templates = self.all_templates
        elif filter_type == "Word文档":
            filtered_templates = [t for t in self.all_templates if t['type'] == 'word']
        elif filter_type == "Excel表格":
            filtered_templates = [t for t in self.all_templates if t['type'] == 'excel']
        else:
            filtered_templates = self.all_templates
            
        self._display_templates(filtered_templates)
        
    def _on_template_selected(self, item: QListWidgetItem):
        """模板选择事件"""
        template_key = item.data(Qt.UserRole)
        
        try:
            template_info = self.template_engine.get_template_info(template_key)
            
            # 更新预览信息
            self.template_name_label.setText(template_info['name'])
            self.template_description.setPlainText(
                f"类型: {template_info['type']}\n"
                f"描述: {template_info.get('description', '暂无描述')}\n"
                f"创建时间: {template_info.get('created_time', '未知')}"
            )
            
            # 发送选择信号
            self.template_selected.emit(template_key)
            
        except Exception as e:
            self.logger.error(f"获取模板信息失败: {e}")


class ReportPreviewWidget(QWidget):
    """报告预览组件"""
    
    def __init__(self, parent=None):
        """初始化预览组件"""
        super().__init__(parent)
        self._init_ui()
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("报告预览")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 预览区域
        self.preview_area = QScrollArea()
        self.preview_area.setMinimumHeight(300)
        self.preview_area.setWidgetResizable(True)
        self.preview_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #d0d7de;
                border-radius: 6px;
                background-color: white;
            }
        """)
        
        # 预览内容
        self.preview_content = QLabel("暂无预览内容")
        self.preview_content.setAlignment(Qt.AlignCenter)
        self.preview_content.setStyleSheet("color: #6c757d; font-size: 14px;")
        self.preview_area.setWidget(self.preview_content)
        
        layout.addWidget(self.preview_area)
        
        # 预览控制
        controls_layout = QHBoxLayout()
        
        self.refresh_preview_btn = QPushButton("刷新预览")
        self.refresh_preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                border: 1px solid #d0d7de;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e9ecef;
            }
        """)
        controls_layout.addWidget(self.refresh_preview_btn)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
    def update_preview(self, preview_text: str):
        """更新预览内容"""
        self.preview_content.setText(preview_text)
        self.preview_content.setAlignment(Qt.AlignTop | Qt.AlignLeft)


class ReportGenerationStatusWidget(QWidget):
    """报告生成状态组件"""
    
    def __init__(self, parent=None):
        """初始化状态组件"""
        super().__init__(parent)
        self._init_ui()
        self._init_animations()
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 状态标题
        self.status_label = QLabel("生成状态")
        status_font = QFont()
        status_font.setPointSize(12)
        status_font.setBold(True)
        self.status_label.setFont(status_font)
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: #f8f9fa;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                           stop:0 #28a745, stop:1 #20c997);
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 状态描述
        self.status_description = QLabel("就绪")
        self.status_description.setStyleSheet("color: #6c757d; font-size: 13px;")
        layout.addWidget(self.status_description)
        
        # 生成信息
        info_group = QGroupBox("生成信息")
        info_layout = QVBoxLayout(info_group)
        
        self.template_label = QLabel("模板: 未选择")
        self.output_path_label = QLabel("输出路径: 未设置")
        self.generation_time_label = QLabel("生成时间: -")
        self.file_size_label = QLabel("文件大小: -")
        
        for label in [self.template_label, self.output_path_label, 
                     self.generation_time_label, self.file_size_label]:
            label.setStyleSheet("font-size: 12px; color: #495057;")
            info_layout.addWidget(label)
        
        layout.addWidget(info_group)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        self.open_file_btn = QPushButton("打开文件")
        self.open_file_btn.setEnabled(False)
        self.open_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover:enabled {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)
        buttons_layout.addWidget(self.open_file_btn)
        
        self.open_folder_btn = QPushButton("打开文件夹")
        self.open_folder_btn.setEnabled(False)
        self.open_folder_btn.setStyleSheet(self.open_file_btn.styleSheet())
        buttons_layout.addWidget(self.open_folder_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
    def _init_animations(self):
        """初始化动画效果"""
        self.progress_animation = QPropertyAnimation(self.progress_bar, b"value")
        self.progress_animation.setDuration(300)
        self.progress_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def update_progress(self, progress: int):
        """更新进度"""
        self.progress_animation.setEndValue(progress)
        self.progress_animation.start()
    
    def update_status(self, status: str):
        """更新状态描述"""
        self.status_description.setText(status)
    
    def update_generation_info(self, info: Dict[str, Any]):
        """更新生成信息"""
        if 'template_name' in info:
            self.template_label.setText(f"模板: {info['template_name']}")
        if 'output_path' in info:
            self.output_path_label.setText(f"输出路径: {info['output_path']}")
        if 'generation_time' in info:
            self.generation_time_label.setText(f"生成时间: {info['generation_time']}")
        if 'file_size' in info:
            self.file_size_label.setText(f"文件大小: {info['file_size']}")
    
    def set_generation_completed(self, success: bool, output_path: str = None):
        """设置生成完成状态"""
        if success and output_path:
            self.open_file_btn.setEnabled(True)
            self.open_folder_btn.setEnabled(True)
            self.output_path = output_path
        else:
            self.open_file_btn.setEnabled(False)
            self.open_folder_btn.setEnabled(False)
    
    def reset(self):
        """重置状态"""
        self.progress_bar.setValue(0)
        self.status_description.setText("就绪")
        self.template_label.setText("模板: 未选择")
        self.output_path_label.setText("输出路径: 未设置")
        self.generation_time_label.setText("生成时间: -")
        self.file_size_label.setText("文件大小: -")
        self.open_file_btn.setEnabled(False)
        self.open_folder_btn.setEnabled(False)


class ReportGenerationIntegration(QWidget):
    """
    报告生成集成主组件
    
    整合现代化GUI与报告生成模块，提供完整的报告生成体验。
    """
    
    # 信号定义
    report_generated = pyqtSignal(str, bool)    # 报告生成完成信号
    generation_started = pyqtSignal(str)        # 生成开始信号
    generation_completed = pyqtSignal(bool)     # 生成完成信号
    
    def __init__(self, parent=None):
        """初始化报告生成集成组件"""
        super().__init__(parent)
        base_logger = setup_logger(__name__)
        # 组件级上下文
        self.logger = bind_context(base_logger, component="ReportGenerationIntegration")
        
        # 初始化服务
        self._init_services()
        
        # 初始化UI
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
        
        # 状态管理
        self.current_template_key = None
        self.current_data = {}
        self.generation_worker = None
        
        self.logger.info("报告生成集成组件初始化完成")
        
    def _init_services(self):
        """初始化服务组件"""
        try:
            self.app_service = ApplicationService()
            self.report_manager = ReportManager()
            self.template_engine = self.report_manager.template_engine
            self.toast_system = ToastManager()
            self.toast_system.set_parent(self)
            
        except Exception as e:
            self.logger.error(f"初始化服务失败: {e}")
            raise
    
    def _init_ui(self):
        """初始化用户界面"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(16)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_splitter.setChildrenCollapsible(False)
        
        # 左侧面板 - 模板选择和配置
        left_panel = self._create_left_panel()
        main_splitter.addWidget(left_panel)
        
        # 右侧面板 - 预览和状态
        right_panel = self._create_right_panel()
        main_splitter.addWidget(right_panel)
        
        # 设置分割比例
        main_splitter.setSizes([400, 500])
        
        main_layout.addWidget(main_splitter)
        
    def _create_left_panel(self):
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        # 模板选择
        self.template_selector = TemplateSelectionWidget(self.template_engine)
        layout.addWidget(self.template_selector)
        
        # 数据配置区域
        data_config_group = QGroupBox("数据配置")
        data_config_layout = QVBoxLayout(data_config_group)
        
        # 数据源选择
        data_source_layout = QHBoxLayout()
        data_source_layout.addWidget(QLabel("数据源:"))
        
        self.data_source_combo = QComboBox()
        self.data_source_combo.addItems(["当前表格数据", "从文件导入", "手动输入"])
        data_source_layout.addWidget(self.data_source_combo)
        
        data_config_layout.addLayout(data_source_layout)
        
        # 输出设置
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出文件名:"))
        
        self.output_filename_edit = QLineEdit()
        self.output_filename_edit.setPlaceholderText("留空自动生成")
        output_layout.addWidget(self.output_filename_edit)
        
        self.browse_output_btn = QPushButton("浏览...")
        self.browse_output_btn.setMaximumWidth(80)
        output_layout.addWidget(self.browse_output_btn)
        
        data_config_layout.addLayout(output_layout)
        
        layout.addWidget(data_config_group)
        
        # 生成控制
        controls_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("预览报告")
        self.preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        controls_layout.addWidget(self.preview_btn)
        
        self.generate_btn = QPushButton("生成报告")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        controls_layout.addWidget(self.generate_btn)
        
        layout.addLayout(controls_layout)
        
        return panel
        
    def _create_right_panel(self):
        """创建右侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        # 选项卡
        tab_widget = QTabWidget()
        
        # 预览选项卡
        self.preview_widget = ReportPreviewWidget()
        tab_widget.addTab(self.preview_widget, "预览")
        
        # 状态选项卡
        self.status_widget = ReportGenerationStatusWidget()
        tab_widget.addTab(self.status_widget, "生成状态")
        
        layout.addWidget(tab_widget)
        
        return panel
        
    def _connect_signals(self):
        """连接信号槽"""
        # 模板选择
        self.template_selector.template_selected.connect(self._on_template_selected)
        
        # 按钮事件
        self.browse_output_btn.clicked.connect(self._browse_output_path)
        self.preview_btn.clicked.connect(self._preview_report)
        self.generate_btn.clicked.connect(self._generate_report)
        
        # 状态组件按钮
        self.status_widget.open_file_btn.clicked.connect(self._open_generated_file)
        self.status_widget.open_folder_btn.clicked.connect(self._open_output_folder)
        
        # 预览刷新
        self.preview_widget.refresh_preview_btn.clicked.connect(self._preview_report)
    
    def _on_template_selected(self, template_key: str):
        """模板选择事件"""
        self.current_template_key = template_key
        
        # 更新状态
        try:
            template_info = self.template_engine.get_template_info(template_key)
            self.status_widget.update_generation_info({
                'template_name': template_info['name']
            })
            
            # 启用按钮
            self.preview_btn.setEnabled(True)
            self.generate_btn.setEnabled(True)
            
            self.toast_system.show_success(f"已选择模板: {template_info['name']}")
            
        except Exception as e:
            self.logger.error(f"处理模板选择失败: {e}")
            self.toast_system.show_error("模板选择失败")
    
    def _browse_output_path(self):
        """浏览输出路径"""
        if self.current_template_key:
            try:
                template_info = self.template_engine.get_template_info(self.current_template_key)
                file_extension = '.docx' if template_info['type'] == 'word' else '.xlsx'
                
                file_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "选择输出文件",
                    f"{template_info['name']}{file_extension}",
                    f"{'Word文档 (*.docx)' if template_info['type'] == 'word' else 'Excel文件 (*.xlsx)'}"
                )
                
                if file_path:
                    self.output_filename_edit.setText(file_path)
                    
            except Exception as e:
                self.logger.error(f"浏览输出路径失败: {e}")
    
    def _get_report_data(self) -> Dict[str, Any]:
        """获取报告数据"""
        # 这里应该根据数据源选择获取实际数据
        # 暂时返回示例数据
        return {
            'title': '月度工资异动报告',
            'date': '2025年1月',
            'department': '人力资源部',
            'changes': [
                {'name': '张三', 'position': '工程师', 'change_type': '薪资调整', 'amount': 500},
                {'name': '李四', 'position': '经理', 'change_type': '职位变动', 'amount': 1000},
            ],
            'total_changes': 2,
            'total_amount': 1500
        }
    
    def _preview_report(self):
        """预览报告"""
        if not self.current_template_key:
            self.toast_system.show_warning("请先选择模板")
            return
        
        try:
            # 获取数据
            data = self._get_report_data()
            
            # 生成预览文本
            preview_text = self._generate_preview_text(data)
            
            # 更新预览
            self.preview_widget.update_preview(preview_text)
            
            self.toast_system.show_success("预览已更新")
            
        except Exception as e:
            self.logger.error(f"预览报告失败: {e}")
            self.toast_system.show_error("预览失败")
    
    def _generate_preview_text(self, data: Dict[str, Any]) -> str:
        """生成预览文本"""
        preview_lines = [
            f"报告标题: {data.get('title', '未设置')}",
            f"报告日期: {data.get('date', '未设置')}",
            f"部门: {data.get('department', '未设置')}",
            "",
            "异动明细:",
        ]
        
        for i, change in enumerate(data.get('changes', []), 1):
            preview_lines.append(
                f"{i}. {change.get('name', '')} - {change.get('change_type', '')} - {change.get('amount', 0)}元"
            )
        
        preview_lines.extend([
            "",
            f"总计异动人数: {data.get('total_changes', 0)}",
            f"总计金额变动: {data.get('total_amount', 0)}元"
        ])
        
        return "\n".join(preview_lines)
    
    def _generate_report(self):
        """生成报告"""
        if not self.current_template_key:
            self.toast_system.show_warning("请先选择模板")
            return
        
        try:
            # 获取输出路径
            output_path = self.output_filename_edit.text().strip()
            if not output_path:
                # 自动生成文件名
                template_info = self.template_engine.get_template_info(self.current_template_key)
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                file_extension = '.docx' if template_info['type'] == 'word' else '.xlsx'
                output_path = os.path.join(
                    self.report_manager.output_base_path,
                    f"{template_info['name']}_{timestamp}{file_extension}"
                )
            
            # 获取数据
            data = self._get_report_data()
            
            # 重置状态
            self.status_widget.reset()
            self.status_widget.update_generation_info({
                'template_name': self.template_engine.get_template_info(self.current_template_key)['name'],
                'output_path': output_path
            })
            
            # 创建工作线程
            self.generation_worker = ReportGenerationWorker(
                self.current_template_key, data, output_path, self.report_manager
            )
            # 绑定任务上下文（task_id 在 worker 内部不可见，这里加模板与输出名）
            try:
                from src.utils.logging_utils import bind_context
                self.logger = bind_context(self.logger, template_key=self.current_template_key,
                                           output_basename=os.path.basename(output_path))
            except Exception:
                pass
            
            # 连接工作线程信号
            self.generation_worker.progress_updated.connect(self.status_widget.update_progress)
            self.generation_worker.status_updated.connect(self.status_widget.update_status)
            self.generation_worker.report_generated.connect(self._on_report_generated)
            self.generation_worker.error_occurred.connect(self._on_generation_error)
            self.generation_worker.generation_completed.connect(self._on_generation_completed)
            
            # 启动生成
            self.generation_worker.start()
            
            # 禁用生成按钮
            self.generate_btn.setEnabled(False)
            
            # 发送开始信号
            self.generation_started.emit(self.current_template_key)
            
            self.toast_system.show_info("开始生成报告...")
            
        except Exception as e:
            self.logger.error(f"启动报告生成失败: {e}")
            self.toast_system.show_error("启动生成失败")
    
    def _on_report_generated(self, output_path: str, success: bool):
        """报告生成完成"""
        if success:
            # 更新文件信息
            try:
                file_size = os.path.getsize(output_path)
                file_size_str = f"{file_size / 1024:.1f} KB" if file_size < 1024*1024 else f"{file_size / (1024*1024):.1f} MB"
                
                from datetime import datetime
                generation_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                self.status_widget.update_generation_info({
                    'generation_time': generation_time,
                    'file_size': file_size_str
                })
                
                self.status_widget.set_generation_completed(True, output_path)
                
                self.toast_system.show_success(f"报告生成成功: {os.path.basename(output_path)}")
                
            except Exception as e:
                self.logger.error(f"更新文件信息失败: {e}")
        else:
            self.status_widget.set_generation_completed(False)
            self.toast_system.show_error("报告生成失败")
        
        # 发送完成信号
        self.report_generated.emit(output_path, success)
    
    def _on_generation_error(self, error_message: str):
        """生成错误处理"""
        self.toast_system.show_error(f"生成错误: {error_message}")
    
    def _on_generation_completed(self, success: bool):
        """生成完成处理"""
        # 重新启用生成按钮
        self.generate_btn.setEnabled(True)
        
        # 发送完成信号
        self.generation_completed.emit(success)
    
    def _open_generated_file(self):
        """打开生成的文件"""
        if hasattr(self.status_widget, 'output_path'):
            try:
                os.startfile(self.status_widget.output_path)
            except Exception as e:
                self.logger.error(f"打开文件失败: {e}")
                self.toast_system.show_error("打开文件失败")
    
    def _open_output_folder(self):
        """打开输出文件夹"""
        if hasattr(self.status_widget, 'output_path'):
            try:
                import subprocess
                subprocess.Popen(f'explorer /select,"{self.status_widget.output_path}"')
            except Exception as e:
                self.logger.error(f"打开文件夹失败: {e}")
                self.toast_system.show_error("打开文件夹失败")
    
    def set_data_source(self, data: Dict[str, Any]):
        """设置数据源"""
        self.current_data = data
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """获取可用模板列表"""
        return self.template_engine.get_available_templates()


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = ReportGenerationIntegration()
    window.setWindowTitle("报告生成集成测试")
    window.resize(1000, 700)
    window.show()
    
    sys.exit(app.exec_()) 