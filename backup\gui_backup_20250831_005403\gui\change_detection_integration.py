"""
检测模块集成组件
P3-002-2: 与change_detection模块深度集成

功能特性:
- 实时异动检测和显示
- 检测配置和参数调整
- 检测结果可视化
- 多种异动类型支持
- 置信度评估和过滤
"""

import sys
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel, QProgressBar,
    QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox, QComboBox,
    QCheckBox, QTextEdit, QSplitter, QTreeWidget, QTreeWidgetItem,
    QHeaderView, QAbstractItemView, QFrame, QScrollArea, QTabWidget,
    QMessageBox, QFileDialog, QDialog, QDialogButtonBox
)
from PyQt5.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
)
from PyQt5.QtGui import (
    QFont, QColor, QPalette, QPixmap, QPainter, QBrush
)
import logging
from src.utils.log_config import setup_logger
from src.modules.change_detection import ChangeDetector, BaselineManager
from src.modules.change_detection.change_detector import ChangeType
from src.core.config_manager import ConfigManager
from src.utils.logging_utils import log_sample, redact, bind_context

# 设置日志
base_logger = setup_logger(__name__)
# 组件级上下文
logger = bind_context(base_logger, component="ChangeDetectionIntegration")


class ChangeDetectionWorker(QThread):
    """异动检测工作线程"""
    
    # 信号定义
    detection_started = pyqtSignal()
    detection_progress = pyqtSignal(int, str)  # progress, message
    detection_completed = pyqtSignal(list, dict)  # changes, summary
    detection_error = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = None
        self.baseline_manager = None
        self.change_detector = None
        self.current_data = None
        self.detection_period = None
        self.is_cancelled = False
        
        logger.info("异动检测工作线程初始化完成")
    
    def setup_detection(self, config_manager: ConfigManager, 
                       baseline_manager: BaselineManager,
                       current_data: pd.DataFrame, 
                       detection_period: str):
        """设置检测参数"""
        self.config_manager = config_manager
        self.baseline_manager = baseline_manager
        self.change_detector = ChangeDetector(config_manager, baseline_manager)
        self.current_data = current_data
        self.detection_period = detection_period
        self.is_cancelled = False
    
    def cancel_detection(self):
        """取消检测"""
        self.is_cancelled = True
    
    def run(self):
        """执行异动检测"""
        try:
            self.detection_started.emit()
            
            if self.is_cancelled:
                return
            
            # 开始检测
            self.detection_progress.emit(10, "开始异动检测...")
            
            if self.is_cancelled:
                return
            
            # 执行检测
            self.detection_progress.emit(30, "分析基准数据...")
            changes = self.change_detector.detect_changes(
                self.current_data, 
                self.detection_period
            )
            
            if self.is_cancelled:
                return
            
            self.detection_progress.emit(70, "生成检测摘要...")
            summary = self.change_detector.get_change_summary()
            
            if self.is_cancelled:
                return
            
            self.detection_progress.emit(100, "检测完成")
            self.detection_completed.emit(changes, summary)
            
            logger.info(f"异动检测完成: 发现{len(changes)}项异动")
            
        except Exception as e:
            error_msg = f"异动检测失败: {e}"
            logger.error(error_msg)
            self.detection_error.emit(error_msg)


class ChangeTypeFilterWidget(QWidget):
    """异动类型过滤组件"""
    
    # 信号定义
    filter_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.type_checkboxes = {}
        self.confidence_filter = None
        self.setup_ui()
        logger.info("异动类型过滤组件初始化完成")
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 异动类型过滤
        type_group = QGroupBox("异动类型筛选")
        type_layout = QVBoxLayout(type_group)
        
        # 全选/全不选
        select_all_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("全选")
        self.select_none_btn = QPushButton("全不选")
        select_all_layout.addWidget(self.select_all_btn)
        select_all_layout.addWidget(self.select_none_btn)
        select_all_layout.addStretch()
        
        # 异动类型复选框
        for change_type in ChangeType:
            checkbox = QCheckBox(change_type.value)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.on_filter_changed)
            self.type_checkboxes[change_type.value] = checkbox
            type_layout.addWidget(checkbox)
        
        type_layout.insertLayout(0, select_all_layout)
        
        # 置信度过滤
        confidence_group = QGroupBox("置信度筛选")
        confidence_layout = QFormLayout(confidence_group)
        
        self.confidence_filter = QDoubleSpinBox()
        self.confidence_filter.setRange(0.0, 1.0)
        self.confidence_filter.setSingleStep(0.1)
        self.confidence_filter.setValue(0.7)
        self.confidence_filter.setDecimals(1)
        self.confidence_filter.valueChanged.connect(self.on_filter_changed)
        
        confidence_layout.addRow("最低置信度:", self.confidence_filter)
        
        # 连接信号
        self.select_all_btn.clicked.connect(self.select_all_types)
        self.select_none_btn.clicked.connect(self.select_no_types)
        
        # 布局
        layout.addWidget(type_group)
        layout.addWidget(confidence_group)
        layout.addStretch()
    
    def select_all_types(self):
        """全选异动类型"""
        for checkbox in self.type_checkboxes.values():
            checkbox.setChecked(True)
        self.on_filter_changed()
    
    def select_no_types(self):
        """全不选异动类型"""
        for checkbox in self.type_checkboxes.values():
            checkbox.setChecked(False)
        self.on_filter_changed()
    
    def on_filter_changed(self):
        """过滤条件变化"""
        filter_config = self.get_filter_config()
        self.filter_changed.emit(filter_config)
    
    def get_filter_config(self) -> Dict[str, Any]:
        """获取过滤配置"""
        selected_types = []
        for type_name, checkbox in self.type_checkboxes.items():
            if checkbox.isChecked():
                selected_types.append(type_name)
        
        return {
            'selected_types': selected_types,
            'min_confidence': self.confidence_filter.value()
        }


class ChangeDetailWidget(QWidget):
    """异动详情显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        logger.info("异动详情显示组件初始化完成")
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 基本信息
        info_group = QGroupBox("基本信息")
        info_layout = QFormLayout(info_group)
        
        self.employee_id_label = QLabel("-")
        self.employee_name_label = QLabel("-")
        self.change_type_label = QLabel("-")
        self.confidence_label = QLabel("-")
        self.period_label = QLabel("-")
        self.detection_time_label = QLabel("-")
        
        info_layout.addRow("工号:", self.employee_id_label)
        info_layout.addRow("姓名:", self.employee_name_label)
        info_layout.addRow("异动类型:", self.change_type_label)
        info_layout.addRow("置信度:", self.confidence_label)
        info_layout.addRow("检测周期:", self.period_label)
        info_layout.addRow("检测时间:", self.detection_time_label)
        
        # 详细信息
        detail_group = QGroupBox("详细信息")
        detail_layout = QVBoxLayout(detail_group)
        
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        self.detail_text.setMaximumHeight(150)
        self.detail_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #fafafa;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        
        detail_layout.addWidget(self.detail_text)
        
        # 布局
        layout.addWidget(info_group)
        layout.addWidget(detail_group)
        layout.addStretch()
    
    def update_change_detail(self, change_record: Dict[str, Any]):
        """更新异动详情"""
        try:
            # 基本信息
            self.employee_id_label.setText(str(change_record.get('employee_id', '-')))
            self.employee_name_label.setText(str(change_record.get('name', '-')))
            self.change_type_label.setText(str(change_record.get('change_type', '-')))
            
            # 置信度显示
            confidence = change_record.get('confidence', 0)
            confidence_text = f"{confidence:.1%}"
            if confidence >= 0.9:
                confidence_color = "#28a745"  # 绿色
            elif confidence >= 0.7:
                confidence_color = "#ffc107"  # 黄色
            else:
                confidence_color = "#dc3545"  # 红色
            
            self.confidence_label.setText(f'<span style="color: {confidence_color}; font-weight: bold;">{confidence_text}</span>')
            
            self.period_label.setText(str(change_record.get('period', '-')))
            
            # 检测时间
            detected_time = change_record.get('detected_time')
            if detected_time:
                if isinstance(detected_time, datetime):
                    time_str = detected_time.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    time_str = str(detected_time)
            else:
                time_str = "-"
            self.detection_time_label.setText(time_str)
            
            # 详细信息
            details = change_record.get('details', {})
            detail_text = []
            
            if isinstance(details, dict):
                for key, value in details.items():
                    if key == 'description':
                        detail_text.append(f"描述: {value}")
                    elif key == 'change_amount':
                        detail_text.append(f"变动金额: {value:+.2f}")
                    elif key == 'change_rate':
                        detail_text.append(f"变动率: {value:.1%}")
                    elif key == 'baseline_value':
                        detail_text.append(f"基准值: {value:.2f}")
                    elif key == 'current_value':
                        detail_text.append(f"当前值: {value:.2f}")
                    elif key == 'field':
                        detail_text.append(f"变动字段: {value}")
                    else:
                        detail_text.append(f"{key}: {value}")
            else:
                detail_text.append(str(details))
            
            self.detail_text.setPlainText("\n".join(detail_text))
            
        except Exception as e:
            logger.error(f"更新异动详情失败: {e}")
            self.clear_detail()
    
    def clear_detail(self):
        """清空详情"""
        self.employee_id_label.setText("-")
        self.employee_name_label.setText("-")
        self.change_type_label.setText("-")
        self.confidence_label.setText("-")
        self.period_label.setText("-")
        self.detection_time_label.setText("-")
        self.detail_text.clear()


class ChangeDetectionResultWidget(QWidget):
    """异动检测结果显示组件"""
    
    # 信号定义
    change_selected = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.changes_data = []
        self.filtered_data = []
        self.setup_ui()
        logger.info("异动检测结果组件初始化完成")
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 结果统计
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Box)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        stats_layout = QHBoxLayout(stats_frame)
        
        self.total_label = QLabel("总计: 0")
        self.high_conf_label = QLabel("高置信度: 0")
        self.medium_conf_label = QLabel("中等置信度: 0")
        self.low_conf_label = QLabel("低置信度: 0")
        
        for label in [self.total_label, self.high_conf_label, self.medium_conf_label, self.low_conf_label]:
            label.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    padding: 5px 10px;
                    border-radius: 4px;
                    background-color: white;
                }
            """)
        
        self.high_conf_label.setStyleSheet(self.high_conf_label.styleSheet() + "color: #28a745;")
        self.medium_conf_label.setStyleSheet(self.medium_conf_label.styleSheet() + "color: #ffc107;")
        self.low_conf_label.setStyleSheet(self.low_conf_label.styleSheet() + "color: #dc3545;")
        
        stats_layout.addWidget(self.total_label)
        stats_layout.addWidget(self.high_conf_label)
        stats_layout.addWidget(self.medium_conf_label)
        stats_layout.addWidget(self.low_conf_label)
        stats_layout.addStretch()
        
        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.results_table.setSelectionMode(QAbstractItemView.SingleSelection)
        
        # 设置表头
        headers = ["工号", "姓名", "异动类型", "置信度", "检测周期", "描述"]
        self.results_table.setColumnCount(len(headers))
        self.results_table.setHorizontalHeaderLabels(headers)
        
        # 表格样式
        self.results_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 6px;
                gridline-color: #eee;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                border: none;
                border-right: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # 调整列宽
        header = self.results_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setDefaultSectionSize(100)
        
        # 连接信号
        self.results_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        # 布局
        layout.addWidget(stats_frame)
        layout.addWidget(self.results_table)
    
    def update_results(self, changes: List[Dict[str, Any]]):
        """更新检测结果"""
        try:
            self.changes_data = changes
            self.filtered_data = changes.copy()
            self._populate_table()
            self._update_statistics()
            
            logger.info(f"检测结果更新完成: {len(changes)}项异动")
            
        except Exception as e:
            logger.error(f"更新检测结果失败: {e}")
    
    def apply_filter(self, filter_config: Dict[str, Any]):
        """应用过滤条件"""
        try:
            selected_types = filter_config.get('selected_types', [])
            min_confidence = filter_config.get('min_confidence', 0.0)
            
            self.filtered_data = []
            for change in self.changes_data:
                # 类型过滤
                if change.get('change_type') not in selected_types:
                    continue
                
                # 置信度过滤
                if change.get('confidence', 0) < min_confidence:
                    continue
                
                self.filtered_data.append(change)
            
            self._populate_table()
            self._update_statistics()
            
            logger.debug(f"过滤应用完成: {len(self.filtered_data)}/{len(self.changes_data)}项")
            
        except Exception as e:
            logger.error(f"应用过滤失败: {e}")
    
    def _populate_table(self):
        """填充表格"""
        try:
            self.results_table.setRowCount(len(self.filtered_data))
            
            for row, change in enumerate(self.filtered_data):
                # 工号
                self.results_table.setItem(row, 0, QTableWidgetItem(str(change.get('employee_id', ''))))
                
                # 姓名
                self.results_table.setItem(row, 1, QTableWidgetItem(str(change.get('name', ''))))
                
                # 异动类型
                change_type_item = QTableWidgetItem(str(change.get('change_type', '')))
                self.results_table.setItem(row, 2, change_type_item)
                
                # 置信度
                confidence = change.get('confidence', 0)
                confidence_item = QTableWidgetItem(f"{confidence:.1%}")
                
                # 根据置信度设置颜色
                if confidence >= 0.9:
                    confidence_item.setForeground(QColor("#28a745"))
                elif confidence >= 0.7:
                    confidence_item.setForeground(QColor("#ffc107"))
                else:
                    confidence_item.setForeground(QColor("#dc3545"))
                
                self.results_table.setItem(row, 3, confidence_item)
                
                # 检测周期
                self.results_table.setItem(row, 4, QTableWidgetItem(str(change.get('period', ''))))
                
                # 描述
                details = change.get('details', {})
                description = details.get('description', '') if isinstance(details, dict) else str(details)
                self.results_table.setItem(row, 5, QTableWidgetItem(description))
            
            # 调整列宽
            self.results_table.resizeColumnsToContents()
            
        except Exception as e:
            logger.error(f"填充表格失败: {e}")
    
    def _update_statistics(self):
        """更新统计信息"""
        try:
            total = len(self.filtered_data)
            high_conf = len([c for c in self.filtered_data if c.get('confidence', 0) >= 0.9])
            medium_conf = len([c for c in self.filtered_data if 0.7 <= c.get('confidence', 0) < 0.9])
            low_conf = len([c for c in self.filtered_data if c.get('confidence', 0) < 0.7])
            
            self.total_label.setText(f"总计: {total}")
            self.high_conf_label.setText(f"高置信度: {high_conf}")
            self.medium_conf_label.setText(f"中等置信度: {medium_conf}")
            self.low_conf_label.setText(f"低置信度: {low_conf}")
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def on_selection_changed(self):
        """选择变化处理"""
        try:
            current_row = self.results_table.currentRow()
            if 0 <= current_row < len(self.filtered_data):
                change_record = self.filtered_data[current_row]
                self.change_selected.emit(change_record)
        except Exception as e:
            logger.error(f"选择变化处理失败: {e}")


class ChangeDetectionIntegration(QWidget):
    """异动检测集成组件"""
    
    # 信号定义
    detection_completed = pyqtSignal(list, dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = None
        self.baseline_manager = None
        self.worker = None
        self.current_data = None
        self.setup_ui()
        self.setup_managers()
        logger.info("异动检测集成组件初始化完成")
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_panel = self.create_control_panel()
        
        # 主要内容
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：过滤和控制
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 过滤组件
        self.filter_widget = ChangeTypeFilterWidget()
        left_layout.addWidget(self.filter_widget)
        
        # 详情组件
        self.detail_widget = ChangeDetailWidget()
        left_layout.addWidget(self.detail_widget)
        
        left_widget.setMaximumWidth(300)
        
        # 右侧：结果显示
        self.result_widget = ChangeDetectionResultWidget()
        
        main_splitter.addWidget(left_widget)
        main_splitter.addWidget(self.result_widget)
        main_splitter.setSizes([300, 700])
        
        # 连接信号
        self.filter_widget.filter_changed.connect(self.result_widget.apply_filter)
        self.result_widget.change_selected.connect(self.detail_widget.update_change_detail)
        
        # 布局
        layout.addWidget(control_panel)
        layout.addWidget(main_splitter)
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Box)
        panel.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        layout = QHBoxLayout(panel)
        
        # 检测控制
        self.start_detection_btn = QPushButton("🔍 开始检测")
        self.cancel_detection_btn = QPushButton("⏹️ 取消检测")
        self.load_data_btn = QPushButton("📂 加载数据")
        self.export_results_btn = QPushButton("📤 导出结果")
        
        # 按钮样式
        button_style = """
            QPushButton {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e9ecef;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
            QPushButton:disabled {
                background-color: #f8f9fa;
                color: #6c757d;
            }
        """
        
        for btn in [self.start_detection_btn, self.cancel_detection_btn, 
                   self.load_data_btn, self.export_results_btn]:
            btn.setStyleSheet(button_style)
        
        # 进度显示
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_label = QLabel("就绪")
        
        # 连接信号
        self.start_detection_btn.clicked.connect(self.start_detection)
        self.cancel_detection_btn.clicked.connect(self.cancel_detection)
        self.load_data_btn.clicked.connect(self.load_data)
        self.export_results_btn.clicked.connect(self.export_results)
        
        # 初始状态
        self.cancel_detection_btn.setEnabled(False)
        self.export_results_btn.setEnabled(False)
        
        # 布局
        layout.addWidget(self.load_data_btn)
        layout.addWidget(self.start_detection_btn)
        layout.addWidget(self.cancel_detection_btn)
        layout.addWidget(self.export_results_btn)
        layout.addStretch()
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        
        return panel
    
    def setup_managers(self):
        """设置管理器"""
        try:
            self.config_manager = ConfigManager()
            self.baseline_manager = BaselineManager(self.config_manager)
            logger.info("管理器设置完成")
        except Exception as e:
            logger.error(f"管理器设置失败: {e}")
    
    def load_data(self):
        """加载数据"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择工资数据文件", "", 
                "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv)"
            )
            
            if file_path:
                # 加载数据
                if file_path.endswith('.csv'):
                    self.current_data = pd.read_csv(file_path, encoding='utf-8')
                else:
                    self.current_data = pd.read_excel(file_path)
                
                self.status_label.setText(f"数据已加载: {len(self.current_data)}行")
                self.start_detection_btn.setEnabled(True)
                
                logger.info(f"数据加载成功: {file_path}, {len(self.current_data)}行")
                
        except Exception as e:
            error_msg = f"数据加载失败: {e}"
            logger.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def start_detection(self):
        """开始异动检测"""
        try:
            if self.current_data is None:
                QMessageBox.warning(self, "警告", "请先加载数据")
                return
            
            # 获取检测周期
            period = datetime.now().strftime("%Y-%m")
            
            # 创建工作线程
            self.worker = ChangeDetectionWorker()
            self.worker.setup_detection(
                self.config_manager,
                self.baseline_manager,
                self.current_data,
                period
            )
            
            # 连接信号
            self.worker.detection_started.connect(self.on_detection_started)
            self.worker.detection_progress.connect(self.on_detection_progress)
            self.worker.detection_completed.connect(self.on_detection_completed)
            self.worker.detection_error.connect(self.on_detection_error)
            
            # 启动检测
            self.worker.start()
            
        except Exception as e:
            error_msg = f"启动检测失败: {e}"
            logger.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def cancel_detection(self):
        """取消检测"""
        if self.worker:
            self.worker.cancel_detection()
            self.worker.wait()
            self.reset_ui_state()
    
    def export_results(self):
        """导出结果"""
        try:
            if not hasattr(self.result_widget, 'filtered_data') or not self.result_widget.filtered_data:
                QMessageBox.warning(self, "警告", "没有可导出的结果")
                return
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出检测结果", "detection_results.xlsx",
                "Excel文件 (*.xlsx)"
            )
            
            if file_path:
                # 转换为DataFrame并导出
                export_data = []
                for change in self.result_widget.filtered_data:
                    row = {
                        '工号': change.get('employee_id', ''),
                        '姓名': change.get('name', ''),
                        '异动类型': change.get('change_type', ''),
                        '置信度': change.get('confidence', 0),
                        '检测周期': change.get('period', ''),
                        '检测时间': change.get('detected_time', ''),
                        '详细信息': str(change.get('details', {}))
                    }
                    export_data.append(row)
                
                df = pd.DataFrame(export_data)
                df.to_excel(file_path, index=False)
                
                QMessageBox.information(self, "成功", f"结果已导出到:\n{file_path}")
                logger.info(f"检测结果已导出: {file_path}")
                
        except Exception as e:
            error_msg = f"导出结果失败: {e}"
            logger.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def on_detection_started(self):
        """检测开始处理"""
        self.start_detection_btn.setEnabled(False)
        self.cancel_detection_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("检测中...")
    
    def on_detection_progress(self, progress: int, message: str):
        """检测进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
        # 高频进度日志：每5次输出一次，避免日志膨胀
        try:
            if log_sample("detect-progress-integration", 5):
                safe_msg = redact(str(message)) if message else ""
                logger.debug(f"检测进度: {progress}% - {safe_msg}")
        except Exception:
            pass
    
    def on_detection_completed(self, changes: List[Dict], summary: Dict):
        """检测完成处理"""
        self.result_widget.update_results(changes)
        self.reset_ui_state()
        self.export_results_btn.setEnabled(True)
        self.status_label.setText(f"检测完成: 发现{len(changes)}项异动")
        self.detection_completed.emit(changes, summary)
        
        # 显示摘要
        QMessageBox.information(
            self, "检测完成",
            f"异动检测完成！\n"
            f"总异动数: {summary.get('总异动数', 0)}\n"
            f"检测时间: {summary.get('检测时间', '')}"
        )
    
    def on_detection_error(self, error_msg: str):
        """检测错误处理"""
        self.reset_ui_state()
        QMessageBox.critical(self, "检测错误", error_msg)
    
    def reset_ui_state(self):
        """重置UI状态"""
        self.start_detection_btn.setEnabled(True)
        self.cancel_detection_btn.setEnabled(False)
        self.progress_bar.setVisible(False)


class ChangeDetectionDemo(QMainWindow):
    """异动检测集成演示"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        logger.info("异动检测集成演示启动")
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("异动检测集成演示 - P3-002-2")
        self.setGeometry(100, 100, 1200, 800)
        
        # 中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🎯 P3-002-2: 异动检测模块集成演示")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        
        # 功能说明
        info_label = QLabel("""
        📋 功能特性:
        • 与change_detection模块深度集成
        • 支持8种异动类型的智能检测
        • 实时检测进度和结果可视化
        • 灵活的过滤和筛选功能
        • 详细的异动信息展示和导出
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 1px solid #c8e6c9;
                border-radius: 6px;
                padding: 10px;
                font-size: 12px;
                color: #2e7d32;
            }
        """)
        
        # 检测集成组件
        self.detection_integration = ChangeDetectionIntegration()
        
        # 布局
        layout.addWidget(title_label)
        layout.addWidget(info_label)
        layout.addWidget(self.detection_integration)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建演示窗口
    demo = ChangeDetectionDemo()
    demo.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 