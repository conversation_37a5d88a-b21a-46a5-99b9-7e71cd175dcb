#!/usr/bin/env python3
"""
测试表格编辑功能修复
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTableWidget, QTableWidgetItem, QLabel
from PyQt5.QtCore import Qt, QTimer

def test_table_editing_fix():
    """测试表格编辑功能修复"""
    
    app = QApplication([])
    
    # 创建主窗口
    window = QMainWindow()
    window.setWindowTitle("表格编辑功能修复测试")
    window.resize(800, 600)
    
    # 创建中心部件
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("双击'数据库字段'列的单元格进行编辑测试")
    info_label.setStyleSheet("font-weight: bold; color: blue; padding: 10px;")
    layout.addWidget(info_label)
    
    # 创建表格
    table = QTableWidget()
    table.setColumnCount(6)
    table.setHorizontalHeaderLabels([
        "Excel列名", "数据库字段", "显示名称", "数据类型", "是否必需", "验证状态"
    ])
    
    # 设置表格属性（修复后的设置）
    table.setAlternatingRowColors(True)
    table.setSelectionBehavior(QTableWidget.SelectItems)  # 允许选择单个单元格进行编辑
    table.setSelectionMode(QTableWidget.SingleSelection)
    table.verticalHeader().setVisible(False)
    
    # 设置行高以确保编辑器有足够空间
    table.verticalHeader().setDefaultSectionSize(35)  # 设置默认行高为35px
    
    # 设置列宽
    column_widths = [150, 150, 120, 100, 80, 80]
    for col, width in enumerate(column_widths):
        table.setColumnWidth(col, width)
    
    # 添加测试数据
    test_data = [
        ("人员 代码", "人员代码", "人员代码"),
        ("姓 名", "姓名", "姓名"),
        ("基本\n工资", "基本工资", "基本工资"),
        ("年龄(岁)", "年龄岁", "年龄岁"),
        ("2025年津贴", "field_2025年津贴", "2025年津贴"),
    ]
    
    table.setRowCount(len(test_data))
    
    for row, (excel_col, db_field, display_name) in enumerate(test_data):
        # Excel列名（只读）
        excel_item = QTableWidgetItem(excel_col)
        excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
        excel_item.setToolTip("只读列：Excel原始列名")
        table.setItem(row, 0, excel_item)
        
        # 数据库字段（可编辑）
        db_item = QTableWidgetItem(db_field)
        db_item.setToolTip(f"可编辑列：双击编辑\n原字段名: {excel_col}\n清理后: {db_field}")
        table.setItem(row, 1, db_item)
        
        # 显示名称（可编辑）
        display_item = QTableWidgetItem(display_name)
        display_item.setToolTip("可编辑列：双击编辑")
        table.setItem(row, 2, display_item)
        
        # 数据类型（只读）
        type_item = QTableWidgetItem("VARCHAR(100)")
        type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
        table.setItem(row, 3, type_item)
        
        # 是否必需（只读）
        required_item = QTableWidgetItem("否")
        required_item.setFlags(required_item.flags() & ~Qt.ItemIsEditable)
        table.setItem(row, 4, required_item)
        
        # 验证状态（只读）
        status_item = QTableWidgetItem("✅")
        status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
        table.setItem(row, 5, status_item)
    
    layout.addWidget(table)
    
    # 添加状态标签
    status_label = QLabel("状态：等待测试...")
    status_label.setStyleSheet("padding: 10px; background: #f0f0f0;")
    layout.addWidget(status_label)
    
    # 显示窗口
    window.show()
    
    def show_instructions():
        print("=== 表格编辑功能修复测试 ===")
        print()
        print("修复内容：")
        print("1. ✅ 设置行高为35px（解决编辑器太窄问题）")
        print("2. ✅ 修改选择模式为SelectItems（允许单元格编辑）")
        print()
        print("测试步骤：")
        print("1. 双击'数据库字段'列的任意单元格")
        print("2. 检查编辑器是否有足够的高度（不再是一条缝）")
        print("3. 输入一些文字测试编辑功能")
        print("4. 按Enter确认编辑")
        print("5. 双击'显示名称'列测试其他可编辑列")
        print()
        print("预期结果：")
        print("- 编辑器高度约30px，清晰可见")
        print("- 可以正常输入和编辑文字")
        print("- 编辑完成后内容正确保存")
        print()
        status_label.setText("状态：请开始测试双击编辑功能")
    
    # 连接编辑信号
    def on_item_changed(item):
        row = item.row()
        col = item.column()
        text = item.text()
        col_names = ["Excel列名", "数据库字段", "显示名称", "数据类型", "是否必需", "验证状态"]
        status_label.setText(f"状态：已编辑 {col_names[col]} (行{row+1})：'{text}'")
        print(f"编辑成功：{col_names[col]} -> '{text}'")
    
    table.itemChanged.connect(on_item_changed)
    
    # 2秒后显示说明
    QTimer.singleShot(2000, show_instructions)
    
    # 15秒后自动关闭
    QTimer.singleShot(15000, app.quit)
    
    app.exec_()

if __name__ == "__main__":
    test_table_editing_fix()
