#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一数据流管理器 - P2级架构优化
实现单向数据流架构，明确数据转换节点
"""

from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
from loguru import logger
import time
from datetime import datetime

class DataFlowStage(Enum):
    """数据流阶段"""
    INPUT = "input"              # 数据输入
    VALIDATION = "validation"    # 数据验证
    TRANSFORMATION = "transformation"  # 数据转换
    FORMATTING = "formatting"    # 格式化
    CACHING = "caching"         # 缓存
    RENDERING = "rendering"     # 渲染
    OUTPUT = "output"           # 输出

@dataclass
class DataFlowContext:
    """数据流上下文"""
    stage: DataFlowStage
    data: Any
    metadata: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metrics: Dict[str, float] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def add_metric(self, name: str, value: float):
        """添加性能指标"""
        self.metrics[name] = value
    
    def add_error(self, error: str):
        """添加错误"""
        self.errors.append(error)
        logger.error(f"🔧 [P2-DataFlow] {self.stage.value}: {error}")
    
    def add_warning(self, warning: str):
        """添加警告"""
        self.warnings.append(warning)
        logger.warning(f"🔧 [P2-DataFlow] {self.stage.value}: {warning}")

class DataFlowNode:
    """数据流节点基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logger
        self.next_node: Optional['DataFlowNode'] = None
        self.error_handler: Optional[Callable] = None
    
    def set_next(self, node: 'DataFlowNode') -> 'DataFlowNode':
        """设置下一个节点"""
        self.next_node = node
        return node
    
    def set_error_handler(self, handler: Callable):
        """设置错误处理器"""
        self.error_handler = handler
    
    def process(self, context: DataFlowContext) -> DataFlowContext:
        """处理数据"""
        start_time = time.time()
        
        try:
            # 执行节点处理
            context = self._execute(context)
            
            # 记录性能指标
            elapsed = (time.time() - start_time) * 1000
            context.add_metric(f"{self.name}_ms", elapsed)
            
            # 传递给下一个节点
            if self.next_node and not context.errors:
                context = self.next_node.process(context)
            
        except Exception as e:
            context.add_error(f"{self.name} 处理失败: {str(e)}")
            
            # 调用错误处理器
            if self.error_handler:
                context = self.error_handler(context, e)
        
        return context
    
    def _execute(self, context: DataFlowContext) -> DataFlowContext:
        """执行节点逻辑（子类实现）"""
        raise NotImplementedError

class InputNode(DataFlowNode):
    """输入节点"""
    
    def __init__(self):
        super().__init__("Input")
    
    def _execute(self, context: DataFlowContext) -> DataFlowContext:
        context.stage = DataFlowStage.INPUT
        
        # 验证输入数据
        if context.data is None:
            context.add_error("输入数据为空")
        elif isinstance(context.data, pd.DataFrame) and context.data.empty:
            context.add_warning("输入DataFrame为空")
        else:
            self.logger.info(f"🔧 [P2-DataFlow] 输入数据: {type(context.data)}")
        
        return context

class ValidationNode(DataFlowNode):
    """验证节点"""
    
    def __init__(self):
        super().__init__("Validation")
        self._init_validators()
    
    def _init_validators(self):
        """初始化验证器"""
        from src.modules.data_management.data_flow_validator import DataFlowValidator, ValidationLevel
        self.validator = DataFlowValidator(ValidationLevel.MODERATE)
    
    def _execute(self, context: DataFlowContext) -> DataFlowContext:
        context.stage = DataFlowStage.VALIDATION
        
        if isinstance(context.data, pd.DataFrame):
            # 转换DataFrame为List[Dict]格式以兼容现有验证器
            data_list = context.data.to_dict('records')
            headers = list(context.data.columns)
            
            # 验证数据一致性
            result = self.validator.validate_data_consistency(
                data=data_list,
                headers=headers,
                table_type=context.metadata.get('table_type'),
                context=f"UnifiedDataFlow_{context.metadata.get('table_name', 'unknown')}"
            )
            
            if result.issues:
                for issue in result.issues:
                    context.add_warning(issue)
            
            if result.fixes_applied:
                # 转换回DataFrame
                if result.data:
                    context.data = pd.DataFrame(result.data)
                if result.headers:
                    context.metadata['headers'] = result.headers
                self.logger.info(f"🔧 [P2-DataFlow] 应用了 {len(result.fixes_applied)} 个修复")
        
        return context

class TransformationNode(DataFlowNode):
    """转换节点"""
    
    def __init__(self):
        super().__init__("Transformation")
    
    def _execute(self, context: DataFlowContext) -> DataFlowContext:
        context.stage = DataFlowStage.TRANSFORMATION
        
        if isinstance(context.data, pd.DataFrame):
            # 数据类型转换
            df = context.data
            
            # 自动检测并转换数值列
            for col in df.columns:
                if '工资' in col or '金额' in col or '津贴' in col:
                    try:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    except:
                        pass
            
            # 日期列转换
            for col in df.columns:
                if '日期' in col or '时间' in col:
                    try:
                        df[col] = pd.to_datetime(df[col], errors='coerce')
                    except:
                        pass
            
            context.data = df
            self.logger.info(f"🔧 [P2-DataFlow] 数据转换完成: {df.shape}")
        
        return context

class FormattingNode(DataFlowNode):
    """格式化节点"""
    
    def __init__(self):
        super().__init__("Formatting")
        self._init_formatter()
    
    def _init_formatter(self):
        """初始化格式化器"""
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        self.formatter = get_unified_format_manager()
    
    def _execute(self, context: DataFlowContext) -> DataFlowContext:
        context.stage = DataFlowStage.FORMATTING
        
        if isinstance(context.data, pd.DataFrame):
            # 格式化数据
            table_type = context.metadata.get('table_type', 'default')
            formatted = self.formatter.format_data(
                data=context.data,
                table_type=table_type
            )
            
            if formatted is not None:
                context.data = formatted
                self.logger.info(f"🔧 [P2-DataFlow] 格式化完成: {formatted.shape}")
            else:
                context.add_warning("格式化返回空结果")
        
        return context

class CachingNode(DataFlowNode):
    """缓存节点"""
    
    def __init__(self):
        super().__init__("Caching")
        self._cache = {}
        self._max_cache_size = 100
    
    def _execute(self, context: DataFlowContext) -> DataFlowContext:
        context.stage = DataFlowStage.CACHING
        
        # 生成缓存键
        table_name = context.metadata.get('table_name', 'unknown')
        cache_key = f"{table_name}_{hash(str(context.data))}"
        
        # 存储到缓存
        if len(self._cache) >= self._max_cache_size:
            # LRU清理
            oldest = min(self._cache.keys(), key=lambda k: self._cache[k]['timestamp'])
            del self._cache[oldest]
        
        self._cache[cache_key] = {
            'data': context.data,
            'timestamp': time.time()
        }
        
        context.metadata['cache_key'] = cache_key
        self.logger.debug(f"🔧 [P2-DataFlow] 数据已缓存: {cache_key}")
        
        return context

class RenderingNode(DataFlowNode):
    """渲染节点"""
    
    def __init__(self):
        super().__init__("Rendering")
    
    def _execute(self, context: DataFlowContext) -> DataFlowContext:
        context.stage = DataFlowStage.RENDERING
        
        # 这里可以添加具体的渲染逻辑
        # 例如：准备数据用于UI显示
        
        if isinstance(context.data, pd.DataFrame):
            # 确保数据适合渲染
            max_rows = context.metadata.get('max_rows', 1000)
            if len(context.data) > max_rows:
                context.data = context.data.head(max_rows)
                context.add_warning(f"数据截断到 {max_rows} 行用于渲染")
        
        self.logger.info(f"🔧 [P2-DataFlow] 渲染准备完成")
        return context

class OutputNode(DataFlowNode):
    """输出节点"""
    
    def __init__(self):
        super().__init__("Output")
    
    def _execute(self, context: DataFlowContext) -> DataFlowContext:
        context.stage = DataFlowStage.OUTPUT
        
        # 输出统计
        total_time = sum(v for k, v in context.metrics.items() if k.endswith('_ms'))
        
        self.logger.info(f"🔧 [P2-DataFlow] 数据流完成:")
        self.logger.info(f"  - 总耗时: {total_time:.2f}ms")
        self.logger.info(f"  - 错误数: {len(context.errors)}")
        self.logger.info(f"  - 警告数: {len(context.warnings)}")
        
        return context

class UnifiedDataFlow:
    """统一数据流管理器"""
    
    def __init__(self):
        self.logger = logger
        self._build_pipeline()
        self._stats = {
            'total_processed': 0,
            'total_errors': 0,
            'total_time': 0
        }
        logger.info("🔧 [P2-DataFlow] 统一数据流管理器初始化完成")
    
    def _build_pipeline(self):
        """构建数据流管道"""
        self.input_node = InputNode()
        self.validation_node = ValidationNode()
        self.transformation_node = TransformationNode()
        self.formatting_node = FormattingNode()
        self.caching_node = CachingNode()
        self.rendering_node = RenderingNode()
        self.output_node = OutputNode()
        
        # 连接节点形成管道
        self.input_node.set_next(self.validation_node) \
            .set_next(self.transformation_node) \
            .set_next(self.formatting_node) \
            .set_next(self.caching_node) \
            .set_next(self.rendering_node) \
            .set_next(self.output_node)
        
        # 设置错误处理器
        self.validation_node.set_error_handler(self._handle_validation_error)
        self.formatting_node.set_error_handler(self._handle_formatting_error)
    
    def process(self, data: Any, **metadata) -> DataFlowContext:
        """处理数据"""
        start_time = time.time()
        
        # 创建上下文
        context = DataFlowContext(
            stage=DataFlowStage.INPUT,
            data=data,
            metadata=metadata
        )
        
        # 执行数据流
        context = self.input_node.process(context)
        
        # 更新统计
        self._stats['total_processed'] += 1
        if context.errors:
            self._stats['total_errors'] += 1
        self._stats['total_time'] += (time.time() - start_time) * 1000
        
        return context
    
    def _handle_validation_error(self, context: DataFlowContext, error: Exception) -> DataFlowContext:
        """处理验证错误"""
        logger.warning(f"🔧 [P2-DataFlow] 验证错误，尝试恢复: {error}")
        
        # 尝试恢复
        from src.core.error_recovery_manager import get_error_recovery_manager
        recovery_manager = get_error_recovery_manager()
        
        error_context = recovery_manager.detect_error(error, {
            'component': 'data_flow',
            'operation': 'validation'
        })
        
        success, recovered_data = recovery_manager.attempt_recovery(error_context, context.data)
        if success:
            context.data = recovered_data
            context.add_warning("验证错误已恢复")
        
        return context
    
    def _handle_formatting_error(self, context: DataFlowContext, error: Exception) -> DataFlowContext:
        """处理格式化错误"""
        logger.warning(f"🔧 [P2-DataFlow] 格式化错误，降级处理: {error}")
        
        # 降级处理：保持原始数据
        context.add_warning("格式化失败，使用原始数据")
        
        return context
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        avg_time = self._stats['total_time'] / max(self._stats['total_processed'], 1)
        error_rate = self._stats['total_errors'] / max(self._stats['total_processed'], 1)
        
        return {
            'total_processed': self._stats['total_processed'],
            'total_errors': self._stats['total_errors'],
            'average_time_ms': avg_time,
            'error_rate': error_rate
        }

# 单例实例
_data_flow_instance: Optional[UnifiedDataFlow] = None

def get_unified_data_flow() -> UnifiedDataFlow:
    """获取统一数据流管理器单例"""
    global _data_flow_instance
    if _data_flow_instance is None:
        _data_flow_instance = UnifiedDataFlow()
    return _data_flow_instance