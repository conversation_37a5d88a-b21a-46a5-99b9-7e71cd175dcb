#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置版本管理系统

功能说明:
- 配置的版本控制和历史记录
- 支持配置的保存、回滚和比较
- 自动版本创建和手动版本标记
- 配置变更追踪和审计
"""

import json
import uuid
import copy
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime
from pathlib import Path
from enum import Enum

from src.utils.log_config import setup_logger


class VersionType(Enum):
    """版本类型"""
    AUTO = "auto"        # 自动版本
    MANUAL = "manual"    # 手动版本
    BACKUP = "backup"    # 备份版本
    MILESTONE = "milestone"  # 里程碑版本


class ChangeType(Enum):
    """变更类型"""
    CREATE = "create"    # 创建
    UPDATE = "update"    # 更新
    DELETE = "delete"    # 删除
    RESTORE = "restore"  # 恢复


@dataclass
class ConfigChange:
    """配置变更记录"""
    field_name: str
    change_type: ChangeType
    old_value: Any = None
    new_value: Any = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'field_name': self.field_name,
            'change_type': self.change_type.value,
            'old_value': str(self.old_value) if self.old_value is not None else None,
            'new_value': str(self.new_value) if self.new_value is not None else None
        }


@dataclass
class ConfigVersion:
    """配置版本"""
    version_id: str
    sheet_name: str
    version_number: int
    version_type: VersionType
    config_data: Dict[str, Any]
    changes: List[ConfigChange] = field(default_factory=list)
    
    # 版本元数据
    created_time: datetime = field(default_factory=datetime.now)
    created_by: str = "system"
    description: str = ""
    tags: List[str] = field(default_factory=list)
    
    # 版本关系
    parent_version_id: Optional[str] = None
    is_current: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'version_id': self.version_id,
            'sheet_name': self.sheet_name,
            'version_number': self.version_number,
            'version_type': self.version_type.value,
            'config_data': self.config_data,
            'changes': [change.to_dict() for change in self.changes],
            'created_time': self.created_time.isoformat(),
            'created_by': self.created_by,
            'description': self.description,
            'tags': self.tags,
            'parent_version_id': self.parent_version_id,
            'is_current': self.is_current
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConfigVersion':
        """从字典创建对象"""
        # 处理datetime反序列化
        if 'created_time' in data and isinstance(data['created_time'], str):
            data['created_time'] = datetime.fromisoformat(data['created_time'])
        
        # 处理枚举类型
        if 'version_type' in data and isinstance(data['version_type'], str):
            data['version_type'] = VersionType(data['version_type'])
        
        # 处理变更记录
        if 'changes' in data:
            changes = []
            for change_data in data['changes']:
                change = ConfigChange(
                    field_name=change_data['field_name'],
                    change_type=ChangeType(change_data['change_type']),
                    old_value=change_data.get('old_value'),
                    new_value=change_data.get('new_value')
                )
                changes.append(change)
            data['changes'] = changes
        
        return cls(**data)


@dataclass
class VersionHistory:
    """版本历史"""
    sheet_name: str
    versions: List[ConfigVersion] = field(default_factory=list)
    current_version_id: Optional[str] = None
    
    def get_current_version(self) -> Optional[ConfigVersion]:
        """获取当前版本"""
        if self.current_version_id:
            for version in self.versions:
                if version.version_id == self.current_version_id:
                    return version
        return None
    
    def get_version_by_id(self, version_id: str) -> Optional[ConfigVersion]:
        """根据ID获取版本"""
        for version in self.versions:
            if version.version_id == version_id:
                return version
        return None
    
    def get_latest_version(self) -> Optional[ConfigVersion]:
        """获取最新版本"""
        if not self.versions:
            return None
        return max(self.versions, key=lambda v: v.version_number)
    
    def add_version(self, version: ConfigVersion):
        """添加版本"""
        self.versions.append(version)
        self.versions.sort(key=lambda v: v.version_number)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'sheet_name': self.sheet_name,
            'current_version_id': self.current_version_id,
            'versions': [version.to_dict() for version in self.versions]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VersionHistory':
        """从字典创建对象"""
        history = cls(sheet_name=data['sheet_name'])
        history.current_version_id = data.get('current_version_id')
        
        for version_data in data.get('versions', []):
            version = ConfigVersion.from_dict(version_data)
            history.add_version(version)
        
        return history


class ConfigVersionManager:
    """配置版本管理器"""
    
    def __init__(self, version_dir: Optional[Union[str, Path]] = None):
        """
        初始化版本管理器
        
        Args:
            version_dir: 版本存储目录，默认为 state/config_versions
        """
        self.logger = setup_logger(__name__)
        
        # 版本存储目录
        if version_dir is None:
            version_dir = Path("state/config_versions")
        self.version_dir = Path(version_dir)
        self.version_dir.mkdir(parents=True, exist_ok=True)
        
        # 版本历史存储
        self.version_histories: Dict[str, VersionHistory] = {}
        
        # 版本文件路径
        self.versions_file = self.version_dir / "versions.json"
        
        # 配置
        self.max_versions_per_sheet = 50  # 每个Sheet最大版本数
        self.auto_backup_enabled = True   # 是否启用自动备份
        
        # 加载版本历史
        self._load_version_histories()
        
        self.logger.info(f"配置版本管理器初始化完成，版本目录: {self.version_dir}")
    
    def create_version(self, sheet_name: str, config_data: Dict[str, Any],
                      version_type: VersionType = VersionType.AUTO,
                      description: str = "", tags: Optional[List[str]] = None,
                      created_by: str = "system") -> str:
        """
        创建新版本
        
        Args:
            sheet_name: Sheet名称
            config_data: 配置数据
            version_type: 版本类型
            description: 版本描述
            tags: 版本标签
            created_by: 创建者
            
        Returns:
            版本ID
        """
        try:
            # 获取或创建版本历史
            if sheet_name not in self.version_histories:
                self.version_histories[sheet_name] = VersionHistory(sheet_name=sheet_name)
            
            history = self.version_histories[sheet_name]
            
            # 生成版本号
            latest_version = history.get_latest_version()
            version_number = (latest_version.version_number + 1) if latest_version else 1
            
            # 计算变更
            changes = self._calculate_changes(sheet_name, config_data)
            
            # 创建版本
            version_id = str(uuid.uuid4())
            version = ConfigVersion(
                version_id=version_id,
                sheet_name=sheet_name,
                version_number=version_number,
                version_type=version_type,
                config_data=copy.deepcopy(config_data),
                changes=changes,
                description=description or f"版本 {version_number}",
                tags=tags or [],
                created_by=created_by,
                parent_version_id=latest_version.version_id if latest_version else None,
                is_current=True
            )
            
            # 更新当前版本标记
            if latest_version:
                latest_version.is_current = False
            
            # 添加到历史
            history.add_version(version)
            history.current_version_id = version_id
            
            # 清理旧版本
            self._cleanup_old_versions(sheet_name)
            
            # 保存
            self._save_version_histories()
            
            self.logger.info(f"创建配置版本: {sheet_name} v{version_number} ({version_type.value})")
            return version_id
            
        except Exception as e:
            self.logger.error(f"创建版本失败: {e}")
            raise
    
    def get_version(self, sheet_name: str, version_id: str) -> Optional[ConfigVersion]:
        """
        获取指定版本
        
        Args:
            sheet_name: Sheet名称
            version_id: 版本ID
            
        Returns:
            配置版本
        """
        if sheet_name in self.version_histories:
            return self.version_histories[sheet_name].get_version_by_id(version_id)
        return None
    
    def get_current_version(self, sheet_name: str) -> Optional[ConfigVersion]:
        """
        获取当前版本
        
        Args:
            sheet_name: Sheet名称
            
        Returns:
            当前版本
        """
        if sheet_name in self.version_histories:
            return self.version_histories[sheet_name].get_current_version()
        return None
    
    def get_version_history(self, sheet_name: str, limit: int = 20) -> List[ConfigVersion]:
        """
        获取版本历史
        
        Args:
            sheet_name: Sheet名称
            limit: 返回数量限制
            
        Returns:
            版本历史列表
        """
        if sheet_name not in self.version_histories:
            return []
        
        versions = self.version_histories[sheet_name].versions
        # 按版本号倒序排列
        sorted_versions = sorted(versions, key=lambda v: v.version_number, reverse=True)
        return sorted_versions[:limit] if limit > 0 else sorted_versions

    def restore_version(self, sheet_name: str, version_id: str,
                       create_backup: bool = True) -> Tuple[bool, Optional[str]]:
        """
        恢复到指定版本

        Args:
            sheet_name: Sheet名称
            version_id: 要恢复的版本ID
            create_backup: 是否创建备份版本

        Returns:
            (是否成功, 备份版本ID)
        """
        try:
            # 获取要恢复的版本
            target_version = self.get_version(sheet_name, version_id)
            if not target_version:
                self.logger.warning(f"版本不存在: {version_id}")
                return False, None

            backup_version_id = None

            # 创建当前配置的备份版本
            if create_backup:
                current_version = self.get_current_version(sheet_name)
                if current_version:
                    backup_version_id = self.create_version(
                        sheet_name=sheet_name,
                        config_data=current_version.config_data,
                        version_type=VersionType.BACKUP,
                        description=f"恢复前备份 (恢复到 v{target_version.version_number})",
                        created_by="system"
                    )

            # 创建恢复版本
            restore_version_id = self.create_version(
                sheet_name=sheet_name,
                config_data=target_version.config_data,
                version_type=VersionType.MANUAL,
                description=f"恢复到版本 {target_version.version_number}",
                tags=["restore"],
                created_by="system"
            )

            self.logger.info(f"恢复配置版本: {sheet_name} 恢复到 v{target_version.version_number}")
            return True, backup_version_id

        except Exception as e:
            self.logger.error(f"恢复版本失败: {e}")
            return False, None

    def compare_versions(self, sheet_name: str, version_id1: str, version_id2: str) -> Dict[str, Any]:
        """
        比较两个版本

        Args:
            sheet_name: Sheet名称
            version_id1: 版本1 ID
            version_id2: 版本2 ID

        Returns:
            比较结果
        """
        try:
            version1 = self.get_version(sheet_name, version_id1)
            version2 = self.get_version(sheet_name, version_id2)

            if not version1 or not version2:
                return {'error': '版本不存在'}

            # 比较配置数据
            differences = []
            all_keys = set(version1.config_data.keys()) | set(version2.config_data.keys())

            for key in all_keys:
                value1 = version1.config_data.get(key)
                value2 = version2.config_data.get(key)

                if value1 != value2:
                    differences.append({
                        'field': key,
                        'version1_value': value1,
                        'version2_value': value2,
                        'change_type': self._get_change_type(value1, value2)
                    })

            return {
                'version1': {
                    'id': version1.version_id,
                    'number': version1.version_number,
                    'created_time': version1.created_time.isoformat(),
                    'description': version1.description
                },
                'version2': {
                    'id': version2.version_id,
                    'number': version2.version_number,
                    'created_time': version2.created_time.isoformat(),
                    'description': version2.description
                },
                'differences': differences,
                'differences_count': len(differences),
                'is_identical': len(differences) == 0
            }

        except Exception as e:
            self.logger.error(f"版本比较失败: {e}")
            return {'error': str(e)}

    def create_milestone(self, sheet_name: str, description: str,
                        tags: Optional[List[str]] = None) -> Optional[str]:
        """
        创建里程碑版本

        Args:
            sheet_name: Sheet名称
            description: 里程碑描述
            tags: 标签

        Returns:
            里程碑版本ID
        """
        try:
            current_version = self.get_current_version(sheet_name)
            if not current_version:
                self.logger.warning(f"Sheet '{sheet_name}' 没有当前版本")
                return None

            milestone_tags = tags or []
            milestone_tags.append("milestone")

            milestone_id = self.create_version(
                sheet_name=sheet_name,
                config_data=current_version.config_data,
                version_type=VersionType.MILESTONE,
                description=description,
                tags=milestone_tags,
                created_by="user"
            )

            self.logger.info(f"创建里程碑版本: {sheet_name} - {description}")
            return milestone_id

        except Exception as e:
            self.logger.error(f"创建里程碑失败: {e}")
            return None

    def get_milestones(self, sheet_name: str) -> List[ConfigVersion]:
        """
        获取里程碑版本

        Args:
            sheet_name: Sheet名称

        Returns:
            里程碑版本列表
        """
        if sheet_name not in self.version_histories:
            return []

        versions = self.version_histories[sheet_name].versions
        milestones = [v for v in versions if v.version_type == VersionType.MILESTONE]
        return sorted(milestones, key=lambda v: v.version_number, reverse=True)

    def delete_version(self, sheet_name: str, version_id: str,
                      force: bool = False) -> bool:
        """
        删除版本

        Args:
            sheet_name: Sheet名称
            version_id: 版本ID
            force: 是否强制删除（包括当前版本和里程碑）

        Returns:
            是否删除成功
        """
        try:
            if sheet_name not in self.version_histories:
                return False

            history = self.version_histories[sheet_name]
            version = history.get_version_by_id(version_id)

            if not version:
                return False

            # 检查是否可以删除
            if not force:
                if version.is_current:
                    self.logger.warning("不能删除当前版本")
                    return False

                if version.version_type == VersionType.MILESTONE:
                    self.logger.warning("不能删除里程碑版本")
                    return False

            # 删除版本
            history.versions.remove(version)

            # 如果删除的是当前版本，需要更新当前版本指针
            if version.is_current:
                latest_version = history.get_latest_version()
                if latest_version:
                    latest_version.is_current = True
                    history.current_version_id = latest_version.version_id
                else:
                    history.current_version_id = None

            # 保存
            self._save_version_histories()

            self.logger.info(f"删除版本: {sheet_name} v{version.version_number}")
            return True

        except Exception as e:
            self.logger.error(f"删除版本失败: {e}")
            return False

    def get_version_statistics(self, sheet_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取版本统计信息

        Args:
            sheet_name: Sheet名称，如果为None则统计所有Sheet

        Returns:
            统计信息
        """
        try:
            if sheet_name:
                # 单个Sheet统计
                if sheet_name not in self.version_histories:
                    return {}

                history = self.version_histories[sheet_name]
                versions = history.versions

                return {
                    'sheet_name': sheet_name,
                    'total_versions': len(versions),
                    'auto_versions': len([v for v in versions if v.version_type == VersionType.AUTO]),
                    'manual_versions': len([v for v in versions if v.version_type == VersionType.MANUAL]),
                    'backup_versions': len([v for v in versions if v.version_type == VersionType.BACKUP]),
                    'milestone_versions': len([v for v in versions if v.version_type == VersionType.MILESTONE]),
                    'current_version': history.current_version_id,
                    'latest_version_number': history.get_latest_version().version_number if versions else 0,
                    'first_created': min(v.created_time for v in versions).isoformat() if versions else None,
                    'last_modified': max(v.created_time for v in versions).isoformat() if versions else None
                }
            else:
                # 全局统计
                all_versions = []
                for history in self.version_histories.values():
                    all_versions.extend(history.versions)

                return {
                    'total_sheets': len(self.version_histories),
                    'total_versions': len(all_versions),
                    'auto_versions': len([v for v in all_versions if v.version_type == VersionType.AUTO]),
                    'manual_versions': len([v for v in all_versions if v.version_type == VersionType.MANUAL]),
                    'backup_versions': len([v for v in all_versions if v.version_type == VersionType.BACKUP]),
                    'milestone_versions': len([v for v in all_versions if v.version_type == VersionType.MILESTONE]),
                    'average_versions_per_sheet': len(all_versions) / len(self.version_histories) if self.version_histories else 0,
                    'first_created': min(v.created_time for v in all_versions).isoformat() if all_versions else None,
                    'last_modified': max(v.created_time for v in all_versions).isoformat() if all_versions else None
                }

        except Exception as e:
            self.logger.error(f"获取版本统计失败: {e}")
            return {'error': str(e)}

    def _calculate_changes(self, sheet_name: str, new_config: Dict[str, Any]) -> List[ConfigChange]:
        """计算配置变更"""
        changes = []

        try:
            current_version = self.get_current_version(sheet_name)
            if not current_version:
                # 第一个版本，所有字段都是新增
                for key, value in new_config.items():
                    changes.append(ConfigChange(
                        field_name=key,
                        change_type=ChangeType.CREATE,
                        new_value=value
                    ))
            else:
                old_config = current_version.config_data

                # 检查所有字段的变更
                all_keys = set(old_config.keys()) | set(new_config.keys())

                for key in all_keys:
                    old_value = old_config.get(key)
                    new_value = new_config.get(key)

                    if key not in old_config:
                        # 新增字段
                        changes.append(ConfigChange(
                            field_name=key,
                            change_type=ChangeType.CREATE,
                            new_value=new_value
                        ))
                    elif key not in new_config:
                        # 删除字段
                        changes.append(ConfigChange(
                            field_name=key,
                            change_type=ChangeType.DELETE,
                            old_value=old_value
                        ))
                    elif old_value != new_value:
                        # 更新字段
                        changes.append(ConfigChange(
                            field_name=key,
                            change_type=ChangeType.UPDATE,
                            old_value=old_value,
                            new_value=new_value
                        ))

        except Exception as e:
            self.logger.error(f"计算配置变更失败: {e}")

        return changes

    def _get_change_type(self, value1: Any, value2: Any) -> str:
        """获取变更类型"""
        if value1 is None and value2 is not None:
            return "added"
        elif value1 is not None and value2 is None:
            return "removed"
        elif value1 != value2:
            return "modified"
        else:
            return "unchanged"

    def _cleanup_old_versions(self, sheet_name: str):
        """清理旧版本"""
        try:
            if sheet_name not in self.version_histories:
                return

            history = self.version_histories[sheet_name]
            versions = history.versions

            if len(versions) <= self.max_versions_per_sheet:
                return

            # 按版本号排序，保留最新的版本
            sorted_versions = sorted(versions, key=lambda v: v.version_number, reverse=True)

            # 确定要删除的版本
            versions_to_keep = []
            versions_to_delete = []

            for i, version in enumerate(sorted_versions):
                # 保留条件：
                # 1. 最新的N个版本
                # 2. 里程碑版本
                # 3. 当前版本
                if (i < self.max_versions_per_sheet or
                    version.version_type == VersionType.MILESTONE or
                    version.is_current):
                    versions_to_keep.append(version)
                else:
                    versions_to_delete.append(version)

            # 删除旧版本
            for version in versions_to_delete:
                versions.remove(version)
                self.logger.debug(f"清理旧版本: {sheet_name} v{version.version_number}")

            if versions_to_delete:
                self.logger.info(f"清理了 {len(versions_to_delete)} 个旧版本: {sheet_name}")

        except Exception as e:
            self.logger.error(f"清理旧版本失败: {e}")

    def _load_version_histories(self):
        """加载版本历史"""
        if not self.versions_file.exists():
            return

        try:
            with open(self.versions_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.version_histories = {}
            for sheet_name, history_data in data.get('histories', {}).items():
                history = VersionHistory.from_dict(history_data)
                self.version_histories[sheet_name] = history

            self.logger.info(f"加载了 {len(self.version_histories)} 个Sheet的版本历史")

        except Exception as e:
            self.logger.error(f"加载版本历史失败: {e}")

    def _save_version_histories(self):
        """保存版本历史"""
        try:
            data = {
                'histories': {
                    sheet_name: history.to_dict()
                    for sheet_name, history in self.version_histories.items()
                },
                'saved_time': datetime.now().isoformat(),
                'version': '1.0'
            }

            with open(self.versions_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"保存版本历史失败: {e}")
