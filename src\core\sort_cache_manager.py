"""
排序缓存管理器 - P2级优化
提供高性能的排序结果缓存，减少重复排序计算
"""

import hashlib
import time
import threading
import pickle
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
from loguru import logger


@dataclass
class SortCacheEntry:
    """缓存条目"""
    cache_key: str
    table_name: str
    sort_columns: List[Dict[str, Any]]
    data: pd.DataFrame
    row_count: int
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    size_bytes: int = 0
    
    def is_expired(self, ttl_seconds: int) -> bool:
        """检查是否过期"""
        return (datetime.now() - self.created_at).total_seconds() > ttl_seconds
    
    def update_access(self):
        """更新访问信息"""
        self.last_accessed = datetime.now()
        self.access_count += 1


class SortCacheManager:
    """
    排序缓存管理器
    
    主要功能：
    1. 缓存排序结果，避免重复计算
    2. 智能缓存策略（LRU + TTL）
    3. 内存管理和限制
    4. 缓存命中率统计
    5. 增量排序优化
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化缓存管理器"""
        if not hasattr(self, '_initialized'):
            self.logger = logger
            self._cache_lock = threading.RLock()
            
            # 缓存存储
            self._cache: Dict[str, SortCacheEntry] = {}
            
            # 缓存配置
            self._max_cache_size = 100 * 1024 * 1024  # 100MB
            self._max_cache_entries = 50  # 最多50个缓存条目
            self._default_ttl = 300  # 5分钟TTL
            
            # 统计信息
            self._stats = {
                'hits': 0,
                'misses': 0,
                'evictions': 0,
                'total_sorts': 0,
                'time_saved': 0.0
            }
            
            # 持久化配置（可选）
            self._cache_dir = Path('cache/sort')
            self._cache_dir.mkdir(parents=True, exist_ok=True)
            self._enable_disk_cache = False  # 默认不启用磁盘缓存
            
            self._initialized = True
            self.logger.info("SortCacheManager 初始化完成")
    
    def get_sorted_data(self, table_name: str, data: pd.DataFrame, 
                       sort_columns: List[Dict[str, Any]]) -> Tuple[pd.DataFrame, bool]:
        """
        获取排序后的数据（优先从缓存）
        
        Args:
            table_name: 表名
            data: 原始数据
            sort_columns: 排序列配置
        
        Returns:
            (排序后的数据, 是否命中缓存)
        """
        if not sort_columns or data.empty:
            return data, False
        
        # 生成缓存键
        cache_key = self._generate_cache_key(table_name, data, sort_columns)
        
        # 尝试从缓存获取
        with self._cache_lock:
            if cache_key in self._cache:
                entry = self._cache[cache_key]
                
                # 检查是否过期
                if not entry.is_expired(self._default_ttl):
                    # 检查数据是否变化（行数检查）
                    if len(data) == entry.row_count:
                        entry.update_access()
                        self._stats['hits'] += 1
                        self.logger.debug(f"缓存命中: {table_name}, 键={cache_key[:8]}")
                        return entry.data.copy(), True
                else:
                    # 过期，移除缓存
                    del self._cache[cache_key]
                    self._stats['evictions'] += 1
        
        # 缓存未命中，执行排序
        self._stats['misses'] += 1
        start_time = time.time()
        
        sorted_data = self._perform_sort(data, sort_columns)
        
        sort_time = time.time() - start_time
        self._stats['total_sorts'] += 1
        
        # 添加到缓存
        self._add_to_cache(cache_key, table_name, sorted_data, sort_columns)
        
        self.logger.debug(f"排序完成并缓存: {table_name}, 耗时={sort_time:.3f}秒")
        
        return sorted_data, False
    
    def _perform_sort(self, data: pd.DataFrame, sort_columns: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        执行实际的排序操作
        
        Args:
            data: 待排序数据
            sort_columns: 排序列配置
        
        Returns:
            排序后的数据
        """
        try:
            # 准备排序参数
            by_columns = []
            ascending_list = []
            
            for col_config in sort_columns:
                col_name = col_config.get('column_name', col_config.get('column'))
                order = col_config.get('sort_order', col_config.get('order', 'asc'))
                
                if col_name and col_name in data.columns:
                    by_columns.append(col_name)
                    ascending_list.append(order.lower() == 'asc')
            
            if not by_columns:
                return data
            
            # 执行排序
            sorted_data = data.sort_values(
                by=by_columns,
                ascending=ascending_list,
                inplace=False,
                ignore_index=True
            )
            
            return sorted_data
            
        except Exception as e:
            self.logger.error(f"排序执行失败: {e}")
            return data
    
    def _generate_cache_key(self, table_name: str, data: pd.DataFrame, 
                          sort_columns: List[Dict[str, Any]]) -> str:
        """
        生成缓存键
        
        Args:
            table_name: 表名
            data: 数据
            sort_columns: 排序列配置
        
        Returns:
            缓存键
        """
        # 组合关键信息
        key_parts = [
            table_name,
            str(len(data)),  # 行数
            str(data.shape[1]),  # 列数
            str(sort_columns),  # 排序配置
        ]
        
        # 添加数据特征（使用前几行的哈希）
        if len(data) > 0:
            sample_rows = min(5, len(data))
            data_sample = data.head(sample_rows).to_string()
            key_parts.append(hashlib.md5(data_sample.encode()).hexdigest()[:8])
        
        # 生成最终的缓存键
        key_string = '|'.join(key_parts)
        return hashlib.sha256(key_string.encode()).hexdigest()
    
    def _add_to_cache(self, cache_key: str, table_name: str, 
                     data: pd.DataFrame, sort_columns: List[Dict[str, Any]]):
        """
        添加到缓存
        
        Args:
            cache_key: 缓存键
            table_name: 表名
            data: 排序后的数据
            sort_columns: 排序列配置
        """
        with self._cache_lock:
            # 检查缓存大小限制
            if len(self._cache) >= self._max_cache_entries:
                self._evict_lru()
            
            # 估算数据大小
            size_bytes = data.memory_usage(deep=True).sum()
            
            # 检查内存限制
            current_size = sum(entry.size_bytes for entry in self._cache.values())
            if current_size + size_bytes > self._max_cache_size:
                self._evict_until_size(size_bytes)
            
            # 创建缓存条目
            entry = SortCacheEntry(
                cache_key=cache_key,
                table_name=table_name,
                sort_columns=sort_columns,
                data=data.copy(),
                row_count=len(data),
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                size_bytes=size_bytes
            )
            
            self._cache[cache_key] = entry
            self.logger.debug(f"添加缓存: {table_name}, 大小={size_bytes/1024:.1f}KB")
    
    def _evict_lru(self):
        """移除最近最少使用的缓存条目"""
        if not self._cache:
            return
        
        # 找出最近最少使用的条目
        lru_key = min(self._cache.keys(), 
                     key=lambda k: self._cache[k].last_accessed)
        
        del self._cache[lru_key]
        self._stats['evictions'] += 1
        self.logger.debug(f"LRU移除缓存: {lru_key[:8]}")
    
    def _evict_until_size(self, required_size: int):
        """移除缓存直到有足够空间"""
        current_size = sum(entry.size_bytes for entry in self._cache.values())
        target_size = self._max_cache_size - required_size
        
        if current_size <= target_size:
            return
        
        # 按访问时间排序，优先移除旧的
        sorted_keys = sorted(self._cache.keys(),
                           key=lambda k: self._cache[k].last_accessed)
        
        for key in sorted_keys:
            if current_size <= target_size:
                break
            
            entry = self._cache[key]
            current_size -= entry.size_bytes
            del self._cache[key]
            self._stats['evictions'] += 1
            self.logger.debug(f"空间不足移除缓存: {key[:8]}")
    
    def invalidate_cache(self, table_name: Optional[str] = None):
        """
        使缓存失效
        
        Args:
            table_name: 表名，None表示清空所有缓存
        """
        with self._cache_lock:
            if table_name:
                # 移除特定表的缓存
                keys_to_remove = [
                    key for key, entry in self._cache.items()
                    if entry.table_name == table_name
                ]
                
                for key in keys_to_remove:
                    del self._cache[key]
                
                self.logger.info(f"清除表缓存: {table_name}, 移除{len(keys_to_remove)}个条目")
            else:
                # 清空所有缓存
                count = len(self._cache)
                self._cache.clear()
                self.logger.info(f"清空所有缓存: {count}个条目")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._cache_lock:
            total_size = sum(entry.size_bytes for entry in self._cache.values())
            
            hit_rate = 0
            if self._stats['hits'] + self._stats['misses'] > 0:
                hit_rate = self._stats['hits'] / (self._stats['hits'] + self._stats['misses'])
            
            return {
                'cache_entries': len(self._cache),
                'cache_size_mb': total_size / (1024 * 1024),
                'hit_rate': f"{hit_rate:.1%}",
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'evictions': self._stats['evictions'],
                'total_sorts': self._stats['total_sorts']
            }
    
    def optimize_cache(self):
        """优化缓存（清理过期条目）"""
        with self._cache_lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired(self._default_ttl)
            ]
            
            for key in expired_keys:
                del self._cache[key]
            
            if expired_keys:
                self.logger.info(f"清理过期缓存: {len(expired_keys)}个条目")
    
    def enable_disk_cache(self, enable: bool = True):
        """启用/禁用磁盘缓存"""
        self._enable_disk_cache = enable
        self.logger.info(f"磁盘缓存: {'启用' if enable else '禁用'}")
    
    def save_to_disk(self, cache_key: str, entry: SortCacheEntry):
        """保存缓存到磁盘（可选功能）"""
        if not self._enable_disk_cache:
            return
        
        try:
            cache_file = self._cache_dir / f"{cache_key[:16]}.pkl"
            with open(cache_file, 'wb') as f:
                pickle.dump(entry, f)
            self.logger.debug(f"缓存保存到磁盘: {cache_key[:8]}")
        except Exception as e:
            self.logger.error(f"磁盘缓存保存失败: {e}")
    
    def load_from_disk(self, cache_key: str) -> Optional[SortCacheEntry]:
        """从磁盘加载缓存（可选功能）"""
        if not self._enable_disk_cache:
            return None
        
        try:
            cache_file = self._cache_dir / f"{cache_key[:16]}.pkl"
            if cache_file.exists():
                with open(cache_file, 'rb') as f:
                    entry = pickle.load(f)
                self.logger.debug(f"从磁盘加载缓存: {cache_key[:8]}")
                return entry
        except Exception as e:
            self.logger.error(f"磁盘缓存加载失败: {e}")
        
        return None


# 全局实例获取函数
def get_sort_cache_manager() -> SortCacheManager:
    """获取排序缓存管理器的全局实例"""
    return SortCacheManager()