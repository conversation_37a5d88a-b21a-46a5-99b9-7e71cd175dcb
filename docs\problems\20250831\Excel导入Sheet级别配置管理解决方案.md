# Excel导入Sheet级别配置管理解决方案

**日期**: 2025-08-31  
**版本**: v1.0  
**状态**: 方案设计完成，待实施

## 📋 问题描述

在导入Excel文件时，每个Sheet表情况不同：
- 有的第一行是表头，有的是标题或说明
- 有些Sheet表最后一行是汇总数据、说明或备注
- 甚至有不少空行需要处理
- 需要按各个表情况分别处理

**用户预期**：在"统一数据导入配置"窗口中，点击左侧Sheet列表中某个表后，在右侧通过选项卡的形式，针对每个Sheet表特殊情况，具体进行导入配置。

## 🔍 现状分析

### 核心问题识别

1. **缺乏Sheet级别的个性化配置**：当前系统只有全局配置，无法为每个Sheet单独设置导入参数
2. **选项卡内容不随Sheet变化**：右侧选项卡显示的是全局配置，不会根据选中的Sheet动态更新
3. **导入参数固化**：起始行、结束行、表头检测等参数无法按Sheet个性化设置
4. **配置存储结构单一**：配置数据结构不支持Sheet级别的差异化存储

### 当前架构分析

```mermaid
graph TB
    A[统一数据导入窗口] --> B[左侧Sheet管理面板]
    A --> C[右侧选项卡面板]

    B --> D[EnhancedSheetManagementWidget]
    D --> E[Sheet列表树]
    D --> F[导入策略选择]

    C --> G[字段映射选项卡]
    C --> H[预览验证选项卡]

    G --> I[UnifiedMappingConfigWidget]
    I --> J[映射配置表格]

    H --> K[PreviewValidationWidget]
    K --> L[数据预览表格]
```

**架构层次**：
```
UnifiedDataImportWindow (主窗口)
├── EnhancedSheetManagementWidget (左侧Sheet管理)
└── QTabWidget (右侧选项卡容器)
    ├── UnifiedMappingConfigWidget (字段映射)
    ├── DataProcessingWidget (数据处理)
    └── PreviewValidationWidget (预览验证)
```

## 💡 解决方案设计

### 1. Sheet配置数据结构

```python
@dataclass
class SheetImportConfig:
    """Sheet级别的导入配置"""
    sheet_name: str
    
    # 数据范围配置
    header_row: int = 1          # 表头行号
    data_start_row: int = 2      # 数据起始行
    data_end_row: Optional[int] = None  # 数据结束行（None表示到文件末尾）
    skip_empty_rows: bool = True # 跳过空行
    
    # 数据处理配置
    has_header: bool = True      # 是否有表头
    auto_detect_header: bool = True  # 自动检测表头
    remove_summary_rows: bool = False  # 移除汇总行
    summary_keywords: List[str] = field(default_factory=lambda: ["合计", "小计", "总计"])
    
    # 字段映射配置
    field_mappings: Dict[str, str] = field(default_factory=dict)
    field_types: Dict[str, str] = field(default_factory=dict)
    required_fields: List[str] = field(default_factory=list)
    
    # 验证规则
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    
    # 元数据
    created_time: datetime = field(default_factory=datetime.now)
    modified_time: datetime = field(default_factory=datetime.now)
    is_enabled: bool = True
```

### 2. Sheet配置管理器

```python
class SheetConfigManager:
    """Sheet配置管理器"""
    
    def __init__(self):
        self.sheet_configs: Dict[str, SheetImportConfig] = {}
        self.current_sheet: Optional[str] = None
        
    def get_or_create_config(self, sheet_name: str) -> SheetImportConfig:
        """获取或创建Sheet配置"""
        if sheet_name not in self.sheet_configs:
            self.sheet_configs[sheet_name] = self._create_default_config(sheet_name)
        return self.sheet_configs[sheet_name]
    
    def switch_sheet(self, sheet_name: str) -> SheetImportConfig:
        """切换到指定Sheet"""
        self.current_sheet = sheet_name
        return self.get_or_create_config(sheet_name)
    
    def _create_default_config(self, sheet_name: str) -> SheetImportConfig:
        """创建默认配置"""
        # 根据Sheet名称智能推断配置
        config = SheetImportConfig(sheet_name=sheet_name)
        
        # 智能检测配置
        if "汇总" in sheet_name or "统计" in sheet_name:
            config.remove_summary_rows = True
            config.is_enabled = False  # 默认不导入汇总表
        
        return config
```

### 3. 选项卡内容增强

#### 新增数据处理选项卡界面设计

```
┌─────────────────────────────────────────────────────────────┐
│ 🧹 数据处理配置 - [当前Sheet: 2024年1月]                      │
├─────────────────────────────────────────────────────────────┤
│ 📊 数据范围设置                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 表头行号: [1    ] ☑ 自动检测表头                        │ │
│ │ 数据起始行: [2    ] 数据结束行: [     ] (空=到文件末尾)   │ │
│ │ ☑ 跳过空行  ☑ 移除汇总行                               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 🔧 数据清洗规则                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 汇总关键词: [合计] [小计] [总计] [+ 添加]                │ │
│ │ ☑ 自动去除前后空格  ☑ 统一数字格式                     │ │
│ │ ☑ 处理合并单元格    ☑ 填充空值                         │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 📋 预览效果                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 原始数据: 150行 → 处理后: 145行 (移除5行汇总数据)        │ │
│ │ 检测到表头: 姓名, 工号, 基本工资, 绩效工资...            │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 选项卡功能增强

**字段映射选项卡增强**：
- 显示当前Sheet的字段列表
- 支持Sheet特定的字段映射规则
- 提供字段类型和验证规则配置

**新增数据处理选项卡**：
- 数据范围设置（起始行、结束行）
- 表头检测配置
- 空行和汇总行处理规则
- 数据清洗规则

**预览验证选项卡增强**：
- 显示当前Sheet的实际数据预览
- 应用当前配置的预览效果
- 数据质量检查结果

## 🔄 实现流程

### Sheet配置管理方案流程图

```mermaid
graph TB
    A[用户点击Sheet] --> B[触发Sheet选择事件]
    B --> C[SheetConfigManager]
    C --> D{Sheet配置是否存在?}

    D -->|是| E[加载已有配置]
    D -->|否| F[创建默认配置]

    E --> G[更新右侧选项卡内容]
    F --> G

    G --> H[字段映射选项卡]
    G --> I[数据处理选项卡]
    G --> J[预览验证选项卡]

    H --> K[显示Sheet特定字段映射]
    I --> L[显示Sheet特定处理规则]
    J --> M[显示Sheet特定预览数据]

    K --> N[用户修改配置]
    L --> N
    M --> N

    N --> O[自动保存到SheetConfig]
    O --> P[更新配置缓存]
```

### Sheet配置联动时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant SM as SheetManagementWidget
    participant SCM as SheetConfigManager
    participant MT as MappingTab
    participant PT as ProcessingTab
    participant VT as ValidationTab
    participant EI as ExcelImporter

    U->>SM: 点击Sheet列表中的Sheet
    SM->>SCM: switch_sheet(sheet_name)
    SCM->>SCM: get_or_create_config(sheet_name)
    SCM-->>SM: 返回SheetConfig

    SM->>MT: update_for_sheet(sheet_config)
    MT->>EI: preview_data(sheet_name, config)
    EI-->>MT: 返回字段信息
    MT->>MT: 更新字段映射表格

    SM->>PT: update_for_sheet(sheet_config)
    PT->>PT: 更新数据处理配置界面

    SM->>VT: update_for_sheet(sheet_config)
    VT->>EI: preview_data(sheet_name, config)
    EI-->>VT: 返回预览数据
    VT->>VT: 更新预览表格

    Note over U,VT: 用户在任意选项卡中修改配置

    MT->>SCM: save_config(sheet_name, config)
    PT->>SCM: save_config(sheet_name, config)
    VT->>SCM: save_config(sheet_name, config)

    SCM->>SCM: 自动保存配置到缓存
```

### Sheet配置联动流程

1. **用户点击Sheet** → 触发Sheet选择事件
2. **SheetConfigManager** → 获取或创建Sheet配置
3. **更新选项卡内容** → 各选项卡显示Sheet特定配置
4. **用户修改配置** → 自动保存到SheetConfig
5. **配置缓存更新** → 实时保存配置变更

### 关键代码修改点

**A. EnhancedSheetManagementWidget 增强**
- 添加Sheet选择事件处理
- 集成SheetConfigManager
- 实现选项卡内容联动

**B. 选项卡组件改造**
- UnifiedMappingConfigWidget：支持Sheet级别字段映射
- 新增DataProcessingWidget：数据处理配置
- PreviewValidationWidget：Sheet特定预览

**C. ExcelImporter 增强**
- 支持Sheet级别的导入参数
- 智能表头检测
- 灵活的数据范围处理

## 📁 配置持久化方案

### 配置存储结构

```json
{
  "file_path": "/path/to/excel/file.xlsx",
  "global_settings": {
    "import_strategy": "merge_to_single_table",
    "target_table_type": "salary_table"
  },
  "sheet_configs": {
    "2024年1月": {
      "header_row": 1,
      "data_start_row": 2,
      "data_end_row": null,
      "has_header": true,
      "remove_summary_rows": true,
      "field_mappings": {
        "姓名": "employee_name",
        "工号": "employee_id",
        "基本工资": "basic_salary"
      },
      "field_types": {
        "employee_name": "VARCHAR(50)",
        "employee_id": "VARCHAR(20)",
        "basic_salary": "DECIMAL(10,2)"
      },
      "is_enabled": true
    },
    "2024年2月": {
      "header_row": 2,
      "data_start_row": 3,
      "remove_summary_rows": false,
      "field_mappings": {...},
      "is_enabled": true
    },
    "统计汇总": {
      "is_enabled": false,
      "note": "汇总表，不导入数据"
    }
  }
}
```

## 🚀 实施计划

### 第一阶段（核心功能）
1. **SheetConfigManager** - Sheet配置管理核心
2. **Sheet选择联动** - 左侧选择右侧更新
3. **数据处理选项卡** - 新增Sheet级别数据处理配置

### 第二阶段（增强功能）
1. **智能配置推荐** - 根据Sheet名称和内容自动推荐配置
2. **配置模板系统** - 保存和复用常用配置
3. **批量配置应用** - 将一个Sheet的配置应用到多个Sheet

### 第三阶段（高级功能）
1. **配置验证和冲突检测** - 检查配置的合理性
2. **导入预览和模拟** - 在实际导入前预览结果
3. **配置版本管理** - 支持配置的版本控制和回滚

## 📝 技术实现要点

### 性能优化
- 配置缓存机制，避免重复计算
- 延迟加载Sheet数据，只在需要时读取
- 异步预览，不阻塞UI操作

### 用户体验
- 配置变更实时预览
- 智能默认值推荐
- 清晰的状态提示和错误信息

### 数据一致性
- 配置变更自动保存
- 跨选项卡配置同步
- 导入时配置验证

## ✅ 预期效果

通过这个方案的实施，将实现：

1. ✅ **Sheet个性化配置**：每个Sheet都有独立的导入配置
2. ✅ **选项卡联动**：右侧内容根据选中Sheet动态更新
3. ✅ **灵活的数据处理**：支持不同Sheet的特殊处理需求
4. ✅ **配置持久化**：配置可保存和复用

这个方案既保持了现有架构的稳定性，又大幅提升了功能的灵活性和用户体验。
