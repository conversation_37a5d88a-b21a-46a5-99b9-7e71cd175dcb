#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置文件单元测试
"""

import unittest
import json
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestConfiguration(unittest.TestCase):
    """配置文件测试"""
    
    def setUp(self):
        """测试初始化"""
        self.config_file = Path("config.json")
        
    def test_config_file_exists(self):
        """测试配置文件存在"""
        self.assertTrue(self.config_file.exists())
        
    def test_config_valid_json(self):
        """测试配置文件格式"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.assertIsInstance(config, dict)
        except json.JSONDecodeError:
            self.fail("配置文件不是有效的JSON")
    
    def test_cache_config(self):
        """测试缓存配置"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        cache_config = config.get('cache', {})
        
        # 验证缓存配置
        self.assertIn('enabled', cache_config)
        self.assertIn('ttl_seconds', cache_config)
        self.assertIn('max_entries', cache_config)
        self.assertIn('max_memory_mb', cache_config)
        
        # 验证值的合理性
        self.assertIsInstance(cache_config['enabled'], bool)
        self.assertGreater(cache_config['ttl_seconds'], 0)
        self.assertGreater(cache_config['max_entries'], 0)
        self.assertGreater(cache_config['max_memory_mb'], 0)
    
    def test_database_config(self):
        """测试数据库配置"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        db_config = config.get('database', {})
        
        # 验证数据库配置
        self.assertIn('type', db_config)
        self.assertEqual(db_config['type'], 'sqlite')
        
        if 'path' in db_config:
            # 验证路径格式
            self.assertTrue(db_config['path'].endswith('.db'))

if __name__ == '__main__':
    unittest.main()
