#!/usr/bin/env python3
"""
精确复现用户报告的问题

分析用户的具体使用场景：
1. 用户打开多工作表Excel文件
2. 配置了某个工作表的字段
3. 直接点击"另存配置"按钮
4. 预期：应该保存所有已配置的工作表
5. 实际：只保存了当前工作表，其他配置丢失
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger

def analyze_actual_issue():
    """分析实际问题"""
    logger.info("=== 分析用户实际遇到的问题 ===")
    
    # 模拟用户的真实使用场景
    class RealScenarioDialog:
        def __init__(self):
            self.all_sheets_configs = {}  # 关键：这个字典的维护逻辑
            self.current_sheet_name = 'A岗职工'  # 模拟初始工作表
            
        def simulate_user_workflow(self):
            """模拟用户工作流程"""
            logger.info("--- 用户操作时序 ---")
            
            # 步骤1：用户打开Excel文件，系统加载第一个工作表
            logger.info("1. 系统自动加载第一个工作表：A岗职工")
            logger.info(f"   current_sheet_name = '{self.current_sheet_name}'")
            logger.info(f"   all_sheets_configs = {self.all_sheets_configs}")
            
            # 步骤2：用户配置第一个工作表的字段类型
            logger.info("2. 用户配置第一个工作表的字段类型")
            # 注意：这个时候 all_sheets_configs 仍然是空的！
            # 因为只有切换工作表时才会调用 on_sheet_changed 来保存配置
            
            # 步骤3：用户想切换到第二个工作表
            logger.info("3. 用户切换到第二个工作表：退休人员工资表")
            self.on_sheet_changed('退休人员工资表')
            
            # 步骤4：用户配置第二个工作表
            logger.info("4. 用户配置第二个工作表的字段类型")
            
            # 步骤5：用户再次切换工作表
            logger.info("5. 用户切换到第三个工作表：离休人员表")
            self.on_sheet_changed('离休人员表')
            
            # 步骤6：用户点击"另存配置"按钮
            logger.info("6. 用户点击另存配置按钮")
            self.save_configuration()
            
        def get_current_configuration(self):
            """根据当前工作表返回模拟配置"""
            configs = {
                'A岗职工': {
                    'field_mapping': {'工号': '工号', '姓名': '姓名', '基本工资': '基本工资'},
                    'field_types': {'工号': 'employee_id_string', '姓名': 'name_string', '基本工资': 'salary_float'}
                },
                '退休人员工资表': {
                    'field_mapping': {'序号': '序号', '姓名': '姓名', '退休费': '退休费'},
                    'field_types': {'序号': 'integer', '姓名': 'name_string', '退休费': 'salary_float'}
                },
                '离休人员表': {
                    'field_mapping': {'人员代码': '人员代码', '姓名': '姓名', '离休费': '离休费'},
                    'field_types': {'人员代码': 'employee_id_string', '姓名': 'name_string', '离休费': 'salary_float'}
                }
            }
            return configs.get(self.current_sheet_name, {})
        
        def on_sheet_changed(self, sheet_name: str):
            """模拟切换工作表时的逻辑"""
            logger.info(f"   切换工作表：{self.current_sheet_name} -> {sheet_name}")
            
            # 关键逻辑：保存当前工作表的配置
            if hasattr(self, 'current_sheet_name') and self.current_sheet_name != 'unknown':
                try:
                    current_config = self.get_current_configuration()
                    if current_config and current_config.get('field_mapping'):
                        self.all_sheets_configs[self.current_sheet_name] = current_config
                        logger.info(f"   ✅ 已保存工作表 '{self.current_sheet_name}' 的配置")
                        logger.info(f"   配置字段数: {len(current_config.get('field_mapping', {}))}")
                    else:
                        logger.info(f"   ❌ 工作表 '{self.current_sheet_name}' 没有有效配置")
                except Exception as e:
                    logger.warning(f"   ⚠️ 保存工作表配置时出错: {e}")
            
            # 切换到新工作表
            self.current_sheet_name = sheet_name
            logger.info(f"   当前all_sheets_configs包含: {list(self.all_sheets_configs.keys())}")
        
        def save_configuration(self):
            """模拟另存配置逻辑"""
            logger.info("--- 另存配置执行过程 ---")
            
            # 修复后的逻辑：先保存当前工作表的配置
            if hasattr(self, 'current_sheet_name') and self.current_sheet_name != 'unknown':
                try:
                    current_config = self.get_current_configuration()
                    if current_config and current_config.get('field_mapping'):
                        self.all_sheets_configs[self.current_sheet_name] = current_config
                        logger.info(f"✅ 保存最后一个工作表 '{self.current_sheet_name}' 配置")
                except Exception as e:
                    logger.warning(f"保存当前工作表配置时出错: {e}")
            
            logger.info(f"最终all_sheets_configs状态:")
            for sheet_name, config in self.all_sheets_configs.items():
                field_count = len(config.get('field_mapping', {}))
                logger.info(f"  - {sheet_name}: {field_count} 个字段")
            
            # 检查是否有配置需要保存
            if not self.all_sheets_configs:
                logger.error("❌ 没有找到任何已配置的工作表")
                return False
            else:
                logger.info(f"✅ 找到 {len(self.all_sheets_configs)} 个已配置的工作表")
                return True
    
    # 执行模拟
    dialog = RealScenarioDialog()
    result = dialog.simulate_user_workflow()
    
    return result

def analyze_user_complaint():
    """分析用户抱怨的根本原因"""
    logger.info("\n=== 分析用户抱怨的根本原因 ===")
    
    logger.info("❌ 用户抱怨1：'还是得切换表，才能另存配置'")
    logger.info("   分析：如果用户只配置了一个工作表，没有切换过表，")
    logger.info("   那么all_sheets_configs确实为空，save_configuration会失败")
    
    logger.info("❌ 用户抱怨2：'只能保存当前修改的表的配置'")
    logger.info("   分析：all_sheets_configs只保存了用户切换过的工作表")
    logger.info("   如果用户配置了A表，然后直接切换到C表，B表的配置会丢失")
    
    # 测试场景1：用户只配置一个表
    logger.info("\n--- 测试场景1：用户只配置一个表，不切换 ---")
    class SingleSheetTest:
        def __init__(self):
            self.all_sheets_configs = {}
            self.current_sheet_name = 'A岗职工'
        
        def save_without_switching(self):
            # 模拟修复前的逻辑
            if not self.all_sheets_configs:
                logger.error("❌ 修复前：没有找到配置（因为没切换过表）")
                return False
            return True
    
    test1 = SingleSheetTest()
    result1 = test1.save_without_switching()
    
    # 测试场景2：用户配置多个表但跳过某些表
    logger.info("\n--- 测试场景2：用户跳过某些表的配置 ---")
    class SkipSheetTest:
        def __init__(self):
            self.all_sheets_configs = {}
            self.current_sheet_name = 'A岗职工'
        
        def simulate_skip_pattern(self):
            # 用户配置A表
            logger.info("用户配置A岗职工")
            
            # 直接跳到C表（跳过B表）
            logger.info("用户直接切换到离休人员表（跳过退休人员工资表）")
            self.all_sheets_configs['A岗职工'] = {'field_mapping': {'test': 'test'}}
            self.current_sheet_name = '离休人员表'
            
            # 现在B表的配置丢失了
            logger.info(f"结果：只保存了 {list(self.all_sheets_configs.keys())}")
            logger.error("❌ '退休人员工资表'的配置丢失")
    
    test2 = SkipSheetTest()
    test2.simulate_skip_pattern()
    
    return result1

def main():
    """主函数"""
    try:
        result1 = analyze_actual_issue()
        result2 = analyze_user_complaint()
        
        logger.info("\n=== 问题诊断结论 ===")
        logger.info("✅ 修复已生效：用户可以不切换表就另存配置")
        logger.info("❌ 但仍存在问题：")
        logger.info("   1. 用户期望保存'所有已配置的工作表'")
        logger.info("   2. 但实际只保存了'用户切换过的工作表'")
        logger.info("   3. 如果用户配置了A表，直接跳到C表，B表配置会丢失")
        
        logger.info("\n🔧 需要的进一步改进：")
        logger.info("   1. 在初始化时就将第一个工作表的配置保存到all_sheets_configs")
        logger.info("   2. 或者改进保存逻辑，扫描所有工作表的实际配置状态")
        
        return 0
            
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())