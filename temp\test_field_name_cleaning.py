#!/usr/bin/env python3
"""
测试字段名清理功能
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

import re

def clean_field_name(field_name: str) -> str:
    """清理字段名，移除特殊字符"""

    if not field_name:
        return "field_name"

    # 移除所有空白字符（换行符、制表符、空格等）
    cleaned = re.sub(r'\s+', '', field_name.strip())

    # 移除特殊字符，只保留字母、数字、下划线和中文
    cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', cleaned)

    # 如果清理后为空，使用默认名称
    if not cleaned:
        cleaned = "field_name"

    # 确保不以数字开头（数据库字段名规范）
    if cleaned and cleaned[0].isdigit():
        cleaned = f"field_{cleaned}"

    return cleaned

def test_field_name_cleaning():
    """测试各种字段名清理场景"""
    
    test_cases = [
        # 基本测试
        ("姓名", "姓名"),
        ("人员代码", "人员代码"),
        ("工资", "工资"),

        # 包含空格的字段名 - 删除空格
        ("姓 名", "姓名"),
        ("人员 代码", "人员代码"),
        ("基本 工资", "基本工资"),

        # 包含换行符的字段名 - 删除换行符
        ("姓名\n", "姓名"),
        ("人员\n代码", "人员代码"),
        ("\n工资\n", "工资"),

        # 包含制表符的字段名 - 删除制表符
        ("姓名\t", "姓名"),
        ("人员\t代码", "人员代码"),
        ("\t工资\t", "工资"),

        # 包含特殊字符的字段名 - 删除特殊字符
        ("姓名@", "姓名"),
        ("人员#代码", "人员代码"),
        ("工资$金额", "工资金额"),
        ("年龄(岁)", "年龄岁"),
        ("工资[元]", "工资元"),

        # 包含数字开头的字段名
        ("2016年基础工资", "field_2016年基础工资"),
        ("2017年津贴", "field_2017年津贴"),
        ("123测试", "field_123测试"),

        # 复杂情况 - 删除所有特殊字符和空白
        ("  姓 名  \n\t", "姓名"),
        ("人员@#代码  \n", "人员代码"),
        ("2020年\n基础\t工资$", "field_2020年基础工资"),

        # 边界情况
        ("", "field_name"),
        ("   ", "field_name"),
        ("\n\t", "field_name"),
        ("@#$%", "field_name"),
        ("___", "___"),  # 下划线保留

        # 英文字段名
        ("Name", "Name"),
        ("Employee ID", "EmployeeID"),  # 删除空格
        ("Basic Salary", "BasicSalary"),  # 删除空格
        ("2020_Salary", "field_2020_Salary"),  # 下划线保留

        # 混合字段名
        ("Name姓名", "Name姓名"),
        ("ID人员代码", "ID人员代码"),
        ("Salary工资", "Salary工资"),
    ]
    
    print("=== 字段名清理功能测试 ===\n")
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (input_name, expected) in enumerate(test_cases, 1):
        result = clean_field_name(input_name)
        status = "✅" if result == expected else "❌"
        
        if result == expected:
            success_count += 1
        
        print(f"{i:2d}. {status} 输入: '{input_name}' -> 输出: '{result}' (期望: '{expected}')")
        
        if result != expected:
            print(f"    ⚠️  不匹配！期望 '{expected}'，实际得到 '{result}'")
    
    print(f"\n=== 测试结果 ===")
    print(f"总测试用例: {total_count}")
    print(f"成功: {success_count}")
    print(f"失败: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试用例通过！")
    else:
        print("⚠️  部分测试用例失败，需要检查清理逻辑")

if __name__ == "__main__":
    test_field_name_cleaning()
