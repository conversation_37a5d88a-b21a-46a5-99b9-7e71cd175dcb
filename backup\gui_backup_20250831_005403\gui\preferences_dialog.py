"""
用户偏好设置对话框模块

提供完整的用户偏好设置功能，包括主题外观、快捷键自定义、默认值管理等。

主要组件:
- PreferencesDialog: 主偏好设置对话框
- ThemeSettingsWidget: 主题和外观设置
- ShortcutSettingsWidget: 快捷键自定义设置
- DefaultsSettingsWidget: 默认值管理
"""

import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QPushButton, QComboBox, QCheckBox, QSpinBox,
    QGroupBox, QFormLayout, QListWidget, QListWidgetItem,
    QLineEdit, QSlider, QColorDialog, QFontDialog, QMessageBox,
    QSplitter, QTextEdit, QScrollArea, QButtonGroup, QRadioButton,
    QFileDialog, QProgressBar, QTreeWidget, QTreeWidgetItem,
    QHeaderView, QApplication, QFrame, QGridLayout
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QSettings, QTimer, QThread, pyqtSlot
)
from PyQt5.QtGui import (
    QFont, QColor, QPalette, QIcon, QPixmap, QPainter,
    QKeySequence, QValidator
)

from src.utils.log_config import get_module_logger
from src.gui.state_manager import get_state_manager
from src.gui.toast_system import ToastManager

logger = get_module_logger(__name__)


class PreferencesDialog(QDialog):
    """用户偏好设置主对话框"""
    
    # 信号定义
    preferences_changed = pyqtSignal(dict)  # 偏好设置改变信号
    theme_changed = pyqtSignal(str)  # 主题改变信号
    shortcuts_changed = pyqtSignal(dict)  # 快捷键改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.state_manager = get_state_manager()
        self.toast_manager = ToastManager()
        self.settings = QSettings("SalaryChangesSystem", "Preferences")
        
        # 偏好设置缓存
        self.preferences_cache = {}
        self.original_preferences = {}
        
        # 初始化UI
        self.init_ui()
        
        # 加载当前设置
        self.load_preferences()
        
        # 设置对话框属性
        self.setModal(True)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("用户偏好设置")
        self.setFixedSize(800, 600)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 创建各个设置页面
        self.create_appearance_tab()
        self.create_shortcuts_tab()
        self.create_defaults_tab()
        self.create_advanced_tab()
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 重置按钮
        self.reset_button = QPushButton("重置为默认")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        # 应用按钮
        self.apply_button = QPushButton("应用")
        self.apply_button.clicked.connect(self.apply_preferences)
        button_layout.addWidget(self.apply_button)
        
        # 确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept_preferences)
        self.ok_button.setDefault(True)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_dialog_style()
    
    def create_appearance_tab(self):
        """创建外观设置选项卡"""
        tab = QWidget()
        self.tab_widget.addTab(tab, "外观")
        
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 主题设置组
        theme_group = QGroupBox("主题设置")
        theme_layout = QFormLayout(theme_group)
        
        # 主题选择
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["浅色主题", "深色主题", "自动"])
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        theme_layout.addRow("应用主题:", self.theme_combo)
        
        # 强调色选择
        self.accent_color_button = QPushButton("选择强调色")
        self.accent_color_button.clicked.connect(self.choose_accent_color)
        self.accent_color_display = QLabel()
        self.accent_color_display.setFixedSize(30, 20)
        self.accent_color_display.setStyleSheet("background-color: #2196F3; border: 1px solid #ccc;")
        
        accent_layout = QHBoxLayout()
        accent_layout.addWidget(self.accent_color_button)
        accent_layout.addWidget(self.accent_color_display)
        accent_layout.addStretch()
        theme_layout.addRow("强调色:", accent_layout)
        
        layout.addWidget(theme_group)
        
        # 字体设置组
        font_group = QGroupBox("字体设置")
        font_layout = QFormLayout(font_group)
        
        # 应用字体
        self.font_button = QPushButton("选择应用字体")
        self.font_button.clicked.connect(self.choose_app_font)
        self.font_display = QLabel("微软雅黑, 9pt")
        
        font_button_layout = QHBoxLayout()
        font_button_layout.addWidget(self.font_button)
        font_button_layout.addWidget(self.font_display)
        font_button_layout.addStretch()
        font_layout.addRow("应用字体:", font_button_layout)
        
        # 表格字体
        self.table_font_button = QPushButton("选择表格字体")
        self.table_font_button.clicked.connect(self.choose_table_font)
        self.table_font_display = QLabel("Consolas, 9pt")
        
        table_font_layout = QHBoxLayout()
        table_font_layout.addWidget(self.table_font_button)
        table_font_layout.addWidget(self.table_font_display)
        table_font_layout.addStretch()
        font_layout.addRow("表格字体:", table_font_layout)
        
        layout.addWidget(font_group)
        
        # 界面设置组
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout(ui_group)
        
        # 界面缩放
        self.scale_slider = QSlider(Qt.Horizontal)
        self.scale_slider.setRange(80, 150)
        self.scale_slider.setValue(100)
        self.scale_slider.setTickPosition(QSlider.TicksBelow)
        self.scale_slider.setTickInterval(10)
        self.scale_slider.valueChanged.connect(self.on_scale_changed)
        
        self.scale_label = QLabel("100%")
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(self.scale_slider)
        scale_layout.addWidget(self.scale_label)
        ui_layout.addRow("界面缩放:", scale_layout)
        
        # 显示选项
        self.show_toolbar_cb = QCheckBox("显示工具栏")
        self.show_toolbar_cb.setChecked(True)
        ui_layout.addRow("", self.show_toolbar_cb)
        
        self.show_statusbar_cb = QCheckBox("显示状态栏")
        self.show_statusbar_cb.setChecked(True)
        ui_layout.addRow("", self.show_statusbar_cb)
        
        self.show_tooltips_cb = QCheckBox("显示工具提示")
        self.show_tooltips_cb.setChecked(True)
        ui_layout.addRow("", self.show_tooltips_cb)
        
        layout.addWidget(ui_group)
        
        layout.addStretch()
    
    def create_shortcuts_tab(self):
        """创建快捷键设置选项卡"""
        tab = QWidget()
        self.tab_widget.addTab(tab, "快捷键")
        
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 说明标签
        info_label = QLabel("双击快捷键项目可以修改快捷键设置")
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(info_label)
        
        # 快捷键树形控件
        self.shortcuts_tree = QTreeWidget()
        self.shortcuts_tree.setHeaderLabels(["功能", "快捷键", "描述"])
        self.shortcuts_tree.setAlternatingRowColors(True)
        self.shortcuts_tree.itemDoubleClicked.connect(self.edit_shortcut)
        
        # 设置列宽
        header = self.shortcuts_tree.header()
        header.resizeSection(0, 200)
        header.resizeSection(1, 150)
        header.resizeSection(2, 300)
        
        layout.addWidget(self.shortcuts_tree)
        
        # 快捷键操作按钮
        shortcut_button_layout = QHBoxLayout()
        
        self.edit_shortcut_button = QPushButton("编辑快捷键")
        self.edit_shortcut_button.clicked.connect(self.edit_selected_shortcut)
        shortcut_button_layout.addWidget(self.edit_shortcut_button)
        
        self.reset_shortcut_button = QPushButton("重置快捷键")
        self.reset_shortcut_button.clicked.connect(self.reset_selected_shortcut)
        shortcut_button_layout.addWidget(self.reset_shortcut_button)
        
        shortcut_button_layout.addStretch()
        
        self.import_shortcuts_button = QPushButton("导入快捷键")
        self.import_shortcuts_button.clicked.connect(self.import_shortcuts)
        shortcut_button_layout.addWidget(self.import_shortcuts_button)
        
        self.export_shortcuts_button = QPushButton("导出快捷键")
        self.export_shortcuts_button.clicked.connect(self.export_shortcuts)
        shortcut_button_layout.addWidget(self.export_shortcuts_button)
        
        layout.addLayout(shortcut_button_layout)
        
        # 填充快捷键数据
        self.populate_shortcuts_tree()
    
    def create_defaults_tab(self):
        """创建默认值设置选项卡"""
        tab = QWidget()
        self.tab_widget.addTab(tab, "默认值")
        
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 文件和目录默认值
        file_group = QGroupBox("文件和目录")
        file_layout = QFormLayout(file_group)
        
        # 默认工作目录
        self.work_dir_edit = QLineEdit()
        self.work_dir_button = QPushButton("浏览...")
        self.work_dir_button.clicked.connect(self.choose_work_directory)
        
        work_dir_layout = QHBoxLayout()
        work_dir_layout.addWidget(self.work_dir_edit)
        work_dir_layout.addWidget(self.work_dir_button)
        file_layout.addRow("默认工作目录:", work_dir_layout)
        
        # 自动保存间隔
        self.autosave_spinbox = QSpinBox()
        self.autosave_spinbox.setRange(1, 60)
        self.autosave_spinbox.setValue(5)
        self.autosave_spinbox.setSuffix(" 分钟")
        file_layout.addRow("自动保存间隔:", self.autosave_spinbox)
        
        # 备份文件数量
        self.backup_count_spinbox = QSpinBox()
        self.backup_count_spinbox.setRange(1, 50)
        self.backup_count_spinbox.setValue(10)
        self.backup_count_spinbox.setSuffix(" 个")
        file_layout.addRow("保留备份数量:", self.backup_count_spinbox)
        
        layout.addWidget(file_group)
        
        # 数据处理默认值
        data_group = QGroupBox("数据处理")
        data_layout = QFormLayout(data_group)
        
        # 默认编码
        self.encoding_combo = QComboBox()
        self.encoding_combo.addItems(["UTF-8", "GBK", "GB2312", "UTF-16"])
        data_layout.addRow("默认文件编码:", self.encoding_combo)
        
        # 数据验证级别
        self.validation_combo = QComboBox()
        self.validation_combo.addItems(["严格", "标准", "宽松"])
        self.validation_combo.setCurrentText("标准")
        data_layout.addRow("数据验证级别:", self.validation_combo)
        
        # 错误处理方式
        self.error_handling_combo = QComboBox()
        self.error_handling_combo.addItems(["停止处理", "跳过错误", "询问用户"])
        self.error_handling_combo.setCurrentText("询问用户")
        data_layout.addRow("错误处理方式:", self.error_handling_combo)
        
        layout.addWidget(data_group)
        
        # 显示选项默认值
        display_group = QGroupBox("显示选项")
        display_layout = QFormLayout(display_group)
        
        # 每页显示行数
        self.rows_per_page_spinbox = QSpinBox()
        self.rows_per_page_spinbox.setRange(10, 1000)
        self.rows_per_page_spinbox.setValue(100)
        self.rows_per_page_spinbox.setSuffix(" 行")
        display_layout.addRow("每页显示行数:", self.rows_per_page_spinbox)
        
        # 数字格式
        self.number_format_combo = QComboBox()
        self.number_format_combo.addItems(["1,234.56", "1234.56", "1 234,56"])
        display_layout.addRow("数字格式:", self.number_format_combo)
        
        # 日期格式
        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems(["YYYY-MM-DD", "YYYY/MM/DD", "DD/MM/YYYY", "MM/DD/YYYY"])
        display_layout.addRow("日期格式:", self.date_format_combo)
        
        layout.addWidget(display_group)
        
        layout.addStretch()
    
    def create_advanced_tab(self):
        """创建高级设置选项卡"""
        tab = QWidget()
        self.tab_widget.addTab(tab, "高级")
        
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 性能设置
        performance_group = QGroupBox("性能设置")
        performance_layout = QFormLayout(performance_group)
        
        # 内存缓存大小
        self.cache_size_spinbox = QSpinBox()
        self.cache_size_spinbox.setRange(50, 2000)
        self.cache_size_spinbox.setValue(200)
        self.cache_size_spinbox.setSuffix(" MB")
        performance_layout.addRow("内存缓存大小:", self.cache_size_spinbox)
        
        # 线程池大小
        self.thread_pool_spinbox = QSpinBox()
        self.thread_pool_spinbox.setRange(1, 16)
        self.thread_pool_spinbox.setValue(4)
        performance_layout.addRow("线程池大小:", self.thread_pool_spinbox)
        
        # 启用硬件加速
        self.hardware_accel_cb = QCheckBox("启用硬件加速")
        self.hardware_accel_cb.setChecked(True)
        performance_layout.addRow("", self.hardware_accel_cb)
        
        layout.addWidget(performance_group)
        
        # 调试设置
        debug_group = QGroupBox("调试设置")
        debug_layout = QFormLayout(debug_group)
        
        # 日志级别
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        debug_layout.addRow("日志级别:", self.log_level_combo)
        
        # 启用详细日志
        self.verbose_logging_cb = QCheckBox("启用详细日志")
        debug_layout.addRow("", self.verbose_logging_cb)
        
        # 启用性能监控
        self.performance_monitor_cb = QCheckBox("启用性能监控")
        debug_layout.addRow("", self.performance_monitor_cb)
        
        layout.addWidget(debug_group)
        
        # 实验性功能
        experimental_group = QGroupBox("实验性功能")
        experimental_layout = QFormLayout(experimental_group)
        
        # 启用实验性功能
        self.experimental_cb = QCheckBox("启用实验性功能")
        experimental_layout.addRow("", self.experimental_cb)
        
        # 实验功能列表
        self.experimental_list = QListWidget()
        self.experimental_list.setEnabled(False)
        experimental_layout.addRow("实验功能:", self.experimental_list)
        
        # 连接信号
        self.experimental_cb.toggled.connect(self.experimental_list.setEnabled)
        
        layout.addWidget(experimental_group)
        
        layout.addStretch()
    
    def apply_dialog_style(self):
        """应用对话框样式"""
        self.setStyleSheet("""
        QDialog {
            background-color: #FFFFFF;
        }
        
        QTabWidget::pane {
            border: 1px solid #C0C0C0;
            top: -1px;
            background-color: #FFFFFF;
        }
        
        QTabWidget::tab-bar {
            alignment: left;
        }
        
        QTabBar::tab {
            background-color: #F0F0F0;
            border: 1px solid #C0C0C0;
            border-bottom-color: #C0C0C0;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            min-width: 80px;
            padding: 8px 16px;
        }
        
        QTabBar::tab:selected {
            background-color: #FFFFFF;
            border-bottom-color: #FFFFFF;
        }
        
        QTabBar::tab:hover {
            background-color: #E0E0E0;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #C0C0C0;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #F0F0F0;
            border: 1px solid #C0C0C0;
            border-radius: 4px;
            padding: 6px 12px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #E0E0E0;
        }
        
        QPushButton:pressed {
            background-color: #D0D0D0;
        }
        
        QPushButton:default {
            background-color: #2196F3;
            color: white;
            border-color: #1976D2;
        }
        
        QPushButton:default:hover {
            background-color: #1976D2;
        }
        """)
    
    def populate_shortcuts_tree(self):
        """填充快捷键树形控件"""
        try:
            # 定义快捷键分类和项目
            shortcuts_data = {
                "文件操作": [
                    ("新建", "Ctrl+N", "创建新文件"),
                    ("打开", "Ctrl+O", "打开文件"),
                    ("保存", "Ctrl+S", "保存当前文件"),
                    ("另存为", "Ctrl+Shift+S", "文件另存为"),
                    ("退出", "Ctrl+Q", "退出应用程序")
                ],
                "编辑操作": [
                    ("撤销", "Ctrl+Z", "撤销上一个操作"),
                    ("重做", "Ctrl+Y", "重做操作"),
                    ("复制", "Ctrl+C", "复制选中内容"),
                    ("剪切", "Ctrl+X", "剪切选中内容"),
                    ("粘贴", "Ctrl+V", "粘贴内容"),
                    ("全选", "Ctrl+A", "选择全部内容")
                ],
                "数据操作": [
                    ("导入数据", "Ctrl+I", "导入数据文件"),
                    ("导出数据", "Ctrl+E", "导出数据"),
                    ("刷新", "F5", "刷新数据"),
                    ("查找", "Ctrl+F", "查找数据"),
                    ("替换", "Ctrl+H", "查找替换")
                ],
                "视图操作": [
                    ("放大", "Ctrl+=", "放大视图"),
                    ("缩小", "Ctrl+-", "缩小视图"),
                    ("重置缩放", "Ctrl+0", "重置视图缩放"),
                    ("全屏", "F11", "切换全屏模式")
                ],
                "工具和帮助": [
                    ("偏好设置", "Ctrl+,", "打开偏好设置"),
                    ("帮助", "F1", "显示帮助"),
                    ("关于", "", "关于程序")
                ]
            }
            
            # 清空现有项目
            self.shortcuts_tree.clear()
            
            # 添加分类和快捷键项目
            for category, shortcuts in shortcuts_data.items():
                category_item = QTreeWidgetItem(self.shortcuts_tree)
                category_item.setText(0, category)
                category_item.setExpanded(True)
                
                # 设置分类项样式
                font = category_item.font(0)
                font.setBold(True)
                category_item.setFont(0, font)
                
                for name, shortcut, description in shortcuts:
                    shortcut_item = QTreeWidgetItem(category_item)
                    shortcut_item.setText(0, name)
                    shortcut_item.setText(1, shortcut)
                    shortcut_item.setText(2, description)
                    
                    # 存储原始数据
                    shortcut_item.setData(0, Qt.UserRole, {
                        'name': name,
                        'shortcut': shortcut,
                        'description': description,
                        'category': category
                    })
            
        except Exception as e:
            logger.error(f"填充快捷键树形控件失败: {e}")
    
    def load_preferences(self):
        """加载偏好设置"""
        try:
            # 从状态管理器加载偏好设置
            user_manager = self.state_manager.user_manager
            
            # 外观设置
            theme = user_manager.get_preference('theme', '浅色主题')
            self.theme_combo.setCurrentText(theme)
            
            accent_color = user_manager.get_preference('accent_color', '#2196F3')
            self.update_accent_color_display(accent_color)
            
            # 字体设置
            app_font = user_manager.get_preference('app_font', 'Microsoft YaHei,9')
            self.font_display.setText(app_font)
            
            table_font = user_manager.get_preference('table_font', 'Consolas,9')
            self.table_font_display.setText(table_font)
            
            # 界面设置
            scale = user_manager.get_preference('ui_scale', 100)
            self.scale_slider.setValue(scale)
            self.scale_label.setText(f"{scale}%")
            
            self.show_toolbar_cb.setChecked(user_manager.get_preference('show_toolbar', True))
            self.show_statusbar_cb.setChecked(user_manager.get_preference('show_statusbar', True))
            self.show_tooltips_cb.setChecked(user_manager.get_preference('show_tooltips', True))
            
            # 默认值设置
            self.work_dir_edit.setText(user_manager.get_preference('work_directory', ''))
            self.autosave_spinbox.setValue(user_manager.get_preference('autosave_interval', 5))
            self.backup_count_spinbox.setValue(user_manager.get_preference('backup_count', 10))
            
            self.encoding_combo.setCurrentText(user_manager.get_preference('default_encoding', 'UTF-8'))
            self.validation_combo.setCurrentText(user_manager.get_preference('validation_level', '标准'))
            self.error_handling_combo.setCurrentText(user_manager.get_preference('error_handling', '询问用户'))
            
            self.rows_per_page_spinbox.setValue(user_manager.get_preference('rows_per_page', 100))
            self.number_format_combo.setCurrentText(user_manager.get_preference('number_format', '1,234.56'))
            self.date_format_combo.setCurrentText(user_manager.get_preference('date_format', 'YYYY-MM-DD'))
            
            # 高级设置
            self.cache_size_spinbox.setValue(user_manager.get_preference('cache_size_mb', 200))
            self.thread_pool_spinbox.setValue(user_manager.get_preference('thread_pool_size', 4))
            self.hardware_accel_cb.setChecked(user_manager.get_preference('hardware_acceleration', True))
            
            self.log_level_combo.setCurrentText(user_manager.get_preference('log_level', 'INFO'))
            self.verbose_logging_cb.setChecked(user_manager.get_preference('verbose_logging', False))
            self.performance_monitor_cb.setChecked(user_manager.get_preference('performance_monitor', False))
            
            self.experimental_cb.setChecked(user_manager.get_preference('enable_experimental', False))
            
            # 保存原始设置用于比较
            self.original_preferences = self.collect_preferences()
            
            logger.debug("偏好设置加载完成")
            
        except Exception as e:
            logger.error(f"加载偏好设置失败: {e}")
    
    def collect_preferences(self) -> Dict[str, Any]:
        """收集当前偏好设置"""
        try:
            preferences = {
                # 外观设置
                'theme': self.theme_combo.currentText(),
                'accent_color': getattr(self, 'current_accent_color', '#2196F3'),
                'app_font': self.font_display.text(),
                'table_font': self.table_font_display.text(),
                'ui_scale': self.scale_slider.value(),
                'show_toolbar': self.show_toolbar_cb.isChecked(),
                'show_statusbar': self.show_statusbar_cb.isChecked(),
                'show_tooltips': self.show_tooltips_cb.isChecked(),
                
                # 默认值设置
                'work_directory': self.work_dir_edit.text(),
                'autosave_interval': self.autosave_spinbox.value(),
                'backup_count': self.backup_count_spinbox.value(),
                'default_encoding': self.encoding_combo.currentText(),
                'validation_level': self.validation_combo.currentText(),
                'error_handling': self.error_handling_combo.currentText(),
                'rows_per_page': self.rows_per_page_spinbox.value(),
                'number_format': self.number_format_combo.currentText(),
                'date_format': self.date_format_combo.currentText(),
                
                # 高级设置
                'cache_size_mb': self.cache_size_spinbox.value(),
                'thread_pool_size': self.thread_pool_spinbox.value(),
                'hardware_acceleration': self.hardware_accel_cb.isChecked(),
                'log_level': self.log_level_combo.currentText(),
                'verbose_logging': self.verbose_logging_cb.isChecked(),
                'performance_monitor': self.performance_monitor_cb.isChecked(),
                'enable_experimental': self.experimental_cb.isChecked()
            }
            
            return preferences
            
        except Exception as e:
            logger.error(f"收集偏好设置失败: {e}")
            return {}
    
    def apply_preferences(self):
        """应用偏好设置"""
        try:
            current_preferences = self.collect_preferences()
            
            # 保存到状态管理器
            user_manager = self.state_manager.user_manager
            for key, value in current_preferences.items():
                user_manager.set_preference(key, value)
            
            # 发送信号通知偏好设置改变
            self.preferences_changed.emit(current_preferences)
            
            # 检查主题是否改变
            if current_preferences.get('theme') != self.original_preferences.get('theme'):
                self.theme_changed.emit(current_preferences['theme'])
            
            # 显示成功消息
            if self.toast_manager:
                self.toast_manager.show_success("偏好设置已应用")
            
            logger.info("偏好设置应用成功")
            
        except Exception as e:
            logger.error(f"应用偏好设置失败: {e}")
            if self.toast_manager:
                self.toast_manager.show_error(f"应用设置失败: {e}")
    
    def accept_preferences(self):
        """确定并应用偏好设置"""
        self.apply_preferences()
        self.accept()
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        try:
            reply = QMessageBox.question(
                self, "确认重置",
                "确定要将所有设置重置为默认值吗？\n此操作无法撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 重置所有控件为默认值
                self.theme_combo.setCurrentText('浅色主题')
                self.update_accent_color_display('#2196F3')
                self.font_display.setText('Microsoft YaHei,9')
                self.table_font_display.setText('Consolas,9')
                self.scale_slider.setValue(100)
                self.scale_label.setText('100%')
                
                self.show_toolbar_cb.setChecked(True)
                self.show_statusbar_cb.setChecked(True)
                self.show_tooltips_cb.setChecked(True)
                
                self.work_dir_edit.setText('')
                self.autosave_spinbox.setValue(5)
                self.backup_count_spinbox.setValue(10)
                
                self.encoding_combo.setCurrentText('UTF-8')
                self.validation_combo.setCurrentText('标准')
                self.error_handling_combo.setCurrentText('询问用户')
                
                self.rows_per_page_spinbox.setValue(100)
                self.number_format_combo.setCurrentText('1,234.56')
                self.date_format_combo.setCurrentText('YYYY-MM-DD')
                
                self.cache_size_spinbox.setValue(200)
                self.thread_pool_spinbox.setValue(4)
                self.hardware_accel_cb.setChecked(True)
                
                self.log_level_combo.setCurrentText('INFO')
                self.verbose_logging_cb.setChecked(False)
                self.performance_monitor_cb.setChecked(False)
                self.experimental_cb.setChecked(False)
                
                if self.toast_manager:
                    self.toast_manager.show_info("设置已重置为默认值")
                
                logger.info("偏好设置已重置为默认值")
            
        except Exception as e:
            logger.error(f"重置默认设置失败: {e}")
            if self.toast_manager:
                self.toast_manager.show_error(f"重置失败: {e}")
    
    # 事件处理方法
    def on_theme_changed(self, theme_name: str):
        """主题改变处理"""
        logger.debug(f"主题改变: {theme_name}")
    
    def on_scale_changed(self, value: int):
        """缩放改变处理"""
        self.scale_label.setText(f"{value}%")
        logger.debug(f"界面缩放改变: {value}%")
    
    def choose_accent_color(self):
        """选择强调色"""
        try:
            current_color = getattr(self, 'current_accent_color', '#2196F3')
            color = QColorDialog.getColor(QColor(current_color), self, "选择强调色")
            
            if color.isValid():
                self.update_accent_color_display(color.name())
                
        except Exception as e:
            logger.error(f"选择强调色失败: {e}")
    
    def update_accent_color_display(self, color_name: str):
        """更新强调色显示"""
        try:
            self.current_accent_color = color_name
            self.accent_color_display.setStyleSheet(
                f"background-color: {color_name}; border: 1px solid #ccc;"
            )
            
        except Exception as e:
            logger.error(f"更新强调色显示失败: {e}")
    
    def choose_app_font(self):
        """选择应用字体"""
        try:
            current_font_str = self.font_display.text()
            font_parts = current_font_str.split(',')
            current_font = QFont(font_parts[0], int(font_parts[1]) if len(font_parts) > 1 else 9)
            
            font, ok = QFontDialog.getFont(current_font, self, "选择应用字体")
            if ok:
                font_str = f"{font.family()},{font.pointSize()}"
                self.font_display.setText(font_str)
                
        except Exception as e:
            logger.error(f"选择应用字体失败: {e}")
    
    def choose_table_font(self):
        """选择表格字体"""
        try:
            current_font_str = self.table_font_display.text()
            font_parts = current_font_str.split(',')
            current_font = QFont(font_parts[0], int(font_parts[1]) if len(font_parts) > 1 else 9)
            
            font, ok = QFontDialog.getFont(current_font, self, "选择表格字体")
            if ok:
                font_str = f"{font.family()},{font.pointSize()}"
                self.table_font_display.setText(font_str)
                
        except Exception as e:
            logger.error(f"选择表格字体失败: {e}")
    
    def choose_work_directory(self):
        """选择工作目录"""
        try:
            current_dir = self.work_dir_edit.text() or os.getcwd()
            directory = QFileDialog.getExistingDirectory(
                self, "选择默认工作目录", current_dir
            )
            
            if directory:
                self.work_dir_edit.setText(directory)
                
        except Exception as e:
            logger.error(f"选择工作目录失败: {e}")
    
    def edit_shortcut(self, item: QTreeWidgetItem, column: int):
        """编辑快捷键"""
        try:
            if column == 1 and item.parent():  # 只有快捷键列且不是分类项
                self.edit_shortcut_item(item)
                
        except Exception as e:
            logger.error(f"编辑快捷键失败: {e}")
    
    def edit_selected_shortcut(self):
        """编辑选中的快捷键"""
        try:
            current_item = self.shortcuts_tree.currentItem()
            if current_item and current_item.parent():
                self.edit_shortcut_item(current_item)
                
        except Exception as e:
            logger.error(f"编辑选中快捷键失败: {e}")
    
    def edit_shortcut_item(self, item: QTreeWidgetItem):
        """编辑快捷键项目"""
        try:
            # 这里可以实现快捷键编辑对话框
            # 暂时使用简单的输入对话框
            from PyQt5.QtWidgets import QInputDialog
            
            current_shortcut = item.text(1)
            new_shortcut, ok = QInputDialog.getText(
                self, "编辑快捷键",
                f"请输入 '{item.text(0)}' 的新快捷键:",
                text=current_shortcut
            )
            
            if ok and new_shortcut != current_shortcut:
                item.setText(1, new_shortcut)
                
                if self.toast_manager:
                    self.toast_manager.show_success(f"快捷键已更新: {new_shortcut}")
                
        except Exception as e:
            logger.error(f"编辑快捷键项目失败: {e}")
    
    def reset_selected_shortcut(self):
        """重置选中的快捷键"""
        try:
            current_item = self.shortcuts_tree.currentItem()
            if current_item and current_item.parent():
                # 获取原始数据
                original_data = current_item.data(0, Qt.UserRole)
                if original_data:
                    current_item.setText(1, original_data['shortcut'])
                    
                    if self.toast_manager:
                        self.toast_manager.show_info("快捷键已重置")
                
        except Exception as e:
            logger.error(f"重置快捷键失败: {e}")
    
    def import_shortcuts(self):
        """导入快捷键配置"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入快捷键配置", "", "JSON文件 (*.json)"
            )
            
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    shortcuts_data = json.load(f)
                
                # 应用导入的快捷键
                # 这里需要实现快捷键应用逻辑
                
                if self.toast_manager:
                    self.toast_manager.show_success("快捷键配置导入成功")
                
        except Exception as e:
            logger.error(f"导入快捷键配置失败: {e}")
            if self.toast_manager:
                self.toast_manager.show_error(f"导入失败: {e}")
    
    def export_shortcuts(self):
        """导出快捷键配置"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出快捷键配置", "shortcuts.json", "JSON文件 (*.json)"
            )
            
            if file_path:
                # 收集当前快捷键配置
                shortcuts_data = {}
                
                # 遍历快捷键树获取数据
                root = self.shortcuts_tree.invisibleRootItem()
                for i in range(root.childCount()):
                    category_item = root.child(i)
                    category_name = category_item.text(0)
                    shortcuts_data[category_name] = {}
                    
                    for j in range(category_item.childCount()):
                        shortcut_item = category_item.child(j)
                        name = shortcut_item.text(0)
                        shortcut = shortcut_item.text(1)
                        shortcuts_data[category_name][name] = shortcut
                
                # 保存到文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(shortcuts_data, f, indent=2, ensure_ascii=False)
                
                if self.toast_manager:
                    self.toast_manager.show_success("快捷键配置导出成功")
                
        except Exception as e:
            logger.error(f"导出快捷键配置失败: {e}")
            if self.toast_manager:
                self.toast_manager.show_error(f"导出失败: {e}")


# 便捷函数
def show_preferences_dialog(parent=None) -> Optional[PreferencesDialog]:
    """显示偏好设置对话框"""
    try:
        dialog = PreferencesDialog(parent)
        return dialog
        
    except Exception as e:
        logger.error(f"显示偏好设置对话框失败: {e}")
        return None 