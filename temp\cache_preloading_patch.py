
    def preload_adjacent_pages(self, table_name: str, current_page: int, page_size: int, 
                              preload_count: int = 2):
        """预加载相邻页面数据
        
        Args:
            table_name: 表名
            current_page: 当前页码
            page_size: 页大小
            preload_count: 预加载页数（前后各预加载几页）
        """
        try:
            # 计算需要预加载的页码
            pages_to_preload = []
            
            # 预加载前面的页
            for i in range(1, preload_count + 1):
                prev_page = current_page - i
                if prev_page > 0:
                    pages_to_preload.append(prev_page)
            
            # 预加载后面的页
            for i in range(1, preload_count + 1):
                next_page = current_page + i
                pages_to_preload.append(next_page)
            
            # 异步预加载（使用线程池）
            from concurrent.futures import ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=2) as executor:
                for page in pages_to_preload:
                    # 检查是否已缓存
                    cache_key = self._generate_cache_key(table_name, page, page_size)
                    if cache_key not in self._page_cache:
                        # 提交预加载任务
                        executor.submit(self._preload_page, table_name, page, page_size)
            
            self.logger.debug(f"预加载页面: {pages_to_preload}")
            
        except Exception as e:
            self.logger.error(f"预加载失败: {e}")
    
    def _preload_page(self, table_name: str, page: int, page_size: int):
        """预加载单个页面（内部方法）"""
        try:
            # 这里需要调用数据加载服务
            # 实际实现需要根据项目结构调整
            pass
        except Exception as e:
            self.logger.debug(f"预加载页面 {page} 失败: {e}")
