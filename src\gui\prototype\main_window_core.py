"""
主窗口核心类 - 简化版
从原12003行精简到核心功能
"""

from PyQt5.QtWidgets import QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from loguru import logger

from src.gui.prototype.components.workers import Worker, PaginationWorker
from src.gui.prototype.services.data_service import DataService
from src.core.unified_state_management import get_unified_state_manager
from src.core.unified_cache_manager import get_unified_cache_manager


class PrototypeMainWindowCore(QMainWindow):
    """
    主窗口核心类
    
    精简版主窗口，只保留核心功能：
    - 窗口管理
    - 服务初始化
    - 事件协调
    """
    
    # 核心信号
    data_loaded = pyqtSignal(object)
    status_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.logger = logger
        
        # 初始化服务
        self._init_services()
        
        # 初始化UI
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
        
        self.logger.info("主窗口核心初始化完成")
    
    def _init_services(self):
        """初始化核心服务"""
        # 数据服务
        self.data_service = DataService(self)
        
        # 状态管理
        self.state_manager = get_unified_state_manager()
        
        # 缓存管理
        self.cache_manager = get_unified_cache_manager()
        
        # 线程池（保持兼容性）
        from PyQt5.QtCore import QThreadPool
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(4)
        
        self.logger.debug("核心服务初始化完成")
    
    def _init_ui(self):
        """初始化UI（基础版）"""
        self.setWindowTitle("月度工资异动处理系统")
        self.setMinimumSize(1200, 700)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        self.main_layout = QVBoxLayout(central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        
        # UI组件将通过扩展添加
        self._init_ui_components()
        
        self.logger.debug("UI初始化完成")
    
    def _init_ui_components(self):
        """初始化UI组件（由子类或扩展实现）"""
        pass
    
    def _connect_signals(self):
        """连接核心信号"""
        # 数据服务信号
        self.data_service.data_loaded.connect(self._on_data_loaded)
        self.data_service.data_error.connect(self._on_data_error)
        
        self.logger.debug("信号连接完成")
    
    def _on_data_loaded(self, data):
        """处理数据加载完成"""
        self.data_loaded.emit(data)
        self.status_updated.emit(f"数据加载完成: {data.get('row_count', 0)}行")
    
    def _on_data_error(self, error):
        """处理数据错误"""
        self.error_occurred.emit(error)
        self.status_updated.emit(f"错误: {error}")
    
    def import_data(self, file_path: str, table_name: str):
        """
        导入数据（异步）
        
        Args:
            file_path: 文件路径
            table_name: 表名
        """
        worker = Worker(
            self.data_service.import_data,
            file_path,
            table_name
        )
        worker.signals.result.connect(self._handle_import_result)
        worker.signals.error.connect(self._handle_worker_error)
        self.thread_pool.start(worker)
    
    def export_data(self, table_name: str, file_path: str):
        """
        导出数据（异步）
        
        Args:
            table_name: 表名
            file_path: 文件路径
        """
        worker = Worker(
            self.data_service.export_data,
            table_name,
            file_path
        )
        worker.signals.result.connect(self._handle_export_result)
        worker.signals.error.connect(self._handle_worker_error)
        self.thread_pool.start(worker)
    
    def load_page_data(self, table_name: str, page: int = 1, page_size: int = 100):
        """
        加载分页数据（异步）
        
        Args:
            table_name: 表名
            page: 页码
            page_size: 页大小
        """
        # 获取排序状态
        sort_columns = self.state_manager.get_sort_state(table_name)
        
        # 创建分页工作线程
        worker = PaginationWorker(
            self.data_service.table_manager,
            table_name,
            page,
            page_size,
            sort_state_manager=self.state_manager
        )
        
        worker.signals.result.connect(self._handle_page_result)
        worker.signals.error.connect(self._handle_worker_error)
        self.thread_pool.start(worker)
    
    def _handle_import_result(self, success):
        """处理导入结果"""
        if success:
            self.status_updated.emit("数据导入成功")
        else:
            self.status_updated.emit("数据导入失败")
    
    def _handle_export_result(self, success):
        """处理导出结果"""
        if success:
            self.status_updated.emit("数据导出成功")
        else:
            self.status_updated.emit("数据导出失败")
    
    def _handle_page_result(self, result):
        """处理分页结果"""
        if result.get('success'):
            self.data_loaded.emit(result)
            self.status_updated.emit(
                f"加载第{result['current_page']}页，"
                f"共{result['total_records']}条记录"
            )
        else:
            self.error_occurred.emit(result.get('error', '未知错误'))
    
    def _handle_worker_error(self, error_tuple):
        """处理工作线程错误"""
        error_type, error_value, error_traceback = error_tuple
        self.logger.error(f"工作线程错误: {error_value}")
        self.error_occurred.emit(str(error_value))
    
    def get_current_table(self) -> str:
        """获取当前表名"""
        return self.state_manager.get_global_state('current_table', '')
    
    def set_current_table(self, table_name: str):
        """设置当前表名"""
        self.state_manager.set_global_state('current_table', table_name)
        self.logger.info(f"切换到表: {table_name}")
    
    def refresh_current_view(self):
        """刷新当前视图"""
        table_name = self.get_current_table()
        if table_name:
            state = self.state_manager.get_table_state(table_name)
            self.load_page_data(
                table_name,
                state.current_page,
                state.page_size
            )
    
    def clear_cache(self):
        """清除缓存"""
        self.cache_manager.clear_all()
        self.status_updated.emit("缓存已清除")
    
    def get_statistics(self):
        """获取统计信息"""
        return {
            'data_service': self.data_service.get_statistics(),
            'thread_pool': {
                'active': self.thread_pool.activeThreadCount(),
                'max': self.thread_pool.maxThreadCount()
            }
        }
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 等待线程池完成
        self.thread_pool.waitForDone(2000)
        
        # 保存状态
        self.logger.info("保存应用状态")
        
        # 清理资源
        self.logger.info("主窗口关闭")
        
        event.accept()