#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
错误恢复管理器 - P2级架构优化
提供自动错误检测、诊断和恢复机制
"""

from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import pandas as pd
from loguru import logger
import traceback
import json

class ErrorSeverity(Enum):
    """错误严重性级别"""
    LOW = "low"          # 可忽略，不影响功能
    MEDIUM = "medium"    # 需要注意，可能影响部分功能
    HIGH = "high"        # 严重，影响核心功能
    CRITICAL = "critical" # 关键，系统无法正常工作

class RecoveryStrategy(Enum):
    """恢复策略"""
    IGNORE = "ignore"              # 忽略错误
    RETRY = "retry"                # 重试操作
    FALLBACK = "fallback"          # 降级处理
    REPAIR = "repair"              # 修复数据
    ALERT = "alert"                # 告警通知
    ABORT = "abort"                # 终止操作

@dataclass
class ErrorContext:
    """错误上下文"""
    error_type: str
    error_message: str
    severity: ErrorSeverity
    timestamp: datetime = field(default_factory=datetime.now)
    component: Optional[str] = None
    operation: Optional[str] = None
    data_info: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    recovery_attempts: int = 0
    recovery_strategy: Optional[RecoveryStrategy] = None
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'error_type': self.error_type,
            'error_message': self.error_message,
            'severity': self.severity.value,
            'timestamp': self.timestamp.isoformat(),
            'component': self.component,
            'operation': self.operation,
            'data_info': self.data_info,
            'recovery_attempts': self.recovery_attempts,
            'recovery_strategy': self.recovery_strategy.value if self.recovery_strategy else None
        }

class ErrorRecoveryManager:
    """
    错误恢复管理器
    提供自动错误检测、诊断和恢复功能
    """
    
    def __init__(self):
        self._error_history: List[ErrorContext] = []
        self._recovery_handlers: Dict[str, Callable] = {}
        self._error_patterns: Dict[str, Tuple[ErrorSeverity, RecoveryStrategy]] = {}
        self._max_retry_attempts = 3
        self._setup_default_patterns()
        self._setup_default_handlers()
        logger.info("🔧 [P2-ErrorRecovery] 错误恢复管理器初始化完成")
    
    def _setup_default_patterns(self):
        """设置默认错误模式"""
        # 表头重复错误
        self._error_patterns['header_duplication'] = (
            ErrorSeverity.HIGH, 
            RecoveryStrategy.REPAIR
        )
        
        # Series传递错误
        self._error_patterns['series_in_formatting'] = (
            ErrorSeverity.HIGH,
            RecoveryStrategy.REPAIR
        )
        
        # 数据类型错误
        self._error_patterns['type_conversion'] = (
            ErrorSeverity.MEDIUM,
            RecoveryStrategy.FALLBACK
        )
        
        # 缓存错误
        self._error_patterns['cache_error'] = (
            ErrorSeverity.LOW,
            RecoveryStrategy.RETRY
        )
        
        # 内存溢出
        self._error_patterns['memory_overflow'] = (
            ErrorSeverity.CRITICAL,
            RecoveryStrategy.FALLBACK
        )
        
        # 配置加载错误
        self._error_patterns['config_load_error'] = (
            ErrorSeverity.HIGH,
            RecoveryStrategy.FALLBACK
        )
    
    def _setup_default_handlers(self):
        """设置默认恢复处理器"""
        self._recovery_handlers['repair_headers'] = self._repair_duplicate_headers
        self._recovery_handlers['fix_series'] = self._fix_series_issue
        self._recovery_handlers['convert_types'] = self._convert_data_types
        self._recovery_handlers['clear_cache'] = self._clear_cache
        self._recovery_handlers['reduce_data'] = self._reduce_data_size
    
    def detect_error(self, exception: Exception, context: Dict[str, Any]) -> ErrorContext:
        """
        检测和分类错误
        
        Args:
            exception: 异常对象
            context: 错误上下文信息
            
        Returns:
            错误上下文对象
        """
        error_type = type(exception).__name__
        error_message = str(exception)
        
        # 检测错误模式
        severity = ErrorSeverity.MEDIUM
        strategy = RecoveryStrategy.RETRY
        
        # 分析错误消息
        if 'Series' in error_message or 'apply' in error_message:
            severity = ErrorSeverity.HIGH
            strategy = RecoveryStrategy.REPAIR
            error_type = 'series_in_formatting'
        elif '重复' in error_message or 'duplicate' in error_message.lower():
            severity = ErrorSeverity.HIGH
            strategy = RecoveryStrategy.REPAIR
            error_type = 'header_duplication'
        elif 'memory' in error_message.lower():
            severity = ErrorSeverity.CRITICAL
            strategy = RecoveryStrategy.FALLBACK
            error_type = 'memory_overflow'
        elif 'type' in error_message.lower() or 'convert' in error_message.lower():
            severity = ErrorSeverity.MEDIUM
            strategy = RecoveryStrategy.FALLBACK
            error_type = 'type_conversion'
        
        # 从预定义模式获取严重性和策略
        if error_type in self._error_patterns:
            severity, strategy = self._error_patterns[error_type]
        
        # 创建错误上下文
        error_context = ErrorContext(
            error_type=error_type,
            error_message=error_message,
            severity=severity,
            component=context.get('component'),
            operation=context.get('operation'),
            data_info=context.get('data_info', {}),
            stack_trace=traceback.format_exc(),
            recovery_strategy=strategy
        )
        
        # 记录错误
        self._error_history.append(error_context)
        
        logger.error(f"🔧 [P2-ErrorRecovery] 检测到错误: {error_type}, 严重性: {severity.value}, 策略: {strategy.value}")
        
        return error_context
    
    def attempt_recovery(self, error_context: ErrorContext, data: Any = None) -> Tuple[bool, Any]:
        """
        尝试错误恢复
        
        Args:
            error_context: 错误上下文
            data: 需要恢复的数据
            
        Returns:
            (是否成功, 恢复后的数据)
        """
        if error_context.recovery_attempts >= self._max_retry_attempts:
            logger.error(f"🔧 [P2-ErrorRecovery] 超过最大恢复尝试次数: {self._max_retry_attempts}")
            return False, data
        
        error_context.recovery_attempts += 1
        strategy = error_context.recovery_strategy
        
        logger.info(f"🔧 [P2-ErrorRecovery] 开始恢复尝试 {error_context.recovery_attempts}/{self._max_retry_attempts}, 策略: {strategy.value}")
        
        try:
            if strategy == RecoveryStrategy.IGNORE:
                return True, data
            
            elif strategy == RecoveryStrategy.RETRY:
                # 简单重试
                return True, data
            
            elif strategy == RecoveryStrategy.REPAIR:
                # 修复数据
                return self._repair_data(error_context, data)
            
            elif strategy == RecoveryStrategy.FALLBACK:
                # 降级处理
                return self._fallback_processing(error_context, data)
            
            elif strategy == RecoveryStrategy.ALERT:
                # 发送告警
                self._send_alert(error_context)
                return False, data
            
            elif strategy == RecoveryStrategy.ABORT:
                # 终止操作
                return False, None
            
        except Exception as e:
            logger.error(f"🔧 [P2-ErrorRecovery] 恢复失败: {e}")
            return False, data
        
        return False, data
    
    def _repair_data(self, error_context: ErrorContext, data: Any) -> Tuple[bool, Any]:
        """修复数据"""
        error_type = error_context.error_type
        
        if error_type == 'header_duplication':
            return self._repair_duplicate_headers(data)
        elif error_type == 'series_in_formatting':
            return self._fix_series_issue(data)
        
        return False, data
    
    def _repair_duplicate_headers(self, data: Any) -> Tuple[bool, Any]:
        """修复重复表头"""
        try:
            if isinstance(data, list):
                # 去重表头
                unique_headers = []
                seen = set()
                for header in data:
                    if header not in seen:
                        unique_headers.append(header)
                        seen.add(header)
                    else:
                        # 添加序号
                        counter = 1
                        new_header = f"{header}_{counter}"
                        while new_header in seen:
                            counter += 1
                            new_header = f"{header}_{counter}"
                        unique_headers.append(new_header)
                        seen.add(new_header)
                
                logger.info(f"🔧 [P2-ErrorRecovery] 表头去重: {len(data)} -> {len(unique_headers)}")
                return True, unique_headers
            
            elif isinstance(data, pd.DataFrame):
                # 修复DataFrame列名
                columns = list(data.columns)
                unique_columns = []
                seen = set()
                
                for col in columns:
                    if col not in seen:
                        unique_columns.append(col)
                        seen.add(col)
                    else:
                        counter = 1
                        new_col = f"{col}_{counter}"
                        while new_col in seen:
                            counter += 1
                            new_col = f"{col}_{counter}"
                        unique_columns.append(new_col)
                        seen.add(new_col)
                
                data.columns = unique_columns
                logger.info(f"🔧 [P2-ErrorRecovery] DataFrame列名去重完成")
                return True, data
                
        except Exception as e:
            logger.error(f"🔧 [P2-ErrorRecovery] 表头修复失败: {e}")
        
        return False, data
    
    def _fix_series_issue(self, data: Any) -> Tuple[bool, Any]:
        """修复Series传递问题"""
        try:
            if isinstance(data, pd.Series):
                # 提取第一个值
                value = data.iloc[0] if len(data) > 0 else None
                logger.info("🔧 [P2-ErrorRecovery] Series转换为单值")
                return True, value
            
            elif isinstance(data, pd.DataFrame):
                # 确保使用map而不是apply
                logger.info("🔧 [P2-ErrorRecovery] 建议使用map替代apply")
                return True, data
                
        except Exception as e:
            logger.error(f"🔧 [P2-ErrorRecovery] Series修复失败: {e}")
        
        return False, data
    
    def _convert_data_types(self, data: Any) -> Tuple[bool, Any]:
        """转换数据类型"""
        try:
            if isinstance(data, pd.DataFrame):
                for col in data.columns:
                    if '工资' in col or '金额' in col:
                        try:
                            data[col] = pd.to_numeric(data[col], errors='coerce')
                        except:
                            pass
                
                logger.info("🔧 [P2-ErrorRecovery] 数据类型转换完成")
                return True, data
                
        except Exception as e:
            logger.error(f"🔧 [P2-ErrorRecovery] 类型转换失败: {e}")
        
        return False, data
    
    def _clear_cache(self, data: Any) -> Tuple[bool, Any]:
        """清理缓存"""
        try:
            # 这里应该调用实际的缓存清理方法
            logger.info("🔧 [P2-ErrorRecovery] 缓存已清理")
            return True, data
        except Exception as e:
            logger.error(f"🔧 [P2-ErrorRecovery] 缓存清理失败: {e}")
            return False, data
    
    def _reduce_data_size(self, data: Any) -> Tuple[bool, Any]:
        """减少数据大小"""
        try:
            if isinstance(data, pd.DataFrame):
                # 限制行数
                max_rows = 10000
                if len(data) > max_rows:
                    data = data.head(max_rows)
                    logger.warning(f"🔧 [P2-ErrorRecovery] 数据量过大，已截断到{max_rows}行")
                
                # 优化数据类型
                for col in data.columns:
                    col_type = data[col].dtype
                    if col_type == 'object':
                        try:
                            data[col] = data[col].astype('category')
                        except:
                            pass
                
                return True, data
                
        except Exception as e:
            logger.error(f"🔧 [P2-ErrorRecovery] 数据缩减失败: {e}")
        
        return False, data
    
    def _fallback_processing(self, error_context: ErrorContext, data: Any) -> Tuple[bool, Any]:
        """降级处理"""
        try:
            # 根据错误类型选择降级方案
            if error_context.error_type == 'memory_overflow':
                return self._reduce_data_size(data)
            elif error_context.error_type == 'type_conversion':
                # 保持原始数据
                logger.info("🔧 [P2-ErrorRecovery] 降级处理: 保持原始数据类型")
                return True, data
            elif error_context.error_type == 'config_load_error':
                # 使用默认配置
                logger.info("🔧 [P2-ErrorRecovery] 降级处理: 使用默认配置")
                return True, {}
                
        except Exception as e:
            logger.error(f"🔧 [P2-ErrorRecovery] 降级处理失败: {e}")
        
        return False, data
    
    def _send_alert(self, error_context: ErrorContext):
        """发送告警"""
        logger.critical(f"🚨 [P2-ErrorRecovery] 严重错误告警: {error_context.to_dict()}")
    
    def get_error_statistics(self) -> Dict:
        """获取错误统计"""
        if not self._error_history:
            return {
                'total_errors': 0,
                'by_severity': {},
                'by_type': {},
                'recovery_success_rate': 0
            }
        
        # 按严重性统计
        by_severity = {}
        for severity in ErrorSeverity:
            count = sum(1 for e in self._error_history if e.severity == severity)
            if count > 0:
                by_severity[severity.value] = count
        
        # 按类型统计
        by_type = {}
        for error in self._error_history:
            if error.error_type not in by_type:
                by_type[error.error_type] = 0
            by_type[error.error_type] += 1
        
        # 恢复成功率
        recovered = sum(1 for e in self._error_history if e.recovery_attempts > 0)
        total = len(self._error_history)
        success_rate = recovered / total if total > 0 else 0
        
        return {
            'total_errors': total,
            'by_severity': by_severity,
            'by_type': by_type,
            'recovery_success_rate': success_rate,
            'recent_errors': [e.to_dict() for e in self._error_history[-5:]]
        }
    
    def clear_history(self):
        """清空错误历史"""
        self._error_history.clear()
        logger.info("🔧 [P2-ErrorRecovery] 错误历史已清空")

# 单例实例
_error_recovery_instance: Optional[ErrorRecoveryManager] = None

def get_error_recovery_manager() -> ErrorRecoveryManager:
    """获取错误恢复管理器单例"""
    global _error_recovery_instance
    if _error_recovery_instance is None:
        _error_recovery_instance = ErrorRecoveryManager()
    return _error_recovery_instance