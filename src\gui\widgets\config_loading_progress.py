#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置加载进度提示组件

提供配置加载过程的可视化反馈和状态提示
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QProgressBar, QPushButton, QTextEdit, QFrame
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QIcon, QPalette
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from loguru import logger


class LoadingStage(Enum):
    """加载阶段"""
    INITIALIZING = "initializing"
    READING_CONFIG = "reading_config"
    VALIDATING = "validating"
    CONVERTING_FORMAT = "converting_format"
    APPLYING_CONFIG = "applying_config"
    UPDATING_UI = "updating_ui"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class LoadingStep:
    """加载步骤"""
    stage: LoadingStage
    name: str
    description: str
    progress: int = 0  # 0-100
    status: str = "pending"  # pending, running, completed, failed
    details: str = ""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class ConfigLoadingWorker(QThread):
    """配置加载工作线程"""
    
    # 信号定义
    step_started = pyqtSignal(str, str)  # stage, description
    step_progress = pyqtSignal(str, int)  # stage, progress
    step_completed = pyqtSignal(str, str)  # stage, details
    step_failed = pyqtSignal(str, str)  # stage, error_message
    loading_completed = pyqtSignal(dict)  # result
    loading_failed = pyqtSignal(str)  # error_message
    
    def __init__(self, config_loader: Callable, config_name: str, parent=None):
        super().__init__(parent)
        self.config_loader = config_loader
        self.config_name = config_name
        self.result = None
        self.error_message = ""
        
    def run(self):
        """执行配置加载"""
        try:
            # 初始化阶段
            self.step_started.emit(LoadingStage.INITIALIZING.value, "初始化配置加载器...")
            self.msleep(100)  # 模拟处理时间
            self.step_progress.emit(LoadingStage.INITIALIZING.value, 50)
            self.msleep(100)
            self.step_completed.emit(LoadingStage.INITIALIZING.value, "配置加载器初始化完成")
            
            # 读取配置阶段
            self.step_started.emit(LoadingStage.READING_CONFIG.value, f"读取配置文件: {self.config_name}")
            self.step_progress.emit(LoadingStage.READING_CONFIG.value, 30)
            
            # 调用实际的配置加载函数
            config_data = self.config_loader(self.config_name)
            
            self.step_progress.emit(LoadingStage.READING_CONFIG.value, 100)
            self.step_completed.emit(LoadingStage.READING_CONFIG.value, "配置文件读取完成")
            
            if config_data is None:
                self.step_failed.emit(LoadingStage.READING_CONFIG.value, "配置文件不存在或读取失败")
                self.loading_failed.emit("配置加载失败：文件不存在或读取失败")
                return
            
            # 验证配置阶段
            self.step_started.emit(LoadingStage.VALIDATING.value, "验证配置数据...")
            self.step_progress.emit(LoadingStage.VALIDATING.value, 50)
            self.msleep(200)
            
            # 简单验证
            if not isinstance(config_data, dict):
                self.step_failed.emit(LoadingStage.VALIDATING.value, "配置数据格式不正确")
                self.loading_failed.emit("配置验证失败：数据格式不正确")
                return
                
            self.step_progress.emit(LoadingStage.VALIDATING.value, 100)
            self.step_completed.emit(LoadingStage.VALIDATING.value, "配置数据验证通过")
            
            # 格式转换阶段
            self.step_started.emit(LoadingStage.CONVERTING_FORMAT.value, "转换配置格式...")
            self.step_progress.emit(LoadingStage.CONVERTING_FORMAT.value, 70)
            self.msleep(150)
            self.step_completed.emit(LoadingStage.CONVERTING_FORMAT.value, "配置格式转换完成")
            
            # 应用配置阶段
            self.step_started.emit(LoadingStage.APPLYING_CONFIG.value, "应用配置到界面...")
            self.step_progress.emit(LoadingStage.APPLYING_CONFIG.value, 80)
            self.msleep(200)
            self.step_completed.emit(LoadingStage.APPLYING_CONFIG.value, "配置应用完成")
            
            # 更新UI阶段
            self.step_started.emit(LoadingStage.UPDATING_UI.value, "更新用户界面...")
            self.step_progress.emit(LoadingStage.UPDATING_UI.value, 90)
            self.msleep(100)
            self.step_completed.emit(LoadingStage.UPDATING_UI.value, "界面更新完成")
            
            # 完成
            self.result = config_data
            self.loading_completed.emit(config_data)
            
        except Exception as e:
            error_msg = f"配置加载过程中发生错误: {str(e)}"
            logger.error(error_msg)
            self.loading_failed.emit(error_msg)


class ConfigLoadingProgressDialog(QDialog):
    """配置加载进度对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("配置加载进度")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # 加载步骤
        self.steps: Dict[str, LoadingStep] = {}
        self.current_step = ""
        
        # 工作线程
        self.worker = None
        
        self._init_ui()
        self._init_steps()
        
    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("正在加载配置...")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 总体进度条
        self.overall_progress = QProgressBar()
        self.overall_progress.setRange(0, 100)
        self.overall_progress.setValue(0)
        layout.addWidget(self.overall_progress)
        
        # 当前步骤信息
        self.current_step_label = QLabel("准备开始...")
        layout.addWidget(self.current_step_label)
        
        # 当前步骤进度条
        self.step_progress = QProgressBar()
        self.step_progress.setRange(0, 100)
        self.step_progress.setValue(0)
        layout.addWidget(self.step_progress)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
        
        # 详细日志
        log_label = QLabel("详细信息:")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.cancel_loading)
        button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        self.close_button.setEnabled(False)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        
    def _init_steps(self):
        """初始化加载步骤"""
        steps_config = [
            (LoadingStage.INITIALIZING, "初始化", "初始化配置加载器"),
            (LoadingStage.READING_CONFIG, "读取配置", "从文件读取配置数据"),
            (LoadingStage.VALIDATING, "验证配置", "验证配置数据的完整性"),
            (LoadingStage.CONVERTING_FORMAT, "格式转换", "转换配置为统一格式"),
            (LoadingStage.APPLYING_CONFIG, "应用配置", "将配置应用到系统"),
            (LoadingStage.UPDATING_UI, "更新界面", "更新用户界面显示"),
        ]
        
        for stage, name, description in steps_config:
            self.steps[stage.value] = LoadingStep(
                stage=stage,
                name=name,
                description=description
            )
    
    def start_loading(self, config_loader: Callable, config_name: str):
        """开始加载配置"""
        self.log_text.clear()
        self.add_log(f"开始加载配置: {config_name}")
        
        # 创建工作线程
        self.worker = ConfigLoadingWorker(config_loader, config_name, self)
        
        # 连接信号
        self.worker.step_started.connect(self.on_step_started)
        self.worker.step_progress.connect(self.on_step_progress)
        self.worker.step_completed.connect(self.on_step_completed)
        self.worker.step_failed.connect(self.on_step_failed)
        self.worker.loading_completed.connect(self.on_loading_completed)
        self.worker.loading_failed.connect(self.on_loading_failed)
        
        # 启动线程
        self.worker.start()
        
    @pyqtSlot(str, str)
    def on_step_started(self, stage: str, description: str):
        """步骤开始"""
        self.current_step = stage
        if stage in self.steps:
            step = self.steps[stage]
            step.status = "running"
            step.start_time = datetime.now()
            
            self.current_step_label.setText(f"正在执行: {step.name} - {description}")
            self.step_progress.setValue(0)
            self.add_log(f"开始: {step.name}")
    
    @pyqtSlot(str, int)
    def on_step_progress(self, stage: str, progress: int):
        """步骤进度更新"""
        if stage in self.steps:
            self.steps[stage].progress = progress
            
        if stage == self.current_step:
            self.step_progress.setValue(progress)
            
        # 更新总体进度
        self._update_overall_progress()
    
    @pyqtSlot(str, str)
    def on_step_completed(self, stage: str, details: str):
        """步骤完成"""
        if stage in self.steps:
            step = self.steps[stage]
            step.status = "completed"
            step.progress = 100
            step.details = details
            step.end_time = datetime.now()
            
            self.add_log(f"完成: {step.name} - {details}")
            
        self._update_overall_progress()
    
    @pyqtSlot(str, str)
    def on_step_failed(self, stage: str, error_message: str):
        """步骤失败"""
        if stage in self.steps:
            step = self.steps[stage]
            step.status = "failed"
            step.details = error_message
            step.end_time = datetime.now()
            
            self.add_log(f"失败: {step.name} - {error_message}", is_error=True)
    
    @pyqtSlot(dict)
    def on_loading_completed(self, result: dict):
        """加载完成"""
        self.current_step_label.setText("配置加载完成！")
        self.overall_progress.setValue(100)
        self.step_progress.setValue(100)
        
        self.add_log("配置加载成功完成")
        
        self.cancel_button.setEnabled(False)
        self.close_button.setEnabled(True)
        
        # 自动关闭对话框
        QTimer.singleShot(1500, self.accept)
    
    @pyqtSlot(str)
    def on_loading_failed(self, error_message: str):
        """加载失败"""
        self.current_step_label.setText("配置加载失败")
        
        self.add_log(f"配置加载失败: {error_message}", is_error=True)
        
        self.cancel_button.setText("确定")
        self.close_button.setEnabled(True)
    
    def _update_overall_progress(self):
        """更新总体进度"""
        total_steps = len(self.steps)
        completed_steps = sum(1 for step in self.steps.values() if step.status == "completed")
        
        # 计算当前步骤的贡献
        current_contribution = 0
        if self.current_step in self.steps:
            current_step = self.steps[self.current_step]
            if current_step.status == "running":
                current_contribution = current_step.progress / 100 / total_steps
        
        overall_progress = (completed_steps / total_steps + current_contribution) * 100
        self.overall_progress.setValue(int(overall_progress))
    
    def add_log(self, message: str, is_error: bool = False):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        if is_error:
            formatted_message = f"<span style='color: red;'>{formatted_message}</span>"
        
        self.log_text.append(formatted_message)
        
        # 滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def cancel_loading(self):
        """取消加载"""
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()
            self.add_log("用户取消了配置加载")
        
        self.reject()
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()
        event.accept()


# 便捷函数
def show_config_loading_progress(parent, config_loader: Callable, config_name: str) -> Optional[dict]:
    """显示配置加载进度对话框"""
    dialog = ConfigLoadingProgressDialog(parent)
    dialog.start_loading(config_loader, config_name)
    
    if dialog.exec_() == QDialog.Accepted:
        return dialog.worker.result if dialog.worker else None
    else:
        return None


# 导出的类和函数
__all__ = [
    "LoadingStage",
    "LoadingStep",
    "ConfigLoadingWorker",
    "ConfigLoadingProgressDialog",
    "show_config_loading_progress"
]
