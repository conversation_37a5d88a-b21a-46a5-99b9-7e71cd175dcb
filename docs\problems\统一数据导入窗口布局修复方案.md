# 统一数据导入配置窗口布局修复方案

## 问题描述

通过点击主界面"导入数据"按钮，弹出"统一数据导入配置"窗口时发现以下布局问题：

1. **文件选择区域被遮挡**：在窗口顶部区域，"文件选择"相关内容没有全部显示，被遮挡了
2. **工具栏按钮过多挤压**：sheet名称列表上面的一排按钮过多，都挤到一起，很多按钮名称都看不清了

## 问题分析

### 1. 文件选择区域问题
- 文件信息组（QGroupBox）高度不够
- 内部组件间距设置不合理
- 缺少最小高度限制

### 2. 工具栏按钮问题
- Sheet工具栏：5个按钮挤在一行
- 映射工具栏：6个按钮挤在一行
- 按钮文字过长，导致显示不全
- 缺少合理的分组和换行

## 修复方案

### 1. 顶部工具栏优化

#### 修改内容
- 增加工具栏最小高度到80px，最大高度到100px
- 增加边距到15px，确保内容不被遮挡
- 为各个组件设置最小高度60px
- 统一按钮和控件高度为32px

#### 代码修改
```python
def _create_top_toolbar(self) -> QWidget:
    toolbar = QFrame()
    toolbar.setMinimumHeight(80)  # 增加最小高度
    toolbar.setMaximumHeight(100)  # 增加最大高度

    layout = QHBoxLayout(toolbar)
    layout.setContentsMargins(15, 15, 15, 15)  # 增加边距

    # 文件选择区
    file_group = QGroupBox("📁 文件选择")
    file_group.setMinimumHeight(60)  # 设置最小高度

    # 统一控件高度
    self.select_file_btn.setMinimumHeight(32)
    self.table_type_combo.setMinimumHeight(32)
```

### 2. 工具栏按钮布局优化

#### Sheet工具栏改进
- 采用垂直布局，分为两行
- 第一行：全选、全不选按钮
- 第二行：批量类型设置
- 限制工具栏最大高度为80px

#### 映射工具栏改进
- 采用垂直布局，分为两行
- 第一行：智能映射、批量编辑、导入、导出
- 第二行：检测冲突、解决冲突
- 缩短按钮文字，设置合理宽度

#### 代码修改
```python
def _create_sheet_toolbar(self) -> QWidget:
    toolbar = QWidget()
    toolbar.setMaximumHeight(80)  # 限制高度
    main_layout = QVBoxLayout(toolbar)  # 垂直布局
    
    # 第一行：选择操作
    first_row = QHBoxLayout()
    # 第二行：批量设置
    second_row = QHBoxLayout()
    
    main_layout.addLayout(first_row)
    main_layout.addLayout(second_row)
```

## 修复效果

### 预期改进
1. **文件选择区域**：内容完全显示，不再被遮挡
2. **工具栏按钮**：分两行显示，文字清晰可读
3. **整体布局**：更加协调美观，用户体验提升

### 兼容性
- 支持不同窗口大小
- 保持响应式布局特性
- 不影响现有功能

## 测试验证

### 测试脚本
创建了 `test/test_layout_fixes.py` 测试脚本，包含：
- 正常窗口大小测试 (1400x900)
- 小窗口大小测试 (1200x800)
- 大窗口大小测试 (1600x1000)
- 响应式调整测试

### 测试要点
1. 文件选择区域完整显示
2. 工具栏按钮分行显示
3. 按钮文字清晰可读
4. 不同窗口大小下的适应性

## 实施状态

- [x] 顶部工具栏布局优化
- [x] Sheet工具栏按钮重新布局
- [x] 映射工具栏按钮重新布局
- [x] 去除按钮图标，确保文字完全显示
- [x] 创建测试脚本
- [ ] 用户验收测试

## 最新修复（按钮文字显示问题）

### 问题
按钮上的图标占用过多空间，导致按钮名称显示不全。

### 解决方案
去除所有按钮前面的图标，只保留文字：
- Sheet工具栏：去除 ✅❌🔄👁️🔍 等图标
- 映射工具栏：去除 🤖💾📂🔄✅⚙️ 等图标
- 顶部工具栏：去除 📁📊⚙️❓ 等图标

### 修复效果
- 按钮文字完全显示，清晰可读
- 保持功能不变，只是视觉更简洁
- 按钮宽度得到更好利用

## 后续优化建议

1. **响应式设计**：根据窗口宽度动态调整按钮布局
2. **按钮分组**：使用分隔符或颜色区分不同功能组
3. **工具提示**：为缩短的按钮文字添加详细工具提示
4. **快捷键**：为常用按钮添加快捷键支持
