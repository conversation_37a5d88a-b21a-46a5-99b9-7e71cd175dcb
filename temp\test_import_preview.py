#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入预览和模拟功能测试脚本
"""

import sys
import os
import pandas as pd
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.import_preview_engine import ImportPreviewEngine, PreviewIssueLevel
from src.modules.data_import.sheet_config_manager import SheetConfigManager, SheetImportConfig

def create_test_excel_file():
    """创建测试Excel文件"""
    test_file = "temp/test_preview_data.xlsx"
    
    # 创建测试数据
    data1 = {
        '姓名': ['张三', '李四', '王五', '合计', '', '赵六'],
        '工号': ['001', '002', '003', '', '', '004'],
        '基本工资': [5000, 6000, 5500, 16500, '', 4800],
        '绩效工资': [1000, 1200, 800, 3000, '', 900],
        '备注': ['正常', '优秀', '良好', '', '', '新员工']
    }
    
    data2 = {
        '部门': ['技术部', '销售部', '人事部'],
        '人数': [10, 8, 5],
        '平均工资': [8000, 7000, 6000]
    }
    
    # 创建Excel文件
    with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
        pd.DataFrame(data1).to_excel(writer, sheet_name='工资表', index=False)
        pd.DataFrame(data2).to_excel(writer, sheet_name='部门统计', index=False)
        
        # 创建一个有问题的Sheet
        problem_data = {
            'A': ['', '', '姓名', '张三', '李四', ''],
            'B': ['2024年工资表', '', '工资', '5000', 'abc', ''],
            'C': ['', '', '部门', '技术部', '销售部', '']
        }
        pd.DataFrame(problem_data).to_excel(writer, sheet_name='问题表', index=False, header=False)
    
    print(f"✅ 创建测试Excel文件: {test_file}")
    return test_file

def test_preview_engine():
    """测试预览引擎"""
    print("=== 测试导入预览引擎 ===")
    
    # 创建测试文件
    test_file = create_test_excel_file()
    
    engine = ImportPreviewEngine()
    
    # 1. 测试正常Sheet预览
    print("\n📋 测试正常Sheet预览:")
    normal_config = SheetImportConfig(
        sheet_name="工资表",
        header_row=1,
        data_start_row=2,
        remove_summary_rows=True,
        summary_keywords=['合计', '小计'],
        skip_empty_rows=True,
        trim_whitespace=True,
        normalize_numbers=True
    )
    
    result = engine.preview_import(
        file_path=test_file,
        sheet_name="工资表",
        sheet_config=normal_config,
        max_preview_rows=100
    )
    
    print(f"  预览结果: {'✅ 成功' if result.is_successful else '❌ 失败'}")
    print(f"  处理时间: {result.processing_time_ms:.1f}ms")
    print(f"  问题数量: {len(result.issues)}")
    print(f"  风险级别: {result.get_risk_level()}")
    
    # 显示统计信息
    stats = result.statistics
    print(f"  📊 统计信息:")
    print(f"    总行数: {stats.total_rows}")
    print(f"    有效行数: {stats.valid_rows}")
    print(f"    无效行数: {stats.invalid_rows}")
    print(f"    空行数: {stats.empty_rows}")
    print(f"    重复行数: {stats.duplicate_rows}")
    print(f"    数据质量评分: {stats.get_data_quality_score():.1f}/100")
    
    # 显示问题
    if result.issues:
        print(f"  🔍 发现的问题:")
        for issue in result.issues:
            level_icon = {"info": "ℹ️", "warning": "⚠️", "error": "❌", "critical": "🚨"}
            print(f"    {level_icon.get(issue.level.value, '?')} {issue.title}")
            print(f"      {issue.description}")
    
    # 显示预览数据
    if result.processed_data is not None:
        print(f"  📄 处理后数据预览 (前3行):")
        print(result.processed_data.head(3).to_string(index=False))
    
    # 2. 测试有问题的Sheet预览
    print("\n📋 测试有问题的Sheet预览:")
    problem_config = SheetImportConfig(
        sheet_name="问题表",
        header_row=3,
        data_start_row=4,
        skip_empty_rows=True,
        trim_whitespace=True,
        normalize_numbers=True
    )
    
    result2 = engine.preview_import(
        file_path=test_file,
        sheet_name="问题表",
        sheet_config=problem_config,
        max_preview_rows=100
    )
    
    print(f"  预览结果: {'✅ 成功' if result2.is_successful else '❌ 失败'}")
    print(f"  问题数量: {len(result2.issues)}")
    print(f"  风险级别: {result2.get_risk_level()}")
    
    # 按级别显示问题
    for level in [PreviewIssueLevel.CRITICAL, PreviewIssueLevel.ERROR, PreviewIssueLevel.WARNING]:
        level_issues = result2.get_issues_by_level(level)
        if level_issues:
            level_icon = {"critical": "🚨", "error": "❌", "warning": "⚠️"}
            print(f"  {level_icon.get(level.value, '?')} {level.value.upper()}级问题 ({len(level_issues)}个):")
            for issue in level_issues[:3]:  # 只显示前3个
                print(f"    • {issue.title}")
    
    # 3. 测试字段映射
    print("\n📋 测试字段映射预览:")
    field_mappings = {
        '姓名': 'employee_name',
        '工号': 'employee_id',
        '基本工资': 'base_salary',
        '绩效工资': 'performance_bonus'
    }
    
    result3 = engine.preview_import(
        file_path=test_file,
        sheet_name="工资表",
        sheet_config=normal_config,
        field_mappings=field_mappings,
        max_preview_rows=100
    )
    
    print(f"  字段映射预览: {'✅ 成功' if result3.is_successful else '❌ 失败'}")
    if result3.processed_data is not None:
        print(f"  映射后字段: {list(result3.processed_data.columns)}")
    
    # 4. 测试预览摘要
    print("\n📊 测试预览摘要:")
    summary = engine.get_preview_summary([result, result2, result3])
    print(f"  总Sheet数: {summary.get('total_sheets', 0)}")
    print(f"  成功Sheet数: {summary.get('successful_sheets', 0)}")
    print(f"  总问题数: {summary.get('total_issues', 0)}")
    print(f"  数据质量评分: {summary.get('data_quality_score', 0):.1f}/100")
    print(f"  整体风险: {summary.get('overall_risk', 'unknown')}")
    
    return engine, test_file

def test_sheet_manager_integration():
    """测试与Sheet配置管理器的集成"""
    print("\n=== 测试Sheet配置管理器集成 ===")
    
    # 创建测试文件
    test_file = create_test_excel_file()
    
    manager = SheetConfigManager()
    manager.current_file_path = test_file
    
    # 1. 配置测试Sheet
    test_sheets = ["工资表", "部门统计", "问题表"]
    
    for sheet_name in test_sheets:
        if sheet_name == "工资表":
            manager.update_config(sheet_name,
                                header_row=1,
                                data_start_row=2,
                                remove_summary_rows=True,
                                summary_keywords=['合计', '小计'],
                                is_enabled=True)
        elif sheet_name == "部门统计":
            manager.update_config(sheet_name,
                                header_row=1,
                                data_start_row=2,
                                is_enabled=True)
        else:  # 问题表
            manager.update_config(sheet_name,
                                header_row=3,
                                data_start_row=4,
                                is_enabled=True)
    
    # 2. 单个Sheet预览
    print("\n🔍 单个Sheet预览:")
    for sheet_name in test_sheets:
        result = manager.preview_import(sheet_name, max_preview_rows=50)
        if result:
            print(f"  {sheet_name}: {'✅' if result.is_successful else '❌'} "
                  f"({len(result.issues)}个问题, 风险: {result.get_risk_level()})")
    
    # 3. 批量Sheet预览
    print("\n🔍 批量Sheet预览:")
    batch_results = manager.preview_multiple_sheets(test_sheets, max_preview_rows=50)
    print(f"  预览了 {len(batch_results)} 个Sheet")
    
    # 4. 预览摘要
    print("\n📊 预览摘要:")
    summary = manager.get_import_preview_summary(test_sheets)
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 5. 完整导入模拟
    print("\n🎭 完整导入过程模拟:")
    simulation = manager.simulate_import_process(test_sheets)
    
    if 'overall_assessment' in simulation:
        assessment = simulation['overall_assessment']
        print(f"  综合评分: {assessment.get('overall_score', 0)}/100")
        print(f"  准备状态: {assessment.get('readiness_text', '未知')}")
        print(f"  整体风险: {assessment.get('overall_risk', '未知')}")
        
        recommendations = assessment.get('recommendations', [])
        if recommendations:
            print(f"  💡 建议:")
            for rec in recommendations:
                print(f"    • {rec}")
    
    # 6. 配置健康度检查
    print("\n💊 配置健康度:")
    health = manager.get_config_health_status()
    print(f"  健康评分: {health.get('health_score', 0)}/100")
    print(f"  健康等级: {health.get('health_text', '未知')}")

def main():
    """主函数"""
    print("🚀 导入预览和模拟功能测试")
    print("=" * 50)
    
    try:
        # 测试预览引擎
        engine, test_file = test_preview_engine()
        
        # 测试集成功能
        test_sheet_manager_integration()
        
        print("\n✅ 所有测试完成")
        
        # 清理测试文件
        try:
            os.remove(test_file)
            print(f"🗑️ 清理测试文件: {test_file}")
        except:
            pass
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
