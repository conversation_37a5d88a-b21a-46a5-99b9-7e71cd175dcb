"""
字段类型管理对话框
提供字段类型的管理界面
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QMenu, QAction, QFileDialog,
    QInputDialog, QWidget
)
from PyQt5.QtCore import Qt, pyqtSignal
from typing import Optional
from loguru import logger
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from src.modules.data_import.field_type_manager import FieldTypeManager
from src.modules.data_import.formatting_engine import get_formatting_engine
from src.gui.field_type_editor_dialog import FieldTypeEditorDialog


class FieldTypeManagerDialog(QDialog):
    """字段类型管理对话框"""
    
    def __init__(self, parent=None):
        """初始化对话框"""
        super().__init__(parent)
        self.manager = FieldTypeManager()
        self.engine = get_formatting_engine()
        
        self.init_ui()
        self.load_field_types()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("字段类型管理")
        self.setMinimumSize(900, 600)
        
        layout = QVBoxLayout()
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("➕ 新建类型")
        self.add_btn.clicked.connect(self.create_field_type)
        toolbar_layout.addWidget(self.add_btn)
        
        self.edit_btn = QPushButton("✏️ 编辑")
        self.edit_btn.clicked.connect(self.edit_field_type)
        toolbar_layout.addWidget(self.edit_btn)
        
        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.clicked.connect(self.delete_field_type)
        toolbar_layout.addWidget(self.delete_btn)
        
        toolbar_layout.addStretch()
        
        self.import_btn = QPushButton("📥 导入")
        self.import_btn.clicked.connect(self.import_field_types)
        toolbar_layout.addWidget(self.import_btn)
        
        self.export_btn = QPushButton("📤 导出")
        self.export_btn.clicked.connect(self.export_field_types)
        toolbar_layout.addWidget(self.export_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 字段类型表格
        self.type_table = QTableWidget()
        self.type_table.setColumnCount(5)
        self.type_table.setHorizontalHeaderLabels([
            "类型ID", "类型名称", "描述", "基础规则", "来源"
        ])
        
        # 设置列宽
        header = self.type_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        # 设置行高
        self.type_table.verticalHeader().setDefaultSectionSize(30)
        
        # 双击编辑
        self.type_table.itemDoubleClicked.connect(self.on_item_double_clicked)
        
        # 右键菜单
        self.type_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.type_table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.type_table)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_field_types(self):
        """加载字段类型列表"""
        self.type_table.setRowCount(0)
        
        # 加载内置类型
        builtin_types = self.engine.get_field_types()
        for type_id, type_info in builtin_types.items():
            self.add_type_to_table(
                type_id,
                type_info.get("name", type_id),
                type_info.get("description", ""),
                type_info.get("rule_type", ""),
                "内置",
                False  # 内置类型不可编辑
            )
        
        # 加载自定义类型
        custom_types = self.manager.list_custom_field_types()
        for type_info in custom_types:
            self.add_type_to_table(
                type_info["id"],
                type_info.get("name", ""),
                type_info.get("description", ""),
                type_info.get("rule_type", ""),
                "自定义",
                True  # 自定义类型可编辑
            )
    
    def add_type_to_table(self, type_id: str, name: str, description: str,
                          rule_type: str, source: str, editable: bool):
        """添加类型到表格"""
        row = self.type_table.rowCount()
        self.type_table.insertRow(row)
        
        # 类型ID
        id_item = QTableWidgetItem(type_id)
        id_item.setData(Qt.UserRole, {"editable": editable, "type_id": type_id})
        self.type_table.setItem(row, 0, id_item)
        
        # 类型名称
        self.type_table.setItem(row, 1, QTableWidgetItem(name))
        
        # 描述
        self.type_table.setItem(row, 2, QTableWidgetItem(description))
        
        # 基础规则
        self.type_table.setItem(row, 3, QTableWidgetItem(rule_type))
        
        # 来源
        source_item = QTableWidgetItem(source)
        if source == "内置":
            source_item.setForeground(Qt.gray)
        else:
            source_item.setForeground(Qt.blue)
        self.type_table.setItem(row, 4, source_item)
        
        # 设置不可编辑的行样式
        if not editable:
            for col in range(5):
                item = self.type_table.item(row, col)
                if item:
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
    
    def create_field_type(self):
        """创建新字段类型"""
        dialog = FieldTypeEditorDialog(parent=self)
        dialog.field_type_created.connect(self.on_field_type_created)
        
        if dialog.exec_() == QDialog.Accepted:
            self.load_field_types()
    
    def edit_field_type(self):
        """编辑字段类型"""
        current_row = self.type_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要编辑的字段类型")
            return
        
        # 获取类型信息
        id_item = self.type_table.item(current_row, 0)
        type_data = id_item.data(Qt.UserRole)
        
        if not type_data.get("editable"):
            QMessageBox.warning(self, "提示", "内置字段类型不可编辑")
            return
        
        type_id = type_data.get("type_id")
        
        dialog = FieldTypeEditorDialog(field_type_id=type_id, parent=self)
        dialog.field_type_updated.connect(self.on_field_type_updated)
        
        if dialog.exec_() == QDialog.Accepted:
            self.load_field_types()
    
    def delete_field_type(self):
        """删除字段类型"""
        current_row = self.type_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "提示", "请先选择要删除的字段类型")
            return
        
        # 获取类型信息
        id_item = self.type_table.item(current_row, 0)
        type_data = id_item.data(Qt.UserRole)
        
        if not type_data.get("editable"):
            QMessageBox.warning(self, "提示", "内置字段类型不可删除")
            return
        
        type_id = type_data.get("type_id")
        type_name = self.type_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除字段类型 '{type_name}' 吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.manager.delete_field_type(type_id):
                QMessageBox.information(self, "成功", "字段类型已删除")
                self.load_field_types()
            else:
                QMessageBox.warning(self, "错误", "删除字段类型失败")
    
    def on_item_double_clicked(self, item):
        """双击事件处理"""
        self.edit_field_type()
    
    def show_context_menu(self, pos):
        """显示右键菜单"""
        menu = QMenu(self)
        
        current_row = self.type_table.currentRow()
        if current_row >= 0:
            id_item = self.type_table.item(current_row, 0)
            type_data = id_item.data(Qt.UserRole)
            
            if type_data.get("editable"):
                edit_action = QAction("编辑", self)
                edit_action.triggered.connect(self.edit_field_type)
                menu.addAction(edit_action)
                
                delete_action = QAction("删除", self)
                delete_action.triggered.connect(self.delete_field_type)
                menu.addAction(delete_action)
                
                menu.addSeparator()
            
            duplicate_action = QAction("复制为新类型", self)
            duplicate_action.triggered.connect(self.duplicate_field_type)
            menu.addAction(duplicate_action)
        
        menu.exec_(self.type_table.mapToGlobal(pos))
    
    def duplicate_field_type(self):
        """复制字段类型"""
        current_row = self.type_table.currentRow()
        if current_row < 0:
            return
        
        # 获取类型信息
        id_item = self.type_table.item(current_row, 0)
        type_data = id_item.data(Qt.UserRole)
        source_type_id = type_data.get("type_id")
        
        # 获取新的类型ID
        new_id, ok = QInputDialog.getText(
            self,
            "复制字段类型",
            "请输入新的类型ID:",
            text=f"{source_type_id}_copy"
        )
        
        if ok and new_id:
            # 获取源类型信息
            if type_data.get("editable"):
                # 自定义类型
                source_info = self.manager.get_field_type(source_type_id)
            else:
                # 内置类型
                source_info = self.engine.get_field_types().get(source_type_id)
            
            if source_info:
                # 创建新类型
                success = self.manager.create_field_type(
                    new_id,
                    f"{source_info.get('name', '')} (副本)",
                    source_info.get("description", ""),
                    source_info.get("rule_type", "string"),
                    source_info.get("default_config"),
                    source_info.get("validation_rules")
                )
                
                if success:
                    QMessageBox.information(self, "成功", f"已创建字段类型 '{new_id}'")
                    self.load_field_types()
                else:
                    QMessageBox.warning(self, "错误", "创建字段类型失败，ID可能已存在")
    
    def import_field_types(self):
        """导入字段类型"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "导入字段类型",
            "",
            "JSON Files (*.json)"
        )
        
        if file_path:
            reply = QMessageBox.question(
                self,
                "导入选项",
                "是否覆盖现有的字段类型？\n"
                "选择'是'将覆盖同名类型\n"
                "选择'否'将跳过同名类型",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
            )
            
            if reply == QMessageBox.Cancel:
                return
            
            overwrite = reply == QMessageBox.Yes
            
            if self.manager.import_field_types(file_path, overwrite):
                QMessageBox.information(self, "成功", "字段类型导入成功")
                self.load_field_types()
            else:
                QMessageBox.warning(self, "错误", "字段类型导入失败")
    
    def export_field_types(self):
        """导出字段类型"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出字段类型",
            "field_types.json",
            "JSON Files (*.json)"
        )
        
        if file_path:
            if self.manager.export_field_types(file_path):
                QMessageBox.information(self, "成功", f"字段类型已导出到\n{file_path}")
            else:
                QMessageBox.warning(self, "错误", "字段类型导出失败")
    
    def on_field_type_created(self, type_id: str, type_config: dict):
        """字段类型创建完成"""
        logger.info(f"字段类型 {type_id} 创建成功")
    
    def on_field_type_updated(self, type_id: str, type_config: dict):
        """字段类型更新完成"""
        logger.info(f"字段类型 {type_id} 更新成功")