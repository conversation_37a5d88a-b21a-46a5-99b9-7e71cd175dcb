{"key": "config:tt1", "data": {"field_mapping": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "field_types": {"序号": "integer", "工号": "employee_id_string", "姓名": "name_string", "部门名称": "text_string", "人员类别": "text_string", "人员类别代码": "personnel_category_code", "2025年岗位工资": "salary_float", "2025年校龄工资": "salary_float", "津贴": "salary_float", "结余津贴": "salary_float", "2025年基础性绩效": "salary_float", "卫生费": "salary_float", "2025年生活补贴": "salary_float", "车补": "salary_float", "2025年奖励性绩效预发": "salary_float", "补发": "salary_float", "借支": "salary_float", "应发工资": "salary_float", "2025公积金": "salary_float", "保险扣款": "salary_float", "代扣代存养老保险": "salary_float"}, "formatting_rules": {}}, "created_at": "2025-08-28T12:23:00.368450", "last_accessed": "2025-08-28T12:23:00.368450", "access_count": 1, "file_hash": "b98693e3075a20c18c51d86100798b7a", "file_mtime": 1756295031.5684166, "ttl_seconds": 3600.0, "metadata": {"config_name": "tt1", "config_type": "multi_sheet"}}