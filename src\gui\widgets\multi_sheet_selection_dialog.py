#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多工作表选择对话框

提供支持复选框的多工作表选择功能，包括全选/取消全选等便捷操作。
"""

from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QCheckBox, QScrollArea, QWidget, QFrame, QGroupBox,
    QDialogButtonBox, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from src.utils.log_config import setup_logger


class MultiSheetSelectionDialog(QDialog):
    """多工作表选择对话框
    
    支持复选框多选、全选/取消全选、预览工作表信息等功能
    """
    
    # 信号定义
    sheets_selected = pyqtSignal(list)  # 选中的工作表列表
    
    def __init__(self, parent=None, config_name: str = "", sheets_data: Dict[str, Any] = None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 基本属性
        self.config_name = config_name
        self.sheets_data = sheets_data or {}
        self.selected_sheets = []
        
        # UI组件
        self.sheet_checkboxes = {}
        self.info_labels = {}
        
        self._init_ui()
        self._setup_connections()
        self._load_sheet_data()
        
        self.logger.info(f"多工作表选择对话框初始化完成，配置: {config_name}, 工作表数: {len(self.sheets_data)}")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"选择工作表 - {self.config_name}")
        self.setModal(True)
        self.resize(600, 500)  # 增大窗口尺寸

        # 设置窗口图标和属性
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(16)  # 增加间距
        main_layout.setContentsMargins(20, 20, 20, 20)  # 增加边距

        # 标题和说明
        self._create_header(main_layout)

        # 统计信息区域
        self._create_stats_section(main_layout)

        # 全选控制区域
        self._create_select_all_section(main_layout)

        # 工作表列表区域
        self._create_sheets_section(main_layout)

        # 按钮区域
        self._create_buttons_section(main_layout)

        # 应用样式
        self._apply_styles()
    
    def _create_header(self, parent_layout):
        """创建标题和说明区域"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(8)
        header_layout.setContentsMargins(0, 10, 0, 15)

        # 主标题
        title_label = QLabel(f"配置选择：{self.config_name}")
        title_label.setObjectName("titleLabel")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("多工作表配置")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_font = QFont()
        subtitle_font.setPointSize(10)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)

        # 说明文字
        desc_label = QLabel("请选择要加载的工作表，支持单选或多选：")
        desc_label.setObjectName("descLabel")
        desc_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(desc_label)

        parent_layout.addWidget(header_frame)

    def _create_stats_section(self, parent_layout):
        """创建统计信息区域"""
        stats_frame = QFrame()
        stats_frame.setObjectName("statsFrame")
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setContentsMargins(15, 10, 15, 10)

        # 总工作表数
        total_sheets = len(self.sheets_data)
        total_label = QLabel(f"总工作表数：{total_sheets}")
        total_label.setObjectName("statsLabel")
        stats_layout.addWidget(total_label)

        stats_layout.addStretch()

        # 总字段数统计
        total_fields = 0
        for sheet_config in self.sheets_data.values():
            config_data = sheet_config.get('config', {})
            field_mapping = config_data.get('field_mapping', {})
            field_types = config_data.get('field_types', {})

            if field_mapping:
                total_fields += len(field_mapping)
            elif field_types:
                total_fields += len(field_types)
            else:
                total_fields += sheet_config.get('field_count', 0)

        fields_label = QLabel(f"总字段数：{total_fields}")
        fields_label.setObjectName("statsLabel")
        stats_layout.addWidget(fields_label)

        stats_layout.addStretch()

        # 选中状态
        self.selected_count_label = QLabel("已选择：0 个工作表")
        self.selected_count_label.setObjectName("selectedLabel")
        stats_layout.addWidget(self.selected_count_label)

        parent_layout.addWidget(stats_frame)

    def _create_select_all_section(self, parent_layout):
        """创建全选控制区域"""
        select_all_frame = QFrame()
        select_all_frame.setObjectName("selectAllFrame")
        select_all_layout = QHBoxLayout(select_all_frame)
        select_all_layout.setContentsMargins(15, 8, 15, 8)

        # 操作说明
        instruction_label = QLabel("请选择要加载的工作表（支持多选）：")
        instruction_label.setObjectName("instructionLabel")
        select_all_layout.addWidget(instruction_label)

        select_all_layout.addStretch()

        # 快捷按钮组
        button_group_layout = QHBoxLayout()
        button_group_layout.setSpacing(8)

        select_all_btn = QPushButton("✓ 全选")
        select_all_btn.setObjectName("actionButton")
        select_all_btn.setMinimumWidth(80)
        select_all_btn.clicked.connect(self._select_all_sheets)
        button_group_layout.addWidget(select_all_btn)

        deselect_all_btn = QPushButton("✗ 取消全选")
        deselect_all_btn.setObjectName("actionButton")
        deselect_all_btn.setMinimumWidth(80)
        deselect_all_btn.clicked.connect(self._deselect_all_sheets)
        button_group_layout.addWidget(deselect_all_btn)

        # 预览按钮
        preview_btn = QPushButton("👁 预览选中")
        preview_btn.setObjectName("previewButton")
        preview_btn.setMinimumWidth(80)
        preview_btn.clicked.connect(self._preview_selected)
        button_group_layout.addWidget(preview_btn)

        select_all_layout.addLayout(button_group_layout)
        parent_layout.addWidget(select_all_frame)
    
    def _create_sheets_section(self, parent_layout):
        """创建工作表列表区域"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 滚动内容容器
        scroll_widget = QWidget()
        self.sheets_layout = QVBoxLayout(scroll_widget)
        self.sheets_layout.setSpacing(8)
        
        scroll_area.setWidget(scroll_widget)
        parent_layout.addWidget(scroll_area)
    
    def _create_buttons_section(self, parent_layout):
        """创建按钮区域"""
        button_box = QDialogButtonBox()
        
        # 确定按钮
        ok_button = button_box.addButton("确定", QDialogButtonBox.AcceptRole)
        ok_button.setDefault(True)
        ok_button.clicked.connect(self._on_accept)
        
        # 取消按钮
        cancel_button = button_box.addButton("取消", QDialogButtonBox.RejectRole)
        cancel_button.clicked.connect(self.reject)
        
        parent_layout.addWidget(button_box)
    
    def _setup_connections(self):
        """设置信号连接"""
        # 移除了全选复选框，不需要连接信号
        pass
    
    def _load_sheet_data(self):
        """加载工作表数据"""
        if not self.sheets_data:
            self.logger.warning("没有工作表数据可加载")
            return

        self.logger.info(f"开始加载工作表数据，共 {len(self.sheets_data)} 个工作表")

        for sheet_name, sheet_config in self.sheets_data.items():
            self.logger.debug(f"加载工作表: {sheet_name}, 配置: {sheet_config}")
            self._create_sheet_item(sheet_name, sheet_config)

        # 添加弹性空间
        self.sheets_layout.addStretch()

        self.logger.info(f"已加载 {len(self.sheets_data)} 个工作表选项")
    
    def _create_sheet_item(self, sheet_name: str, sheet_config: Dict[str, Any]):
        """创建单个工作表选项"""
        # 创建工作表卡片
        sheet_card = QFrame()
        sheet_card.setObjectName("sheetCard")
        sheet_card.setFrameStyle(QFrame.Box)

        card_layout = QVBoxLayout(sheet_card)
        card_layout.setSpacing(8)
        card_layout.setContentsMargins(15, 12, 15, 12)

        # 顶部：工作表名称和复选框
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)

        # 工作表复选框
        checkbox = QCheckBox()
        checkbox.setObjectName(f"checkbox_{sheet_name}")
        checkbox.toggled.connect(self._on_sheet_toggled)
        self.sheet_checkboxes[sheet_name] = checkbox
        header_layout.addWidget(checkbox)

        # 工作表名称
        name_label = QLabel(sheet_name)
        name_label.setObjectName("sheetNameLabel")
        name_font = QFont()
        name_font.setPointSize(11)
        name_font.setBold(True)
        name_label.setFont(name_font)
        header_layout.addWidget(name_label)

        header_layout.addStretch()

        # 字段数标签
        config_data = sheet_config.get('config', {})
        field_mapping = config_data.get('field_mapping', {})
        field_types = config_data.get('field_types', {})

        if field_mapping:
            field_count = len(field_mapping)
        elif field_types:
            field_count = len(field_types)
        else:
            field_count = sheet_config.get('field_count', 0)

        field_count_label = QLabel(f"{field_count} 个字段")
        field_count_label.setObjectName("fieldCountLabel")
        header_layout.addWidget(field_count_label)

        card_layout.addLayout(header_layout)

        # 底部：详细信息
        info_text = self._format_sheet_info(sheet_config)
        if info_text:
            info_label = QLabel(info_text)
            info_label.setObjectName("sheetInfoLabel")
            info_label.setWordWrap(True)
            self.info_labels[sheet_name] = info_label
            card_layout.addWidget(info_label)

        self.sheets_layout.addWidget(sheet_card)
    
    def _format_sheet_info(self, sheet_config: Dict[str, Any]) -> str:
        """格式化工作表信息"""
        info_parts = []

        # 🔧 [界面优化] 去除多余的字段数显示，因为每个表项右边已有字段统计
        # 注释掉字段数量显示逻辑
        # config_data = sheet_config.get('config', {})
        # if config_data:
        #     # 优先使用field_mapping的长度，其次使用field_types
        #     field_mapping = config_data.get('field_mapping', {})
        #     field_types = config_data.get('field_types', {})
        #
        #     if field_mapping:
        #         field_count = len(field_mapping)
        #     elif field_types:
        #         field_count = len(field_types)
        #     else:
        #         # 如果都没有，使用预设的field_count或计算所有非系统字段
        #         field_count = sheet_config.get('field_count', 0)
        #         if field_count == 0:
        #             # 排除系统字段，计算实际字段数
        #             system_keys = {'field_mapping', 'field_types', 'formatting_rules', 'template_based', 'created_at', 'updated_at'}
        #             field_count = len([k for k in config_data.keys() if k not in system_keys])
        #
        #     info_parts.append(f"字段数: {field_count}")
        # else:
        #     # 如果没有config数据，尝试从顶级获取field_count
        #     field_count = sheet_config.get('field_count', 0)
        #     if field_count > 0:
        #         info_parts.append(f"字段数: {field_count}")

        # 描述信息
        description = sheet_config.get('description', '')
        if description:
            info_parts.append(f"描述: {description}")

        # 更新时间
        updated_at = sheet_config.get('updated_at', '')
        if updated_at:
            info_parts.append(f"更新: {updated_at[:19]}")  # 只显示日期时间部分

        return " | ".join(info_parts)
    

    
    def _on_sheet_toggled(self):
        """处理单个工作表复选框切换"""
        # 更新选中列表
        self.selected_sheets = []
        for sheet_name, checkbox in self.sheet_checkboxes.items():
            if checkbox.isChecked():
                self.selected_sheets.append(sheet_name)

        # 更新选中计数显示
        self._update_selected_count()

        self.logger.debug(f"工作表选择状态更新: {self.selected_sheets}")

    def _update_selected_count(self):
        """更新选中计数显示"""
        selected_count = len(self.selected_sheets)
        total_count = len(self.sheet_checkboxes)

        if hasattr(self, 'selected_count_label'):
            if selected_count == 0:
                self.selected_count_label.setText("已选择：0 个工作表")
                self.selected_count_label.setStyleSheet("color: #666;")
            elif selected_count == total_count:
                self.selected_count_label.setText(f"已选择：全部 {selected_count} 个工作表")
                self.selected_count_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            else:
                self.selected_count_label.setText(f"已选择：{selected_count} / {total_count} 个工作表")
                self.selected_count_label.setStyleSheet("color: #2196F3; font-weight: bold;")

    def _preview_selected(self):
        """预览选中的工作表"""
        if not self.selected_sheets:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "请先选择要预览的工作表")
            return

        # 简单预览
        preview_text = f"已选择 {len(self.selected_sheets)} 个工作表：\n\n"

        total_fields = 0
        for sheet_name in self.selected_sheets:
            if sheet_name in self.sheets_data:
                sheet_config = self.sheets_data[sheet_name]
                config_data = sheet_config.get('config', {})
                field_mapping = config_data.get('field_mapping', {})
                field_types = config_data.get('field_types', {})

                if field_mapping:
                    field_count = len(field_mapping)
                elif field_types:
                    field_count = len(field_types)
                else:
                    field_count = sheet_config.get('field_count', 0)

                total_fields += field_count
                preview_text += f"• {sheet_name}: {field_count} 个字段\n"

        preview_text += f"\n总计：{total_fields} 个字段"

        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "预览选中的工作表", preview_text)
    
    def _select_all_sheets(self):
        """全选所有工作表"""
        # 临时断开信号连接，避免循环触发
        for checkbox in self.sheet_checkboxes.values():
            checkbox.toggled.disconnect(self._on_sheet_toggled)
            checkbox.setChecked(True)
            checkbox.toggled.connect(self._on_sheet_toggled)
        # 手动触发一次更新
        self._on_sheet_toggled()

    def _deselect_all_sheets(self):
        """取消选择所有工作表"""
        # 临时断开信号连接，避免循环触发
        for checkbox in self.sheet_checkboxes.values():
            checkbox.toggled.disconnect(self._on_sheet_toggled)
            checkbox.setChecked(False)
            checkbox.toggled.connect(self._on_sheet_toggled)
        # 手动触发一次更新
        self._on_sheet_toggled()
    
    def _on_accept(self):
        """处理确定按钮点击"""
        # 收集选中的工作表
        self.selected_sheets = [
            sheet_name for sheet_name, checkbox in self.sheet_checkboxes.items()
            if checkbox.isChecked()
        ]
        
        if not self.selected_sheets:
            # 没有选择任何工作表，提示用户
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(
                self, "提示", 
                "请至少选择一个工作表！"
            )
            return
        
        self.logger.info(f"用户选择了 {len(self.selected_sheets)} 个工作表: {self.selected_sheets}")
        
        # 发出信号并关闭对话框
        self.sheets_selected.emit(self.selected_sheets)
        self.accept()
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            }

            /* 标题样式 */
            #titleLabel {
                color: #2c3e50;
                background: transparent;
                padding: 5px;
            }
            #subtitleLabel {
                color: #7f8c8d;
                background: transparent;
            }
            #descLabel {
                color: #5a6c7d;
                background: transparent;
                margin: 8px 0;
            }

            /* 统计信息框架 */
            #statsFrame {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin: 5px 0;
            }
            #statsLabel {
                color: #495057;
                font-weight: 500;
                padding: 2px 5px;
            }
            #selectedLabel {
                color: #2196F3;
                font-weight: bold;
                padding: 2px 5px;
            }

            /* 全选控制区域 */
            #selectAllFrame {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin: 5px 0;
            }

            #instructionLabel {
                color: #495057;
                font-weight: 600;
                font-size: 11px;
            }
            #selectAllCheckbox {
                font-weight: 600;
                color: #2196F3;
                spacing: 8px;
            }

            /* 工作表卡片 */
            #sheetCard {
                background-color: #ffffff;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                margin: 4px 0;
            }
            #sheetCard:hover {
                border-color: #2196F3;
                background-color: #f8f9ff;
            }

            #sheetNameLabel {
                color: #2c3e50;
                font-weight: bold;
            }
            #fieldCountLabel {
                color: #6c757d;
                background-color: #e9ecef;
                border-radius: 12px;
                padding: 4px 8px;
                font-size: 10px;
                font-weight: 500;
            }
            #sheetInfoLabel {
                color: #6c757d;
                font-size: 11px;
                line-height: 1.4;
                margin-top: 5px;
            }

            /* 复选框样式 */
            QCheckBox {
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #ced4da;
                background-color: #ffffff;
                border-radius: 4px;
            }
            QCheckBox::indicator:unchecked:hover {
                border-color: #2196F3;
                background-color: #f8f9ff;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #2196F3;
                background-color: #2196F3;
                border-radius: 4px;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QCheckBox::indicator:indeterminate {
                border: 2px solid #ffc107;
                background-color: #ffc107;
                border-radius: 4px;
            }

            /* 按钮样式 */
            #actionButton {
                background-color: #ffffff;
                border: 2px solid #2196F3;
                border-radius: 6px;
                color: #2196F3;
                font-weight: 600;
                padding: 8px 16px;
                min-height: 20px;
            }
            #actionButton:hover {
                background-color: #2196F3;
                color: #ffffff;
            }
            #actionButton:pressed {
                background-color: #1976D2;
                border-color: #1976D2;
            }

            #previewButton {
                background-color: #ffffff;
                border: 2px solid #ff9800;
                border-radius: 6px;
                color: #ff9800;
                font-weight: 600;
                padding: 8px 16px;
                min-height: 20px;
            }
            #previewButton:hover {
                background-color: #ff9800;
                color: #ffffff;
            }
            #previewButton:pressed {
                background-color: #f57c00;
                border-color: #f57c00;
            }

            /* 对话框按钮 */
            QDialogButtonBox QPushButton {
                background-color: #ffffff;
                border: 2px solid #6c757d;
                border-radius: 6px;
                color: #6c757d;
                font-weight: 600;
                padding: 10px 20px;
                min-width: 80px;
                min-height: 20px;
            }
            QDialogButtonBox QPushButton:hover {
                background-color: #6c757d;
                color: #ffffff;
            }
            QDialogButtonBox QPushButton[default="true"] {
                border-color: #28a745;
                color: #28a745;
            }
            QDialogButtonBox QPushButton[default="true"]:hover {
                background-color: #28a745;
                color: #ffffff;
            }

            /* 滚动条样式 */
            QScrollArea {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
            }
            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #ced4da;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #adb5bd;
            }
        """)
    
    def get_selected_sheets(self) -> List[str]:
        """获取选中的工作表列表"""
        return self.selected_sheets.copy()
