#!/usr/bin/env python3
"""
测试数据处理组件功能
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.gui.unified_data_import_window import UnifiedDataImportWindow
from src.utils.log_config import setup_logger

def test_data_processing_widget():
    """测试数据处理组件功能"""
    logger = setup_logger(__name__)
    
    app = QApplication([])
    
    try:
        # 创建窗口
        window = UnifiedDataImportWindow()
        window.show()
        
        # 切换到数据处理选项卡
        window.config_details_widget.setCurrentIndex(1)  # 数据处理选项卡
        
        def test_processing_features():
            logger.info("开始测试数据处理功能...")
            
            processing_widget = window.processing_tab
            
            # 测试1: 检查UI组件是否存在
            logger.info("检查UI组件...")
            
            components = {
                'remove_duplicates_cb': '去除重复数据复选框',
                'missing_values_combo': '缺失值处理下拉框',
                'trim_whitespace_cb': '去除空白字符复选框',
                'format_numbers_cb': '格式化数字复选框',
                'format_dates_cb': '格式化日期复选框',
                'convert_types_cb': '数据类型转换复选框',
                'data_validation_cb': '数据验证复选框',
                'preview_processing_btn': '预览处理效果按钮',
                'reset_config_btn': '重置配置按钮',
                'save_config_btn': '保存配置按钮',
                'load_config_btn': '加载配置按钮',
                'add_rule_btn': '添加规则按钮',
                'rules_list': '自定义规则列表'
            }
            
            for attr, desc in components.items():
                if hasattr(processing_widget, attr):
                    logger.info(f"✅ {desc} 存在")
                else:
                    logger.warning(f"❌ {desc} 不存在")
            
            # 测试2: 测试配置变化
            logger.info("测试配置变化...")
            
            # 修改一些配置
            processing_widget.remove_duplicates_cb.setChecked(True)
            processing_widget.missing_values_combo.setCurrentIndex(1)  # 删除包含空值的行
            processing_widget.data_validation_cb.setChecked(False)
            
            config = processing_widget.get_processing_config()
            logger.info(f"当前配置: {config}")
            
            # 测试3: 测试预览功能
            logger.info("测试预览功能...")
            processing_widget.preview_processing_btn.click()
            
            # 测试4: 测试重置功能
            logger.info("测试重置功能...")
            # 注意：这会弹出确认对话框，需要手动处理
            
            # 测试5: 测试自定义规则
            logger.info("测试自定义规则...")
            processing_widget.add_rule_btn.click()
            # 注意：这会弹出添加规则对话框
            
            logger.info("数据处理功能测试完成")
            
            # 5秒后关闭
            QTimer.singleShot(5000, app.quit)
        
        # 2秒后开始测试
        QTimer.singleShot(2000, test_processing_features)
        
        app.exec_()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_processing_widget()
