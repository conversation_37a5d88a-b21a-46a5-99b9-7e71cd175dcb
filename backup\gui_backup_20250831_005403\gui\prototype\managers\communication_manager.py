"""
PyQt5重构高保真原型 - 组件通信架构管理器

基于PyQt5原生信号槽机制的组件通信系统，提供统一的组件注册、连接管理和错误处理。
实现了安全连接管理、动态组件注册、连接状态监控等功能。

主要功能:
1. 组件统一注册和管理
2. 信号槽连接的集中化管理
3. 安全连接管理器避免内存泄漏
4. 组件间通信状态监控
5. 错误处理和异常传播机制
"""

from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QWidget
from typing import Dict, Any, Callable, Optional, List
import weakref
import logging

from src.utils.log_config import setup_logger


class SafeConnectionManager:
    """
    安全连接管理器
    
    使用弱引用避免循环引用，提供连接状态监控和自动清理功能。
    """
    
    def __init__(self):
        self.connections: List[Dict[str, Any]] = []
        self.logger = setup_logger(__name__ + ".SafeConnectionManager")
    
    def connect(self, sender: QObject, signal, receiver: QObject, slot: Callable):
        """
        创建安全的信号槽连接
        
        Args:
            sender: 信号发送者
            signal: 信号
            receiver: 信号接收者  
            slot: 槽函数
        """
        try:
            # 建立连接
            signal.connect(slot)
            
            # 记录连接信息（使用弱引用避免内存泄漏）
            connection_info = {
                'sender_ref': weakref.ref(sender),
                'receiver_ref': weakref.ref(receiver),
                'signal_name': signal.signal if hasattr(signal, 'signal') else str(signal),
                'slot_name': slot.__name__ if hasattr(slot, '__name__') else str(slot)
            }
            self.connections.append(connection_info)
            
            self.logger.debug(f"连接建立: {sender.__class__.__name__} -> {receiver.__class__.__name__}")
            
        except Exception as e:
            self.logger.error(f"连接失败: {e}")
            raise
    
    def disconnect_all(self):
        """
        断开所有连接
        """
        disconnected_count = 0
        for connection in self.connections:
            sender = connection['sender_ref']()
            receiver = connection['receiver_ref']()
            
            if sender and receiver:
                try:
                    # 这里需要具体的断开逻辑，暂时记录
                    self.logger.debug(f"断开连接: {connection['signal_name']}")
                    disconnected_count += 1
                except Exception as e:
                    self.logger.warning(f"断开连接失败: {e}")
        
        self.connections.clear()
        self.logger.info(f"断开 {disconnected_count} 个连接")
    
    def cleanup_dead_references(self):
        """
        清理无效的弱引用
        """
        before_count = len(self.connections)
        self.connections = [
            conn for conn in self.connections 
            if conn['sender_ref']() is not None and conn['receiver_ref']() is not None
        ]
        after_count = len(self.connections)
        
        if before_count != after_count:
            self.logger.debug(f"清理了 {before_count - after_count} 个无效连接引用")


class ComponentCommunicationManager(QObject):
    """
    组件通信架构管理器
    
    基于PyQt5信号槽机制的组件通信系统，提供组件注册、连接管理、
    状态监控等功能。
    """
    
    # 通信状态信号
    component_registered = pyqtSignal(str, QObject)  # (component_name, component)
    component_unregistered = pyqtSignal(str)  # (component_name)
    connection_established = pyqtSignal(str, str)  # (sender_name, receiver_name)
    communication_error = pyqtSignal(str, str)  # (component_name, error_message)
    
    def __init__(self, main_window: QWidget):
        """
        初始化组件通信管理器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.components: Dict[str, QObject] = {}
        self.component_refs: Dict[str, weakref.ref] = {}
        
        # 初始化日志
        self.logger = setup_logger(__name__)
        
        # 安全连接管理器
        self.connection_manager = SafeConnectionManager()
        
        # 连接映射记录
        self.connection_map: Dict[str, List[Dict[str, str]]] = {}
        
        self.logger.info("组件通信管理器初始化完成")
    
    def register_component(self, name: str, component: QObject):
        """
        注册组件
        
        Args:
            name: 组件名称
            component: 组件实例
        """
        if name in self.components:
            self.logger.warning(f"组件 {name} 已存在，将被替换")
        
        self.components[name] = component
        self.component_refs[name] = weakref.ref(component, 
                                               lambda ref: self._cleanup_component(name))
        
        self.logger.info(f"注册组件: {name} ({component.__class__.__name__})")
        self.component_registered.emit(name, component)
    
    def unregister_component(self, name: str):
        """
        注销组件
        
        Args:
            name: 组件名称
        """
        if name in self.components:
            del self.components[name]
        if name in self.component_refs:
            del self.component_refs[name]
        if name in self.connection_map:
            del self.connection_map[name]
            
        self.logger.info(f"注销组件: {name}")
        self.component_unregistered.emit(name)
    
    def _cleanup_component(self, name: str):
        """
        清理组件（当组件被垃圾回收时调用）
        
        Args:
            name: 组件名称
        """
        self.logger.debug(f"自动清理组件: {name}")
        self.unregister_component(name)
    
    def get_component(self, name: str) -> Optional[QObject]:
        """
        获取组件实例
        
        Args:
            name: 组件名称
            
        Returns:
            组件实例，如果不存在则返回None
        """
        return self.components.get(name)
    
    def setup_all_connections(self):
        """
        设置所有组件间的连接
        """
        self.logger.info("开始建立组件连接")
        
        try:
            self._setup_navigation_connections()
            self._setup_search_connections()
            self._setup_table_connections()
            self._setup_layout_connections()
            self._setup_animation_connections()
            
            self.logger.info("所有组件连接建立完成")
            
        except Exception as e:
            self.logger.error(f"建立组件连接失败: {e}")
            self.communication_error.emit("ComponentCommunicationManager", str(e))
    
    def _setup_navigation_connections(self):
        """
        设置导航相关连接
        """
        # 获取实际存在的组件
        navigation_panel = self.get_component('navigation_panel')
        main_workspace = self.get_component('main_workspace')
        
        if navigation_panel and main_workspace:
            # 检查组件是否有相应的信号
            if hasattr(navigation_panel, 'navigation_changed'):
                try:
                    navigation_panel.navigation_changed.connect(
                        lambda category, path: self._on_navigation_changed(category, path)
                    )
                    self._record_connection('navigation_panel', 'communication_manager', 'navigation')
                    self.connection_established.emit('navigation_panel', 'communication_manager')
                    self.logger.debug("导航面板连接建立成功")
                except Exception as e:
                    self.logger.error(f"导航面板连接失败: {e}")
    
    def _setup_search_connections(self):
        """
        设置搜索相关连接
        """
        # 目前暂时跳过搜索连接，因为相关组件可能不存在
        self.logger.debug("搜索连接暂时跳过")
    
    def _setup_table_connections(self):
        """
        设置表格相关连接
        """
        # 目前暂时跳过表格连接，因为相关组件可能不存在
        self.logger.debug("表格连接暂时跳过")
    
    def _setup_layout_connections(self):
        """
        设置布局相关连接
        """
        layout_manager = self.get_component('layout_manager')
        
        if layout_manager and hasattr(layout_manager, 'breakpoint_changed'):
            try:
                # 布局变化连接
                layout_manager.breakpoint_changed.connect(self._on_breakpoint_changed)
                self._record_connection('layout_manager', 'communication_manager', 'responsive')
                self.connection_established.emit('layout_manager', 'communication_manager')
                self.logger.debug("布局管理器连接建立成功")
            except Exception as e:
                self.logger.error(f"布局管理器连接失败: {e}")
    
    def _setup_animation_connections(self):
        """
        设置动画相关连接
        """
        # 目前暂时跳过动画连接，因为相关组件可能不存在
        self.logger.debug("动画连接暂时跳过")
    
    def _on_navigation_changed(self, category: str, path: str):
        """
        处理导航变化事件
        
        Args:
            category: 导航分类
            path: 导航路径
        """
        self.logger.debug(f"导航变化: {category} -> {path}")
        
        # 通知其他相关组件
        main_workspace = self.get_component('main_workspace')
        if main_workspace and hasattr(main_workspace, 'handle_navigation_change'):
            try:
                main_workspace.handle_navigation_change(category, path)
            except Exception as e:
                self.logger.error(f"主工作区导航响应失败: {e}")
    
    def _on_breakpoint_changed(self, breakpoint: str, config: dict):
        """
        处理断点变化事件
        
        Args:
            breakpoint: 新断点名称
            config: 布局配置
        """
        self.logger.info(f"断点变化: {breakpoint}")
        
        # 通知所有注册的组件进行响应式调整
        for name, component in self.components.items():
            if hasattr(component, 'handle_responsive_change'):
                try:
                    component.handle_responsive_change(breakpoint, config)
                except Exception as e:
                    self.logger.error(f"组件 {name} 响应式调整失败: {e}")
                    self.communication_error.emit(name, str(e))
    
    def _record_connection(self, sender_name: str, receiver_name: str, connection_type: str):
        """
        记录连接信息
        
        Args:
            sender_name: 发送者组件名称
            receiver_name: 接收者组件名称
            connection_type: 连接类型
        """
        if sender_name not in self.connection_map:
            self.connection_map[sender_name] = []
        
        self.connection_map[sender_name].append({
            'receiver': receiver_name,
            'type': connection_type
        })
    
    def get_connection_map(self) -> Dict[str, List[Dict[str, str]]]:
        """
        获取连接映射
        
        Returns:
            连接映射字典
        """
        return self.connection_map.copy()
    
    def get_component_list(self) -> List[str]:
        """
        获取已注册组件列表
        
        Returns:
            组件名称列表
        """
        return list(self.components.keys())
    
    def cleanup_all_connections(self):
        """
        清理所有连接
        """
        self.connection_manager.disconnect_all()
        self.connection_manager.cleanup_dead_references()
        self.logger.info("清理所有组件连接完成")
    
    def verify_components_health(self) -> Dict[str, bool]:
        """
        验证组件健康状态
        
        Returns:
            组件健康状态字典 {component_name: is_healthy}
        """
        health_status = {}
        
        for name, ref in self.component_refs.items():
            component = ref()
            is_healthy = component is not None
            health_status[name] = is_healthy
            
            if not is_healthy:
                self.logger.warning(f"组件 {name} 不健康（可能已被垃圾回收）")
        
        return health_status 