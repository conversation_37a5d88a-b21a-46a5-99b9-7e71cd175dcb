#!/usr/bin/env python3
"""
诊断按钮功能问题
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def diagnose_button_issue():
    """诊断按钮功能问题"""
    print("开始诊断按钮功能问题...")
    
    try:
        # 检查核心组件是否可以导入
        print("1. 检查核心组件导入...")
        from src.gui.core import SmartMappingEngine, TemplateManager, ValidationEngine
        print("   ✅ 核心组件导入成功")
        
        # 检查统一数据导入窗口是否可以导入
        print("2. 检查统一数据导入窗口导入...")
        from src.gui.unified_data_import_window import UnifiedDataImportWindow
        print("   ✅ 统一数据导入窗口导入成功")
        
        # 检查PyQt5是否可用
        print("3. 检查PyQt5...")
        from PyQt5.QtWidgets import QApplication
        print("   ✅ PyQt5导入成功")
        
        # 创建应用程序实例
        print("4. 创建应用程序实例...")
        app = QApplication([])
        print("   ✅ 应用程序实例创建成功")
        
        # 创建窗口实例
        print("5. 创建窗口实例...")
        window = UnifiedDataImportWindow()
        print("   ✅ 窗口实例创建成功")
        
        # 检查按钮是否存在
        print("6. 检查按钮是否存在...")
        
        # 检查Sheet管理按钮
        if hasattr(window.sheet_management_widget, 'select_all_btn'):
            print("   ✅ Sheet管理按钮存在")
        else:
            print("   ❌ Sheet管理按钮不存在")
        
        # 检查映射配置按钮
        if hasattr(window.mapping_tab, 'smart_mapping_btn'):
            print("   ✅ 映射配置按钮存在")
        else:
            print("   ❌ 映射配置按钮不存在")
        
        # 检查顶部工具栏按钮
        if hasattr(window, 'help_btn'):
            print("   ✅ 顶部工具栏按钮存在")
        else:
            print("   ❌ 顶部工具栏按钮不存在")
        
        # 检查信号连接
        print("7. 检查信号连接...")
        
        # 检查Sheet管理按钮信号
        try:
            btn = window.sheet_management_widget.select_all_btn
            receivers = btn.receivers(btn.clicked)
            if receivers > 0:
                print(f"   ✅ 全选按钮已连接信号 ({receivers} 个接收者)")
            else:
                print("   ❌ 全选按钮未连接信号")
        except Exception as e:
            print(f"   ❌ 检查全选按钮信号失败: {e}")
        
        # 检查映射按钮信号
        try:
            btn = window.mapping_tab.smart_mapping_btn
            receivers = btn.receivers(btn.clicked)
            if receivers > 0:
                print(f"   ✅ 智能映射按钮已连接信号 ({receivers} 个接收者)")
            else:
                print("   ❌ 智能映射按钮未连接信号")
        except Exception as e:
            print(f"   ❌ 检查智能映射按钮信号失败: {e}")
        
        print("\n诊断完成！")
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_button_issue()
