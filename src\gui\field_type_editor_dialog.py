"""
字段类型编辑对话框
提供自定义字段类型的创建和编辑界面
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QTextEdit, QComboBox, QPushButton, QTableWidget,
    QTableWidgetItem, QGroupBox, QMessageBox, QTabWidget,
    QFormLayout, QSpinBox, QCheckBox, QDialogButtonBox,
    QHeaderView, QMenu, QAction, QFileDialog, QWidget
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
import json
from typing import Dict, Any, Optional
from loguru import logger
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from src.modules.data_import.field_type_manager import FieldTypeManager
from src.modules.data_import.formatting_engine import get_formatting_engine


class FieldTypeEditorDialog(QDialog):
    """字段类型编辑对话框"""
    
    # 信号
    field_type_created = pyqtSignal(str, dict)  # type_id, type_config
    field_type_updated = pyqtSignal(str, dict)
    
    def __init__(self, field_type_id: str = None, parent=None):
        """
        初始化对话框
        
        Args:
            field_type_id: 要编辑的字段类型ID（None表示创建新类型）
            parent: 父窗口
        """
        super().__init__(parent)
        self.field_type_id = field_type_id
        self.is_edit_mode = field_type_id is not None
        self.manager = FieldTypeManager()
        self.engine = get_formatting_engine()
        
        # 如果是编辑模式，加载现有数据
        self.current_field_type = None
        if self.is_edit_mode:
            self.current_field_type = self.manager.get_field_type(field_type_id)
            if not self.current_field_type:
                QMessageBox.warning(self, "错误", f"字段类型 {field_type_id} 不存在")
                self.reject()
                return
        
        self.init_ui()
        
        # 如果是编辑模式，填充数据
        if self.is_edit_mode and self.current_field_type:
            self.load_field_type_data()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("编辑字段类型" if self.is_edit_mode else "创建字段类型")
        self.setMinimumSize(800, 600)
        
        layout = QVBoxLayout()
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        self.tab_widget.addTab(basic_tab, "基本信息")
        
        # 格式化配置标签页
        format_tab = self.create_format_config_tab()
        self.tab_widget.addTab(format_tab, "格式化配置")
        
        # 验证规则标签页
        validation_tab = self.create_validation_tab()
        self.tab_widget.addTab(validation_tab, "验证规则")
        
        # 自定义代码标签页（仅当基础类型为custom时）
        self.custom_code_tab = self.create_custom_code_tab()
        self.tab_widget.addTab(self.custom_code_tab, "自定义代码")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("测试")
        self.test_btn.clicked.connect(self.test_field_type)
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_field_type)
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QFormLayout()
        
        # 类型ID（创建后不可修改）
        self.type_id_edit = QLineEdit()
        if self.is_edit_mode:
            self.type_id_edit.setText(self.field_type_id)
            self.type_id_edit.setEnabled(False)
        else:
            self.type_id_edit.setPlaceholderText("例如: custom_amount")
        layout.addRow("类型标识:", self.type_id_edit)
        
        # 类型名称
        self.type_name_edit = QLineEdit()
        self.type_name_edit.setPlaceholderText("例如: 自定义金额")
        layout.addRow("类型名称:", self.type_name_edit)
        
        # 类型描述
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("详细描述该字段类型的用途和特点")
        layout.addRow("类型描述:", self.description_edit)
        
        # 基础规则类型
        self.base_rule_combo = QComboBox()
        self.base_rule_combo.addItems(["number", "string", "date", "code", "custom"])
        self.base_rule_combo.currentTextChanged.connect(self.on_base_rule_changed)
        layout.addRow("基础规则类型:", self.base_rule_combo)
        
        # 说明文本
        info_label = QLabel("""
        <b>基础规则类型说明：</b><br>
        • <b>number</b>: 数值类型，支持小数位、千位分隔符等<br>
        • <b>string</b>: 字符串类型，支持大小写、长度限制等<br>
        • <b>date</b>: 日期类型，支持格式转换<br>
        • <b>code</b>: 代码/ID类型，支持前导零、前后缀等<br>
        • <b>custom</b>: 完全自定义，需要编写Python代码
        """)
        info_label.setWordWrap(True)
        layout.addRow("", info_label)
        
        widget.setLayout(layout)
        return widget
    
    def create_format_config_tab(self) -> QWidget:
        """创建格式化配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 说明
        info_label = QLabel("配置该字段类型的默认格式化参数")
        layout.addWidget(info_label)
        
        # 配置表格
        self.format_config_table = QTableWidget()
        self.format_config_table.setColumnCount(3)
        self.format_config_table.setHorizontalHeaderLabels(["参数名", "参数值", "说明"])
        self.format_config_table.horizontalHeader().setStretchLastSection(True)
        
        # 右键菜单
        self.format_config_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.format_config_table.customContextMenuRequested.connect(self.show_config_context_menu)
        
        layout.addWidget(self.format_config_table)
        
        # 添加参数按钮
        button_layout = QHBoxLayout()
        
        self.add_param_btn = QPushButton("添加参数")
        self.add_param_btn.clicked.connect(self.add_format_parameter)
        button_layout.addWidget(self.add_param_btn)
        
        self.load_preset_btn = QPushButton("加载预设")
        self.load_preset_btn.clicked.connect(self.load_preset_config)
        button_layout.addWidget(self.load_preset_btn)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 预设配置（根据基础类型自动加载）
        self.load_default_format_config()
        
        widget.setLayout(layout)
        return widget
    
    def create_validation_tab(self) -> QWidget:
        """创建验证规则标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 说明
        info_label = QLabel("设置数据验证规则，确保输入数据的有效性")
        layout.addWidget(info_label)
        
        # 验证规则组
        validation_group = QGroupBox("验证规则")
        validation_layout = QFormLayout()
        
        # 是否必填
        self.required_check = QCheckBox("必填字段")
        validation_layout.addRow("", self.required_check)
        
        # 最小长度
        self.min_length_spin = QSpinBox()
        self.min_length_spin.setRange(0, 1000)
        self.min_length_spin.setSpecialValueText("不限制")
        validation_layout.addRow("最小长度:", self.min_length_spin)
        
        # 最大长度
        self.max_length_spin = QSpinBox()
        self.max_length_spin.setRange(0, 1000)
        self.max_length_spin.setSpecialValueText("不限制")
        validation_layout.addRow("最大长度:", self.max_length_spin)
        
        # 正则表达式
        self.pattern_edit = QLineEdit()
        self.pattern_edit.setPlaceholderText("例如: ^\\d{18}$ (18位数字)")
        validation_layout.addRow("正则表达式:", self.pattern_edit)
        
        # 自定义验证代码
        self.custom_validation_edit = QTextEdit()
        self.custom_validation_edit.setMaximumHeight(150)
        self.custom_validation_edit.setPlaceholderText(
            "# Python验证代码（返回True/False）\n"
            "# 可用变量: value, config\n"
            "return len(str(value)) == 18"
        )
        validation_layout.addRow("自定义验证:", self.custom_validation_edit)
        
        validation_group.setLayout(validation_layout)
        layout.addWidget(validation_group)
        
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def create_custom_code_tab(self) -> QWidget:
        """创建自定义代码标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 说明
        info_label = QLabel(
            "<b>自定义代码（仅当基础类型为'custom'时生效）</b><br>"
            "编写Python代码实现完全自定义的格式化和验证逻辑"
        )
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 格式化代码
        format_group = QGroupBox("格式化函数")
        format_layout = QVBoxLayout()
        
        self.format_code_edit = QTextEdit()
        self.format_code_edit.setFont(QFont("Consolas", 10))
        self.format_code_edit.setPlainText("""def format_function(value, config):
    \"\"\"
    格式化函数
    Args:
        value: 输入值
        config: 配置参数字典
    Returns:
        格式化后的值
    \"\"\"
    import pandas as pd
    
    if pd.isna(value):
        return ""
    
    # 在这里实现格式化逻辑
    result = str(value)
    
    return result""")
        
        format_layout.addWidget(self.format_code_edit)
        format_group.setLayout(format_layout)
        layout.addWidget(format_group)
        
        # 验证代码
        validate_group = QGroupBox("验证函数")
        validate_layout = QVBoxLayout()
        
        self.validate_code_edit = QTextEdit()
        self.validate_code_edit.setFont(QFont("Consolas", 10))
        self.validate_code_edit.setPlainText("""def validate_function(value, config):
    \"\"\"
    验证函数
    Args:
        value: 输入值
        config: 配置参数字典
    Returns:
        bool: 是否验证通过
    \"\"\"
    import pandas as pd
    
    if pd.isna(value):
        return True
    
    # 在这里实现验证逻辑
    
    return True""")
        
        validate_layout.addWidget(self.validate_code_edit)
        validate_group.setLayout(validate_layout)
        layout.addWidget(validate_group)
        
        widget.setLayout(layout)
        return widget
    
    def on_base_rule_changed(self, rule_type: str):
        """基础规则类型改变时的处理"""
        # 启用/禁用自定义代码标签页
        custom_tab_index = self.tab_widget.indexOf(self.custom_code_tab)
        self.tab_widget.setTabEnabled(custom_tab_index, rule_type == "custom")
        
        # 加载对应的默认配置
        self.load_default_format_config()
    
    def load_default_format_config(self):
        """根据基础类型加载默认格式化配置"""
        rule_type = self.base_rule_combo.currentText()
        
        # 清空现有配置
        self.format_config_table.setRowCount(0)
        
        # 根据类型加载默认参数
        default_configs = {
            "number": [
                ("decimal_places", "2", "小数位数"),
                ("thousands_separator", "True", "是否使用千位分隔符"),
                ("negative_format", "minus", "负数格式: minus/parentheses/red")
            ],
            "string": [
                ("case", "original", "大小写: original/upper/lower/title"),
                ("trim_spaces", "True", "是否去除首尾空格"),
                ("max_length", "", "最大长度（留空不限制）")
            ],
            "date": [
                ("format", "%Y-%m-%d", "日期格式字符串")
            ],
            "code": [
                ("min_length", "6", "最小长度"),
                ("padding_char", "0", "填充字符"),
                ("preserve_leading_zeros", "True", "保留前导零"),
                ("prefix", "", "前缀"),
                ("suffix", "", "后缀")
            ]
        }
        
        if rule_type in default_configs:
            for param_name, param_value, param_desc in default_configs[rule_type]:
                self.add_format_parameter(param_name, param_value, param_desc)
    
    def add_format_parameter(self, name: str = "", value: str = "", description: str = ""):
        """添加格式化参数"""
        row = self.format_config_table.rowCount()
        self.format_config_table.insertRow(row)
        
        self.format_config_table.setItem(row, 0, QTableWidgetItem(name))
        self.format_config_table.setItem(row, 1, QTableWidgetItem(value))
        self.format_config_table.setItem(row, 2, QTableWidgetItem(description))
    
    def show_config_context_menu(self, pos):
        """显示配置表格的右键菜单"""
        menu = QMenu(self)
        
        delete_action = QAction("删除", self)
        delete_action.triggered.connect(self.delete_config_row)
        menu.addAction(delete_action)
        
        menu.exec_(self.format_config_table.mapToGlobal(pos))
    
    def delete_config_row(self):
        """删除配置行"""
        current_row = self.format_config_table.currentRow()
        if current_row >= 0:
            self.format_config_table.removeRow(current_row)
    
    def load_preset_config(self):
        """加载预设配置"""
        # TODO: 实现预设配置选择对话框
        QMessageBox.information(self, "提示", "预设配置功能待实现")
    
    def load_field_type_data(self):
        """加载字段类型数据到界面"""
        if not self.current_field_type:
            return
        
        # 基本信息
        self.type_id_edit.setText(self.current_field_type.get("id", ""))
        self.type_name_edit.setText(self.current_field_type.get("name", ""))
        self.description_edit.setPlainText(self.current_field_type.get("description", ""))
        
        rule_type = self.current_field_type.get("rule_type", "string")
        index = self.base_rule_combo.findText(rule_type)
        if index >= 0:
            self.base_rule_combo.setCurrentIndex(index)
        
        # 格式化配置
        default_config = self.current_field_type.get("default_config", {})
        self.format_config_table.setRowCount(0)
        for key, value in default_config.items():
            self.add_format_parameter(key, str(value), "")
        
        # 验证规则
        validation_rules = self.current_field_type.get("validation_rules", {})
        self.required_check.setChecked(validation_rules.get("required", False))
        self.min_length_spin.setValue(validation_rules.get("min_length", 0))
        self.max_length_spin.setValue(validation_rules.get("max_length", 0))
        self.pattern_edit.setText(validation_rules.get("pattern", ""))
        self.custom_validation_edit.setPlainText(validation_rules.get("custom_code", ""))
        
        # 自定义代码（如果有）
        if rule_type == "custom":
            custom_config = self.current_field_type.get("custom_config", {})
            self.format_code_edit.setPlainText(custom_config.get("format_code", ""))
            self.validate_code_edit.setPlainText(custom_config.get("validate_code", ""))
    
    def get_field_type_data(self) -> Dict[str, Any]:
        """从界面获取字段类型数据"""
        # 基本信息
        type_id = self.type_id_edit.text().strip()
        if not type_id:
            raise ValueError("类型标识不能为空")
        
        type_name = self.type_name_edit.text().strip()
        if not type_name:
            raise ValueError("类型名称不能为空")
        
        # 格式化配置
        default_config = {}
        for row in range(self.format_config_table.rowCount()):
            param_name = self.format_config_table.item(row, 0).text()
            param_value = self.format_config_table.item(row, 1).text()
            
            if param_name:
                # 尝试转换类型
                if param_value.lower() in ["true", "false"]:
                    default_config[param_name] = param_value.lower() == "true"
                elif param_value.isdigit():
                    default_config[param_name] = int(param_value)
                else:
                    try:
                        default_config[param_name] = float(param_value)
                    except:
                        default_config[param_name] = param_value
        
        # 验证规则
        validation_rules = {
            "required": self.required_check.isChecked(),
            "min_length": self.min_length_spin.value() if self.min_length_spin.value() > 0 else None,
            "max_length": self.max_length_spin.value() if self.max_length_spin.value() > 0 else None,
            "pattern": self.pattern_edit.text().strip() or None,
            "custom_code": self.custom_validation_edit.toPlainText().strip() or None
        }
        
        # 移除None值
        validation_rules = {k: v for k, v in validation_rules.items() if v is not None}
        
        field_type_data = {
            "id": type_id,
            "name": type_name,
            "description": self.description_edit.toPlainText().strip(),
            "rule_type": self.base_rule_combo.currentText(),
            "default_config": default_config,
            "validation_rules": validation_rules
        }
        
        # 如果是自定义类型，添加代码
        if self.base_rule_combo.currentText() == "custom":
            field_type_data["custom_config"] = {
                "format_code": self.format_code_edit.toPlainText(),
                "validate_code": self.validate_code_edit.toPlainText()
            }
        
        return field_type_data
    
    def test_field_type(self):
        """测试字段类型"""
        try:
            field_type_data = self.get_field_type_data()
            
            # TODO: 实现测试对话框
            QMessageBox.information(self, "提示", "测试功能待实现")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"获取配置失败: {e}")
    
    def save_field_type(self):
        """保存字段类型"""
        try:
            field_type_data = self.get_field_type_data()
            
            if self.is_edit_mode:
                # 更新现有类型
                success = self.manager.update_field_type(
                    self.field_type_id,
                    field_type_data
                )
                
                if success:
                    self.field_type_updated.emit(self.field_type_id, field_type_data)
                    QMessageBox.information(self, "成功", "字段类型已更新")
                    self.accept()
                else:
                    QMessageBox.warning(self, "错误", "更新字段类型失败")
            else:
                # 创建新类型
                success = self.manager.create_field_type(
                    field_type_data["id"],
                    field_type_data["name"],
                    field_type_data["description"],
                    field_type_data["rule_type"],
                    field_type_data.get("default_config"),
                    field_type_data.get("validation_rules")
                )
                
                if success:
                    # 如果有自定义代码，保存到规则中
                    if field_type_data.get("custom_config"):
                        # TODO: 保存自定义代码
                        pass
                    
                    self.field_type_created.emit(field_type_data["id"], field_type_data)
                    QMessageBox.information(self, "成功", "字段类型已创建")
                    self.accept()
                else:
                    QMessageBox.warning(self, "错误", "创建字段类型失败，可能ID已存在")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {e}")