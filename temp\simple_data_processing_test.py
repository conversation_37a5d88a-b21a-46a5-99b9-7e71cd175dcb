#!/usr/bin/env python3
"""
简单的数据处理组件测试
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_data_processing_import():
    """测试数据处理组件导入"""
    try:
        print("开始测试数据处理组件导入...")
        
        # 测试导入
        from src.gui.unified_data_import_window import DataProcessingWidget
        print("✅ DataProcessingWidget 导入成功")
        
        # 测试创建实例
        from PyQt5.QtWidgets import QApplication
        app = QApplication([])
        
        widget = DataProcessingWidget()
        print("✅ DataProcessingWidget 实例创建成功")
        
        # 测试基本属性
        if hasattr(widget, 'processing_config'):
            print("✅ processing_config 属性存在")
            print(f"   默认配置: {widget.processing_config}")
        
        if hasattr(widget, 'get_processing_config'):
            config = widget.get_processing_config()
            print("✅ get_processing_config 方法可用")
            print(f"   当前配置: {config}")
        
        # 测试UI组件
        ui_components = [
            'remove_duplicates_cb',
            'missing_values_combo', 
            'trim_whitespace_cb',
            'format_numbers_cb',
            'format_dates_cb',
            'convert_types_cb',
            'data_validation_cb',
            'preview_processing_btn',
            'reset_config_btn',
            'save_config_btn',
            'load_config_btn'
        ]
        
        for component in ui_components:
            if hasattr(widget, component):
                print(f"✅ {component} 组件存在")
            else:
                print(f"❌ {component} 组件不存在")
        
        print("\n数据处理组件测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_processing_import()
