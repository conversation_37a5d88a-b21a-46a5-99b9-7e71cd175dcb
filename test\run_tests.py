#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试运行器 - 运行所有单元测试
"""

import unittest
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_all_tests():
    """运行所有测试"""
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试模块
    test_modules = [
        'test.test_cache_manager',
        'test.test_field_mapping',
        'test.test_configuration'
    ]
    
    for module_name in test_modules:
        try:
            module = __import__(module_name, fromlist=[''])
            suite.addTests(loader.loadTestsFromModule(module))
            print(f"已加载测试模块: {module_name}")
        except ImportError as e:
            print(f"无法加载测试模块 {module_name}: {e}")
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出统计
    print("\n" + "="*60)
    print("测试统计")
    print("="*60)
    print(f"运行测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    # 计算覆盖率（如果安装了coverage）
    try:
        import coverage
        print("\n提示：使用以下命令运行带覆盖率的测试：")
        print("coverage run -m unittest discover test/")
        print("coverage report")
        print("coverage html")
    except ImportError:
        print("\n提示：安装coverage以获取测试覆盖率：")
        print("pip install coverage")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
