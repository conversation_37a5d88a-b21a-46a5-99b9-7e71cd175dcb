# 字段类型配置选项卡设计方案

## 一、背景分析

### 1.1 项目现状
- **框架**：PyQt5 + SQLite数据库
- **模块化设计**：核心功能分离在 `src/modules` 和 `src/gui` 目录
- **数据导入窗口**：`UnifiedDataImportWindow` 已有3个配置选项卡
  - 字段映射
  - 数据处理  
  - 预览验证

### 1.2 现有字段类型处理机制

#### 核心组件
1. **FieldTypeManager** (`src/modules/data_import/field_type_manager.py`)
   - 管理自定义字段类型的CRUD操作
   - 数据持久化：`state/field_types/custom_field_types.json`
   - 支持格式化规则和验证规则配置
   - 提供导入/导出功能

2. **FormattingEngine** (`src/modules/data_import/formatting_engine.py`)
   - 管理10种内置字段类型
     - 工资金额 (salary_float)
     - 工号 (employee_id_string)
     - 姓名 (name_string)
     - 日期 (date_string)
     - 身份证号 (id_number_string)
     - 代码 (code_string)
     - 浮点数 (float)
     - 整数 (integer)
     - 文本字符串 (text_string)
     - 人员类别代码 (personnel_category_code)
   - 支持5种基础规则类型：number、string、date、code、custom
   - 提供统一的格式化和验证接口

3. **现有对话框**
   - `FieldTypeManagerDialog`：字段类型管理主界面
   - `FieldTypeEditorDialog`：字段类型编辑器（含4个选项卡）
     - 基本信息
     - 格式化配置
     - 验证规则
     - 自定义代码

## 二、设计方案

### 2.1 集成方式
在 `UnifiedDataImportWindow` 的配置详情面板中新增第4个选项卡

```python
# 位置：unified_data_import_window.py 的 _create_config_details_panel 方法
self.field_types_tab = FieldTypeConfigWidget()
self.config_tab_widget.addTab(self.field_types_tab, "📝 字段类型")
```

### 2.2 新组件设计：FieldTypeConfigWidget

#### 界面布局
```
┌──────────────────────────────────────────────────────┐
│  工具栏：[➕ 新建] [✏️ 编辑] [🗑️ 删除] [📥 导入] [📤 导出]  │
├──────────────────────────────────────────────────────┤
│  ┌────────────┬──────────────────────────────────┐  │
│  │ 类型列表    │        类型详情展示                │  │
│  │            │  ┌───────────────────────────┐  │  │
│  │ ▶ 内置类型  │  │ 基本信息                   │  │  │
│  │   工资金额   │  │ 类型ID: salary_float      │  │  │
│  │   工号      │  │ 名称: 工资金额              │  │  │
│  │   姓名      │  │ 描述: 金额数值，保留小数      │  │  │
│  │   日期      │  ├───────────────────────────┤  │  │
│  │            │  │ 格式化配置                  │  │  │
│  │ ▶ 自定义类型 │  │ 小数位数: 2                │  │  │
│  │   自定义1    │  │ 千位分隔符: ✓              │  │  │
│  │   自定义2    │  │ 负数格式: minus            │  │  │
│  │            │  ├───────────────────────────┤  │  │
│  │            │  │ 验证规则                   │  │  │
│  │            │  │ 最小值: 0                  │  │  │
│  │            │  │ 最大值: 999999999          │  │  │
│  │            │  ├───────────────────────────┤  │  │
│  │            │  │ 预览示例                   │  │  │
│  │            │  │ 输入: 12345.678            │  │  │
│  │            │  │ 输出: 12,345.68            │  │  │
│  └────────────┴──┴───────────────────────────┘  │  │
└──────────────────────────────────────────────────────┘
```

### 2.3 功能设计

#### 2.3.1 左侧类型列表
- **树形结构**：分组显示内置类型和自定义类型
- **搜索过滤**：支持按名称或ID快速查找
- **使用统计**：显示每个类型的使用次数
- **状态标识**：区分内置（只读）和自定义（可编辑）

#### 2.3.2 右侧详情面板
- **只读模式**：默认显示选中类型的详细信息
- **编辑模式**：双击或点击编辑按钮后进入（仅自定义类型）
- **实时预览**：输入测试值，显示格式化效果
- **配置分组**：基本信息、格式化配置、验证规则分组显示

#### 2.3.3 工具栏功能
| 功能 | 说明 | 权限 |
|------|------|------|
| 新建 | 打开 FieldTypeEditorDialog 创建新类型 | 所有用户 |
| 编辑 | 编辑选中的字段类型 | 仅自定义类型 |
| 删除 | 删除自定义类型 | 需检查依赖 |
| 导入 | 从JSON文件导入字段类型配置 | 所有用户 |
| 导出 | 导出当前配置到JSON文件 | 所有用户 |

## 三、实施细节

### 3.1 文件结构
```
src/gui/widgets/
└── field_type_config_widget.py  # 新建组件文件
```

### 3.2 核心类设计

```python
class FieldTypeConfigWidget(QWidget):
    """字段类型配置组件"""
    
    # 信号定义
    field_type_changed = pyqtSignal(str)  # 字段类型变更信号
    field_type_deleted = pyqtSignal(str)  # 字段类型删除信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.field_type_manager = FieldTypeManager()
        self.formatting_engine = get_formatting_engine()
        self._init_ui()
        self._connect_signals()
        self._load_field_types()
```

### 3.3 数据联动机制

#### 3.3.1 与字段映射选项卡联动
```python
# 字段类型变更时刷新映射配置中的类型选择
self.field_types_tab.field_type_changed.connect(
    self.mapping_tab.refresh_field_types
)
```

#### 3.3.2 与数据处理选项卡联动
```python
# 字段类型的验证规则变更时更新数据处理配置
self.field_types_tab.field_type_changed.connect(
    self.processing_tab.update_validation_rules
)
```

#### 3.3.3 与预览验证选项卡联动
```python
# 格式化规则变更时刷新预览效果
self.field_types_tab.field_type_changed.connect(
    self.preview_tab.refresh_preview
)
```

### 3.4 性能优化策略

1. **延迟加载**
   - 初始只加载类型列表，不加载详细配置
   - 选中类型时才加载完整配置信息

2. **缓存机制**
   - 缓存常用字段类型配置
   - 使用 LRU 缓存策略，缓存最近使用的10个类型

3. **异步保存**
   - 配置变更后延迟500ms自动保存
   - 批量操作时合并保存请求

### 3.5 安全考虑

1. **权限控制**
   - 内置类型设为只读，防止误修改
   - 删除前检查字段类型是否被使用

2. **自定义代码执行**
   - 使用受限的执行环境
   - 禁止危险操作（文件系统访问、网络请求等）
   - 设置执行超时限制（默认1秒）

3. **数据验证**
   - 导入配置前进行格式验证
   - 检查版本兼容性
   - 备份现有配置

## 四、实施步骤

### 第一阶段：基础功能实现（2天）
1. 创建 `FieldTypeConfigWidget` 组件
2. 实现类型列表展示
3. 实现详情面板显示
4. 集成到统一数据导入窗口

### 第二阶段：编辑功能实现（2天）
1. 实现新建/编辑/删除功能
2. 集成现有的编辑对话框
3. 实现配置保存和加载

### 第三阶段：高级功能实现（1天）
1. 实现导入/导出功能
2. 添加搜索过滤功能
3. 实现使用统计功能

### 第四阶段：联动和优化（1天）
1. 实现与其他选项卡的数据联动
2. 添加缓存机制
3. 性能优化和测试

## 五、预期效果

### 5.1 用户体验提升
- **一站式管理**：在数据导入配置界面直接管理字段类型
- **实时预览**：配置时即可看到格式化效果
- **批量操作**：支持导入导出，方便配置迁移

### 5.2 系统架构改进
- **模块解耦**：字段类型管理独立成组件
- **复用性强**：其他模块可直接使用字段类型配置
- **可扩展性**：便于后续添加新的字段类型

### 5.3 维护性提升
- **配置集中**：所有字段类型配置在一处管理
- **版本控制**：支持配置导出，便于版本管理
- **错误减少**：统一的格式化规则减少不一致性

## 六、风险评估与应对

| 风险项 | 可能影响 | 应对措施 |
|--------|----------|----------|
| 现有数据兼容性 | 旧配置无法使用 | 提供配置迁移工具 |
| 性能问题 | 大量字段类型时响应慢 | 实施缓存和延迟加载 |
| 自定义代码安全 | 恶意代码执行 | 沙箱环境+权限限制 |
| 用户学习成本 | 新功能使用困难 | 提供使用说明和示例 |

## 七、后续优化方向

1. **智能推荐**：基于字段名称智能推荐合适的字段类型
2. **模板市场**：提供常用字段类型模板下载
3. **批量应用**：支持将字段类型批量应用到多个字段
4. **历史版本**：支持字段类型配置的版本管理和回滚
5. **可视化配置**：提供更直观的图形化配置界面

## 八、总结

本方案基于现有项目架构，充分利用已有的 `FieldTypeManager` 和 `FormattingEngine` 组件，通过在统一数据导入窗口中新增字段类型配置选项卡，实现了字段类型的集中管理和配置。方案具有以下优势：

1. **无缝集成**：与现有功能完美融合
2. **复用性高**：最大程度复用现有代码
3. **用户友好**：操作流程自然，学习成本低
4. **可扩展性强**：便于后续功能增强

建议按照四个阶段逐步实施，确保每个阶段的功能稳定可用，降低实施风险。
