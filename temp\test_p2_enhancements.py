#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试P2级优化改进效果
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from PyQt5.QtWidgets import QApplication, QTableWidget
from PyQt5.QtCore import Qt

print("=" * 60)
print("P2级优化改进验证测试")
print("=" * 60)

print("\n测试1: 动态格式化规则管理")
print("-" * 40)
try:
    from src.gui.change_data_config_dialog import ChangeDataConfigDialog, AddRuleDialog
    
    app = QApplication([])
    
    # 创建测试数据
    test_data = pd.DataFrame({
        "工号": ["001"],
        "姓名": ["测试"],
        "工资": [5000.00]
    })
    
    dialog = ChangeDataConfigDialog(excel_data=test_data)
    
    # 检查规则表格是否存在
    if hasattr(dialog, 'rules_table'):
        print("[OK] 格式化规则表格已创建")
        print(f"     列数: {dialog.rules_table.columnCount()}")
        print(f"     初始规则数: {dialog.rules_table.rowCount()}")
        
        # 检查表头
        headers = []
        for i in range(dialog.rules_table.columnCount()):
            header = dialog.rules_table.horizontalHeaderItem(i)
            if header:
                headers.append(header.text())
        print(f"     表头: {headers}")
        
        if dialog.rules_table.rowCount() > 0:
            print("[OK] 已加载默认格式化规则")
        else:
            print("[WARN] 没有默认规则")
    else:
        print("[FAIL] 未找到规则表格")
    
    # 测试添加规则对话框
    add_dialog = AddRuleDialog()
    if add_dialog:
        print("[OK] 添加规则对话框创建成功")
        
        # 检查对话框字段
        if hasattr(add_dialog, 'field_type_combo'):
            print(f"     字段类型选项数: {add_dialog.field_type_combo.count()}")
        if hasattr(add_dialog, 'rule_name_edit'):
            print("[OK] 规则名称输入框存在")
        if hasattr(add_dialog, 'rule_value_edit'):
            print("[OK] 规则值输入框存在")
    
except Exception as e:
    print(f"[FAIL] 动态规则管理测试失败: {e}")

print("\n测试2: 规则操作功能")
print("-" * 40)
try:
    # 测试规则操作按钮
    if hasattr(dialog, 'add_rule_btn'):
        print("[OK] 添加规则按钮存在")
    else:
        print("[FAIL] 未找到添加规则按钮")
    
    if hasattr(dialog, 'apply_template_btn'):
        print("[OK] 应用模板按钮存在")
    else:
        print("[FAIL] 未找到应用模板按钮")
    
    if hasattr(dialog, 'clear_rules_btn'):
        print("[OK] 清空规则按钮存在")
    else:
        print("[FAIL] 未找到清空规则按钮")
    
    # 测试规则管理方法
    if hasattr(dialog, 'add_rule_to_table'):
        # 测试添加规则
        initial_count = dialog.rules_table.rowCount()
        dialog.add_rule_to_table("test_type", "test_rule", "test_value")
        new_count = dialog.rules_table.rowCount()
        
        if new_count > initial_count:
            print(f"[OK] 成功添加规则，当前规则数: {new_count}")
        else:
            print("[FAIL] 添加规则失败")
    
    if hasattr(dialog, 'get_formatting_rules'):
        rules = dialog.get_formatting_rules()
        print(f"[OK] 可以获取格式化规则，规则类型数: {len(rules)}")
        
except Exception as e:
    print(f"[FAIL] 规则操作功能测试失败: {e}")

print("\n测试3: 配置向导功能")
print("-" * 40)
try:
    from src.gui.config_wizard_dialog import ConfigWizardDialog
    
    wizard = ConfigWizardDialog()
    print("[OK] 配置向导对话框创建成功")
    
    # 检查向导页面
    page_count = wizard.pageIds()
    print(f"     向导页面数: {len(page_count)}")
    
    # 检查各个页面
    expected_titles = [
        "欢迎使用异动表配置向导",
        "选择数据类型",
        "选择需要的字段",
        "设置格式化规则",
        "配置总结"
    ]
    
    for i, page_id in enumerate(page_count):
        page = wizard.page(page_id)
        if page:
            title = page.title()
            if i < len(expected_titles):
                if title == expected_titles[i]:
                    print(f"[OK] 页面{i+1}: {title}")
                else:
                    print(f"[WARN] 页面{i+1}标题不匹配: {title}")
    
    # 检查向导按钮
    if hasattr(dialog, 'wizard_btn'):
        print("[OK] 配置向导按钮已添加到主对话框")
    else:
        print("[FAIL] 主对话框中未找到向导按钮")
    
except ImportError as e:
    print(f"[FAIL] 无法导入配置向导: {e}")
except Exception as e:
    print(f"[FAIL] 配置向导测试失败: {e}")

print("\n测试4: 规则模板功能")
print("-" * 40)
try:
    if hasattr(dialog, 'apply_rule_template'):
        print("[OK] 规则模板应用功能存在")
        
        # 测试模板应用
        if hasattr(dialog, 'apply_rule_template_by_name'):
            # 清空规则
            dialog.rules_table.setRowCount(0)
            
            # 应用标准模板
            dialog.apply_rule_template_by_name("standard")
            standard_count = dialog.rules_table.rowCount()
            
            if standard_count > 0:
                print(f"[OK] 标准模板应用成功，规则数: {standard_count}")
            
            # 应用精确计算模板
            dialog.rules_table.setRowCount(0)
            dialog.apply_rule_template_by_name("precise")
            precise_count = dialog.rules_table.rowCount()
            
            if precise_count > 0:
                print(f"[OK] 精确计算模板应用成功，规则数: {precise_count}")
            
            # 应用国际格式模板
            dialog.rules_table.setRowCount(0)
            dialog.apply_rule_template_by_name("international")
            intl_count = dialog.rules_table.rowCount()
            
            if intl_count > 0:
                print(f"[OK] 国际格式模板应用成功，规则数: {intl_count}")
        
    else:
        print("[FAIL] 未找到规则模板功能")
        
except Exception as e:
    print(f"[FAIL] 规则模板测试失败: {e}")

print("\n" + "=" * 60)
print("P2级优化改进验证完成！")
print("=" * 60)

print("\n改进总结:")
print("1. ✅ 格式化规则改为动态管理，支持增删改")
print("2. ✅ 提供了3种预设规则模板")
print("3. ✅ 添加了配置向导，简化配置流程")
print("4. ✅ 规则编辑更加灵活，适应不同需求")