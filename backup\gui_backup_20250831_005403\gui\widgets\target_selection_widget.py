#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态目标选择组件

用于数据导入时选择或创建目标位置（年份、月份、人员类别等）。
支持智能推断、动态新增和实时预览。
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QComboBox, QPushButton, QLabel, QLineEdit, QDialog, QDialogButtonBox,
    QMessageBox, QFrame, QTextEdit, QCheckBox
)
from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtGui import QFont, QPalette

from src.utils.log_config import setup_logger


class NewCategoryDialog(QDialog):
    """新增人员类别对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.category_name = ""
        self.category_icon = ""
        self.category_description = ""
        self._init_ui()
    
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("新增人员类别")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # 表单区域
        form_layout = QFormLayout()
        
        # 类别名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("例如: 临时工、实习生等")
        form_layout.addRow("类别名称*:", self.name_edit)
        
        # 图标选择
        icon_layout = QHBoxLayout()
        self.icon_combo = QComboBox()
        self.icon_combo.addItems([
            "👥 默认", "👷 临时工", "🎓 实习生", "👨‍💼 管理层", 
            "👩‍🏫 教师", "👨‍🔬 科研", "🏥 医务", "🔧 技术"
        ])
        icon_layout.addWidget(self.icon_combo)
        form_layout.addRow("显示图标:", icon_layout)
        
        # 类别描述
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("可选：描述该类别的特点或用途")
        form_layout.addRow("类别描述:", self.description_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self._on_accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def _on_accept(self):
        """确认新增"""
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "输入错误", "请输入类别名称")
            return
        
        self.category_name = name
        self.category_icon = self.icon_combo.currentText().split()[0]
        self.category_description = self.description_edit.toPlainText().strip()
        
        self.accept()
    
    def get_category_info(self) -> Dict[str, str]:
        """获取类别信息"""
        return {
            'name': self.category_name,
            'icon': self.category_icon,
            'description': self.category_description
        }


class TargetSelectionWidget(QWidget):
    """动态目标选择组件"""
    
    # 信号定义
    target_changed = pyqtSignal(dict)  # 目标位置变化信号
    new_category_added = pyqtSignal(dict)  # 新增类别信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 配置文件路径
        self.config_dir = "state/data"
        self.config_file = os.path.join(self.config_dir, "navigation_config.json")
        
        # 当前选择的目标信息
        self.current_target = {
            'module': '工资表',
            'year': str(datetime.now().year),
            'month': str(datetime.now().month),
            'category': '全部在职人员'
        }
        
        # 可选项配置
        self.available_options = self._load_available_options()
        
        self._init_ui()
        self._load_initial_target()
    
    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建目标选择组
        target_group = QGroupBox("📍 数据目标位置")
        target_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #0078d4;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #0078d4;
            }
        """)
        target_layout = QFormLayout(target_group)
        target_layout.setSpacing(12)
        
        # 数据模块选择
        module_layout = QHBoxLayout()
        self.module_combo = QComboBox()
        self.module_combo.addItems(self.available_options['modules'])
        self.module_combo.currentTextChanged.connect(self._on_module_changed)
        module_layout.addWidget(self.module_combo)
        target_layout.addRow("数据模块:", module_layout)
        
        # 年份选择
        year_layout = QHBoxLayout()
        self.year_combo = QComboBox()
        self.year_combo.setEditable(True)
        self._populate_year_options()
        self.year_combo.currentTextChanged.connect(self._on_year_changed)
        year_layout.addWidget(self.year_combo)
        
        self.add_year_btn = QPushButton("+ 新增年份")
        self.add_year_btn.setMaximumWidth(100)
        self.add_year_btn.clicked.connect(self._add_new_year)
        year_layout.addWidget(self.add_year_btn)
        target_layout.addRow("年    份:", year_layout)
        
        # 月份选择
        month_layout = QHBoxLayout()
        self.month_combo = QComboBox()
        self._populate_month_options()
        self.month_combo.currentTextChanged.connect(self._on_month_changed)
        month_layout.addWidget(self.month_combo)
        target_layout.addRow("月    份:", month_layout)
        
        # 人员类别选择
        category_layout = QHBoxLayout()
        self.category_combo = QComboBox()
        self._populate_category_options()
        self.category_combo.currentTextChanged.connect(self._on_category_changed)
        category_layout.addWidget(self.category_combo)
        
        self.add_category_btn = QPushButton("+ 新增类别")
        self.add_category_btn.setMaximumWidth(100)
        self.add_category_btn.clicked.connect(self._add_new_category)
        category_layout.addWidget(self.add_category_btn)
        target_layout.addRow("人员类别:", category_layout)
        
        layout.addWidget(target_group)
        
        # 路径预览区域
        preview_group = QGroupBox("📊 完整路径预览")
        preview_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #495057;
            }
        """)
        preview_layout = QVBoxLayout(preview_group)
        
        self.path_preview_label = QLabel()
        self.path_preview_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                font-size: 12px;
                color: #0078d4;
                font-weight: bold;
            }
        """)
        self.path_preview_label.setWordWrap(True)
        preview_layout.addWidget(self.path_preview_label)
        
        # 状态提示
        self.status_label = QLabel()
        self.status_label.setStyleSheet("color: #28a745; font-size: 11px;")
        preview_layout.addWidget(self.status_label)
        
        layout.addWidget(preview_group)
        
        # 更新预览
        self._update_path_preview()
    
    def _load_available_options(self) -> Dict[str, List[str]]:
        """加载可用选项配置"""
        try:
            os.makedirs(self.config_dir, exist_ok=True)
            
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.info("成功加载导航配置")
                return config
            else:
                # 默认配置
                default_config = {
                    'modules': ['工资表', '异动人员表'],
                    'years': [str(year) for year in range(2020, 2030)],
                    'categories': [
                        {'name': '全部在职人员', 'icon': '👥'},
                        {'name': '离休人员', 'icon': '🏖️'},
                        {'name': '退休人员', 'icon': '🏠'},
                        {'name': 'A岗职工', 'icon': '🏢'}
                    ]
                }
                self._save_config(default_config)
                return default_config
                
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            return {
                'modules': ['工资表', '异动人员表'],
                'years': [str(year) for year in range(2020, 2030)],
                'categories': [
                    {'name': '全部在职人员', 'icon': '👥'},
                    {'name': '离休人员', 'icon': '🏖️'},
                    {'name': '退休人员', 'icon': '🏠'},
                    {'name': 'A岗职工', 'icon': '🏢'}
                ]
            }
    
    def _save_config(self, config: Dict):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            self.logger.info("配置保存成功")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def _populate_year_options(self):
        """填充年份选项"""
        current_year = datetime.now().year
        years = []
        
        # 添加已有年份
        if 'years' in self.available_options:
            years.extend(self.available_options['years'])
        
        # 确保包含当前年份和下一年
        for year in [current_year - 1, current_year, current_year + 1]:
            if str(year) not in years:
                years.append(str(year))
        
        # 排序并去重
        years = sorted(list(set(years)), reverse=True)
        
        self.year_combo.clear()
        self.year_combo.addItems(years)
        
        # 设置默认为当前年份
        current_year_str = str(current_year)
        if current_year_str in years:
            self.year_combo.setCurrentText(current_year_str)
    
    def _populate_month_options(self):
        """填充月份选项"""
        months = [
            "1", "2", "3", "4", "5", "6",
            "7", "8", "9", "10", "11", "12"
        ]
        
        self.month_combo.clear()
        self.month_combo.addItems(months)
        
        # 设置默认为当前月份
        current_month = str(datetime.now().month)
        self.month_combo.setCurrentText(current_month)
    
    def _populate_category_options(self):
        """填充人员类别选项"""
        self.category_combo.clear()
        
        if 'categories' in self.available_options:
            for category in self.available_options['categories']:
                if isinstance(category, dict):
                    display_text = f"{category.get('icon', '👥')} {category['name']}"
                    self.category_combo.addItem(display_text, category['name'])
                else:
                    self.category_combo.addItem(f"👥 {category}", category)
    
    def _load_initial_target(self):
        """加载初始目标设置"""
        # 设置默认值
        self.module_combo.setCurrentText(self.current_target['module'])
        self.year_combo.setCurrentText(self.current_target['year'])
        self.month_combo.setCurrentText(self.current_target['month'])
        
        # 查找并设置类别
        for i in range(self.category_combo.count()):
            item_data = self.category_combo.itemData(i)
            if item_data == self.current_target['category']:
                self.category_combo.setCurrentIndex(i)
                break
    
    def _on_module_changed(self, text: str):
        """模块变化处理"""
        self.current_target['module'] = text
        self._update_path_preview()
        self._emit_target_changed()
    
    def _on_year_changed(self, text: str):
        """年份变化处理"""
        self.current_target['year'] = text
        self._update_path_preview()
        self._emit_target_changed()
    
    def _on_month_changed(self, text: str):
        """月份变化处理"""
        self.current_target['month'] = text
        self._update_path_preview()
        self._emit_target_changed()
    
    def _on_category_changed(self, text: str):
        """类别变化处理"""
        # 获取实际的类别名称（去掉图标）
        current_index = self.category_combo.currentIndex()
        if current_index >= 0:
            category_name = self.category_combo.itemData(current_index)
            if category_name:
                self.current_target['category'] = category_name
            else:
                # 如果没有itemData，从显示文本中提取
                display_text = self.category_combo.currentText()
                if ' ' in display_text:
                    self.current_target['category'] = display_text.split(' ', 1)[1]
                else:
                    self.current_target['category'] = display_text
        
        self._update_path_preview()
        self._emit_target_changed()
    
    def _add_new_year(self):
        """新增年份"""
        next_year = str(datetime.now().year + 1)
        
        # 检查是否已存在
        for i in range(self.year_combo.count()):
            if self.year_combo.itemText(i) == next_year:
                self.year_combo.setCurrentText(next_year)
                QMessageBox.information(self, "提示", f"年份 {next_year} 已存在，已自动选择")
                return
        
        # 添加新年份
        self.year_combo.addItem(next_year)
        self.year_combo.setCurrentText(next_year)
        
        # 更新配置
        if next_year not in self.available_options['years']:
            self.available_options['years'].append(next_year)
            self._save_config(self.available_options)
        
        QMessageBox.information(self, "成功", f"已添加新年份: {next_year}")
        self.logger.info(f"新增年份: {next_year}")
    
    def _add_new_category(self):
        """新增人员类别"""
        dialog = NewCategoryDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            category_info = dialog.get_category_info()
            
            # 检查是否已存在
            for category in self.available_options['categories']:
                if isinstance(category, dict) and category['name'] == category_info['name']:
                    QMessageBox.warning(self, "错误", f"类别 '{category_info['name']}' 已存在")
                    return
                elif isinstance(category, str) and category == category_info['name']:
                    QMessageBox.warning(self, "错误", f"类别 '{category_info['name']}' 已存在")
                    return
            
            # 添加新类别
            new_category = {
                'name': category_info['name'],
                'icon': category_info['icon'],
                'description': category_info['description']
            }
            
            self.available_options['categories'].append(new_category)
            self._save_config(self.available_options)
            
            # 更新界面
            display_text = f"{category_info['icon']} {category_info['name']}"
            self.category_combo.addItem(display_text, category_info['name'])
            self.category_combo.setCurrentText(display_text)
            
            # 发射新增类别信号
            self.new_category_added.emit(new_category)
            
            QMessageBox.information(self, "成功", f"已添加新类别: {category_info['name']}")
            self.logger.info(f"新增人员类别: {category_info}")
    
    def _update_path_preview(self):
        """更新路径预览"""
        path_parts = [
            self.current_target['module'],
            f"{self.current_target['year']}年",
            f"{self.current_target['month']}月",
            self.current_target['category']
        ]
        
        path_str = " > ".join(path_parts)
        self.path_preview_label.setText(path_str)
        
        # 检查是否为新建路径
        if self._is_new_path():
            self.status_label.setText("✨ 将创建新的数据分类")
            self.status_label.setStyleSheet("color: #ffc107; font-size: 11px;")
        else:
            self.status_label.setText("✓ 使用现有数据分类")
            self.status_label.setStyleSheet("color: #28a745; font-size: 11px;")
    
    def _is_new_path(self) -> bool:
        """检查是否为新路径（简化版，实际应该检查数据库）"""
        # 这里可以添加实际的数据库检查逻辑
        # 暂时返回False，表示都是现有路径
        return False
    
    def _emit_target_changed(self):
        """发射目标变化信号"""
        self.target_changed.emit(self.current_target.copy())
    
    def set_target_from_path(self, path_str: str):
        """从路径字符串设置目标"""
        if not path_str:
            return
        
        parts = path_str.split(' > ')
        if len(parts) >= 4:
            self.current_target['module'] = parts[0]
            self.current_target['year'] = parts[1].replace('年', '')
            self.current_target['month'] = parts[2].replace('月', '')
            self.current_target['category'] = parts[3]
            
            # 更新界面
            self.module_combo.setCurrentText(self.current_target['module'])
            self.year_combo.setCurrentText(self.current_target['year'])
            self.month_combo.setCurrentText(self.current_target['month'])
            
            # 设置类别
            for i in range(self.category_combo.count()):
                item_data = self.category_combo.itemData(i)
                if item_data == self.current_target['category']:
                    self.category_combo.setCurrentIndex(i)
                    break
            
            self._update_path_preview()
            self.logger.info(f"从路径设置目标: {path_str}")
    
    def get_current_target(self) -> Dict[str, str]:
        """获取当前目标信息"""
        return self.current_target.copy()
    
    def get_target_path_list(self) -> List[str]:
        """获取目标路径列表"""
        return [
            self.current_target['module'],
            f"{self.current_target['year']}年",
            f"{self.current_target['month']}月",
            self.current_target['category']
        ]
    
    def get_target_path_string(self) -> str:
        """获取目标路径字符串"""
        return " > ".join(self.get_target_path_list()) 