"""
统一缓存管理器 - P0级优化
整合所有分散的缓存机制
"""

import time
import hashlib
import json
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from collections import OrderedDict
from loguru import logger
import pandas as pd


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    timestamp: float
    ttl: float
    size: int
    hit_count: int = 0
    last_accessed: float = None
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl <= 0:  # 永不过期
            return False
        return time.time() - self.timestamp > self.ttl
    
    def access(self):
        """访问记录"""
        self.hit_count += 1
        self.last_accessed = time.time()


class UnifiedCacheManager:
    """
    统一缓存管理器
    
    整合功能：
    1. 排序结果缓存（原sort_cache_manager）
    2. 分页数据缓存（原pagination_cache_manager）
    3. 查询结果缓存
    4. 字段映射缓存
    5. 格式化结果缓存
    
    特性：
    - LRU淘汰策略
    - TTL过期机制
    - 内存限制
    - 命中率统计
    - 分区存储
    """
    
    _instance = None
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化管理器"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.logger = logger
        
        # 缓存分区
        self._caches: Dict[str, OrderedDict[str, CacheEntry]] = {
            'sort': OrderedDict(),      # 排序结果
            'pagination': OrderedDict(), # 分页数据
            'query': OrderedDict(),      # 查询结果
            'mapping': OrderedDict(),    # 字段映射
            'format': OrderedDict(),     # 格式化结果
            'general': OrderedDict()     # 通用缓存
        }
        
        # 配置
        self._max_memory = 100 * 1024 * 1024  # 100MB
        self._current_memory = 0
        self._default_ttl = 300  # 默认5分钟
        
        # 分区配置
        self._partition_config = {
            'sort': {'max_size': 20 * 1024 * 1024, 'ttl': 600},
            'pagination': {'max_size': 30 * 1024 * 1024, 'ttl': 300},
            'query': {'max_size': 20 * 1024 * 1024, 'ttl': 300},
            'mapping': {'max_size': 5 * 1024 * 1024, 'ttl': 3600},
            'format': {'max_size': 10 * 1024 * 1024, 'ttl': 1800},
            'general': {'max_size': 15 * 1024 * 1024, 'ttl': 300}
        }
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0
        }
        
        self.logger.info("UnifiedCacheManager 初始化完成")
    
    def get(self, key: str, partition: str = 'general') -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            partition: 缓存分区
        
        Returns:
            缓存值，如果不存在或过期返回None
        """
        if partition not in self._caches:
            return None
        
        cache = self._caches[partition]
        
        if key in cache:
            entry = cache[key]
            
            # 检查过期
            if entry.is_expired():
                self._remove_entry(partition, key)
                self._stats['expired'] += 1
                self._stats['misses'] += 1
                return None
            
            # 更新访问信息
            entry.access()
            
            # LRU: 移到末尾
            cache.move_to_end(key)
            
            self._stats['hits'] += 1
            self.logger.debug(f"缓存命中: {partition}/{key}")
            return entry.value
        
        self._stats['misses'] += 1
        return None
    
    def set(self, key: str, value: Any, partition: str = 'general',
            ttl: Optional[float] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            partition: 缓存分区
            ttl: 过期时间（秒）
        
        Returns:
            是否设置成功
        """
        if partition not in self._caches:
            return False
        
        # 估算大小
        size = self._estimate_size(value)
        
        # 检查分区大小限制
        partition_max = self._partition_config[partition]['max_size']
        partition_current = self._get_partition_size(partition)
        
        # 如果超过分区限制，进行淘汰
        while partition_current + size > partition_max and len(self._caches[partition]) > 0:
            self._evict_lru(partition)
            partition_current = self._get_partition_size(partition)
        
        # 检查总内存限制
        while self._current_memory + size > self._max_memory:
            self._evict_oldest_global()
        
        # 如果已存在，先删除旧的
        if key in self._caches[partition]:
            self._remove_entry(partition, key)
        
        # 创建新条目
        if ttl is None:
            ttl = self._partition_config[partition]['ttl']
        
        entry = CacheEntry(
            key=key,
            value=value,
            timestamp=time.time(),
            ttl=ttl,
            size=size
        )
        
        # 添加到缓存
        self._caches[partition][key] = entry
        self._current_memory += size
        
        self.logger.debug(f"缓存设置: {partition}/{key}, 大小={size}")
        return True
    
    def get_sorted_data(self, table_name: str, data: pd.DataFrame,
                       sort_columns: List[Dict[str, Any]]) -> Tuple[pd.DataFrame, bool]:
        """
        获取排序数据（带缓存）
        
        Args:
            table_name: 表名
            data: 原始数据
            sort_columns: 排序列配置
        
        Returns:
            (排序后的数据, 是否从缓存获取)
        """
        # 生成缓存键
        cache_key = self._generate_sort_key(table_name, sort_columns, len(data))
        
        # 尝试从缓存获取
        cached = self.get(cache_key, 'sort')
        if cached is not None:
            return cached, True
        
        # 执行排序
        sorted_data = self._perform_sort(data, sort_columns)
        
        # 缓存结果
        self.set(cache_key, sorted_data, 'sort')
        
        return sorted_data, False
    
    def get_page_data(self, table_name: str, page: int, page_size: int,
                     sort_columns: Optional[List] = None) -> Optional[Any]:
        """
        获取分页数据（带缓存）
        
        Args:
            table_name: 表名
            page: 页码
            page_size: 页大小
            sort_columns: 排序配置
        
        Returns:
            缓存的分页数据
        """
        cache_key = self._generate_page_key(table_name, page, page_size, sort_columns)
        return self.get(cache_key, 'pagination')
    
    def set_page_data(self, table_name: str, page: int, page_size: int,
                     data: Any, sort_columns: Optional[List] = None) -> bool:
        """
        缓存分页数据
        
        Args:
            table_name: 表名
            page: 页码
            page_size: 页大小
            data: 分页数据
            sort_columns: 排序配置
        
        Returns:
            是否缓存成功
        """
        cache_key = self._generate_page_key(table_name, page, page_size, sort_columns)
        return self.set(cache_key, data, 'pagination')
    
    def clear_table_cache(self, table_name: str):
        """清除特定表的所有缓存"""
        cleared = 0
        
        for partition in self._caches:
            keys_to_remove = []
            for key in self._caches[partition]:
                if table_name in key:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                self._remove_entry(partition, key)
                cleared += 1
        
        self.logger.info(f"清除表缓存: {table_name}, 清除{cleared}项")
    
    def clear_partition(self, partition: str):
        """清除特定分区的所有缓存"""
        if partition in self._caches:
            count = len(self._caches[partition])
            
            for key in list(self._caches[partition].keys()):
                self._remove_entry(partition, key)
            
            self.logger.info(f"清除分区缓存: {partition}, 清除{count}项")
    
    def clear_all(self):
        """清除所有缓存"""
        total = 0
        for partition in self._caches:
            total += len(self._caches[partition])
            self._caches[partition].clear()
        
        self._current_memory = 0
        self.logger.info(f"清除所有缓存: {total}项")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            统计信息字典
        """
        total_items = sum(len(cache) for cache in self._caches.values())
        hit_rate = self._stats['hits'] / max(1, self._stats['hits'] + self._stats['misses'])
        
        partition_stats = {}
        for partition, cache in self._caches.items():
            partition_stats[partition] = {
                'items': len(cache),
                'size': self._get_partition_size(partition),
                'max_size': self._partition_config[partition]['max_size']
            }
        
        return {
            'total_items': total_items,
            'memory_used': self._current_memory,
            'memory_limit': self._max_memory,
            'hit_rate': f"{hit_rate:.2%}",
            'hits': self._stats['hits'],
            'misses': self._stats['misses'],
            'evictions': self._stats['evictions'],
            'expired': self._stats['expired'],
            'partitions': partition_stats
        }
    
    def _generate_sort_key(self, table_name: str, sort_columns: List[Dict], 
                          data_size: int) -> str:
        """生成排序缓存键"""
        sort_str = json.dumps(sort_columns, sort_keys=True)
        raw_key = f"{table_name}_{sort_str}_{data_size}"
        return hashlib.md5(raw_key.encode()).hexdigest()
    
    def _generate_page_key(self, table_name: str, page: int, page_size: int,
                          sort_columns: Optional[List]) -> str:
        """生成分页缓存键"""
        sort_str = json.dumps(sort_columns, sort_keys=True) if sort_columns else "nosort"
        return f"{table_name}_p{page}_s{page_size}_{hashlib.md5(sort_str.encode()).hexdigest()[:8]}"
    
    def _perform_sort(self, data: pd.DataFrame, sort_columns: List[Dict]) -> pd.DataFrame:
        """执行排序"""
        if not sort_columns or data.empty:
            return data
        
        by_columns = []
        ascending_list = []
        
        for col_config in sort_columns:
            col_name = col_config.get('column_name', col_config.get('column'))
            if col_name and col_name in data.columns:
                by_columns.append(col_name)
                order = col_config.get('sort_order', col_config.get('order', 'asc'))
                ascending_list.append(order == 'asc')
        
        if by_columns:
            return data.sort_values(by=by_columns, ascending=ascending_list, ignore_index=True)
        
        return data
    
    def _estimate_size(self, value: Any) -> int:
        """估算对象大小"""
        import sys
        
        if isinstance(value, pd.DataFrame):
            return value.memory_usage(deep=True).sum()
        else:
            return sys.getsizeof(value)
    
    def _get_partition_size(self, partition: str) -> int:
        """获取分区当前大小"""
        return sum(entry.size for entry in self._caches[partition].values())
    
    def _remove_entry(self, partition: str, key: str):
        """移除缓存条目"""
        if key in self._caches[partition]:
            entry = self._caches[partition][key]
            self._current_memory -= entry.size
            del self._caches[partition][key]
    
    def _evict_lru(self, partition: str):
        """淘汰最近最少使用的条目"""
        if self._caches[partition]:
            # OrderedDict的第一个元素是最旧的
            key = next(iter(self._caches[partition]))
            self._remove_entry(partition, key)
            self._stats['evictions'] += 1
            self.logger.debug(f"LRU淘汰: {partition}/{key}")
    
    def _evict_oldest_global(self):
        """全局淘汰最旧的条目"""
        oldest_time = float('inf')
        oldest_partition = None
        oldest_key = None
        
        for partition, cache in self._caches.items():
            for key, entry in cache.items():
                if entry.timestamp < oldest_time:
                    oldest_time = entry.timestamp
                    oldest_partition = partition
                    oldest_key = key
        
        if oldest_partition and oldest_key:
            self._remove_entry(oldest_partition, oldest_key)
            self._stats['evictions'] += 1
            self.logger.debug(f"全局淘汰: {oldest_partition}/{oldest_key}")


# 创建全局实例
_unified_cache_manager = None

def get_unified_cache_manager() -> UnifiedCacheManager:
    """获取统一缓存管理器的全局实例"""
    global _unified_cache_manager
    if _unified_cache_manager is None:
        _unified_cache_manager = UnifiedCacheManager()
    return _unified_cache_manager