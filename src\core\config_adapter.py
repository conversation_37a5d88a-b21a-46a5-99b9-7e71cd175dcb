#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置适配器

提供新旧配置格式之间的转换功能，确保向后兼容性
"""

from typing import Dict, List, Optional, Any, Union
from loguru import logger
from .unified_config_schema import (
    ConfigType, FieldType, FieldConfig, SheetConfig,
    SingleSheetConfig, MultiSheetConfig, TemplateConfig,
    ConfigSerializer, ConfigDeserializer
)


class ConfigAdapter:
    """配置适配器"""
    
    @staticmethod
    def detect_config_format(data: Dict[str, Any]) -> str:
        """检测配置格式类型"""
        if "type" in data:
            config_type = data["type"]
            if config_type in ["system_config", "user_single_config", "user_multi_config", "template_config"]:
                return "unified"  # 新的统一格式
        
        # 检测旧格式
        if "sheets" in data and isinstance(data["sheets"], dict):
            # 检查是否是多表用户配置格式
            first_sheet = next(iter(data["sheets"].values()), {})
            if "config" in first_sheet:
                return "legacy_multi_user"
        
        if "data" in data and isinstance(data["data"], dict):
            # 检查是否是系统配置格式
            data_content = data["data"]
            if "field_mapping" in data_content or "field_types" in data_content:
                return "legacy_system"
        
        if "field_mapping" in data or "field_types" in data:
            return "legacy_single"
        
        return "unknown"
    
    @staticmethod
    def convert_to_unified(data: Dict[str, Any]) -> Union[SingleSheetConfig, MultiSheetConfig, TemplateConfig]:
        """将旧格式配置转换为统一格式"""
        format_type = ConfigAdapter.detect_config_format(data)
        
        if format_type == "unified":
            return ConfigDeserializer.from_dict(data)
        elif format_type == "legacy_multi_user":
            return ConfigAdapter._convert_legacy_multi_user(data)
        elif format_type == "legacy_system":
            return ConfigAdapter._convert_legacy_system(data)
        elif format_type == "legacy_single":
            return ConfigAdapter._convert_legacy_single(data)
        else:
            raise ValueError(f"无法识别的配置格式: {format_type}")
    
    @staticmethod
    def _convert_legacy_multi_user(data: Dict[str, Any]) -> MultiSheetConfig:
        """转换旧的多表用户配置格式"""
        sheets = {}
        
        for sheet_name, sheet_data in data.get("sheets", {}).items():
            config_data = sheet_data.get("config", {})
            
            # 构建字段配置
            field_mapping = config_data.get("field_mapping", {})
            field_types = config_data.get("field_types", {})
            
            fields = []
            for excel_field, db_field in field_mapping.items():
                field_type_str = field_types.get(excel_field, "text_string")
                try:
                    field_type = FieldType(field_type_str)
                except ValueError:
                    field_type = FieldType.TEXT_STRING
                    logger.warning(f"未知字段类型 '{field_type_str}'，使用默认类型")
                
                fields.append(FieldConfig(
                    excel_field=excel_field,
                    db_field=db_field,
                    field_type=field_type
                ))
            
            sheet_config = SheetConfig(
                sheet_name=sheet_name,
                description=sheet_data.get("description", ""),
                fields=fields
            )
            
            sheets[sheet_name] = sheet_config
        
        return MultiSheetConfig(
            name=data.get("name", ""),
            description=data.get("description", ""),
            version=data.get("version", "1.0"),
            sheets=sheets
        )
    
    @staticmethod
    def _convert_legacy_system(data: Dict[str, Any]) -> SingleSheetConfig:
        """转换旧的系统配置格式"""
        config_data = data.get("data", {})
        
        # 构建字段配置
        field_mapping = config_data.get("field_mapping", {})
        field_types = config_data.get("field_types", {})
        
        fields = []
        for excel_field, db_field in field_mapping.items():
            field_type_str = field_types.get(excel_field, "text_string")
            try:
                field_type = FieldType(field_type_str)
            except ValueError:
                field_type = FieldType.TEXT_STRING
                logger.warning(f"未知字段类型 '{field_type_str}'，使用默认类型")
            
            fields.append(FieldConfig(
                excel_field=excel_field,
                db_field=db_field,
                field_type=field_type
            ))
        
        sheet_config = SheetConfig(
            sheet_name=data.get("name", "default"),
            description=data.get("description", ""),
            fields=fields
        )
        
        return SingleSheetConfig(
            name=data.get("name", ""),
            description=data.get("description", ""),
            version=data.get("version", "1.0"),
            sheet=sheet_config
        )
    
    @staticmethod
    def _convert_legacy_single(data: Dict[str, Any]) -> SingleSheetConfig:
        """转换旧的单表配置格式"""
        # 构建字段配置
        field_mapping = data.get("field_mapping", {})
        field_types = data.get("field_types", {})
        
        fields = []
        for excel_field, db_field in field_mapping.items():
            field_type_str = field_types.get(excel_field, "text_string")
            try:
                field_type = FieldType(field_type_str)
            except ValueError:
                field_type = FieldType.TEXT_STRING
                logger.warning(f"未知字段类型 '{field_type_str}'，使用默认类型")
            
            fields.append(FieldConfig(
                excel_field=excel_field,
                db_field=db_field,
                field_type=field_type
            ))
        
        sheet_config = SheetConfig(
            sheet_name=data.get("name", "default"),
            description=data.get("description", ""),
            fields=fields
        )
        
        return SingleSheetConfig(
            name=data.get("name", ""),
            description=data.get("description", ""),
            version=data.get("version", "1.0"),
            sheet=sheet_config
        )
    
    @staticmethod
    def convert_to_legacy(config: Union[SingleSheetConfig, MultiSheetConfig, TemplateConfig], 
                         target_format: str = "legacy_multi_user") -> Dict[str, Any]:
        """将统一格式转换为旧格式（用于向后兼容）"""
        if target_format == "legacy_multi_user":
            return ConfigAdapter._convert_to_legacy_multi_user(config)
        elif target_format == "legacy_system":
            return ConfigAdapter._convert_to_legacy_system(config)
        elif target_format == "legacy_single":
            return ConfigAdapter._convert_to_legacy_single(config)
        else:
            raise ValueError(f"不支持的目标格式: {target_format}")
    
    @staticmethod
    def _convert_to_legacy_multi_user(config: Union[SingleSheetConfig, MultiSheetConfig]) -> Dict[str, Any]:
        """转换为旧的多表用户配置格式"""
        if isinstance(config, SingleSheetConfig):
            # 单表配置转为多表格式
            sheets = {
                config.sheet.sheet_name: {
                    "sheet_name": config.sheet.sheet_name,
                    "field_count": config.sheet.field_count,
                    "description": config.sheet.description,
                    "config": {
                        "field_mapping": config.sheet.field_mapping,
                        "field_types": config.sheet.field_types,
                        "formatting_rules": {}
                    }
                }
            } if config.sheet else {}
        else:
            # 多表配置
            sheets = {}
            for sheet_name, sheet in config.sheets.items():
                sheets[sheet_name] = {
                    "sheet_name": sheet.sheet_name,
                    "field_count": sheet.field_count,
                    "description": sheet.description,
                    "config": {
                        "field_mapping": sheet.field_mapping,
                        "field_types": sheet.field_types,
                        "formatting_rules": {}
                    }
                }
        
        return {
            "name": config.name,
            "description": config.description,
            "version": config.version,
            "type": "multi_sheet_user_config",
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            "sheet_count": len(sheets),
            "sheets": sheets
        }
    
    @staticmethod
    def _convert_to_legacy_system(config: SingleSheetConfig) -> Dict[str, Any]:
        """转换为旧的系统配置格式"""
        if not config.sheet:
            return {
                "name": config.name,
                "description": config.description,
                "data": {}
            }
        
        return {
            "name": config.name,
            "description": config.description,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            "data": {
                "field_mapping": config.sheet.field_mapping,
                "field_types": config.sheet.field_types,
                "formatting_rules": {}
            }
        }
    
    @staticmethod
    def _convert_to_legacy_single(config: SingleSheetConfig) -> Dict[str, Any]:
        """转换为旧的单表配置格式"""
        if not config.sheet:
            return {
                "name": config.name,
                "description": config.description,
                "field_mapping": {},
                "field_types": {}
            }
        
        return {
            "name": config.name,
            "description": config.description,
            "field_mapping": config.sheet.field_mapping,
            "field_types": config.sheet.field_types,
            "formatting_rules": {}
        }
    
    @staticmethod
    def get_legacy_field_mapping(config: Union[SingleSheetConfig, MultiSheetConfig], 
                                sheet_name: Optional[str] = None) -> Dict[str, str]:
        """获取兼容旧格式的字段映射"""
        if isinstance(config, SingleSheetConfig):
            return config.sheet.field_mapping if config.sheet else {}
        elif isinstance(config, MultiSheetConfig):
            if sheet_name and sheet_name in config.sheets:
                return config.sheets[sheet_name].field_mapping
            elif config.sheets:
                # 返回第一个工作表的映射
                first_sheet = next(iter(config.sheets.values()))
                return first_sheet.field_mapping
            else:
                return {}
        else:
            return {}
    
    @staticmethod
    def get_legacy_field_types(config: Union[SingleSheetConfig, MultiSheetConfig], 
                              sheet_name: Optional[str] = None) -> Dict[str, str]:
        """获取兼容旧格式的字段类型"""
        if isinstance(config, SingleSheetConfig):
            return config.sheet.field_types if config.sheet else {}
        elif isinstance(config, MultiSheetConfig):
            if sheet_name and sheet_name in config.sheets:
                return config.sheets[sheet_name].field_types
            elif config.sheets:
                # 返回第一个工作表的类型
                first_sheet = next(iter(config.sheets.values()))
                return first_sheet.field_types
            else:
                return {}
        else:
            return {}


# 导出的类和函数
__all__ = [
    "ConfigAdapter"
]
