#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置模板管理器

功能说明:
- 保存和管理常用的Sheet配置模板
- 支持模板的创建、编辑、删除和应用
- 提供内置模板和用户自定义模板
- 模板分类和标签管理
"""

import json
import uuid
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field, asdict
from datetime import datetime

from src.utils.log_config import setup_logger


@dataclass
class ConfigTemplate:
    """配置模板数据类"""
    id: str
    name: str
    description: str
    category: str  # 模板分类：builtin, user, imported
    tags: List[str] = field(default_factory=list)  # 标签
    
    # 配置内容
    config_data: Dict[str, Any] = field(default_factory=dict)
    
    # 元数据
    created_time: datetime = field(default_factory=datetime.now)
    modified_time: datetime = field(default_factory=datetime.now)
    usage_count: int = 0  # 使用次数
    is_favorite: bool = False  # 是否收藏
    author: str = "system"  # 作者
    version: str = "1.0"  # 版本
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 处理datetime序列化
        data['created_time'] = self.created_time.isoformat()
        data['modified_time'] = self.modified_time.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConfigTemplate':
        """从字典创建对象"""
        # 处理datetime反序列化
        if 'created_time' in data and isinstance(data['created_time'], str):
            data['created_time'] = datetime.fromisoformat(data['created_time'])
        if 'modified_time' in data and isinstance(data['modified_time'], str):
            data['modified_time'] = datetime.fromisoformat(data['modified_time'])
        
        return cls(**data)
    
    def update_modified_time(self):
        """更新修改时间"""
        self.modified_time = datetime.now()
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.update_modified_time()


class ConfigTemplateManager:
    """配置模板管理器"""
    
    def __init__(self, template_dir: Optional[Union[str, Path]] = None):
        """
        初始化配置模板管理器
        
        Args:
            template_dir: 模板存储目录，默认为 state/config_templates
        """
        self.logger = setup_logger(__name__)
        
        # 模板存储目录
        if template_dir is None:
            template_dir = Path("state/config_templates")
        self.template_dir = Path(template_dir)
        self.template_dir.mkdir(parents=True, exist_ok=True)
        
        # 模板存储
        self.templates: Dict[str, ConfigTemplate] = {}
        
        # 模板文件路径
        self.templates_file = self.template_dir / "templates.json"
        
        # 加载模板
        self._load_templates()
        self._init_builtin_templates()
        
        self.logger.info(f"配置模板管理器初始化完成，模板目录: {self.template_dir}")
    
    def _init_builtin_templates(self):
        """初始化内置模板"""
        builtin_templates = [
            {
                'id': 'builtin_standard_salary',
                'name': '标准工资表',
                'description': '适用于标准格式的工资表，第1行为表头，第2行开始为数据',
                'category': 'builtin',
                'tags': ['工资表', '标准格式'],
                'config_data': {
                    'header_row': 1,
                    'data_start_row': 2,
                    'has_header': True,
                    'auto_detect_header': True,
                    'remove_summary_rows': True,
                    'summary_keywords': ['合计', '小计', '总计', '汇总'],
                    'skip_empty_rows': True,
                    'trim_whitespace': True,
                    'normalize_numbers': True,
                    'handle_merged_cells': True,
                    'is_enabled': True
                },
                'author': 'system'
            },
            {
                'id': 'builtin_with_title',
                'name': '带标题的数据表',
                'description': '第1行为标题，第2行为表头，第3行开始为数据',
                'category': 'builtin',
                'tags': ['带标题', '数据表'],
                'config_data': {
                    'header_row': 2,
                    'data_start_row': 3,
                    'has_header': True,
                    'auto_detect_header': True,
                    'remove_summary_rows': True,
                    'summary_keywords': ['合计', '小计', '总计', '汇总'],
                    'skip_empty_rows': True,
                    'trim_whitespace': True,
                    'normalize_numbers': True,
                    'handle_merged_cells': True,
                    'is_enabled': True
                },
                'author': 'system'
            },
            {
                'id': 'builtin_summary_sheet',
                'name': '汇总表（不导入）',
                'description': '汇总表模板，默认不导入数据',
                'category': 'builtin',
                'tags': ['汇总表', '不导入'],
                'config_data': {
                    'is_enabled': False,
                    'remove_summary_rows': True,
                    'summary_keywords': ['合计', '小计', '总计', '汇总', '统计'],
                    'notes': '汇总表，不导入数据'
                },
                'author': 'system'
            },
            {
                'id': 'builtin_documentation',
                'name': '说明文档（不导入）',
                'description': '说明文档模板，默认不导入',
                'category': 'builtin',
                'tags': ['说明文档', '不导入'],
                'config_data': {
                    'is_enabled': False,
                    'notes': '说明文档，不导入数据'
                },
                'author': 'system'
            }
        ]
        
        # 添加内置模板（如果不存在）
        for template_data in builtin_templates:
            if template_data['id'] not in self.templates:
                template = ConfigTemplate(**template_data)
                self.templates[template.id] = template
        
        # 保存模板
        self._save_templates()
        
        self.logger.info(f"初始化了 {len(builtin_templates)} 个内置模板")
    
    def create_template(self, name: str, description: str, config_data: Dict[str, Any],
                       category: str = "user", tags: Optional[List[str]] = None,
                       author: str = "user") -> str:
        """
        创建新模板
        
        Args:
            name: 模板名称
            description: 模板描述
            config_data: 配置数据
            category: 模板分类
            tags: 标签列表
            author: 作者
            
        Returns:
            模板ID
        """
        try:
            template_id = str(uuid.uuid4())
            
            template = ConfigTemplate(
                id=template_id,
                name=name,
                description=description,
                category=category,
                tags=tags or [],
                config_data=config_data.copy(),
                author=author
            )
            
            self.templates[template_id] = template
            self._save_templates()
            
            self.logger.info(f"创建模板: {name} (ID: {template_id})")
            return template_id
            
        except Exception as e:
            self.logger.error(f"创建模板失败: {e}")
            raise
    
    def get_template(self, template_id: str) -> Optional[ConfigTemplate]:
        """
        获取模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            模板对象，如果不存在则返回None
        """
        return self.templates.get(template_id)
    
    def get_templates_by_category(self, category: str) -> List[ConfigTemplate]:
        """
        按分类获取模板
        
        Args:
            category: 分类名称
            
        Returns:
            模板列表
        """
        return [template for template in self.templates.values() 
                if template.category == category]
    
    def get_templates_by_tags(self, tags: List[str]) -> List[ConfigTemplate]:
        """
        按标签获取模板
        
        Args:
            tags: 标签列表
            
        Returns:
            包含任一标签的模板列表
        """
        return [template for template in self.templates.values()
                if any(tag in template.tags for tag in tags)]
    
    def search_templates(self, keyword: str) -> List[ConfigTemplate]:
        """
        搜索模板
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的模板列表
        """
        keyword_lower = keyword.lower()
        results = []
        
        for template in self.templates.values():
            if (keyword_lower in template.name.lower() or
                keyword_lower in template.description.lower() or
                any(keyword_lower in tag.lower() for tag in template.tags)):
                results.append(template)
        
        return results

    def update_template(self, template_id: str, **kwargs) -> bool:
        """
        更新模板

        Args:
            template_id: 模板ID
            **kwargs: 要更新的属性

        Returns:
            是否更新成功
        """
        try:
            template = self.templates.get(template_id)
            if not template:
                self.logger.warning(f"模板不存在: {template_id}")
                return False

            # 更新属性
            for key, value in kwargs.items():
                if hasattr(template, key):
                    setattr(template, key, value)
                else:
                    self.logger.warning(f"未知的模板属性: {key}")

            # 更新修改时间
            template.update_modified_time()

            # 保存
            self._save_templates()

            self.logger.info(f"更新模板: {template.name} (ID: {template_id})")
            return True

        except Exception as e:
            self.logger.error(f"更新模板失败: {e}")
            return False

    def delete_template(self, template_id: str) -> bool:
        """
        删除模板

        Args:
            template_id: 模板ID

        Returns:
            是否删除成功
        """
        try:
            template = self.templates.get(template_id)
            if not template:
                self.logger.warning(f"模板不存在: {template_id}")
                return False

            # 不允许删除内置模板
            if template.category == 'builtin':
                self.logger.warning(f"不能删除内置模板: {template.name}")
                return False

            del self.templates[template_id]
            self._save_templates()

            self.logger.info(f"删除模板: {template.name} (ID: {template_id})")
            return True

        except Exception as e:
            self.logger.error(f"删除模板失败: {e}")
            return False

    def apply_template(self, template_id: str, sheet_config) -> bool:
        """
        应用模板到Sheet配置

        Args:
            template_id: 模板ID
            sheet_config: Sheet配置对象

        Returns:
            是否应用成功
        """
        try:
            template = self.templates.get(template_id)
            if not template:
                self.logger.warning(f"模板不存在: {template_id}")
                return False

            # 应用配置数据
            for key, value in template.config_data.items():
                if hasattr(sheet_config, key):
                    setattr(sheet_config, key, value)

            # 增加使用次数
            template.increment_usage()
            self._save_templates()

            self.logger.info(f"应用模板: {template.name} 到 {sheet_config.sheet_name}")
            return True

        except Exception as e:
            self.logger.error(f"应用模板失败: {e}")
            return False

    def create_template_from_config(self, name: str, description: str,
                                   sheet_config, tags: Optional[List[str]] = None) -> str:
        """
        从Sheet配置创建模板

        Args:
            name: 模板名称
            description: 模板描述
            sheet_config: Sheet配置对象
            tags: 标签列表

        Returns:
            模板ID
        """
        try:
            # 提取配置数据
            config_data = {}

            # 基本配置
            basic_fields = [
                'header_row', 'data_start_row', 'data_end_row', 'skip_empty_rows',
                'has_header', 'auto_detect_header', 'remove_summary_rows', 'summary_keywords',
                'trim_whitespace', 'normalize_numbers', 'handle_merged_cells', 'fill_empty_values',
                'field_mappings', 'field_types', 'required_fields', 'validation_rules',
                'is_enabled', 'notes'
            ]

            for field in basic_fields:
                if hasattr(sheet_config, field):
                    value = getattr(sheet_config, field)
                    if value is not None:  # 只保存非空值
                        config_data[field] = value

            return self.create_template(
                name=name,
                description=description,
                config_data=config_data,
                tags=tags or []
            )

        except Exception as e:
            self.logger.error(f"从配置创建模板失败: {e}")
            raise

    def get_popular_templates(self, limit: int = 10) -> List[ConfigTemplate]:
        """
        获取热门模板（按使用次数排序）

        Args:
            limit: 返回数量限制

        Returns:
            热门模板列表
        """
        templates = list(self.templates.values())
        templates.sort(key=lambda t: t.usage_count, reverse=True)
        return templates[:limit]

    def get_favorite_templates(self) -> List[ConfigTemplate]:
        """
        获取收藏的模板

        Returns:
            收藏模板列表
        """
        return [template for template in self.templates.values() if template.is_favorite]

    def toggle_favorite(self, template_id: str) -> bool:
        """
        切换模板收藏状态

        Args:
            template_id: 模板ID

        Returns:
            是否操作成功
        """
        try:
            template = self.templates.get(template_id)
            if not template:
                return False

            template.is_favorite = not template.is_favorite
            template.update_modified_time()
            self._save_templates()

            self.logger.info(f"模板 {template.name} 收藏状态: {template.is_favorite}")
            return True

        except Exception as e:
            self.logger.error(f"切换收藏状态失败: {e}")
            return False

    def export_template(self, template_id: str, file_path: Union[str, Path]) -> bool:
        """
        导出模板到文件

        Args:
            template_id: 模板ID
            file_path: 导出文件路径

        Returns:
            是否导出成功
        """
        try:
            template = self.templates.get(template_id)
            if not template:
                return False

            export_data = template.to_dict()

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"导出模板: {template.name} 到 {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出模板失败: {e}")
            return False

    def import_template(self, file_path: Union[str, Path]) -> Optional[str]:
        """
        从文件导入模板

        Args:
            file_path: 模板文件路径

        Returns:
            导入的模板ID，失败返回None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)

            # 生成新的ID避免冲突
            template_data['id'] = str(uuid.uuid4())
            template_data['category'] = 'imported'

            template = ConfigTemplate.from_dict(template_data)
            self.templates[template.id] = template
            self._save_templates()

            self.logger.info(f"导入模板: {template.name} (ID: {template.id})")
            return template.id

        except Exception as e:
            self.logger.error(f"导入模板失败: {e}")
            return None

    def get_all_templates(self) -> List[ConfigTemplate]:
        """
        获取所有模板

        Returns:
            所有模板列表
        """
        return list(self.templates.values())

    def get_template_summary(self) -> Dict[str, Any]:
        """
        获取模板统计摘要

        Returns:
            统计摘要字典
        """
        templates = list(self.templates.values())

        return {
            'total_count': len(templates),
            'builtin_count': len([t for t in templates if t.category == 'builtin']),
            'user_count': len([t for t in templates if t.category == 'user']),
            'imported_count': len([t for t in templates if t.category == 'imported']),
            'favorite_count': len([t for t in templates if t.is_favorite]),
            'most_used': max(templates, key=lambda t: t.usage_count) if templates else None,
            'categories': list(set(t.category for t in templates)),
            'all_tags': list(set(tag for t in templates for tag in t.tags))
        }

    def _load_templates(self):
        """从文件加载模板"""
        if not self.templates_file.exists():
            return

        try:
            with open(self.templates_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.templates = {}
            for template_data in data.get('templates', []):
                template = ConfigTemplate.from_dict(template_data)
                self.templates[template.id] = template

            self.logger.info(f"加载了 {len(self.templates)} 个模板")

        except Exception as e:
            self.logger.error(f"加载模板失败: {e}")

    def _save_templates(self):
        """保存模板到文件"""
        try:
            data = {
                'templates': [template.to_dict() for template in self.templates.values()],
                'saved_time': datetime.now().isoformat()
            }

            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"保存模板失败: {e}")
