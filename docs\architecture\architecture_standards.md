# 架构规范文档

## 1. 项目结构规范

### 1.1 目录结构
```
salary_changes/
├── src/
│   ├── core/                  # 核心服务层
│   │   ├── services/          # 业务服务
│   │   ├── managers/          # 管理器（统一后）
│   │   ├── optimization/      # 性能优化
│   │   └── patterns/          # 设计模式实现
│   ├── gui/                   # 表现层
│   │   ├── prototype/         # 现代UI实现
│   │   └── components/        # UI组件
│   ├── modules/               # 业务模块
│   │   ├── data_import/       # 数据导入
│   │   ├── data_storage/      # 数据存储
│   │   └── report_generation/ # 报告生成
│   └── utils/                 # 工具类
├── test/                      # 测试文件
├── docs/                      # 文档
└── config/                    # 配置文件
```

### 1.2 命名规范
- **类名**: PascalCase (如 `UnifiedStateManager`)
- **函数名**: snake_case (如 `get_table_data`)
- **常量**: UPPER_SNAKE_CASE (如 `MAX_CACHE_SIZE`)
- **私有成员**: 前缀下划线 (如 `_internal_state`)

## 2. 设计模式规范

### 2.1 单例模式
```python
class ServiceManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
```

### 2.2 工厂模式
```python
class ComponentFactory:
    @staticmethod
    def create_component(component_type: str):
        # 组件创建逻辑
        pass
```

### 2.3 观察者模式
```python
class EventManager:
    def __init__(self):
        self._listeners = defaultdict(list)
    
    def subscribe(self, event_type, callback):
        self._listeners[event_type].append(callback)
    
    def emit(self, event_type, data):
        for callback in self._listeners[event_type]:
            callback(data)
```

## 3. 服务层规范

### 3.1 服务接口定义
```python
from abc import ABC, abstractmethod

class IService(ABC):
    @abstractmethod
    def initialize(self):
        """初始化服务"""
        pass
    
    @abstractmethod
    def shutdown(self):
        """关闭服务"""
        pass
```

### 3.2 服务生命周期
1. **创建**: 通过ServiceLocator获取
2. **初始化**: 调用initialize()方法
3. **使用**: 业务逻辑调用
4. **销毁**: 调用shutdown()方法

## 4. 依赖管理规范

### 4.1 依赖注入
```python
class ServiceContainer:
    def __init__(self):
        self._services = {}
        self._singletons = {}
    
    def register(self, service_type, factory, singleton=True):
        """注册服务"""
        pass
    
    def resolve(self, service_type):
        """解析服务"""
        pass
```

### 4.2 循环依赖处理
- 使用延迟初始化
- 使用接口解耦
- 使用事件驱动通信

## 5. 错误处理规范

### 5.1 异常层次
```python
class ApplicationException(Exception):
    """应用异常基类"""
    pass

class ServiceException(ApplicationException):
    """服务异常"""
    pass

class ValidationException(ApplicationException):
    """验证异常"""
    pass

class DataException(ApplicationException):
    """数据异常"""
    pass
```

### 5.2 错误处理策略
- **UI层**: 捕获并显示友好错误信息
- **服务层**: 记录日志并抛出业务异常
- **数据层**: 事务回滚并抛出数据异常

## 6. 性能规范

### 6.1 缓存策略
- 使用UnifiedCacheManager统一管理
- LRU + TTL缓存策略
- 内存限制100MB

### 6.2 异步处理
- 使用ThreadPoolManager管理线程
- 批处理优化IO操作
- 延迟加载重量级组件

## 7. 日志规范

### 7.1 日志级别
- **DEBUG**: 详细调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

### 7.2 日志格式
```python
logger.info(f"操作: {operation}, 表: {table_name}, 结果: {result}")
```

## 8. 测试规范

### 8.1 测试覆盖率
- 单元测试覆盖率 > 80%
- 核心功能100%覆盖
- 边界条件测试

### 8.2 测试命名
```python
def test_<被测试方法>_<场景>_<预期结果>():
    """测试描述"""
    pass
```

## 9. 代码审查清单

### 9.1 架构检查
- [ ] 符合分层架构
- [ ] 无循环依赖
- [ ] 遵循SOLID原则

### 9.2 代码质量
- [ ] 命名清晰
- [ ] 注释完整
- [ ] 错误处理合理

### 9.3 性能考虑
- [ ] 避免N+1查询
- [ ] 合理使用缓存
- [ ] 异步处理耗时操作

## 10. 版本管理规范

### 10.1 分支策略
- **main**: 生产分支
- **develop**: 开发分支
- **feature/**: 功能分支
- **bugfix/**: 修复分支

### 10.2 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型:
- feat: 新功能
- fix: 修复
- docs: 文档
- style: 格式
- refactor: 重构
- test: 测试
- chore: 构建

## 11. 安全规范

### 11.1 数据安全
- 敏感数据加密存储
- SQL注入防护
- 输入验证

### 11.2 权限控制
- 最小权限原则
- 操作审计日志
- 会话管理

## 12. 部署规范

### 12.1 环境配置
- 开发环境: development.json
- 测试环境: testing.json
- 生产环境: production.json

### 12.2 部署检查清单
- [ ] 配置文件正确
- [ ] 依赖版本锁定
- [ ] 日志配置合理
- [ ] 错误监控就绪