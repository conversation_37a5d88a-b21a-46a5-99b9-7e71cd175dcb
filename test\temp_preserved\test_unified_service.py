"""
测试统一映射服务
确保所有路径都能正常工作
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.unified_mapping_service import get_unified_mapping_service
from src.core.unified_data_request_manager import UnifiedDataRequestManager, FieldMappingService
from src.modules.system_config import ConfigManager
import pandas as pd

def test_unified_service():
    """测试统一映射服务"""
    print("=" * 60)
    print("测试统一映射服务")
    print("=" * 60)
    
    # 1. 获取服务实例（单例）
    print("\n1. 测试单例模式...")
    service1 = get_unified_mapping_service()
    service2 = get_unified_mapping_service()
    assert service1 is service2, "应该返回同一个实例"
    print("   [PASS] 单例模式正常")
    
    # 2. 测试基本功能
    print("\n2. 测试基本功能...")
    test_table = "test_unified_2025_01"
    
    # 获取默认映射
    mapping = service1.get_field_mapping(test_table)
    print(f"   获取映射成功，字段数: {len(mapping)}")
    
    # 保存用户映射
    user_config = {
        'field_mapping': {
            'test_field1': '测试字段1',
            'test_field2': '测试字段2',
            'unified_field': '统一字段'
        },
        'is_change_table': False
    }
    
    success = service1.save_user_mapping(test_table, user_config)
    print(f"   保存用户映射: {'成功' if success else '失败'}")
    
    # 验证用户映射生效
    new_mapping = service1.get_field_mapping(test_table)
    if 'unified_field' in new_mapping:
        print(f"   [PASS] 用户映射已生效: unified_field -> {new_mapping['unified_field']}")
    else:
        print("   [FAIL] 用户映射未生效")
    
    # 3. 测试辅助功能
    print("\n3. 测试辅助功能...")
    
    # 获取显示名称
    display_name = service1.get_display_name(test_table, 'unified_field')
    print(f"   显示名称: {display_name}")
    
    # 获取字段类型
    field_type = service1.get_field_type(test_table, 'employee_id')
    print(f"   字段类型: {field_type}")
    
    # 判断是否异动表
    is_change = service1.is_change_table(test_table)
    print(f"   是否异动表: {is_change}")
    
    # 4. 测试验证功能
    print("\n4. 测试验证功能...")
    test_columns = ['test_field1', 'test_field2', 'new_field', 'another_field']
    validation_result = service1.validate_mapping(test_table, test_columns)
    print(f"   验证结果: 有效={validation_result.get('is_valid', False)}")
    if validation_result.get('missing_mappings'):
        print(f"   缺失映射: {validation_result['missing_mappings']}")
    
    # 5. 测试统计功能
    print("\n5. 测试统计功能...")
    stats = service1.get_statistics()
    print(f"   总表数: {stats.get('total_tables', 0)}")
    print(f"   用户配置表数: {stats.get('user_configured_tables', 0)}")
    print(f"   缓存大小: {stats.get('cache_size', 0)}")
    print(f"   异动表数: {stats.get('change_tables', 0)}")
    
    # 6. 测试FieldMappingService集成
    print("\n6. 测试FieldMappingService集成...")
    config_manager = ConfigManager()
    field_service = FieldMappingService(config_manager)
    
    # 通过FieldMappingService获取映射（应该委托给统一服务）
    service_mapping = field_service.get_field_mapping(test_table)
    print(f"   通过FieldMappingService获取映射，字段数: {len(service_mapping)}")
    
    # 验证返回相同的映射
    if 'unified_field' in service_mapping:
        print("   [PASS] FieldMappingService正确委托给统一服务")
    else:
        print("   [FAIL] FieldMappingService未正确委托")
    
    # 7. 测试DataFrame字段映射
    print("\n7. 测试DataFrame字段映射...")
    test_df = pd.DataFrame({
        'test_field1': [1, 2, 3],
        'test_field2': ['A', 'B', 'C'],
        'unified_field': [10.0, 20.0, 30.0]
    })
    
    mapped_df = field_service.apply_field_mapping(test_df, test_table)
    print(f"   原始列: {list(test_df.columns)}")
    print(f"   映射后列: {list(mapped_df.columns)}")
    
    # 检查列名是否被正确映射
    expected_columns = ['测试字段1', '测试字段2', '统一字段']
    actual_columns = list(mapped_df.columns)
    if actual_columns == expected_columns:
        print("   [PASS] DataFrame字段映射成功")
    else:
        print(f"   [FAIL] 映射不正确: {actual_columns} != {expected_columns}")
    
    # 8. 测试缓存机制
    print("\n8. 测试缓存机制...")
    
    # 第一次调用（会加载）
    import time
    start = time.time()
    mapping1 = service1.get_field_mapping(test_table)
    time1 = time.time() - start
    
    # 第二次调用（使用缓存）
    start = time.time()
    mapping2 = service1.get_field_mapping(test_table)
    time2 = time.time() - start
    
    print(f"   第一次调用时间: {time1:.4f}秒")
    print(f"   第二次调用时间: {time2:.4f}秒")
    if time2 < time1:
        print("   [PASS] 缓存机制生效")
    
    # 强制刷新
    mapping3 = service1.get_field_mapping(test_table, {'force_refresh': True})
    print("   [PASS] 强制刷新成功")
    
    print("\n" + "=" * 60)
    print("统一映射服务测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_unified_service()