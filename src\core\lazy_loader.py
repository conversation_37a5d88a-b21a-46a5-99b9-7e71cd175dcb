"""
延迟加载管理器 - P2级优化
实现模块和组件的延迟加载
"""

import importlib
import functools
from typing import Any, Optional, Dict, Callable
from loguru import logger
from src.core.performance_profiler import profile_function


class LazyLoader:
    """
    延迟加载管理器
    
    功能：
    - 模块延迟导入
    - 组件延迟初始化
    - 资源按需加载
    """
    
    def __init__(self):
        self.logger = logger
        self._loaded_modules: Dict[str, Any] = {}
        self._loading_callbacks: Dict[str, Callable] = {}
        
    @profile_function
    def lazy_import(self, module_name: str) -> Any:
        """
        延迟导入模块
        
        Args:
            module_name: 模块名称
        
        Returns:
            导入的模块
        """
        if module_name in self._loaded_modules:
            return self._loaded_modules[module_name]
        
        try:
            self.logger.debug(f"延迟加载模块: {module_name}")
            module = importlib.import_module(module_name)
            self._loaded_modules[module_name] = module
            
            # 执行加载回调
            if module_name in self._loading_callbacks:
                self._loading_callbacks[module_name](module)
            
            return module
        except ImportError as e:
            self.logger.error(f"模块加载失败: {module_name} - {e}")
            raise
    
    def lazy_attribute(self, module_name: str, attribute_name: str) -> Any:
        """
        延迟获取模块属性
        
        Args:
            module_name: 模块名称
            attribute_name: 属性名称
        
        Returns:
            模块属性
        """
        module = self.lazy_import(module_name)
        return getattr(module, attribute_name)
    
    def register_callback(self, module_name: str, callback: Callable):
        """
        注册模块加载回调
        
        Args:
            module_name: 模块名称
            callback: 回调函数
        """
        self._loading_callbacks[module_name] = callback
    
    def is_loaded(self, module_name: str) -> bool:
        """
        检查模块是否已加载
        
        Args:
            module_name: 模块名称
        
        Returns:
            是否已加载
        """
        return module_name in self._loaded_modules
    
    def get_loaded_modules(self) -> list:
        """获取已加载的模块列表"""
        return list(self._loaded_modules.keys())


class LazyProperty:
    """
    延迟属性装饰器
    
    属性在第一次访问时才初始化
    """
    
    def __init__(self, func):
        self.func = func
        self.__doc__ = func.__doc__
        
    def __get__(self, obj, type=None):
        if obj is None:
            return self
        
        # 计算属性值并缓存
        value = self.func(obj)
        obj.__dict__[self.func.__name__] = value
        return value


def lazy_import_property(module_name: str, attribute_name: Optional[str] = None):
    """
    延迟导入属性装饰器
    
    Args:
        module_name: 模块名称
        attribute_name: 属性名称（可选）
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self):
            loader = LazyLoader()
            if attribute_name:
                return loader.lazy_attribute(module_name, attribute_name)
            else:
                return loader.lazy_import(module_name)
        
        return LazyProperty(wrapper)
    
    return decorator


class LazyComponentLoader:
    """
    组件延迟加载器
    
    用于延迟初始化重量级组件
    """
    
    def __init__(self):
        self.logger = logger
        self._components: Dict[str, Any] = {}
        self._initializers: Dict[str, Callable] = {}
        
    def register_component(self, name: str, initializer: Callable):
        """
        注册组件初始化器
        
        Args:
            name: 组件名称
            initializer: 初始化函数
        """
        self._initializers[name] = initializer
        self.logger.debug(f"注册延迟组件: {name}")
    
    @profile_function
    def get_component(self, name: str) -> Any:
        """
        获取组件（延迟初始化）
        
        Args:
            name: 组件名称
        
        Returns:
            组件实例
        """
        if name in self._components:
            return self._components[name]
        
        if name not in self._initializers:
            raise ValueError(f"组件未注册: {name}")
        
        self.logger.info(f"延迟初始化组件: {name}")
        component = self._initializers[name]()
        self._components[name] = component
        
        return component
    
    def is_initialized(self, name: str) -> bool:
        """
        检查组件是否已初始化
        
        Args:
            name: 组件名称
        
        Returns:
            是否已初始化
        """
        return name in self._components
    
    def preload_components(self, names: list):
        """
        预加载指定组件
        
        Args:
            names: 组件名称列表
        """
        for name in names:
            if not self.is_initialized(name):
                self.get_component(name)


# 全局实例
_lazy_loader = LazyLoader()
_component_loader = LazyComponentLoader()

def get_lazy_loader() -> LazyLoader:
    """获取延迟加载器"""
    return _lazy_loader

def get_component_loader() -> LazyComponentLoader:
    """获取组件加载器"""
    return _component_loader