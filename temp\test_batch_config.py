#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量配置应用功能测试脚本
"""

import sys
import os
import time
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.sheet_config_manager import SheetConfigManager
from src.modules.data_import.batch_config_manager import BatchOperationType

def test_batch_copy_config():
    """测试批量复制配置"""
    print("=== 测试批量复制配置 ===")
    
    manager = SheetConfigManager()
    
    # 1. 创建源配置
    source_sheet = "源工资表"
    print(f"\n📋 创建源配置: {source_sheet}")
    
    # 设置源配置
    manager.update_config(source_sheet,
                         header_row=2,
                         data_start_row=3,
                         remove_summary_rows=True,
                         skip_empty_rows=True,
                         trim_whitespace=True)
    
    source_config = manager.get_or_create_config(source_sheet)
    print(f"  表头行: {source_config.header_row}")
    print(f"  数据起始行: {source_config.data_start_row}")
    print(f"  移除汇总行: {source_config.remove_summary_rows}")
    
    # 2. 批量复制到目标Sheet
    target_sheets = ["2024年1月", "2024年2月", "2024年3月", "2024年4月"]
    print(f"\n🎯 批量复制到目标Sheet: {len(target_sheets)} 个")
    
    def progress_callback(current, total):
        print(f"  进度: {current}/{total} ({current/total*100:.1f}%)")
    
    result = manager.batch_copy_config(
        source_sheet=source_sheet,
        target_sheets=target_sheets,
        exclude_fields=['notes']  # 排除备注字段
    )
    
    print(f"\n📊 复制结果:")
    print(f"  总数: {result.total_count}")
    print(f"  成功: {result.success_count}")
    print(f"  失败: {result.failed_count}")
    print(f"  跳过: {result.skipped_count}")
    print(f"  成功率: {result.success_rate:.1%}")
    print(f"  耗时: {result.duration_seconds:.2f} 秒")
    
    if result.success_sheets:
        print(f"  成功Sheet: {', '.join(result.success_sheets)}")
    
    if result.failed_sheets:
        print(f"  失败Sheet: {result.failed_sheets}")
    
    # 3. 验证复制结果
    print(f"\n✅ 验证复制结果:")
    for sheet_name in target_sheets[:2]:  # 验证前2个
        config = manager.get_or_create_config(sheet_name)
        print(f"  {sheet_name}: 表头行={config.header_row}, 数据起始行={config.data_start_row}")
    
    return manager, target_sheets

def test_batch_apply_template():
    """测试批量应用模板"""
    print("\n=== 测试批量应用模板 ===")
    
    manager = SheetConfigManager()
    
    # 1. 获取可用模板
    print("\n📋 可用模板:")
    builtin_templates = manager.template_manager.get_templates_by_category('builtin')
    for template in builtin_templates[:3]:  # 显示前3个
        print(f"  - {template.name}: {template.description}")
    
    # 2. 选择模板进行批量应用
    if builtin_templates:
        template = builtin_templates[0]  # 使用第一个模板
        target_sheets = ["模板测试1", "模板测试2", "模板测试3"]
        
        print(f"\n🎯 批量应用模板 '{template.name}' 到 {len(target_sheets)} 个Sheet")
        
        result = manager.batch_apply_template(
            template_id=template.id,
            target_sheets=target_sheets
        )
        
        print(f"\n📊 应用结果:")
        print(f"  总数: {result.total_count}")
        print(f"  成功: {result.success_count}")
        print(f"  失败: {result.failed_count}")
        print(f"  成功率: {result.success_rate:.1%}")
        print(f"  耗时: {result.duration_seconds:.2f} 秒")
        
        # 验证应用结果
        if result.success_sheets:
            print(f"\n✅ 验证应用结果:")
            for sheet_name in result.success_sheets[:2]:
                config = manager.get_or_create_config(sheet_name)
                print(f"  {sheet_name}: 启用={config.is_enabled}, 表头行={config.header_row}")

def test_batch_smart_recommendations():
    """测试批量智能推荐"""
    print("\n=== 测试批量智能推荐 ===")
    
    manager = SheetConfigManager()
    
    # 1. 准备测试Sheet
    test_sheets = [
        "2024年5月工资表",
        "员工信息汇总表", 
        "数据说明文档",
        "工资计算模板"
    ]
    
    print(f"\n🧠 批量应用智能推荐到 {len(test_sheets)} 个Sheet")
    
    result = manager.batch_apply_smart_recommendations(test_sheets)
    
    print(f"\n📊 推荐结果:")
    print(f"  总数: {result.total_count}")
    print(f"  成功: {result.success_count}")
    print(f"  失败: {result.failed_count}")
    print(f"  成功率: {result.success_rate:.1%}")
    print(f"  耗时: {result.duration_seconds:.2f} 秒")
    
    # 查看推荐结果
    if result.success_sheets:
        print(f"\n✅ 智能推荐结果:")
        for sheet_name in result.success_sheets:
            config = manager.get_or_create_config(sheet_name)
            print(f"  {sheet_name}: 启用={config.is_enabled}, 表头行={config.header_row}, 移除汇总行={config.remove_summary_rows}")

def test_batch_update_config():
    """测试批量更新配置"""
    print("\n=== 测试批量更新配置 ===")
    
    manager = SheetConfigManager()
    
    # 1. 准备目标Sheet
    target_sheets = ["批量更新1", "批量更新2", "批量更新3"]
    
    # 先创建这些Sheet的配置
    for sheet_name in target_sheets:
        manager.get_or_create_config(sheet_name)
    
    # 2. 批量更新配置
    config_updates = {
        'skip_empty_rows': True,
        'trim_whitespace': True,
        'normalize_numbers': True,
        'handle_merged_cells': True
    }
    
    print(f"\n⚙️ 批量更新配置: {len(target_sheets)} 个Sheet")
    print(f"  更新项: {list(config_updates.keys())}")
    
    result = manager.batch_update_config(
        target_sheets=target_sheets,
        config_updates=config_updates
    )
    
    print(f"\n📊 更新结果:")
    print(f"  总数: {result.total_count}")
    print(f"  成功: {result.success_count}")
    print(f"  失败: {result.failed_count}")
    print(f"  成功率: {result.success_rate:.1%}")
    
    # 验证更新结果
    if result.success_sheets:
        print(f"\n✅ 验证更新结果:")
        config = manager.get_or_create_config(result.success_sheets[0])
        print(f"  跳过空行: {config.skip_empty_rows}")
        print(f"  去除空格: {config.trim_whitespace}")
        print(f"  统一数字格式: {config.normalize_numbers}")

def test_similar_sheets_and_preview():
    """测试相似Sheet查找和操作预览"""
    print("\n=== 测试相似Sheet查找和操作预览 ===")
    
    manager = SheetConfigManager()
    
    # 1. 创建一些相似的配置
    reference_sheet = "参考工资表"
    manager.update_config(reference_sheet,
                         header_row=1,
                         data_start_row=2,
                         remove_summary_rows=True,
                         is_enabled=True)
    
    similar_sheets = ["相似表1", "相似表2"]
    for sheet_name in similar_sheets:
        manager.update_config(sheet_name,
                             header_row=1,
                             data_start_row=2,
                             remove_summary_rows=True,
                             is_enabled=True)
    
    different_sheet = "不同表"
    manager.update_config(different_sheet,
                         header_row=3,
                         data_start_row=4,
                         remove_summary_rows=False,
                         is_enabled=False)
    
    # 2. 查找相似Sheet
    print(f"\n🔍 查找与 '{reference_sheet}' 相似的Sheet:")
    similar_found = manager.get_similar_sheets(reference_sheet, similarity_threshold=0.7)
    print(f"  找到 {len(similar_found)} 个相似Sheet: {similar_found}")
    
    # 3. 预览批量操作
    print(f"\n👁️ 预览批量复制配置操作:")
    preview = manager.preview_batch_operation(
        operation_type='copy_config',
        target_sheets=similar_sheets + [different_sheet],
        source_sheet=reference_sheet
    )
    
    print(f"  操作类型: {preview['operation_type']}")
    print(f"  目标数量: {preview['target_count']}")
    print(f"  预估耗时: {preview['estimated_duration']:.1f} 秒")
    
    if preview.get('warnings'):
        print(f"  ⚠️  警告: {preview['warnings']}")
    
    if preview.get('recommendations'):
        print(f"  💡 建议: {preview['recommendations']}")

def test_operation_history_and_statistics():
    """测试操作历史和统计"""
    print("\n=== 测试操作历史和统计 ===")
    
    manager = SheetConfigManager()
    
    # 1. 执行一些批量操作（前面的测试已经执行了一些）
    print("\n📊 批量操作统计:")
    stats = manager.get_batch_operation_statistics()
    
    print(f"  总操作数: {stats.get('total_operations', 0)}")
    print(f"  处理Sheet总数: {stats.get('total_sheets_processed', 0)}")
    print(f"  总成功数: {stats.get('total_success_count', 0)}")
    print(f"  总失败数: {stats.get('total_failed_count', 0)}")
    print(f"  平均成功率: {stats.get('average_success_rate', 0):.1%}")
    print(f"  总耗时: {stats.get('total_duration', 0):.2f} 秒")
    
    if stats.get('operation_types'):
        print(f"  操作类型分布: {stats['operation_types']}")
    
    # 2. 查看操作历史
    print(f"\n📜 最近操作历史:")
    history = manager.get_batch_operation_history(limit=5)
    
    for i, result in enumerate(history, 1):
        print(f"  {i}. {result.operation_type.value}: {result.success_count}/{result.total_count} 成功")

def main():
    """主函数"""
    print("🚀 批量配置应用功能测试")
    print("=" * 50)
    
    try:
        # 测试批量复制配置
        manager, target_sheets = test_batch_copy_config()
        
        # 测试批量应用模板
        test_batch_apply_template()
        
        # 测试批量智能推荐
        test_batch_smart_recommendations()
        
        # 测试批量更新配置
        test_batch_update_config()
        
        # 测试相似Sheet查找和预览
        test_similar_sheets_and_preview()
        
        # 测试操作历史和统计
        test_operation_history_and_statistics()
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
