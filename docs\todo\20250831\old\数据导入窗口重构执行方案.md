# 数据导入窗口重构 - 执行方案

**生成时间**: 2025-08-30 23:58  
**执行目标**: 保留"统一数据导入配置"，删除其他两个窗口

---

## 一、执行步骤清单

### 步骤1：文件和类重命名
```
原文件: src/gui/unified_import_config_dialog.py
新文件: src/gui/unified_data_import_window.py

原类名: UnifiedImportConfigDialog  
新类名: UnifiedDataImportWindow

原标题: "统一数据导入 & 字段配置对话框"
新标题: "统一数据导入配置"
```

### 步骤2：删除文件清单
```
完全删除的文件：
1. src/gui/data_import_integration.py (753行)
2. src/gui/import_settings_dialog.py (449行)

部分删除：
3. src/gui/main_dialogs.py - 只删除 DataImportDialog 类 (约500行)
```

### 步骤3：更新引用
```python
# prototype_main_window.py
# 旧：
from src.gui.unified_import_config_dialog import UnifiedImportConfigDialog
dialog = UnifiedImportConfigDialog(...)

# 新：
from src.gui.unified_data_import_window import UnifiedDataImportWindow  
dialog = UnifiedDataImportWindow(...)
```

### 步骤4：清理 unified_integration_manager.py
- 删除对 UnifiedImportConfigDialog 的引用
- 保留对 UnifiedDataImportWindow 的引用（现在真实存在）

### 步骤5：评估并清理 unified_* 系列文件
```
可能删除（需评估依赖）：
- unified_conflict_analyzer.py
- unified_data_migration_tool.py  
- unified_feedback_system.py
- unified_performance_optimizer.py
- unified_user_guide_system.py
- unified_visual_indicator.py

保留：
- unified_config_manager.py（配置管理核心）
- unified_integration_manager.py（需清理）
```

---

## 二、命令执行序列

### 2.1 备份
```bash
# 创建备份目录
mkdir -p backup/20250831_import_refactor

# 备份关键文件
cp src/gui/unified_import_config_dialog.py backup/20250831_import_refactor/
cp src/gui/main_dialogs.py backup/20250831_import_refactor/
cp src/gui/data_import_integration.py backup/20250831_import_refactor/
cp src/gui/import_settings_dialog.py backup/20250831_import_refactor/
cp src/gui/prototype/prototype_main_window.py backup/20250831_import_refactor/
```

### 2.2 重命名
```bash
# 重命名主文件
mv src/gui/unified_import_config_dialog.py src/gui/unified_data_import_window.py
```

### 2.3 代码修改
```python
# 在 unified_data_import_window.py 中：
# 1. 类名: UnifiedImportConfigDialog → UnifiedDataImportWindow
# 2. 窗口标题: 去掉 "& 字段配置"
# 3. 注释更新

# 在 prototype_main_window.py 中：
# 1. import 语句更新
# 2. 类名引用更新
```

### 2.4 删除废弃文件
```bash
# 删除独立文件
rm src/gui/data_import_integration.py
rm src/gui/import_settings_dialog.py

# DataImportDialog 需要从 main_dialogs.py 中删除（不是删除整个文件）
```

---

## 三、验证检查点

### 3.1 功能验证
- [ ] 主窗口"导入数据"按钮正常工作
- [ ] 窗口标题显示为"统一数据导入配置"
- [ ] Sheet选择功能正常
- [ ] 字段映射功能正常
- [ ] 预览验证功能正常
- [ ] 格式化规则功能正常

### 3.2 代码验证
- [ ] 没有未解决的import错误
- [ ] 没有对已删除类的引用
- [ ] 所有测试用例通过

### 3.3 文件验证
- [ ] 确认只有一个数据导入窗口文件
- [ ] 确认废弃代码已完全删除
- [ ] 确认备份文件完整

---

## 四、回滚方案

如果出现问题，执行以下回滚：

```bash
# 1. 恢复原文件名
mv src/gui/unified_data_import_window.py src/gui/unified_import_config_dialog.py

# 2. 恢复备份文件
cp backup/20250831_import_refactor/main_dialogs.py src/gui/
cp backup/20250831_import_refactor/data_import_integration.py src/gui/
cp backup/20250831_import_refactor/import_settings_dialog.py src/gui/
cp backup/20250831_import_refactor/prototype_main_window.py src/gui/prototype/

# 3. 恢复代码修改
# 使用备份文件覆盖修改的文件
```

---

## 五、预期结果

### 5.1 代码简化
- 删除约 1700 行冗余代码
- 从 3 个窗口简化为 1 个窗口
- 消除重复功能和冲突

### 5.2 维护改进
- 清晰的单一入口
- 统一的配置管理
- 降低维护复杂度

### 5.3 用户体验
- 窗口名称符合预期："统一数据导入配置"
- 功能完整且稳定
- 性能优化

---

## 六、注意事项

1. **关键原则**：
   - 保留功能完整性
   - 确保平滑过渡
   - 每步验证测试

2. **风险点**：
   - unified_* 系列文件的依赖关系需要仔细评估
   - 测试用例可能需要更新
   - 其他模块可能有隐式依赖

3. **时间估算**：
   - 备份：5分钟
   - 重命名和修改：30分钟
   - 测试验证：30分钟
   - 总计：约1小时

---

**状态**: 待用户确认后执行
