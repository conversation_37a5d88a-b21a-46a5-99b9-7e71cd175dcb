#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查项目中方法调用一致性的脚本

用于发现可能存在的方法调用错误
"""

import sys
import os
import ast
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class MethodCallChecker:
    """方法调用一致性检查器"""
    
    def __init__(self):
        self.method_definitions = {}  # {class_name: {method_name: file_path}}
        self.method_calls = []  # [(file_path, line_num, class_name, method_name)]
        self.potential_issues = []
        
    def scan_python_files(self, directory: Path) -> List[Path]:
        """扫描Python文件"""
        python_files = []
        for file_path in directory.rglob("*.py"):
            if not any(part.startswith('.') for part in file_path.parts):
                python_files.append(file_path)
        return python_files
    
    def extract_class_methods(self, file_path: Path):
        """提取类方法定义"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_name = node.name
                    if class_name not in self.method_definitions:
                        self.method_definitions[class_name] = {}
                    
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_name = item.name
                            self.method_definitions[class_name][method_name] = str(file_path)
                            
        except Exception as e:
            print(f"解析文件失败 {file_path}: {e}")
    
    def find_method_calls(self, file_path: Path):
        """查找方法调用"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # 查找self.method_name()调用
                self_calls = re.findall(r'self\.(\w+)\s*\(', line)
                for method_name in self_calls:
                    self.method_calls.append((str(file_path), line_num, 'self', method_name, line.strip()))
                
                # 查找object.method_name()调用
                obj_calls = re.findall(r'(\w+)\.(\w+)\s*\(', line)
                for obj_name, method_name in obj_calls:
                    if obj_name != 'self':
                        self.method_calls.append((str(file_path), line_num, obj_name, method_name, line.strip()))
                        
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
    
    def check_preview_related_methods(self):
        """检查预览相关方法的一致性"""
        preview_methods = [
            'update_preview_data',
            'show_preview_data', 
            '_show_preview_data',
            'preview_data',
            'update_for_sheet',
            'clear_preview'
        ]
        
        print("=== 预览相关方法检查 ===")
        
        for method_name in preview_methods:
            print(f"\n检查方法: {method_name}")
            
            # 查找定义
            definitions = []
            for class_name, methods in self.method_definitions.items():
                if method_name in methods:
                    definitions.append((class_name, methods[method_name]))
            
            if definitions:
                print(f"  定义位置:")
                for class_name, file_path in definitions:
                    print(f"    {class_name} in {file_path}")
            else:
                print(f"  ❌ 未找到定义")
            
            # 查找调用
            calls = [call for call in self.method_calls if call[3] == method_name]
            if calls:
                print(f"  调用位置:")
                for file_path, line_num, obj_name, _, code in calls:
                    print(f"    {file_path}:{line_num} - {obj_name}.{method_name}()")
                    print(f"      代码: {code}")
            else:
                print(f"  未找到调用")
    
    def check_dataframe_conversions(self):
        """检查DataFrame转换相关的代码"""
        print("\n=== DataFrame转换检查 ===")
        
        conversion_patterns = [
            r'\.to_dict\s*\(\s*[\'"]records[\'"]\s*\)',
            r'pd\.DataFrame\s*\(',
            r'DataFrame\s*\(',
        ]
        
        for file_path in self.scan_python_files(project_root / 'src'):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                for line_num, line in enumerate(lines, 1):
                    for pattern in conversion_patterns:
                        if re.search(pattern, line):
                            print(f"  {file_path}:{line_num}")
                            print(f"    {line.strip()}")
                            
            except Exception as e:
                continue
    
    def run_check(self):
        """运行完整检查"""
        print("开始扫描项目文件...")
        
        # 扫描src目录
        src_files = self.scan_python_files(project_root / 'src')
        print(f"找到 {len(src_files)} 个Python文件")
        
        # 提取类方法定义
        print("提取类方法定义...")
        for file_path in src_files:
            self.extract_class_methods(file_path)
        
        print(f"找到 {len(self.method_definitions)} 个类")
        
        # 查找方法调用
        print("查找方法调用...")
        for file_path in src_files:
            self.find_method_calls(file_path)
        
        print(f"找到 {len(self.method_calls)} 个方法调用")
        
        # 执行检查
        self.check_preview_related_methods()
        self.check_dataframe_conversions()

def main():
    """主函数"""
    print("开始检查项目方法调用一致性...")
    
    checker = MethodCallChecker()
    checker.run_check()
    
    print("\n检查完成！")

if __name__ == "__main__":
    main()
