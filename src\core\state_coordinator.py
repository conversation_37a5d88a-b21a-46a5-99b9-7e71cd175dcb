"""
状态协调器 - 协调各组件与统一状态管理器的交互
"""

from typing import Dict, List, Optional, Any, Callable
from loguru import logger
from PyQt5.QtCore import QObject, pyqtSignal

from src.core.unified_state_management import (
    get_unified_state_manager, 
    StateType,
    StateChange as StateChangeEvent  # 兼容别名
)


class StateCoordinator(QObject):
    """
    状态协调器
    
    作为各UI组件与统一状态管理器之间的桥梁
    提供Qt信号支持和状态同步机制
    """
    
    # Qt信号定义
    sort_state_changed = pyqtSignal(str, list)  # table_name, sort_columns
    page_state_changed = pyqtSignal(str, int, int)  # table_name, page, page_size
    filter_state_changed = pyqtSignal(str, dict)  # table_name, filters
    selection_state_changed = pyqtSignal(str, list)  # table_name, selected_rows
    field_mapping_changed = pyqtSignal(str, dict)  # table_name, mapping
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logger
        self.state_manager = get_unified_state_manager()
        
        # 注册状态监听器
        self._register_listeners()
        
        self.logger.info("StateCoordinator 初始化完成")
    
    def _register_listeners(self):
        """注册到统一状态管理器的监听器"""
        # 排序状态监听
        self.state_manager.add_listener(
            StateType.SORT,
            self._on_sort_changed
        )
        
        # 分页状态监听
        self.state_manager.add_listener(
            StateType.PAGINATION,
            self._on_pagination_changed
        )
        
        # 过滤状态监听
        self.state_manager.add_listener(
            StateType.FILTER,
            self._on_filter_changed
        )
        
        # 选择状态监听
        self.state_manager.add_listener(
            StateType.SELECTION,
            self._on_selection_changed
        )
        
        # 字段映射监听
        self.state_manager.add_listener(
            StateType.FIELD_MAPPING,
            self._on_field_mapping_changed
        )
    
    # ========== 状态更新方法（供UI组件调用）==========
    
    def update_sort(self, table_name: str, sort_columns: List[Dict]) -> bool:
        """
        更新排序状态
        
        Args:
            table_name: 表名
            sort_columns: 排序列配置
        
        Returns:
            是否更新成功
        """
        return self.state_manager.update_sort(table_name, sort_columns, "ui")
    
    def update_page(self, table_name: str, page: int, page_size: int = None) -> bool:
        """
        更新分页状态
        
        Args:
            table_name: 表名
            page: 页码
            page_size: 页大小（可选）
        
        Returns:
            是否更新成功
        """
        return self.state_manager.update_pagination(table_name, page, page_size, "ui")
    
    def update_filter(self, table_name: str, filters: Dict) -> bool:
        """
        更新过滤状态
        
        Args:
            table_name: 表名
            filters: 过滤条件
        
        Returns:
            是否更新成功
        """
        return self.state_manager.update_filter(table_name, filters, "ui")
    
    def update_selection(self, table_name: str, selected_rows: List[int]) -> bool:
        """
        更新选择状态
        
        Args:
            table_name: 表名
            selected_rows: 选中的行索引
        
        Returns:
            是否更新成功
        """
        return self.state_manager.update_selection(table_name, selected_rows, "ui")
    
    def update_field_mapping(self, table_name: str, mapping: Dict[str, str]) -> bool:
        """
        更新字段映射
        
        Args:
            table_name: 表名
            mapping: 字段映射
        
        Returns:
            是否更新成功
        """
        return self.state_manager.update_state(
            table_name, StateType.FIELD_MAPPING, mapping, "ui"
        )
    
    # ========== 状态获取方法 ==========
    
    def get_sort_state(self, table_name: str) -> List[Dict]:
        """获取排序状态"""
        return self.state_manager.get_state_value(table_name, StateType.SORT)
    
    def get_page_state(self, table_name: str) -> Dict[str, int]:
        """获取分页状态"""
        return self.state_manager.get_state_value(table_name, StateType.PAGINATION)
    
    def get_filter_state(self, table_name: str) -> Dict:
        """获取过滤状态"""
        return self.state_manager.get_state_value(table_name, StateType.FILTER)
    
    def get_selection_state(self, table_name: str) -> List[int]:
        """获取选择状态"""
        return self.state_manager.get_state_value(table_name, StateType.SELECTION)
    
    def get_field_mapping(self, table_name: str) -> Dict[str, str]:
        """获取字段映射"""
        return self.state_manager.get_state_value(table_name, StateType.FIELD_MAPPING)
    
    def get_complete_state(self, table_name: str) -> Dict[str, Any]:
        """
        获取表的完整状态
        
        Args:
            table_name: 表名
        
        Returns:
            包含所有状态的字典
        """
        state = self.state_manager.get_table_state(table_name)
        return {
            'sort': state.sort_columns,
            'pagination': {
                'page': state.current_page,
                'page_size': state.page_size,
                'total_records': state.total_records
            },
            'filter': state.active_filters,
            'selection': state.selected_rows,
            'field_mapping': state.field_mapping,
            'ui': {
                'column_widths': state.column_widths,
                'visible_fields': state.visible_fields
            }
        }
    
    # ========== 监听器回调 ==========
    
    def _on_sort_changed(self, table_name: str, old_value: Any, new_value: Any):
        """排序状态变更回调"""
        self.logger.debug(f"排序状态变更: {table_name}")
        self.sort_state_changed.emit(table_name, new_value)
    
    def _on_pagination_changed(self, table_name: str, old_value: Any, new_value: Any):
        """分页状态变更回调"""
        self.logger.debug(f"分页状态变更: {table_name}")
        if isinstance(new_value, dict):
            self.page_state_changed.emit(
                table_name,
                new_value.get('page', 1),
                new_value.get('page_size', 50)
            )
    
    def _on_filter_changed(self, table_name: str, old_value: Any, new_value: Any):
        """过滤状态变更回调"""
        self.logger.debug(f"过滤状态变更: {table_name}")
        self.filter_state_changed.emit(table_name, new_value)
    
    def _on_selection_changed(self, table_name: str, old_value: Any, new_value: Any):
        """选择状态变更回调"""
        self.logger.debug(f"选择状态变更: {table_name}")
        self.selection_state_changed.emit(table_name, new_value)
    
    def _on_field_mapping_changed(self, table_name: str, old_value: Any, new_value: Any):
        """字段映射变更回调"""
        self.logger.debug(f"字段映射变更: {table_name}")
        self.field_mapping_changed.emit(table_name, new_value)
    
    # ========== 批量操作 ==========
    
    def sync_table_state(self, table_name: str, state_dict: Dict[str, Any]) -> bool:
        """
        同步表的完整状态
        
        Args:
            table_name: 表名
            state_dict: 状态字典
        
        Returns:
            是否同步成功
        """
        try:
            # 批量更新各种状态
            if 'sort' in state_dict:
                self.update_sort(table_name, state_dict['sort'])
            
            if 'pagination' in state_dict:
                pag = state_dict['pagination']
                self.update_page(table_name, pag.get('page', 1), pag.get('page_size'))
            
            if 'filter' in state_dict:
                self.update_filter(table_name, state_dict['filter'])
            
            if 'selection' in state_dict:
                self.update_selection(table_name, state_dict['selection'])
            
            if 'field_mapping' in state_dict:
                self.update_field_mapping(table_name, state_dict['field_mapping'])
            
            self.logger.info(f"同步表状态成功: {table_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"同步表状态失败: {e}")
            return False
    
    def reset_table_state(self, table_name: str) -> bool:
        """
        重置表状态到默认值
        
        Args:
            table_name: 表名
        
        Returns:
            是否重置成功
        """
        try:
            self.state_manager.clear_table_state(table_name)
            
            # 重新创建默认状态
            self.state_manager.get_table_state(table_name)
            
            # 发送重置信号
            self.sort_state_changed.emit(table_name, [])
            self.page_state_changed.emit(table_name, 1, 50)
            self.filter_state_changed.emit(table_name, {})
            self.selection_state_changed.emit(table_name, [])
            
            self.logger.info(f"重置表状态: {table_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"重置表状态失败: {e}")
            return False
    
    # ========== 调试和统计 ==========
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.state_manager.get_statistics()
    
    def get_change_history(self, table_name: Optional[str] = None) -> List[StateChangeEvent]:
        """获取变更历史"""
        return self.state_manager.get_change_history(table_name)
    
    def save_all_states(self) -> bool:
        """保存所有状态"""
        return self.state_manager.save_states()


# 全局实例
_state_coordinator = None

def get_state_coordinator(parent=None) -> StateCoordinator:
    """获取状态协调器的全局实例"""
    global _state_coordinator
    if _state_coordinator is None:
        _state_coordinator = StateCoordinator(parent)
    return _state_coordinator