"""
统一字段映射服务 - 所有字段映射操作的唯一入口
协调FieldMappingManager、ConfigSyncManager等组件
"""

from typing import Dict, List, Optional, Any, Tuple
from loguru import logger
import threading
from datetime import datetime

from src.core.field_mapping_manager import FieldMappingManager
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.format_management.field_registry import FieldRegistry


class UnifiedMappingService:
    """统一字段映射服务 - 轻量级协调器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式确保全局唯一实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化服务（只执行一次）"""
        if not hasattr(self, '_initialized'):
            self.logger = logger
            
            # 初始化各个管理器
            self.field_mapping_manager = FieldMappingManager()
            self.config_sync_manager = ConfigSyncManager()
            self.field_registry = FieldRegistry(mapping_path='state/data/field_mappings.json')
            
            # 缓存
            self._cache = {}
            self._cache_lock = threading.RLock()
            
            self._initialized = True
            self.logger.info("UnifiedMappingService 初始化完成")
    
    def get_field_mapping(self, table_name: str, context: Dict[str, Any] = None) -> Dict[str, str]:
        """
        获取字段映射 - 统一入口
        
        Args:
            table_name: 表名
            context: 上下文信息，包含：
                - source: 调用来源（'import_dialog', 'data_view', 'report', etc）
                - force_refresh: 是否强制刷新缓存
                - include_hidden: 是否包含隐藏字段
        
        Returns:
            字段映射字典 {db_field: display_name}
        """
        try:
            context = context or {}
            force_refresh = context.get('force_refresh', False)
            
            # 检查缓存
            cache_key = f"mapping_{table_name}"
            if not force_refresh and cache_key in self._cache:
                self.logger.debug(f"使用缓存的字段映射: {table_name}")
                return self._cache[cache_key]
            
            # 通过FieldMappingManager获取映射（已实现优先级机制）
            mapping = self.field_mapping_manager.get_field_mapping(table_name)
            
            if mapping:
                # 根据上下文过滤
                if not context.get('include_hidden', False):
                    hidden_fields = self.field_mapping_manager.get_hidden_fields(table_name)
                    mapping = {k: v for k, v in mapping.items() if k not in hidden_fields}
                
                # 更新缓存
                with self._cache_lock:
                    self._cache[cache_key] = mapping
                
                self.logger.debug(f"获取字段映射成功: {table_name}, 共 {len(mapping)} 个字段")
            else:
                self.logger.warning(f"未找到表 {table_name} 的字段映射")
                mapping = {}
            
            return mapping
            
        except Exception as e:
            self.logger.error(f"获取字段映射失败 {table_name}: {e}")
            return {}
    
    def save_user_mapping(self, table_name: str, config: Dict[str, Any]) -> bool:
        """
        保存用户配置的映射
        
        Args:
            table_name: 表名
            config: 用户配置，包含field_mapping、is_change_table等
        
        Returns:
            是否保存成功
        """
        try:
            # 清理缓存
            self._clear_cache(table_name)
            
            # 保存到FieldMappingManager（最高优先级）
            success = self.field_mapping_manager.save_user_import_config(table_name, config)
            
            if success:
                # 同步到ConfigSyncManager（如果有save_mapping方法）
                try:
                    if hasattr(self.config_sync_manager, 'save_mapping'):
                        self.config_sync_manager.save_mapping(
                            table_name=table_name,
                            mapping=config.get('field_mapping', {}),
                            metadata={'source': 'user_import'}
                        )
                except Exception as e:
                    self.logger.debug(f"同步到ConfigSyncManager失败: {e}")
                
                self.logger.info(f"保存用户映射成功: {table_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"保存用户映射失败 {table_name}: {e}")
            return False
    
    def validate_mapping(self, table_name: str, data_columns: List[str]) -> Dict[str, Any]:
        """
        验证映射完整性
        
        Args:
            table_name: 表名
            data_columns: 实际数据列
        
        Returns:
            验证结果
        """
        try:
            # 使用FieldMappingManager的验证功能
            result = self.field_mapping_manager.validate_mapping(table_name, data_columns)
            
            # 补充建议
            if not result.get('is_valid'):
                missing = result.get('missing_mappings', [])
                if missing:
                    # 尝试生成智能建议
                    suggestions = self._generate_mapping_suggestions(missing)
                    result['suggestions'].extend(suggestions)
            
            return result
            
        except Exception as e:
            self.logger.error(f"验证映射失败 {table_name}: {e}")
            return {
                'is_valid': False,
                'error': str(e)
            }
    
    def get_display_name(self, table_name: str, db_field: str) -> str:
        """
        获取字段的显示名称
        
        Args:
            table_name: 表名
            db_field: 数据库字段名
        
        Returns:
            显示名称
        """
        try:
            mapping = self.get_field_mapping(table_name)
            return mapping.get(db_field, db_field)
            
        except Exception as e:
            self.logger.error(f"获取显示名称失败 {table_name}.{db_field}: {e}")
            return db_field
    
    def get_field_type(self, table_name: str, db_field: str) -> str:
        """
        获取字段类型
        
        Args:
            table_name: 表名
            db_field: 数据库字段名
        
        Returns:
            字段类型
        """
        try:
            field_types = self.field_mapping_manager.get_field_types(table_name)
            return field_types.get(db_field, 'string')
            
        except Exception as e:
            self.logger.error(f"获取字段类型失败 {table_name}.{db_field}: {e}")
            return 'string'
    
    def is_change_table(self, table_name: str) -> bool:
        """
        判断是否为异动表
        
        Args:
            table_name: 表名
        
        Returns:
            是否为异动表
        """
        try:
            # 优先检查用户配置
            user_configs = self.field_mapping_manager._config.get('user_import_configs', {})
            if table_name in user_configs:
                return user_configs[table_name].get('is_change_table', False)
            
            # 其次检查表名特征
            return 'change_data' in table_name.lower()
            
        except Exception as e:
            self.logger.error(f"判断异动表失败 {table_name}: {e}")
            return False
    
    def batch_get_mappings(self, table_names: List[str]) -> Dict[str, Dict[str, str]]:
        """
        批量获取多个表的映射
        
        Args:
            table_names: 表名列表
        
        Returns:
            {table_name: mapping} 字典
        """
        results = {}
        for table_name in table_names:
            results[table_name] = self.get_field_mapping(table_name)
        return results
    
    def _generate_mapping_suggestions(self, missing_fields: List[str]) -> List[str]:
        """生成映射建议"""
        suggestions = []
        
        # 常见字段的建议映射
        common_mappings = {
            'salary': '工资',
            'bonus': '奖金',
            'allowance': '津贴',
            'deduction': '扣除',
            'net_pay': '实发',
            'gross_pay': '应发',
            'tax': '税',
            'insurance': '保险'
        }
        
        for field in missing_fields:
            for key, value in common_mappings.items():
                if key in field.lower():
                    suggestions.append(f"建议将 '{field}' 映射为 '{value}'")
                    break
        
        return suggestions
    
    def _clear_cache(self, table_name: Optional[str] = None):
        """清理缓存"""
        with self._cache_lock:
            if table_name:
                # 清理特定表的缓存
                keys_to_remove = [k for k in self._cache.keys() if table_name in k]
                for key in keys_to_remove:
                    self._cache.pop(key, None)
                self.logger.debug(f"清理缓存: {table_name}")
            else:
                # 清理全部缓存
                self._cache.clear()
                self.logger.debug("清理全部缓存")
    
    def reload_configurations(self):
        """重新加载所有配置"""
        try:
            # 清理缓存
            self._clear_cache()
            
            # 重新加载各管理器的配置
            self.field_mapping_manager.reload_mappings()
            self.config_sync_manager.reload_config()
            
            self.logger.info("配置重新加载完成")
            
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            config = self.field_mapping_manager._config
            
            stats = {
                'total_tables': len(config.get('table_mappings', {})),
                'user_configured_tables': len(config.get('user_import_configs', {})),
                'cache_size': len(self._cache),
                'change_tables': sum(1 for t in config.get('user_import_configs', {}).values() 
                                   if t.get('is_change_table', False))
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}


# 全局实例
_unified_mapping_service = None

def get_unified_mapping_service() -> UnifiedMappingService:
    """获取统一映射服务的全局实例"""
    global _unified_mapping_service
    if _unified_mapping_service is None:
        _unified_mapping_service = UnifiedMappingService()
    return _unified_mapping_service