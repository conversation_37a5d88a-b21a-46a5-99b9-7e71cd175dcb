"""
拖拽排序表格组件
P3-001-3: 实现表格行的拖拽排序功能

功能特性:
- 行级拖拽排序
- 视觉反馈和动画效果
- 拖拽约束和验证
- 排序事件通知
- 现代化样式
"""

import sys
from typing import List, Dict, Any, Optional, Tuple
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QPushButton,
    QLabel, QFrame, QMessageBox, QAbstractItemView, QToolTip
)
from PyQt5.QtCore import (
    Qt, QMimeData, QPoint, QRect, QPropertyAnimation, QEasingCurve,
    pyqtSignal, QTimer, QObject
)
from src.utils.thread_safe_timer import safe_single_shot
from PyQt5.QtGui import (
    QDrag, QPainter, QPixmap, QPalette, QColor, QFont, QCursor
)
import logging
from src.utils.log_config import setup_logger

# 设置日志
logger = setup_logger(__name__)


class DragDropTableWidget(QTableWidget):
    """支持拖拽排序的表格组件"""
    
    # 信号定义
    rowsReordered = pyqtSignal(int, int)  # 行重排序信号 (from_row, to_row)
    dragStarted = pyqtSignal(int)  # 拖拽开始信号
    dragEnded = pyqtSignal(bool)  # 拖拽结束信号 (success)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_drag_drop()
        
        # 拖拽状态
        self.drag_start_row = -1
        self.drag_target_row = -1
        self.is_dragging = False
        self.drag_preview_row = -1
        
        # 动画
        self.drop_animation = None
        self.preview_timer = QTimer()
        self.preview_timer.timeout.connect(self.update_drag_preview)
        
        logger.info("拖拽排序表格组件初始化完成")
    
    def setup_ui(self):
        """设置UI样式"""
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        
        # 设置表头
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setDefaultAlignment(Qt.AlignLeft)
        
        # 现代化样式
        self.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #e3f2fd;
                font-size: 12px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            
            QTableWidget::item:hover {
                background-color: #f5f5f5;
            }
            
            QHeaderView::section {
                background-color: #fafafa;
                border: none;
                border-right: 1px solid #e0e0e0;
                padding: 10px;
                font-weight: bold;
                color: #424242;
            }
            
            /* 拖拽预览样式 */
            QTableWidget[dragging="true"] {
                border: 2px solid #2196f3;
            }
            
            QTableWidget::item[drag_target="true"] {
                background-color: #bbdefb;
                border: 2px dashed #2196f3;
            }
        """)
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)
        self.setDragDropMode(QAbstractItemView.InternalMove)
        self.setDefaultDropAction(Qt.MoveAction)
    
    def startDrag(self, supportedActions):
        """开始拖拽"""
        try:
            current_row = self.currentRow()
            if current_row < 0:
                return
                
            self.drag_start_row = current_row
            self.is_dragging = True
            
            # 发射拖拽开始信号
            self.dragStarted.emit(current_row)
            
            # 创建拖拽对象
            drag = QDrag(self)
            mime_data = QMimeData()
            mime_data.setText(f"row:{current_row}")
            drag.setMimeData(mime_data)
            
            # 创建拖拽预览图
            pixmap = self.create_drag_pixmap(current_row)
            drag.setPixmap(pixmap)
            drag.setHotSpot(QPoint(pixmap.width() // 2, pixmap.height() // 2))
            
            # 设置拖拽样式
            self.setProperty("dragging", True)
            self.style().unpolish(self)
            self.style().polish(self)
            
            # 开始预览更新
            self.preview_timer.start(50)
            
            # 执行拖拽
            result = drag.exec_(Qt.MoveAction)
            
            # 清理拖拽状态
            self.cleanup_drag_state()
            
            # 发射拖拽结束信号
            success = result == Qt.MoveAction
            self.dragEnded.emit(success)
            
            logger.info(f"拖拽操作完成: 从行{current_row}, 成功={success}")
            
        except Exception as e:
            logger.error(f"拖拽开始失败: {e}")
            self.cleanup_drag_state()
    
    def create_drag_pixmap(self, row: int) -> QPixmap:
        """创建拖拽预览图"""
        try:
            # 获取行的可视区域
            rect = self.visualRect(self.model().index(row, 0))
            for col in range(1, self.columnCount()):
                rect = rect.united(self.visualRect(self.model().index(row, col)))
            
            # 创建预览图
            pixmap = QPixmap(rect.size())
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            painter.setOpacity(0.8)
            
            # 绘制背景
            painter.fillRect(pixmap.rect(), QColor(33, 150, 243, 100))
            
            # 绘制边框
            painter.setPen(QColor(33, 150, 243))
            painter.drawRect(pixmap.rect().adjusted(0, 0, -1, -1))
            
            # 绘制行内容
            painter.setPen(Qt.black)
            painter.setFont(self.font())
            
            y_offset = 0
            for col in range(self.columnCount()):
                item = self.item(row, col)
                if item:
                    text = item.text()
                    text_rect = QRect(10 + col * 100, y_offset + 5, 90, 20)
                    painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, text)
            
            painter.end()
            return pixmap
            
        except Exception as e:
            logger.error(f"创建拖拽预览图失败: {e}")
            # 返回默认预览图
            pixmap = QPixmap(200, 30)
            pixmap.fill(QColor(33, 150, 243, 100))
            return pixmap
    
    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasText() and event.mimeData().text().startswith("row:"):
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def dragMoveEvent(self, event):
        """拖拽移动事件"""
        if event.mimeData().hasText() and event.mimeData().text().startswith("row:"):
            # 获取目标行
            target_row = self.rowAt(event.pos().y())
            if target_row >= 0 and target_row != self.drag_start_row:
                self.drag_target_row = target_row
                self.update_drop_indicator(target_row)
                event.acceptProposedAction()
            else:
                self.clear_drop_indicator()
                event.ignore()
        else:
            event.ignore()
    
    def dropEvent(self, event):
        """拖拽放置事件"""
        try:
            if not (event.mimeData().hasText() and event.mimeData().text().startswith("row:")):
                event.ignore()
                return
            
            # 获取源行和目标行
            source_row = self.drag_start_row
            target_row = self.rowAt(event.pos().y())
            
            if target_row < 0 or source_row < 0 or source_row == target_row:
                event.ignore()
                return
            
            # 执行行移动
            success = self.move_row(source_row, target_row)
            
            if success:
                event.acceptProposedAction()
                # 发射重排序信号
                self.rowsReordered.emit(source_row, target_row)
                logger.info(f"行重排序成功: {source_row} -> {target_row}")
            else:
                event.ignore()
                logger.warning(f"行重排序失败: {source_row} -> {target_row}")
                
        except Exception as e:
            logger.error(f"拖拽放置失败: {e}")
            event.ignore()
        finally:
            self.clear_drop_indicator()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.clear_drop_indicator()
        super().dragLeaveEvent(event)
    
    def move_row(self, from_row: int, to_row: int) -> bool:
        """移动行"""
        try:
            if from_row < 0 or to_row < 0 or from_row >= self.rowCount() or to_row >= self.rowCount():
                return False
            
            # 保存源行数据
            row_data = []
            for col in range(self.columnCount()):
                item = self.item(from_row, col)
                if item:
                    row_data.append(item.clone())
                else:
                    row_data.append(None)
            
            # 删除源行
            self.removeRow(from_row)
            
            # 调整目标行索引
            if to_row > from_row:
                to_row -= 1
            
            # 插入新行
            self.insertRow(to_row)
            
            # 恢复数据
            for col, item in enumerate(row_data):
                if item:
                    self.setItem(to_row, col, item)
            
            # 选中移动后的行
            self.selectRow(to_row)
            
            return True
            
        except Exception as e:
            logger.error(f"移动行失败: {e}")
            return False
    
    def update_drop_indicator(self, target_row: int):
        """更新放置指示器"""
        try:
            # 清除之前的指示器
            self.clear_drop_indicator()
            
            # 设置新的指示器
            for col in range(self.columnCount()):
                item = self.item(target_row, col)
                if item:
                    item.setData(Qt.UserRole + 1, "drag_target")
            
            self.drag_preview_row = target_row
            self.update()
            
        except Exception as e:
            logger.error(f"更新放置指示器失败: {e}")
    
    def clear_drop_indicator(self):
        """清除放置指示器"""
        try:
            if self.drag_preview_row >= 0:
                for col in range(self.columnCount()):
                    item = self.item(self.drag_preview_row, col)
                    if item:
                        item.setData(Qt.UserRole + 1, None)
                
                self.drag_preview_row = -1
                self.update()
                
        except Exception as e:
            logger.error(f"清除放置指示器失败: {e}")
    
    def update_drag_preview(self):
        """更新拖拽预览"""
        if self.is_dragging:
            cursor_pos = self.mapFromGlobal(QCursor.pos())
            target_row = self.rowAt(cursor_pos.y())
            
            if target_row >= 0 and target_row != self.drag_preview_row:
                self.update_drop_indicator(target_row)
    
    def cleanup_drag_state(self):
        """清理拖拽状态"""
        self.is_dragging = False
        self.drag_start_row = -1
        self.drag_target_row = -1
        
        # 停止预览更新
        self.preview_timer.stop()
        
        # 清除样式
        self.setProperty("dragging", False)
        self.style().unpolish(self)
        self.style().polish(self)
        
        # 清除指示器
        self.clear_drop_indicator()


class BatchOperationWidget(QWidget):
    """批量操作控制面板"""
    
    # 信号定义
    batchMoveRequested = pyqtSignal(list, int)  # 批量移动请求
    batchDeleteRequested = pyqtSignal(list)  # 批量删除请求
    batchCopyRequested = pyqtSignal(list)  # 批量复制请求
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_rows = []
        self.setup_ui()
        logger.info("批量操作控制面板初始化完成")
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 选择信息
        self.selection_label = QLabel("未选择行")
        self.selection_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 12px;
                padding: 5px;
            }
        """)
        
        # 批量操作按钮
        self.move_up_btn = QPushButton("↑ 上移")
        self.move_down_btn = QPushButton("↓ 下移")
        self.copy_btn = QPushButton("📋 复制")
        self.delete_btn = QPushButton("🗑️ 删除")
        
        # 按钮样式
        button_style = """
            QPushButton {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            QPushButton:disabled {
                background-color: #f9f9f9;
                color: #ccc;
            }
        """
        
        for btn in [self.move_up_btn, self.move_down_btn, self.copy_btn, self.delete_btn]:
            btn.setStyleSheet(button_style)
            btn.setEnabled(False)
        
        # 连接信号
        self.move_up_btn.clicked.connect(self.move_selection_up)
        self.move_down_btn.clicked.connect(self.move_selection_down)
        self.copy_btn.clicked.connect(self.copy_selection)
        self.delete_btn.clicked.connect(self.delete_selection)
        
        # 布局
        layout.addWidget(self.selection_label)
        layout.addStretch()
        layout.addWidget(self.move_up_btn)
        layout.addWidget(self.move_down_btn)
        layout.addWidget(self.copy_btn)
        layout.addWidget(self.delete_btn)
    
    def update_selection(self, selected_rows: List[int]):
        """更新选择状态"""
        self.selected_rows = selected_rows
        count = len(selected_rows)
        
        if count == 0:
            self.selection_label.setText("未选择行")
            self.set_buttons_enabled(False)
        elif count == 1:
            self.selection_label.setText(f"已选择第 {selected_rows[0] + 1} 行")
            self.set_buttons_enabled(True)
        else:
            self.selection_label.setText(f"已选择 {count} 行")
            self.set_buttons_enabled(True)
    
    def set_buttons_enabled(self, enabled: bool):
        """设置按钮启用状态"""
        self.move_up_btn.setEnabled(enabled)
        self.move_down_btn.setEnabled(enabled)
        self.copy_btn.setEnabled(enabled)
        self.delete_btn.setEnabled(enabled)
    
    def move_selection_up(self):
        """上移选择的行"""
        if self.selected_rows:
            min_row = min(self.selected_rows)
            if min_row > 0:
                target_row = min_row - 1
                self.batchMoveRequested.emit(self.selected_rows, target_row)
    
    def move_selection_down(self):
        """下移选择的行"""
        if self.selected_rows:
            # 需要从表格获取总行数
            self.batchMoveRequested.emit(self.selected_rows, -1)  # -1表示下移
    
    def copy_selection(self):
        """复制选择的行"""
        if self.selected_rows:
            self.batchCopyRequested.emit(self.selected_rows)
    
    def delete_selection(self):
        """删除选择的行"""
        if self.selected_rows:
            self.batchDeleteRequested.emit(self.selected_rows)


class DragDropTableDemo(QMainWindow):
    """拖拽排序表格演示"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_demo_data()
        logger.info("拖拽排序表格演示启动")
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("拖拽排序表格演示 - P3-001-3")
        self.setGeometry(100, 100, 800, 600)
        
        # 中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🎯 P3-001-3: 拖拽排序功能演示")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        
        # 操作说明
        info_label = QLabel("""
        📋 操作说明:
        • 选中行后拖拽到目标位置进行重排序
        • 支持多行选择和批量操作
        • 拖拽时显示预览效果和放置指示器
        • 实时反馈拖拽状态和结果
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 1px solid #c8e6c9;
                border-radius: 6px;
                padding: 10px;
                font-size: 12px;
                color: #2e7d32;
            }
        """)
        
        # 拖拽表格
        self.table = DragDropTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["姓名", "部门", "职位", "工资"])
        
        # 批量操作面板
        self.batch_widget = BatchOperationWidget()
        
        # 状态栏
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px 10px;
                font-size: 12px;
            }
        """)
        
        # 连接信号
        self.table.rowsReordered.connect(self.on_rows_reordered)
        self.table.dragStarted.connect(self.on_drag_started)
        self.table.dragEnded.connect(self.on_drag_ended)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        
        self.batch_widget.batchMoveRequested.connect(self.on_batch_move)
        self.batch_widget.batchDeleteRequested.connect(self.on_batch_delete)
        self.batch_widget.batchCopyRequested.connect(self.on_batch_copy)
        
        # 布局
        layout.addWidget(title_label)
        layout.addWidget(info_label)
        layout.addWidget(self.table)
        layout.addWidget(self.batch_widget)
        layout.addWidget(self.status_label)
    
    def setup_demo_data(self):
        """设置演示数据"""
        demo_data = [
            ["张三", "技术部", "高级工程师", "15000"],
            ["李四", "产品部", "产品经理", "12000"],
            ["王五", "设计部", "UI设计师", "10000"],
            ["赵六", "运营部", "运营专员", "8000"],
            ["钱七", "人事部", "HR专员", "7000"],
            ["孙八", "财务部", "会计师", "9000"],
            ["周九", "市场部", "市场专员", "8500"],
            ["吴十", "技术部", "前端工程师", "13000"],
        ]
        
        self.table.setRowCount(len(demo_data))
        
        for row, row_data in enumerate(demo_data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                self.table.setItem(row, col, item)
        
        # 调整列宽
        self.table.resizeColumnsToContents()
        
        logger.info(f"演示数据加载完成: {len(demo_data)}行")
    
    def on_rows_reordered(self, from_row: int, to_row: int):
        """行重排序处理"""
        self.status_label.setText(f"✅ 行重排序成功: 第{from_row + 1}行 → 第{to_row + 1}行")
        safe_single_shot(3000, lambda: self.status_label.setText("就绪"))
    
    def on_drag_started(self, row: int):
        """拖拽开始处理"""
        self.status_label.setText(f"🔄 正在拖拽第{row + 1}行...")
    
    def on_drag_ended(self, success: bool):
        """拖拽结束处理"""
        if success:
            self.status_label.setText("✅ 拖拽操作成功完成")
        else:
            self.status_label.setText("❌ 拖拽操作已取消")
        
        safe_single_shot(2000, lambda: self.status_label.setText("就绪"))
    
    def on_selection_changed(self):
        """选择变化处理"""
        selected_rows = []
        for item in self.table.selectedItems():
            row = item.row()
            if row not in selected_rows:
                selected_rows.append(row)
        
        selected_rows.sort()
        self.batch_widget.update_selection(selected_rows)
    
    def on_batch_move(self, rows: List[int], target: int):
        """批量移动处理"""
        if target == -1:  # 下移
            # 实现批量下移逻辑
            self.status_label.setText(f"批量下移 {len(rows)} 行")
        else:  # 上移或移动到指定位置
            self.status_label.setText(f"批量移动 {len(rows)} 行到位置 {target + 1}")
        
        safe_single_shot(2000, lambda: self.status_label.setText("就绪"))
    
    def on_batch_delete(self, rows: List[int]):
        """批量删除处理"""
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除选中的 {len(rows)} 行吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 从后往前删除，避免索引变化
            for row in sorted(rows, reverse=True):
                self.table.removeRow(row)
            
            self.status_label.setText(f"✅ 已删除 {len(rows)} 行")
            safe_single_shot(2000, lambda: self.status_label.setText("就绪"))
    
    def on_batch_copy(self, rows: List[int]):
        """批量复制处理"""
        # 实现批量复制逻辑
        self.status_label.setText(f"已复制 {len(rows)} 行到剪贴板")
        safe_single_shot(2000, lambda: self.status_label.setText("就绪"))


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建演示窗口
    demo = DragDropTableDemo()
    demo.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 