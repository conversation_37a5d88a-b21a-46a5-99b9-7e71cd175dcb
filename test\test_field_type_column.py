#!/usr/bin/env python3
"""
字段类型列添加功能测试脚本
测试统一数据导入配置窗口的字段类型列功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QComboBox
from PyQt5.QtCore import Qt
from PyQt5.QtTest import QTest

from src.gui.unified_data_import_window import UnifiedMappingConfigWidget


class TestFieldTypeColumn(unittest.TestCase):
    """字段类型列功能测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.widget = UnifiedMappingConfigWidget()
    
    def tearDown(self):
        """每个测试后的清理"""
        if hasattr(self, 'widget'):
            self.widget.close()
    
    def test_table_column_count(self):
        """测试表格列数是否正确"""
        # 创建映射表格
        table = self.widget._create_mapping_table()
        
        # 验证列数为7（新增了字段类型列）
        self.assertEqual(table.columnCount(), 7)
        
        # 验证列标题
        expected_headers = [
            "Excel列名", "数据库字段", "显示名称", "字段类型", "数据类型", "是否必需", "验证状态"
        ]
        for i, expected_header in enumerate(expected_headers):
            actual_header = table.horizontalHeaderItem(i).text()
            self.assertEqual(actual_header, expected_header)
    
    def test_field_type_combo_creation(self):
        """测试字段类型下拉框创建"""
        combo = self.widget._create_field_type_combo()
        
        # 验证下拉框不为空
        self.assertIsInstance(combo, QComboBox)
        self.assertGreater(combo.count(), 0)
        
        # 验证包含基本类型
        items = [combo.itemText(i) for i in range(combo.count())]
        self.assertIn("通用", items)
        self.assertIn("工资金额", items)
        self.assertIn("员工编号", items)
    
    def test_recommended_data_type(self):
        """测试推荐数据类型功能"""
        # 测试工资金额类型
        recommended = self.widget._get_recommended_data_type("salary_amount")
        self.assertEqual(recommended, "DECIMAL(10,2)")
        
        # 测试员工编号类型
        recommended = self.widget._get_recommended_data_type("employee_id")
        self.assertEqual(recommended, "VARCHAR(20)")
        
        # 测试通用类型
        recommended = self.widget._get_recommended_data_type("general")
        self.assertEqual(recommended, "VARCHAR(100)")
        
        # 测试未知类型
        recommended = self.widget._get_recommended_data_type("unknown_type")
        self.assertEqual(recommended, "VARCHAR(100)")
    
    def test_mapping_config_with_field_type(self):
        """测试映射配置包含字段类型"""
        # 模拟添加一些测试数据
        test_headers = ["工号", "姓名", "工资"]
        self.widget.load_excel_headers(test_headers, "test_table")

        # 更新映射配置
        self.widget._update_mapping_config()

        # 验证配置包含字段类型
        config = self.widget.get_mapping_config()
        for field_config in config.values():
            self.assertIn('field_type', field_config)
            self.assertIn('target_field', field_config)
            self.assertIn('display_name', field_config)
            self.assertIn('data_type', field_config)
            self.assertIn('is_required', field_config)


def run_tests():
    """运行测试"""
    print("开始测试字段类型列添加功能...")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestFieldTypeColumn)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！字段类型列功能正常。")
        return True
    else:
        print(f"\n❌ 测试失败：{len(result.failures)} 个失败，{len(result.errors)} 个错误")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
