"""
主窗口适配器 - 保持向后兼容
将拆分后的模块组合成完整的主窗口
"""

from PyQt5.QtWidgets import QVBoxLayout, QHBoxLayout, QSplitter, QWidget
from PyQt5.QtCore import Qt
from loguru import logger

# 导入核心类
from src.gui.prototype.main_window_core import PrototypeMainWindowCore
from src.gui.prototype.components.workers import Worker, PaginationWorker, WorkerSignals
from src.gui.prototype.services.data_service import DataService
from src.gui.prototype.widgets.table_core import VirtualizedExpandableTableCore

# 导入原有组件（如果存在）
try:
    from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
except ImportError:
    EnhancedNavigationPanel = None
    logger.warning("EnhancedNavigationPanel 未找到")

try:
    from src.gui.prototype.widgets.pagination_widget import PaginationWidget
except ImportError:
    PaginationWidget = None
    logger.warning("PaginationWidget 未找到")


class PrototypeMainWindow(PrototypeMainWindowCore):
    """
    完整的主窗口类
    
    通过继承核心类并添加UI组件来实现完整功能
    保持与原PrototypeMainWindow的接口兼容
    """
    
    def __init__(self):
        super().__init__()
        
        # 添加兼容性属性
        self._init_compatibility_attributes()
        
        self.logger.info("PrototypeMainWindow 适配器初始化完成")
    
    def _init_ui_components(self):
        """初始化UI组件（覆盖核心类的方法）"""
        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        self.main_layout.addWidget(main_splitter)
        
        # 左侧导航面板
        if EnhancedNavigationPanel:
            self.navigation_panel = EnhancedNavigationPanel(self)
            main_splitter.addWidget(self.navigation_panel)
        else:
            # 创建占位符
            left_widget = QWidget()
            left_widget.setMaximumWidth(250)
            main_splitter.addWidget(left_widget)
            self.navigation_panel = None
        
        # 右侧工作区
        work_area = QWidget()
        work_layout = QVBoxLayout(work_area)
        work_layout.setContentsMargins(0, 0, 0, 0)
        main_splitter.addWidget(work_area)
        
        # 创建主工作区
        self.main_workspace = MainWorkspaceArea(self)
        work_layout.addWidget(self.main_workspace)
        
        # 分页控件
        if PaginationWidget:
            self.pagination_widget = PaginationWidget(self)
            work_layout.addWidget(self.pagination_widget)
        else:
            self.pagination_widget = None
        
        # 设置分割比例
        main_splitter.setSizes([250, 950])
        
        # 创建状态栏
        self.statusBar().showMessage("就绪")
    
    def _init_compatibility_attributes(self):
        """初始化兼容性属性"""
        # 表管理器（兼容旧代码）
        self.table_manager = self.data_service.table_manager
        
        # 数据库管理器
        self.db_manager = self.data_service.db_manager
        
        # 当前表名
        self._current_table_name = ""
        
        # 扩展表格（如果存在）
        self.expandable_table = None
    
    def load_table_data(self, table_name: str):
        """
        加载表数据（兼容方法）
        
        Args:
            table_name: 表名
        """
        self.set_current_table(table_name)
        self.load_page_data(table_name)
    
    def _apply_field_mapping_to_dataframe(self, df, table_name):
        """
        应用字段映射（兼容方法）
        
        Args:
            df: 数据框
            table_name: 表名
        
        Returns:
            映射后的数据框
        """
        # 使用统一映射服务
        try:
            from src.core.unified_mapping_service import get_unified_mapping_service
            mapping_service = get_unified_mapping_service()
            mapping = mapping_service.get_field_mapping(table_name)
            
            if mapping:
                df = df.rename(columns=mapping)
            
            return df
        except Exception as e:
            self.logger.error(f"字段映射失败: {e}")
            return df
    
    def _apply_system_field_filtering(self, df, table_name):
        """
        应用系统字段过滤（兼容方法）
        
        Args:
            df: 数据框
            table_name: 表名
        
        Returns:
            过滤后的数据框
        """
        # 过滤系统字段
        system_fields = ['_id', '_created_at', '_updated_at', '_version']
        columns_to_keep = [col for col in df.columns if col not in system_fields]
        return df[columns_to_keep]
    
    def _restore_table_ui_state(self, table_name):
        """恢复表UI状态（兼容方法）"""
        # 由表格组件自己处理
        pass
    
    def get_current_table_name(self) -> str:
        """获取当前表名（兼容方法）"""
        return self.get_current_table()
    
    def set_current_table_name(self, table_name: str):
        """设置当前表名（兼容方法）"""
        self.set_current_table(table_name)


class MainWorkspaceArea(QWidget):
    """
    主工作区域
    
    包含表格和相关控件
    """
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.logger = logger
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建表格
        self.expandable_table = VirtualizedExpandableTableCore(self)
        layout.addWidget(self.expandable_table)
        
        # 保存引用（兼容性）
        main_window.expandable_table = self.expandable_table
        
        # 连接信号
        self.expandable_table.data_changed.connect(self._on_data_changed)
        self.expandable_table.sort_changed.connect(self._on_sort_changed)
        
        self.logger.debug("MainWorkspaceArea 初始化完成")
    
    def set_data(self, df=None, table_name="", **kwargs):
        """
        设置数据（兼容方法）
        
        Args:
            df: 数据框
            table_name: 表名
            **kwargs: 其他参数
        """
        if df is not None:
            self.expandable_table.set_data(df, table_name)
    
    def _on_data_changed(self):
        """处理数据变化"""
        self.logger.debug("工作区数据已更新")
    
    def _on_sort_changed(self, sort_columns):
        """处理排序变化"""
        self.logger.debug(f"排序变化: {sort_columns}")


# 导出兼容性别名
Worker = Worker
PaginationWorker = PaginationWorker
WorkerSignals = WorkerSignals