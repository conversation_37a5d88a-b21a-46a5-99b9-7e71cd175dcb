#!/usr/bin/env python3
"""
调试"另存配置"按钮识别问题

重现用户报告的问题：
1. 用户配置了字段类型
2. 点击"另存配置"按钮
3. 系统提示"没有找到任何已配置的工作表"

分析根本原因：
当用户没有手动切换工作表时，all_sheets_configs 字典始终为空
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger

def simulate_save_config_issue():
    """模拟保存配置问题"""
    logger.info("=== 模拟另存配置识别问题 ===")
    
    # 模拟 ChangeDataConfigDialog 的初始化状态
    class MockConfigDialog:
        def __init__(self):
            self.current_sheet_name = 'A岗职工'  # 初始化时设置
            self.all_sheets_configs = {}  # 空字典
            
        def get_current_configuration(self):
            """模拟获取当前配置"""
            return {
                'field_mapping': {
                    '序号': '序号',
                    '工号': '工号', 
                    '姓名': '姓名',
                    '基本工资': '基本工资'
                },
                'field_types': {
                    '序号': 'integer',
                    '工号': 'employee_id_string',
                    '姓名': 'name_string',
                    '基本工资': 'salary_float'
                }
            }
        
        def save_configuration(self):
            """模拟另存配置逻辑"""
            logger.info(f"当前工作表名称: {self.current_sheet_name}")
            logger.info(f"all_sheets_configs 初始状态: {self.all_sheets_configs}")
            
            # 原始逻辑：先保存当前工作表配置
            if hasattr(self, 'current_sheet_name') and self.current_sheet_name != 'unknown':
                try:
                    current_config = self.get_current_configuration()
                    if current_config and current_config.get('field_mapping'):
                        self.all_sheets_configs[self.current_sheet_name] = current_config
                        logger.info(f"✅ 已保存当前工作表配置到 all_sheets_configs")
                        logger.info(f"配置内容: {len(current_config.get('field_mapping', {}))} 个字段")
                except Exception as e:
                    logger.error(f"保存当前工作表配置时出错: {e}")
            
            logger.info(f"检查前 all_sheets_configs 状态: {bool(self.all_sheets_configs)}")
            logger.info(f"包含的工作表: {list(self.all_sheets_configs.keys())}")
            
            # 检查是否有配置需要保存 - 这里是问题所在！
            if not self.all_sheets_configs:
                logger.error("❌ 问题复现：没有找到任何已配置的工作表")
                return False
            else:
                logger.info("✅ 找到已配置的工作表，可以保存")
                return True
    
    # 1. 模拟用户配置了字段但未切换工作表的情况
    logger.info("\n--- 情况1：用户直接配置单个工作表 ---")
    dialog1 = MockConfigDialog()
    result1 = dialog1.save_configuration()
    
    # 2. 验证修复方案
    logger.info("\n--- 验证修复方案 ---")
    
    class FixedConfigDialog(MockConfigDialog):
        def save_configuration(self):
            """修复后的另存配置逻辑"""
            logger.info("使用修复后的逻辑...")
            
            # 修复：总是尝试保存当前配置，不管 all_sheets_configs 是否为空
            if hasattr(self, 'current_sheet_name') and self.current_sheet_name != 'unknown':
                current_config = self.get_current_configuration()
                if current_config and current_config.get('field_mapping'):
                    self.all_sheets_configs[self.current_sheet_name] = current_config
                    logger.info(f"✅ 保存当前工作表 '{self.current_sheet_name}' 配置")
            
            # 修复后的检查逻辑
            if not self.all_sheets_configs:
                logger.error("❌ 仍然没有配置（这不应该发生）")
                return False
            else:
                logger.info(f"✅ 找到 {len(self.all_sheets_configs)} 个已配置的工作表")
                return True
    
    dialog2 = FixedConfigDialog()
    result2 = dialog2.save_configuration()
    
    # 3. 总结分析
    logger.info("\n=== 问题分析总结 ===")
    logger.info("❌ 原始逻辑问题：")
    logger.info("  1. all_sheets_configs 初始化为空字典")
    logger.info("  2. 只有切换工作表时才会调用 on_sheet_changed 保存配置")
    logger.info("  3. 用户直接配置单表时，all_sheets_configs 始终为空")
    logger.info("  4. save_configuration 中虽然尝试保存当前配置，但检查在保存之后")
    
    logger.info("\n✅ 修复方案：")
    logger.info("  1. 在 save_configuration 开始时就保存当前配置")
    logger.info("  2. 确保检查 all_sheets_configs 在保存当前配置之后进行")
    logger.info("  3. 或者改进初始化逻辑，在加载工作表时就保存到 all_sheets_configs")
    
    return result1, result2

def main():
    """主函数"""
    try:
        result1, result2 = simulate_save_config_issue()
        
        print(f"\n>>> 问题复现结果: {'失败' if not result1 else '成功'}")
        print(f">>> 修复方案结果: {'成功' if result2 else '失败'}")
        
        if not result1 and result2:
            print(">>> ✅ 问题分析正确，修复方案有效")
            return 0
        else:
            print(">>> ❌ 问题分析或修复方案有误")
            return 1
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())