#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置数据结构定义

定义系统中所有配置的标准数据结构，确保配置格式的一致性
"""

from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum


class ConfigType(Enum):
    """配置类型枚举"""
    SYSTEM = "system_config"
    USER_SINGLE = "user_single_config"
    USER_MULTI = "user_multi_config"
    TEMPLATE = "template_config"


class FieldType(Enum):
    """字段类型枚举"""
    SALARY_FLOAT = "salary_float"
    EMPLOYEE_ID_STRING = "employee_id_string"
    NAME_STRING = "name_string"
    DATE_STRING = "date_string"
    ID_NUMBER_STRING = "id_number_string"
    CODE_STRING = "code_string"
    TEXT_STRING = "text_string"
    INTEGER = "integer"
    FLOAT = "float"
    PERSONNEL_CATEGORY_CODE = "personnel_category_code"


@dataclass
class FieldConfig:
    """字段配置"""
    excel_field: str  # Excel中的字段名
    db_field: str     # 数据库中的字段名
    field_type: FieldType  # 字段类型
    required: bool = False  # 是否必填
    description: str = ""   # 字段描述
    validation_rules: Dict[str, Any] = field(default_factory=dict)  # 验证规则
    formatting_rules: Dict[str, Any] = field(default_factory=dict)  # 格式化规则


@dataclass
class SheetConfig:
    """工作表配置"""
    sheet_name: str  # 工作表名称
    fields: List[FieldConfig]  # 字段配置列表
    description: str = ""  # 描述
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    
    @property
    def field_count(self) -> int:
        """字段数量"""
        return len(self.fields)
    
    @property
    def field_mapping(self) -> Dict[str, str]:
        """字段映射字典"""
        return {field.excel_field: field.db_field for field in self.fields}
    
    @property
    def field_types(self) -> Dict[str, str]:
        """字段类型字典"""
        return {field.excel_field: field.field_type.value for field in self.fields}


@dataclass
class BaseConfig:
    """基础配置类"""
    name: str  # 配置名称
    description: str = ""  # 配置描述
    version: str = "1.0"  # 配置版本
    created_at: Optional[datetime] = None  # 创建时间
    updated_at: Optional[datetime] = None  # 更新时间
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class SingleSheetConfig(BaseConfig):
    """单工作表配置"""
    config_type: ConfigType = ConfigType.USER_SINGLE
    sheet: SheetConfig = None  # 工作表配置
    
    @property
    def field_count(self) -> int:
        """字段数量"""
        return self.sheet.field_count if self.sheet else 0


@dataclass
class MultiSheetConfig(BaseConfig):
    """多工作表配置"""
    config_type: ConfigType = ConfigType.USER_MULTI
    sheets: Dict[str, SheetConfig] = field(default_factory=dict)  # 工作表配置字典
    
    @property
    def sheet_count(self) -> int:
        """工作表数量"""
        return len(self.sheets)
    
    @property
    def total_field_count(self) -> int:
        """总字段数量"""
        return sum(sheet.field_count for sheet in self.sheets.values())


@dataclass
class TemplateConfig(BaseConfig):
    """模板配置"""
    config_type: ConfigType = ConfigType.TEMPLATE
    template_type: str = "standard"  # 模板类型
    applicable_sheets: List[str] = field(default_factory=list)  # 适用的工作表类型
    default_fields: List[FieldConfig] = field(default_factory=list)  # 默认字段配置


class ConfigSerializer:
    """配置序列化器"""
    
    @staticmethod
    def to_dict(config: Union[SingleSheetConfig, MultiSheetConfig, TemplateConfig]) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        if isinstance(config, SingleSheetConfig):
            return ConfigSerializer._single_sheet_to_dict(config)
        elif isinstance(config, MultiSheetConfig):
            return ConfigSerializer._multi_sheet_to_dict(config)
        elif isinstance(config, TemplateConfig):
            return ConfigSerializer._template_to_dict(config)
        else:
            raise ValueError(f"不支持的配置类型: {type(config)}")
    
    @staticmethod
    def _single_sheet_to_dict(config: SingleSheetConfig) -> Dict[str, Any]:
        """单工作表配置转字典"""
        return {
            "name": config.name,
            "description": config.description,
            "version": config.version,
            "type": config.config_type.value,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            "metadata": config.metadata,
            "sheet": {
                "sheet_name": config.sheet.sheet_name,
                "field_count": config.sheet.field_count,
                "description": config.sheet.description,
                "field_mapping": config.sheet.field_mapping,
                "field_types": config.sheet.field_types,
                "fields": [ConfigSerializer._field_to_dict(field) for field in config.sheet.fields]
            } if config.sheet else None
        }
    
    @staticmethod
    def _multi_sheet_to_dict(config: MultiSheetConfig) -> Dict[str, Any]:
        """多工作表配置转字典"""
        return {
            "name": config.name,
            "description": config.description,
            "version": config.version,
            "type": config.config_type.value,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            "metadata": config.metadata,
            "sheet_count": config.sheet_count,
            "total_field_count": config.total_field_count,
            "sheets": {
                sheet_name: {
                    "sheet_name": sheet.sheet_name,
                    "field_count": sheet.field_count,
                    "description": sheet.description,
                    "field_mapping": sheet.field_mapping,
                    "field_types": sheet.field_types,
                    "fields": [ConfigSerializer._field_to_dict(field) for field in sheet.fields]
                }
                for sheet_name, sheet in config.sheets.items()
            }
        }
    
    @staticmethod
    def _template_to_dict(config: TemplateConfig) -> Dict[str, Any]:
        """模板配置转字典"""
        return {
            "name": config.name,
            "description": config.description,
            "version": config.version,
            "type": config.config_type.value,
            "template_type": config.template_type,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            "metadata": config.metadata,
            "applicable_sheets": config.applicable_sheets,
            "default_fields": [ConfigSerializer._field_to_dict(field) for field in config.default_fields]
        }
    
    @staticmethod
    def _field_to_dict(field: FieldConfig) -> Dict[str, Any]:
        """字段配置转字典"""
        return {
            "excel_field": field.excel_field,
            "db_field": field.db_field,
            "field_type": field.field_type.value,
            "required": field.required,
            "description": field.description,
            "validation_rules": field.validation_rules,
            "formatting_rules": field.formatting_rules
        }


class ConfigDeserializer:
    """配置反序列化器"""
    
    @staticmethod
    def from_dict(data: Dict[str, Any]) -> Union[SingleSheetConfig, MultiSheetConfig, TemplateConfig]:
        """从字典创建配置对象"""
        config_type = data.get("type", "")
        
        if config_type == ConfigType.USER_SINGLE.value:
            return ConfigDeserializer._dict_to_single_sheet(data)
        elif config_type == ConfigType.USER_MULTI.value:
            return ConfigDeserializer._dict_to_multi_sheet(data)
        elif config_type == ConfigType.TEMPLATE.value:
            return ConfigDeserializer._dict_to_template(data)
        else:
            # 兼容旧格式，尝试自动识别
            if "sheets" in data and isinstance(data["sheets"], dict):
                return ConfigDeserializer._dict_to_multi_sheet(data)
            elif "sheet" in data:
                return ConfigDeserializer._dict_to_single_sheet(data)
            else:
                raise ValueError(f"无法识别的配置格式: {config_type}")
    
    @staticmethod
    def _dict_to_single_sheet(data: Dict[str, Any]) -> SingleSheetConfig:
        """字典转单工作表配置"""
        sheet_data = data.get("sheet", {})
        sheet = ConfigDeserializer._dict_to_sheet(sheet_data) if sheet_data else None
        
        return SingleSheetConfig(
            name=data.get("name", ""),
            description=data.get("description", ""),
            version=data.get("version", "1.0"),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
            updated_at=datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else None,
            metadata=data.get("metadata", {}),
            sheet=sheet
        )
    
    @staticmethod
    def _dict_to_multi_sheet(data: Dict[str, Any]) -> MultiSheetConfig:
        """字典转多工作表配置"""
        sheets_data = data.get("sheets", {})
        sheets = {
            sheet_name: ConfigDeserializer._dict_to_sheet(sheet_data)
            for sheet_name, sheet_data in sheets_data.items()
        }
        
        return MultiSheetConfig(
            name=data.get("name", ""),
            description=data.get("description", ""),
            version=data.get("version", "1.0"),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
            updated_at=datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else None,
            metadata=data.get("metadata", {}),
            sheets=sheets
        )
    
    @staticmethod
    def _dict_to_template(data: Dict[str, Any]) -> TemplateConfig:
        """字典转模板配置"""
        default_fields = [
            ConfigDeserializer._dict_to_field(field_data)
            for field_data in data.get("default_fields", [])
        ]
        
        return TemplateConfig(
            name=data.get("name", ""),
            description=data.get("description", ""),
            version=data.get("version", "1.0"),
            template_type=data.get("template_type", "standard"),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
            updated_at=datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else None,
            metadata=data.get("metadata", {}),
            applicable_sheets=data.get("applicable_sheets", []),
            default_fields=default_fields
        )
    
    @staticmethod
    def _dict_to_sheet(data: Dict[str, Any]) -> SheetConfig:
        """字典转工作表配置"""
        # 兼容旧格式：从field_mapping和field_types构建fields
        if "fields" in data:
            fields = [ConfigDeserializer._dict_to_field(field_data) for field_data in data["fields"]]
        else:
            # 兼容旧格式
            field_mapping = data.get("field_mapping", {})
            field_types = data.get("field_types", {})
            fields = []
            
            for excel_field, db_field in field_mapping.items():
                field_type_str = field_types.get(excel_field, "text_string")
                try:
                    field_type = FieldType(field_type_str)
                except ValueError:
                    field_type = FieldType.TEXT_STRING
                
                fields.append(FieldConfig(
                    excel_field=excel_field,
                    db_field=db_field,
                    field_type=field_type
                ))
        
        return SheetConfig(
            sheet_name=data.get("sheet_name", ""),
            description=data.get("description", ""),
            metadata=data.get("metadata", {}),
            fields=fields
        )
    
    @staticmethod
    def _dict_to_field(data: Dict[str, Any]) -> FieldConfig:
        """字典转字段配置"""
        field_type_str = data.get("field_type", "text_string")
        try:
            field_type = FieldType(field_type_str)
        except ValueError:
            field_type = FieldType.TEXT_STRING
        
        return FieldConfig(
            excel_field=data.get("excel_field", ""),
            db_field=data.get("db_field", ""),
            field_type=field_type,
            required=data.get("required", False),
            description=data.get("description", ""),
            validation_rules=data.get("validation_rules", {}),
            formatting_rules=data.get("formatting_rules", {})
        )


# 导出的类和函数
__all__ = [
    "ConfigType",
    "FieldType", 
    "FieldConfig",
    "SheetConfig",
    "BaseConfig",
    "SingleSheetConfig",
    "MultiSheetConfig",
    "TemplateConfig",
    "ConfigSerializer",
    "ConfigDeserializer"
]
