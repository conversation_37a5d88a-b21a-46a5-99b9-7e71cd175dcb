#!/usr/bin/env python3
"""
测试表格单元格内边距修复效果
验证表格单元格内边距是否从8px减小到1px
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_table_padding_fix():
    """测试表格单元格内边距修复"""
    print("🔍 测试表格单元格内边距修复...")
    
    try:
        # 检查_create_mapping_table方法中的样式设置
        import inspect
        from src.gui.unified_data_import_window import UnifiedMappingConfigWidget
        
        # 验证表格创建方法存在
        assert hasattr(UnifiedMappingConfigWidget, '_create_mapping_table'), "缺少 _create_mapping_table 方法"
        print("✅ _create_mapping_table 方法存在")
        
        # 检查表格样式设置
        table_source = inspect.getsource(UnifiedMappingConfigWidget._create_mapping_table)
        assert "padding: 1px;" in table_source, "表格单元格内边距应该是1px"
        assert "padding: 8px;" not in table_source, "不应该还有8px的内边距"
        print("✅ 表格单元格内边距已从8px改为1px")
        
        # 检查下拉框样式设置
        style_source = inspect.getsource(UnifiedMappingConfigWidget._setup_table_combo_style)
        assert "margin: 0px;" in style_source, "下拉框边距应该是0px"
        assert "setFixedHeight(33)" in style_source, "下拉框高度应该是33px"
        print("✅ 下拉框边距0px，高度33px")
        
        print("\n🎉 表格单元格内边距修复验证通过！")
        print("📋 关键修复：")
        print("   - 表格单元格内边距：8px → 1px")
        print("   - 下拉框边距：1px → 0px")
        print("   - 下拉框高度：33px（固定）")
        print("   - 表格行高：35px")
        
        print("\n📊 空间计算：")
        print("   表格行高：35px")
        print("   ├── 单元格上内边距：1px")
        print("   ├── 下拉框：33px")
        print("   └── 单元格下内边距：1px")
        print("   总计：1 + 33 + 1 = 35px ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_padding_fix_summary():
    """显示内边距修复总结"""
    print("\n" + "="*60)
    print("🔧 表格单元格内边距问题修复总结")
    print("="*60)
    
    print("\n🐛 问题根源发现：")
    print("   表格单元格有默认的8px内边距设置")
    print("   这是导致下拉框与单元格边缘有明显间距的真正原因")
    
    print("\n🔍 问题定位：")
    print("   在_create_mapping_table方法的样式设置中：")
    print("   QTableWidget::item { padding: 8px; }")
    print("   ↑ 这个8px内边距导致了5px左右的视觉间距")
    
    print("\n✅ 根本性修复：")
    print("   1. 表格单元格内边距：8px → 1px")
    print("   2. 下拉框边距：1px → 0px")
    print("   3. 保持下拉框高度：33px")
    print("   4. 保持表格行高：35px")
    
    print("\n📐 精确空间分配：")
    print("   ┌─────────────────────────────────┐")
    print("   │ 表格行高：35px                  │")
    print("   │ ┌─────────────────────────────┐ │")
    print("   │ │ 单元格上内边距：1px         │ │")
    print("   │ │ ┌─────────────────────────┐ │ │")
    print("   │ │ │ 下拉框：33px            │ │ │")
    print("   │ │ └─────────────────────────┘ │ │")
    print("   │ │ 单元格下内边距：1px         │ │")
    print("   │ └─────────────────────────────┘ │")
    print("   └─────────────────────────────────┘")
    
    print("\n🎯 预期效果：")
    print("   - 下拉框上边框几乎贴近单元格顶部线框")
    print("   - 下拉框下边框几乎贴近单元格底部线框")
    print("   - 只有1px的最小必要间距")
    print("   - 最大化下拉框的可用显示空间")

if __name__ == "__main__":
    success = test_table_padding_fix()
    show_padding_fix_summary()
    
    if success:
        print("\n🚀 表格内边距修复完成！")
        print("   现在下拉框应该几乎贴近单元格边缘，间距最小化。")
        print("   请重新启动系统测试效果。")
    else:
        print("\n⚠️  修复验证失败，需要检查代码...")
    
    sys.exit(0 if success else 1)
