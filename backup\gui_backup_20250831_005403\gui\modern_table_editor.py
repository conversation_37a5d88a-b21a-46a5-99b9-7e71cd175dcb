"""
月度工资异动处理系统 - 现代化表格编辑器

本模块实现Phase 3的高级表格编辑功能，提供现代化的数据编辑体验。

主要功能:
1. 单元格双击编辑
2. 行内验证和错误提示
3. 批量编辑选择
4. 撤销/重做功能
5. 拖拽排序
6. 批量操作
7. 快捷键支持
8. 右键菜单
"""

import sys
from typing import List, Dict, Any, Optional, Union, Tuple
from PyQt5.QtWidgets import (
    QTableWidget, QTableWidgetItem, QWidget, QVBoxLayout, QHBoxLayout,
    QHeaderView, QAbstractItemView, QMenu, QAction, QMessageBox,
    QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit,
    QCheckBox, QPushButton, QLabel, QFrame, QSplitter, QGroupBox,
    QToolBar, QToolButton, QButtonGroup, QApplication, QDialog,
    QDialogButtonBox, QFormLayout, QTextEdit, QProgressBar
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QPoint, QRect, QTimer, QPropertyAnimation,
    QEasingCurve, QAbstractAnimation, QModelIndex, QMimeData
)
from PyQt5.QtGui import (
    QFont, QColor, QBrush, QCursor, QPalette, QIcon, QPixmap,
    QKeySequence, QDrag, QPainter, QFontMetrics
)

from src.utils.log_config import setup_logger
from src.gui.widgets import DataTableWidget
from src.gui.toast_system import ToastManager


class EditableTableWidget(DataTableWidget):
    """
    可编辑表格控件
    
    扩展基础表格控件，添加高级编辑功能。
    """
    
    # 信号定义
    cell_edited = pyqtSignal(int, int, str, str)  # 单元格编辑信号 (row, col, old_value, new_value)
    batch_edit_requested = pyqtSignal(list)       # 批量编辑请求信号
    validation_error = pyqtSignal(int, int, str)   # 验证错误信号
    undo_requested = pyqtSignal()                  # 撤销请求信号
    redo_requested = pyqtSignal()                  # 重做请求信号
    
    def __init__(self, parent=None, headers: Optional[List[str]] = None):
        """初始化可编辑表格"""
        super().__init__(parent, headers)
        self.logger = setup_logger(__name__)
        
        # 编辑状态管理
        self.is_editing = False
        self.current_editor = None
        self.edit_row = -1
        self.edit_col = -1
        
        # 批量选择管理
        self.batch_selection_mode = False
        self.selected_cells = set()
        
        # 撤销/重做管理
        self.undo_stack = []
        self.redo_stack = []
        self.max_undo_steps = 50
        
        # 拖拽状态
        self.drag_start_position = None
        self.drag_enabled = True
        
        # 数据验证器
        self.validators = {}  # 列索引 -> 验证函数
        self.error_cells = set()  # 错误单元格
        
        # 初始化编辑功能
        self._init_editing_features()
        
        # 设置现代化样式
        self._set_modern_style()
        
        self.logger.info("现代化表格编辑器初始化完成")
    
    def _init_editing_features(self):
        """初始化编辑功能"""
        # 启用编辑
        self.setEditTriggers(QAbstractItemView.DoubleClicked | 
                           QAbstractItemView.SelectedClicked |
                           QAbstractItemView.EditKeyPressed)
        
        # 连接编辑信号
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
        self.itemChanged.connect(self._on_item_edited)
        
        # 设置键盘快捷键
        self._setup_shortcuts()
        
        # 设置拖拽
        self.setDragDropMode(QAbstractItemView.InternalMove)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        
        # 设置选择模式
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)
    
    def _setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+Z 撤销
        self.setShortcut(QKeySequence.Undo, self._undo_action)
        
        # Ctrl+Y 重做
        self.setShortcut(QKeySequence.Redo, self._redo_action)
        
        # Delete 删除选中行
        self.setShortcut(QKeySequence.Delete, self._delete_selected_rows)
        
        # Ctrl+A 全选
        self.setShortcut(QKeySequence.SelectAll, self.selectAll)
        
        # Ctrl+C 复制
        self.setShortcut(QKeySequence.Copy, self._copy_selected_cells)
        
        # Ctrl+V 粘贴
        self.setShortcut(QKeySequence.Paste, self._paste_cells)
        
        # F2 编辑单元格
        self.setShortcut(Qt.Key_F2, self._edit_current_cell)
        
        # Escape 取消编辑
        self.setShortcut(Qt.Key_Escape, self._cancel_editing)
    
    def setShortcut(self, key_sequence, slot):
        """设置快捷键"""
        action = QAction(self)
        action.setShortcut(key_sequence)
        action.triggered.connect(slot)
        self.addAction(action)
    
    def _set_modern_style(self):
        """设置现代化样式"""
        self.setStyleSheet("""
            QTableWidget {
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #0078d4;
                gridline-color: #e1e5e9;
                font-size: 14px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f0f0f0;
            }
            
            QTableWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
            
            QTableWidget::item:hover {
                background-color: #f0f6ff;
            }
            
            QTableWidget::item:focus {
                border: 2px solid #0078d4;
                background-color: #f0f6ff;
            }
            
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                padding: 8px 12px;
                font-weight: 600;
                font-size: 13px;
                color: #495057;
            }
            
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #e9ecef, stop:1 #dee2e6);
            }
            
            /* 错误单元格样式 */
            QTableWidget::item[error="true"] {
                background-color: #ffebee;
                border: 2px solid #f44336;
                color: #d32f2f;
            }
            
            /* 编辑中的单元格样式 */
            QTableWidget::item[editing="true"] {
                background-color: #fff3e0;
                border: 2px solid #ff9800;
            }
        """)
    
    def _on_item_double_clicked(self, item: QTableWidgetItem):
        """处理双击编辑"""
        if not item:
            return
            
        row = item.row()
        col = item.column()
        
        self.logger.debug(f"开始编辑单元格 ({row}, {col})")
        
        # 记录编辑状态
        self.is_editing = True
        self.edit_row = row
        self.edit_col = col
        
        # 设置编辑样式
        item.setData(Qt.UserRole + 1, "true")  # editing标记
        
        # 创建自定义编辑器
        self._create_cell_editor(row, col, item)
    
    def _create_cell_editor(self, row: int, col: int, item: QTableWidgetItem):
        """创建单元格编辑器"""
        try:
            # 获取单元格值和类型
            current_value = item.text()
            column_header = self.horizontalHeaderItem(col).text()
            
            # 根据列类型创建不同的编辑器
            editor = self._get_editor_for_column(col, current_value)
            
            if editor:
                # 设置编辑器位置和大小
                cell_rect = self.visualItemRect(item)
                editor.setGeometry(cell_rect)
                
                # 设置编辑器样式
                editor.setStyleSheet("""
                    QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                        border: 2px solid #0078d4;
                        border-radius: 4px;
                        padding: 4px;
                        background-color: white;
                        font-size: 14px;
                    }
                """)
                
                # 连接编辑完成信号
                if hasattr(editor, 'editingFinished'):
                    editor.editingFinished.connect(lambda: self._finish_editing(row, col, editor))
                elif hasattr(editor, 'currentTextChanged'):
                    editor.currentTextChanged.connect(lambda: self._finish_editing(row, col, editor))
                
                # 设置焦点
                editor.setFocus()
                editor.show()
                
                self.current_editor = editor
                
        except Exception as e:
            self.logger.error(f"创建单元格编辑器失败: {e}")
    
    def _get_editor_for_column(self, col: int, current_value: str) -> Optional[QWidget]:
        """根据列类型获取编辑器"""
        column_header = self.horizontalHeaderItem(col).text()
        
        # 根据列名或数据类型决定编辑器类型
        if "金额" in column_header or "工资" in column_header or "奖金" in column_header:
            # 数值编辑器
            editor = QDoubleSpinBox(self)
            editor.setRange(-999999.99, 999999.99)
            editor.setDecimals(2)
            try:
                editor.setValue(float(current_value) if current_value else 0.0)
            except ValueError:
                editor.setValue(0.0)
            return editor
            
        elif "人数" in column_header or "数量" in column_header:
            # 整数编辑器
            editor = QSpinBox(self)
            editor.setRange(-9999, 9999)
            try:
                editor.setValue(int(current_value) if current_value else 0)
            except ValueError:
                editor.setValue(0)
            return editor
            
        elif "状态" in column_header or "类型" in column_header:
            # 下拉选择器
            editor = QComboBox(self)
            if "状态" in column_header:
                editor.addItems(["正常", "异动", "新增", "删除", "修改"])
            elif "类型" in column_header:
                editor.addItems(["基本工资", "绩效工资", "奖金", "补贴", "扣款"])
            editor.setCurrentText(current_value)
            return editor
            
        else:
            # 默认文本编辑器
            editor = QLineEdit(self)
            editor.setText(current_value)
            return editor
    
    def _finish_editing(self, row: int, col: int, editor: QWidget):
        """完成编辑"""
        try:
            # 获取新值
            new_value = self._get_editor_value(editor)
            old_value = self.item(row, col).text()
            
            # 验证数据
            if self._validate_cell_value(row, col, new_value):
                # 记录到撤销栈
                self._add_to_undo_stack("edit", row, col, old_value, new_value)
                
                # 更新单元格
                self.item(row, col).setText(str(new_value))
                
                # 移除错误标记
                self._remove_error_mark(row, col)
                
                # 发送编辑信号
                self.cell_edited.emit(row, col, old_value, str(new_value))
                
                self.logger.debug(f"单元格 ({row}, {col}) 编辑完成: {old_value} -> {new_value}")
                
            else:
                # 验证失败，标记错误
                self._mark_error_cell(row, col, "数据验证失败")
                
            # 清理编辑状态
            self._cleanup_editing(editor)
            
        except Exception as e:
            self.logger.error(f"完成编辑失败: {e}")
            self._cleanup_editing(editor)
    
    def _get_editor_value(self, editor: QWidget) -> str:
        """获取编辑器的值"""
        if isinstance(editor, QLineEdit):
            return editor.text()
        elif isinstance(editor, QComboBox):
            return editor.currentText()
        elif isinstance(editor, (QSpinBox, QDoubleSpinBox)):
            return str(editor.value())
        else:
            return ""
    
    def _validate_cell_value(self, row: int, col: int, value: str) -> bool:
        """验证单元格值"""
        try:
            # 使用注册的验证器
            if col in self.validators:
                return self.validators[col](value)
            
            # 默认验证：非空检查
            column_header = self.horizontalHeaderItem(col).text()
            if "必填" in column_header and not value.strip():
                return False
            
            # 数值验证
            if "金额" in column_header or "工资" in column_header:
                try:
                    float(value)
                    return True
                except ValueError:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证单元格值失败: {e}")
            return False
    
    def _cleanup_editing(self, editor: QWidget):
        """清理编辑状态"""
        if editor:
            editor.hide()
            editor.deleteLater()
        
        self.current_editor = None
        self.is_editing = False
        self.edit_row = -1
        self.edit_col = -1
    
    def _mark_error_cell(self, row: int, col: int, error_message: str):
        """标记错误单元格"""
        self.error_cells.add((row, col))
        item = self.item(row, col)
        if item:
            item.setBackground(QBrush(QColor(255, 235, 238)))  # 浅红色背景
            item.setToolTip(f"错误: {error_message}")
        
        # 发送验证错误信号
        self.validation_error.emit(row, col, error_message)
    
    def _remove_error_mark(self, row: int, col: int):
        """移除错误标记"""
        self.error_cells.discard((row, col))
        item = self.item(row, col)
        if item:
            item.setBackground(QBrush())  # 恢复默认背景
            item.setToolTip("")
    
    def _add_to_undo_stack(self, action: str, row: int, col: int, old_value: str, new_value: str):
        """添加到撤销栈"""
        undo_action = {
            'action': action,
            'row': row,
            'col': col,
            'old_value': old_value,
            'new_value': new_value
        }
        
        self.undo_stack.append(undo_action)
        
        # 限制撤销栈大小
        if len(self.undo_stack) > self.max_undo_steps:
            self.undo_stack.pop(0)
        
        # 清空重做栈
        self.redo_stack.clear()
    
    def _undo_action(self):
        """撤销操作"""
        if not self.undo_stack:
            return
        
        action = self.undo_stack.pop()
        
        # 执行撤销
        if action['action'] == 'edit':
            row, col = action['row'], action['col']
            item = self.item(row, col)
            if item:
                item.setText(action['old_value'])
                
        # 添加到重做栈
        self.redo_stack.append(action)
        
        self.logger.debug(f"撤销操作: {action}")
        self.undo_requested.emit()
    
    def _redo_action(self):
        """重做操作"""
        if not self.redo_stack:
            return
        
        action = self.redo_stack.pop()
        
        # 执行重做
        if action['action'] == 'edit':
            row, col = action['row'], action['col']
            item = self.item(row, col)
            if item:
                item.setText(action['new_value'])
                
        # 添加到撤销栈
        self.undo_stack.append(action)
        
        self.logger.debug(f"重做操作: {action}")
        self.redo_requested.emit()
    
    def _delete_selected_rows(self):
        """删除选中行"""
        selected_rows = set()
        for item in self.selectedItems():
            selected_rows.add(item.row())
        
        if not selected_rows:
            return
        
        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除选中的 {len(selected_rows)} 行数据吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 从大到小排序，避免索引问题
            for row in sorted(selected_rows, reverse=True):
                self.removeRow(row)
            
            self.logger.info(f"删除了 {len(selected_rows)} 行数据")
    
    def _copy_selected_cells(self):
        """复制选中单元格"""
        selected_items = self.selectedItems()
        if not selected_items:
            return
        
        # 获取选中区域
        rows = set(item.row() for item in selected_items)
        cols = set(item.column() for item in selected_items)
        
        min_row, max_row = min(rows), max(rows)
        min_col, max_col = min(cols), max(cols)
        
        # 构建复制内容
        copy_data = []
        for row in range(min_row, max_row + 1):
            row_data = []
            for col in range(min_col, max_col + 1):
                item = self.item(row, col)
                row_data.append(item.text() if item else "")
            copy_data.append("\t".join(row_data))
        
        # 复制到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText("\n".join(copy_data))
        
        self.logger.debug(f"复制了 {len(copy_data)} 行数据到剪贴板")
    
    def _paste_cells(self):
        """粘贴单元格"""
        clipboard = QApplication.clipboard()
        text = clipboard.text()
        
        if not text:
            return
        
        # 获取当前选中位置
        current_item = self.currentItem()
        if not current_item:
            return
        
        start_row = current_item.row()
        start_col = current_item.column()
        
        # 解析粘贴数据
        lines = text.split('\n')
        for row_offset, line in enumerate(lines):
            if not line.strip():
                continue
                
            cells = line.split('\t')
            for col_offset, cell_value in enumerate(cells):
                target_row = start_row + row_offset
                target_col = start_col + col_offset
                
                # 检查边界
                if target_row >= self.rowCount() or target_col >= self.columnCount():
                    continue
                
                # 设置单元格值
                item = self.item(target_row, target_col)
                if item:
                    old_value = item.text()
                    item.setText(cell_value)
                    
                    # 记录到撤销栈
                    self._add_to_undo_stack("edit", target_row, target_col, old_value, cell_value)
        
        self.logger.debug(f"粘贴了 {len(lines)} 行数据")
    
    def _edit_current_cell(self):
        """编辑当前单元格"""
        current_item = self.currentItem()
        if current_item:
            self._on_item_double_clicked(current_item)
    
    def _cancel_editing(self):
        """取消编辑"""
        if self.current_editor:
            self._cleanup_editing(self.current_editor)
    
    def register_column_validator(self, col: int, validator: callable):
        """注册列验证器"""
        self.validators[col] = validator
        self.logger.debug(f"为列 {col} 注册了验证器")
    
    def get_error_cells(self) -> List[Tuple[int, int]]:
        """获取错误单元格列表"""
        return list(self.error_cells)
    
    def clear_all_errors(self):
        """清除所有错误标记"""
        for row, col in self.error_cells.copy():
            self._remove_error_mark(row, col)
    
    def enable_batch_selection(self, enabled: bool = True):
        """启用批量选择模式"""
        self.batch_selection_mode = enabled
        if enabled:
            self.setSelectionMode(QAbstractItemView.MultiSelection)
        else:
            self.setSelectionMode(QAbstractItemView.ExtendedSelection)
    
    def get_selected_cell_data(self) -> List[Dict[str, Any]]:
        """获取选中单元格的数据"""
        selected_data = []
        for item in self.selectedItems():
            selected_data.append({
                'row': item.row(),
                'col': item.column(),
                'value': item.text(),
                'header': self.horizontalHeaderItem(item.column()).text()
            })
        return selected_data


class ModernTableEditor(QWidget):
    """
    现代化表格编辑器主控件
    
    包含工具栏、表格和状态栏的完整编辑器。
    """
    
    # 信号定义
    data_changed = pyqtSignal(dict)
    export_requested = pyqtSignal(str)
    import_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化现代化表格编辑器"""
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 初始化组件
        self.table = None
        self.toolbar = None
        self.status_bar = None
        self.toast_system = ToastManager()
        self.toast_system.set_parent(self)
        
        # 初始化UI
        self._init_ui()
        
        self.logger.info("现代化表格编辑器初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建工具栏
        self._create_toolbar(layout)
        
        # 创建表格
        self._create_table(layout)
        
        # 创建状态栏
        self._create_status_bar(layout)
        
        # 连接信号
        self._connect_signals()
    
    def _create_toolbar(self, parent_layout):
        """创建工具栏"""
        self.toolbar = QToolBar("表格编辑工具栏")
        self.toolbar.setStyleSheet("""
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #f8f9fa, stop:1 #e9ecef);
                border-bottom: 1px solid #dee2e6;
                padding: 4px;
            }
            QToolButton {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 4px;
                padding: 6px;
                margin: 2px;
            }
            QToolButton:hover {
                background-color: #e9ecef;
                border: 1px solid #adb5bd;
            }
            QToolButton:pressed {
                background-color: #dee2e6;
            }
        """)
        
        # 添加操作按钮
        self._add_toolbar_actions()
        
        parent_layout.addWidget(self.toolbar)
    
    def _add_toolbar_actions(self):
        """添加工具栏操作"""
        # 撤销/重做
        undo_action = QAction("撤销", self)
        undo_action.setShortcut(QKeySequence.Undo)
        undo_action.triggered.connect(self._undo)
        self.toolbar.addAction(undo_action)
        
        redo_action = QAction("重做", self)
        redo_action.setShortcut(QKeySequence.Redo)
        redo_action.triggered.connect(self._redo)
        self.toolbar.addAction(redo_action)
        
        self.toolbar.addSeparator()
        
        # 编辑操作
        add_row_action = QAction("添加行", self)
        add_row_action.triggered.connect(self._add_row)
        self.toolbar.addAction(add_row_action)
        
        delete_row_action = QAction("删除行", self)
        delete_row_action.triggered.connect(self._delete_rows)
        self.toolbar.addAction(delete_row_action)
        
        self.toolbar.addSeparator()
        
        # 批量操作
        batch_edit_action = QAction("批量编辑", self)
        batch_edit_action.triggered.connect(self._batch_edit)
        self.toolbar.addAction(batch_edit_action)
        
        self.toolbar.addSeparator()
        
        # 导入/导出
        import_action = QAction("导入数据", self)
        import_action.triggered.connect(self._import_data)
        self.toolbar.addAction(import_action)
        
        export_action = QAction("导出数据", self)
        export_action.triggered.connect(self._export_data)
        self.toolbar.addAction(export_action)
    
    def _create_table(self, parent_layout):
        """创建表格"""
        self.table = EditableTableWidget(self)
        parent_layout.addWidget(self.table)
    
    def _create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_frame.setMaximumHeight(30)
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
            }
            QLabel {
                color: #6c757d;
                font-size: 12px;
                padding: 4px 8px;
            }
        """)
        
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(8, 4, 8, 4)
        
        self.status_label = QLabel("就绪")
        self.row_count_label = QLabel("行数: 0")
        self.selected_count_label = QLabel("选中: 0")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.row_count_label)
        status_layout.addWidget(QLabel("|"))
        status_layout.addWidget(self.selected_count_label)
        
        parent_layout.addWidget(status_frame)
    
    def _connect_signals(self):
        """连接信号"""
        if self.table:
            self.table.cell_edited.connect(self._on_cell_edited)
            self.table.validation_error.connect(self._on_validation_error)
            self.table.itemSelectionChanged.connect(self._update_status)
    
    def _on_cell_edited(self, row: int, col: int, old_value: str, new_value: str):
        """处理单元格编辑"""
        self.toast_system.show_success(f"单元格 ({row+1}, {col+1}) 已更新")
        self.data_changed.emit({
            'action': 'edit',
            'row': row,
            'col': col,
            'old_value': old_value,
            'new_value': new_value
        })
    
    def _on_validation_error(self, row: int, col: int, error_message: str):
        """处理验证错误"""
        self.toast_system.show_error(f"验证失败: {error_message}")
    
    def _update_status(self):
        """更新状态栏"""
        if self.table:
            row_count = self.table.rowCount()
            selected_count = len(self.table.selectedItems())
            
            self.row_count_label.setText(f"行数: {row_count}")
            self.selected_count_label.setText(f"选中: {selected_count}")
    
    def _undo(self):
        """撤销操作"""
        if self.table:
            self.table._undo_action()
    
    def _redo(self):
        """重做操作"""
        if self.table:
            self.table._redo_action()
    
    def _add_row(self):
        """添加行"""
        if self.table:
            self.table.insertRow(self.table.rowCount())
            self._update_status()
            self.toast_system.show_info("已添加新行")
    
    def _delete_rows(self):
        """删除选中行"""
        if self.table:
            self.table._delete_selected_rows()
            self._update_status()
    
    def _batch_edit(self):
        """批量编辑"""
        if self.table:
            selected_data = self.table.get_selected_cell_data()
            if not selected_data:
                self.toast_system.show_warning("请先选择要编辑的单元格")
                return
            
            self.table.batch_edit_requested.emit(selected_data)
    
    def _import_data(self):
        """导入数据"""
        self.import_requested.emit()
    
    def _export_data(self):
        """导出数据"""
        self.export_requested.emit("excel")
    
    def set_data(self, data: List[Dict[str, Any]], headers: List[str] = None):
        """设置表格数据"""
        if self.table:
            self.table.set_data(data, headers)
            self._update_status()
    
    def get_data(self) -> List[Dict[str, Any]]:
        """获取表格数据"""
        if not self.table:
            return []
        
        data = []
        headers = [self.table.horizontalHeaderItem(i).text() 
                  for i in range(self.table.columnCount())]
        
        for row in range(self.table.rowCount()):
            row_data = {}
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                row_data[headers[col]] = item.text() if item else ""
            data.append(row_data)
        
        return data
    
    def register_column_validator(self, col: int, validator: callable):
        """注册列验证器"""
        if self.table:
            self.table.register_column_validator(col, validator)


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建测试数据
    test_data = [
        {"姓名": "张三", "工号": "001", "基本工资": "5000.00", "绩效工资": "2000.00", "状态": "正常"},
        {"姓名": "李四", "工号": "002", "基本工资": "5500.00", "绩效工资": "2200.00", "状态": "异动"},
        {"姓名": "王五", "工号": "003", "基本工资": "6000.00", "绩效工资": "2500.00", "状态": "正常"},
    ]
    
    # 创建编辑器
    editor = ModernTableEditor()
    editor.set_data(test_data)
    editor.show()
    
    sys.exit(app.exec_()) 