#!/usr/bin/env python3
"""
测试配置持久化修复

测试流程：
1. 模拟用户创建配置
2. 保存配置到文件系统
3. 模拟重新打开对话框
4. 验证配置是否正确加载
"""

import sys
import os
import pandas as pd
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager
from loguru import logger

class TestConfigPersistenceFix:
    """测试配置持久化修复"""
    
    def __init__(self):
        # 使用实际的配置目录进行测试
        self.config_dir = project_root / 'state' / 'change_data_configs'
        self.config_manager = ChangeDataConfigManager()
        logger.info(f"使用配置目录: {self.config_dir}")
    
    def create_test_config(self, sheet_name: str) -> dict:
        """创建测试配置"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        config_name = f"auto_save_{sheet_name}_{timestamp}"
        
        test_config = {
            'field_types': {
                '序号': 'integer',
                '工号': 'employee_id_string', 
                '姓名': 'name_string',
                '部门名称': 'name_string',
                '人员类别代码': 'personnel_category_code',
                '基本工资': 'salary_float',
                '津贴': 'salary_float',
                '应发工资': 'salary_float'
            },
            'field_mapping': {
                '序号': '序号',
                '工号': '工号',
                '姓名': '姓名', 
                '部门名称': '部门名称',
                '人员类别代码': '人员类别代码',
                '基本工资': '基本工资',
                '津贴': '津贴',
                '应发工资': '应发工资'
            },
            'formatting_rules': {}
        }
        
        return config_name, test_config
    
    def test_config_persistence_workflow(self) -> bool:
        """测试完整的配置持久化工作流"""
        try:
            logger.info("=== 开始测试配置持久化工作流 ===")
            
            # 1. 创建测试配置并保存
            sheet_name = "退休人员工资表"
            config_name, test_config = self.create_test_config(sheet_name)
            
            logger.info(f"创建测试配置: {config_name}")
            success = self.config_manager.save_config(
                config_name,
                test_config,
                description=f"测试配置 - {sheet_name}"
            )
            
            if not success:
                logger.error("❌ 配置保存失败")
                return False
            
            logger.info("✅ 配置保存成功")
            
            # 2. 验证配置能够正确加载
            loaded_config = self.config_manager.load_config(config_name)
            if not loaded_config:
                logger.error("❌ 无法加载保存的配置")
                return False
                
            logger.info("✅ 配置加载成功")
            
            # 3. 验证配置内容
            if loaded_config['field_types'] != test_config['field_types']:
                logger.error("❌ 字段类型配置不匹配")
                return False
                
            logger.info("✅ 配置内容验证通过")
            
            # 4. 测试工作表匹配逻辑
            configs_list = self.config_manager.list_configs()
            matching_configs = []
            for config_info in configs_list:
                config_name_check = config_info['name']
                if sheet_name in config_name_check or 'auto_save' in config_name_check:
                    matching_configs.append(config_info)
            
            if not matching_configs:
                logger.error(f"❌ 无法找到匹配工作表 '{sheet_name}' 的配置")
                return False
                
            logger.info(f"✅ 找到 {len(matching_configs)} 个匹配的配置")
            
            # 5. 测试最新配置选择逻辑
            latest_config_info = max(matching_configs, 
                key=lambda x: x.get('created_at', ''))
            
            if latest_config_info['name'] != config_name:
                logger.warning(f"警告: 最新配置名称不匹配，期望: {config_name}, 实际: {latest_config_info['name']}")
                # 这个可能不是错误，如果有更新的配置
            
            logger.info("✅ 最新配置选择逻辑正常")
            
            # 6. 测试多工作表配置管理
            sheet_name2 = "在职人员工资表"
            config_name2, test_config2 = self.create_test_config(sheet_name2)
            
            success2 = self.config_manager.save_config(
                config_name2,
                test_config2,
                description=f"测试配置 - {sheet_name2}"
            )
            
            if not success2:
                logger.error("❌ 第二个工作表配置保存失败")
                return False
                
            # 验证能正确区分不同工作表的配置
            configs_list_after = self.config_manager.list_configs()
            sheet1_configs = [c for c in configs_list_after if sheet_name in c['name']]
            sheet2_configs = [c for c in configs_list_after if sheet_name2 in c['name']]
            
            if not sheet1_configs or not sheet2_configs:
                logger.error("❌ 多工作表配置区分失败")
                return False
                
            logger.info("✅ 多工作表配置管理正常")
            
            logger.info("🎉 所有测试通过！配置持久化修复成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def cleanup_test_configs(self):
        """清理测试配置"""
        try:
            configs_list = self.config_manager.list_configs()
            test_configs = [c for c in configs_list if 'auto_save_退休人员工资表' in c['name'] or 'auto_save_在职人员工资表' in c['name']]
            
            for config_info in test_configs:
                config_name = config_info['name']
                if 'test' in config_name.lower() or datetime.now().strftime('%Y%m%d') in config_name:
                    # 只删除今天创建的测试配置，避免误删其他配置
                    self.config_manager.delete_config(config_name)
                    logger.info(f"清理测试配置: {config_name}")
                    
        except Exception as e:
            logger.warning(f"清理测试配置失败: {e}")

def main():
    """主函数"""
    tester = TestConfigPersistenceFix()
    
    try:
        success = tester.test_config_persistence_workflow()
        
        if success:
            print(">>> 测试结果：配置持久化修复功能正常工作")
            return 0
        else:
            print(">>> 测试结果：配置持久化修复功能存在问题")
            return 1
            
    finally:
        # 清理测试数据
        # tester.cleanup_test_configs()  # 暂时注释掉，保留测试数据用于实际验证
        pass

if __name__ == "__main__":
    sys.exit(main())