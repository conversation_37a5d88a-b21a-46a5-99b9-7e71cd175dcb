"""
增强的统一状态管理器 - P1级实现
整合所有分散的状态管理组件，提供单一的状态管理入口
"""

import json
import threading
import time
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime
from pathlib import Path
from enum import Enum
from loguru import logger

# 导入已有的状态管理器
from src.core.enhanced_sort_state_manager import get_sort_state_manager
from src.core.pagination_state_manager import PaginationStateManager
from src.core.table_sort_state_manager import TableSortStateManager
from src.core.unified_mapping_service import get_unified_mapping_service


class StateType(Enum):
    """状态类型枚举"""
    SORT = "sort"
    PAGINATION = "pagination"
    FILTER = "filter"
    SELECTION = "selection"
    FIELD_MAPPING = "field_mapping"
    UI_LAYOUT = "ui_layout"
    USER_PREFERENCE = "user_preference"


@dataclass
class StateChangeEvent:
    """状态变更事件"""
    state_type: StateType
    table_name: Optional[str]
    old_value: Any
    new_value: Any
    timestamp: datetime = field(default_factory=datetime.now)
    source: str = "unknown"  # 变更来源
    
    def to_dict(self) -> Dict:
        return {
            'state_type': self.state_type.value,
            'table_name': self.table_name,
            'old_value': str(self.old_value),
            'new_value': str(self.new_value),
            'timestamp': self.timestamp.isoformat(),
            'source': self.source
        }


@dataclass
class UnifiedTableState:
    """统一的表状态"""
    table_name: str
    
    # 分页状态
    current_page: int = 1
    page_size: int = 50
    total_records: int = 0
    
    # 排序状态
    sort_columns: List[Dict[str, Any]] = field(default_factory=list)
    is_global_sort: bool = False
    
    # 过滤状态
    active_filters: Dict[str, Any] = field(default_factory=dict)
    filter_mode: str = "AND"  # AND/OR
    
    # 选择状态
    selected_rows: List[int] = field(default_factory=list)
    selected_columns: List[str] = field(default_factory=list)
    
    # 字段映射状态
    field_mapping: Dict[str, str] = field(default_factory=dict)
    visible_fields: List[str] = field(default_factory=list)
    field_order: List[str] = field(default_factory=list)
    
    # UI状态
    column_widths: Dict[str, int] = field(default_factory=dict)
    row_height: int = 30
    
    # 元数据
    last_updated: datetime = field(default_factory=datetime.now)
    last_accessed: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    
    def to_dict(self) -> Dict:
        """转换为可序列化的字典"""
        data = asdict(self)
        data['last_updated'] = self.last_updated.isoformat()
        data['last_accessed'] = self.last_accessed.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'UnifiedTableState':
        """从字典创建实例"""
        if 'last_updated' in data:
            data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        if 'last_accessed' in data:
            data['last_accessed'] = datetime.fromisoformat(data['last_accessed'])
        return cls(**data)


class EnhancedUnifiedStateManager:
    """
    增强的统一状态管理器
    
    主要功能：
    1. 整合所有状态管理器
    2. 提供统一的状态访问接口
    3. 状态变更事件通知
    4. 状态持久化和恢复
    5. 状态历史和回滚
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化管理器"""
        if not hasattr(self, '_initialized'):
            self.logger = logger
            self._state_lock = threading.RLock()
            
            # 集成现有的状态管理器
            self.sort_manager = get_sort_state_manager()
            self.pagination_manager = PaginationStateManager()
            self.mapping_service = get_unified_mapping_service()
            
            # 统一状态存储
            self._table_states: Dict[str, UnifiedTableState] = {}
            self._global_state: Dict[str, Any] = {}
            
            # 状态监听器
            self._state_listeners: Dict[StateType, List[Callable]] = {
                state_type: [] for state_type in StateType
            }
            
            # 状态变更历史
            self._change_history: List[StateChangeEvent] = []
            self._max_history_size = 100
            
            # 持久化配置
            self._state_dir = Path('state/unified')
            self._state_dir.mkdir(parents=True, exist_ok=True)
            self._state_file = self._state_dir / 'unified_state.json'
            
            # 自动保存
            self._auto_save_enabled = True
            self._last_save_time = time.time()
            self._save_interval = 30  # 30秒自动保存
            
            # 加载已保存的状态
            self._load_states()
            
            self._initialized = True
            self.logger.info("EnhancedUnifiedStateManager 初始化完成")
    
    # ========== 核心API ==========
    
    def get_table_state(self, table_name: str) -> UnifiedTableState:
        """
        获取表的完整状态
        
        Args:
            table_name: 表名
        
        Returns:
            表状态对象
        """
        with self._state_lock:
            if table_name not in self._table_states:
                self._table_states[table_name] = UnifiedTableState(table_name=table_name)
                self.logger.debug(f"创建新表状态: {table_name}")
            
            state = self._table_states[table_name]
            state.last_accessed = datetime.now()
            state.access_count += 1
            
            return state
    
    def update_state(self, table_name: str, state_type: StateType, 
                    new_value: Any, source: str = "unknown") -> bool:
        """
        更新状态
        
        Args:
            table_name: 表名
            state_type: 状态类型
            new_value: 新值
            source: 更新来源
        
        Returns:
            是否更新成功
        """
        try:
            with self._state_lock:
                state = self.get_table_state(table_name)
                old_value = None
                
                # 根据状态类型更新相应字段
                if state_type == StateType.SORT:
                    old_value = state.sort_columns.copy()
                    state.sort_columns = new_value
                    # 同步到排序管理器
                    self.sort_manager.save_sort_state(table_name, new_value)
                    
                elif state_type == StateType.PAGINATION:
                    old_value = (state.current_page, state.page_size)
                    if isinstance(new_value, dict):
                        state.current_page = new_value.get('page', state.current_page)
                        state.page_size = new_value.get('page_size', state.page_size)
                    
                elif state_type == StateType.FILTER:
                    old_value = state.active_filters.copy()
                    state.active_filters = new_value
                    
                elif state_type == StateType.SELECTION:
                    old_value = state.selected_rows.copy()
                    state.selected_rows = new_value
                    
                elif state_type == StateType.FIELD_MAPPING:
                    old_value = state.field_mapping.copy()
                    state.field_mapping = new_value
                    # 同步到映射服务
                    self.mapping_service.save_user_mapping(table_name, {
                        'field_mapping': new_value,
                        'is_change_table': 'change' in table_name.lower()
                    })
                
                # 记录变更
                self._record_change(table_name, state_type, old_value, new_value, source)
                
                # 触发监听器
                self._notify_listeners(state_type, table_name, old_value, new_value)
                
                # 标记需要保存
                state.last_updated = datetime.now()
                self._check_auto_save()
                
                self.logger.info(f"状态更新成功: {table_name}.{state_type.value}")
                return True
                
        except Exception as e:
            self.logger.error(f"状态更新失败: {e}")
            return False
    
    def get_state_value(self, table_name: str, state_type: StateType) -> Any:
        """
        获取特定状态值
        
        Args:
            table_name: 表名
            state_type: 状态类型
        
        Returns:
            状态值
        """
        state = self.get_table_state(table_name)
        
        if state_type == StateType.SORT:
            return state.sort_columns
        elif state_type == StateType.PAGINATION:
            return {'page': state.current_page, 'page_size': state.page_size}
        elif state_type == StateType.FILTER:
            return state.active_filters
        elif state_type == StateType.SELECTION:
            return state.selected_rows
        elif state_type == StateType.FIELD_MAPPING:
            return state.field_mapping
        else:
            return None
    
    # ========== 便捷方法 ==========
    
    def update_sort(self, table_name: str, sort_columns: List[Dict], source: str = "user") -> bool:
        """更新排序状态"""
        return self.update_state(table_name, StateType.SORT, sort_columns, source)
    
    def update_pagination(self, table_name: str, page: int, page_size: int = None, source: str = "user") -> bool:
        """更新分页状态"""
        value = {'page': page}
        if page_size is not None:
            value['page_size'] = page_size
        return self.update_state(table_name, StateType.PAGINATION, value, source)
    
    def update_filter(self, table_name: str, filters: Dict, source: str = "user") -> bool:
        """更新过滤状态"""
        return self.update_state(table_name, StateType.FILTER, filters, source)
    
    def update_selection(self, table_name: str, selected_rows: List[int], source: str = "user") -> bool:
        """更新选择状态"""
        return self.update_state(table_name, StateType.SELECTION, selected_rows, source)
    
    # ========== 监听器管理 ==========
    
    def add_listener(self, state_type: StateType, callback: Callable) -> bool:
        """
        添加状态监听器
        
        Args:
            state_type: 状态类型
            callback: 回调函数
        
        Returns:
            是否添加成功
        """
        try:
            if callback not in self._state_listeners[state_type]:
                self._state_listeners[state_type].append(callback)
                self.logger.debug(f"添加监听器: {state_type.value}")
            return True
        except Exception as e:
            self.logger.error(f"添加监听器失败: {e}")
            return False
    
    def remove_listener(self, state_type: StateType, callback: Callable) -> bool:
        """移除状态监听器"""
        try:
            if callback in self._state_listeners[state_type]:
                self._state_listeners[state_type].remove(callback)
                self.logger.debug(f"移除监听器: {state_type.value}")
            return True
        except Exception as e:
            self.logger.error(f"移除监听器失败: {e}")
            return False
    
    def _notify_listeners(self, state_type: StateType, table_name: str, 
                         old_value: Any, new_value: Any):
        """通知监听器"""
        for callback in self._state_listeners[state_type]:
            try:
                callback(table_name, old_value, new_value)
            except Exception as e:
                self.logger.error(f"监听器回调失败: {e}")
    
    # ========== 历史管理 ==========
    
    def _record_change(self, table_name: str, state_type: StateType,
                      old_value: Any, new_value: Any, source: str):
        """记录状态变更"""
        event = StateChangeEvent(
            state_type=state_type,
            table_name=table_name,
            old_value=old_value,
            new_value=new_value,
            source=source
        )
        
        self._change_history.append(event)
        
        # 限制历史大小
        if len(self._change_history) > self._max_history_size:
            self._change_history = self._change_history[-self._max_history_size:]
    
    def get_change_history(self, table_name: Optional[str] = None,
                          state_type: Optional[StateType] = None) -> List[StateChangeEvent]:
        """获取变更历史"""
        history = self._change_history
        
        if table_name:
            history = [e for e in history if e.table_name == table_name]
        
        if state_type:
            history = [e for e in history if e.state_type == state_type]
        
        return history
    
    # ========== 持久化 ==========
    
    def _check_auto_save(self):
        """检查是否需要自动保存"""
        if self._auto_save_enabled:
            current_time = time.time()
            if current_time - self._last_save_time > self._save_interval:
                self.save_states()
                self._last_save_time = current_time
    
    def save_states(self) -> bool:
        """保存所有状态"""
        try:
            with self._state_lock:
                data = {
                    'table_states': {
                        name: state.to_dict()
                        for name, state in self._table_states.items()
                    },
                    'global_state': self._global_state,
                    'change_history': [
                        event.to_dict() for event in self._change_history[-20:]
                    ],
                    'saved_at': datetime.now().isoformat()
                }
                
                with open(self._state_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                self.logger.info(f"状态已保存: {len(self._table_states)} 个表")
                return True
                
        except Exception as e:
            self.logger.error(f"保存状态失败: {e}")
            return False
    
    def _load_states(self):
        """加载已保存的状态"""
        try:
            if self._state_file.exists():
                with open(self._state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 恢复表状态
                for name, state_data in data.get('table_states', {}).items():
                    self._table_states[name] = UnifiedTableState.from_dict(state_data)
                
                # 恢复全局状态
                self._global_state = data.get('global_state', {})
                
                self.logger.info(f"已加载状态: {len(self._table_states)} 个表")
                
        except Exception as e:
            self.logger.error(f"加载状态失败: {e}")
    
    # ========== 统计和调试 ==========
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._state_lock:
            return {
                'total_tables': len(self._table_states),
                'active_listeners': sum(len(l) for l in self._state_listeners.values()),
                'history_size': len(self._change_history),
                'state_file_size': self._state_file.stat().st_size if self._state_file.exists() else 0,
                'last_save': self._last_save_time
            }
    
    def clear_table_state(self, table_name: str):
        """清除表状态"""
        with self._state_lock:
            if table_name in self._table_states:
                del self._table_states[table_name]
                self.logger.info(f"清除表状态: {table_name}")
    
    def clear_all_states(self):
        """清除所有状态"""
        with self._state_lock:
            self._table_states.clear()
            self._global_state.clear()
            self._change_history.clear()
            if self._state_file.exists():
                self._state_file.unlink()
            self.logger.info("清除所有状态")


# 全局实例获取函数
def get_unified_state_manager() -> EnhancedUnifiedStateManager:
    """获取统一状态管理器的全局实例"""
    return EnhancedUnifiedStateManager()