#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置界面用户引导和帮助系统

提供新用户引导、功能说明、操作提示等帮助功能，包括：
- 首次使用引导向导
- 功能介绍弹出层
- 上下文相关帮助
- 快捷键提示
- 常见问题解答

创建时间: 2025-01-20
方案: 统一配置界面详细设计（方案3）- 阶段2实施
"""

import json
import os
import sys
from typing import Dict, List, Optional, Any, Tuple
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, 
    QPushButton, QTextEdit, QScrollArea, QTabWidget,
    QMessageBox, QCheckBox, QWidget, QFrame, QListWidget,
    QListWidgetItem, QSplitter, QStackedWidget, QToolTip
)
from PyQt5.QtCore import Qt, QPoint, QRect, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QPainter, QPen, QBrush

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.utils.log_config import setup_logger


class GuideStep:
    """引导步骤数据结构"""
    
    def __init__(self, step_id: str, title: str, content: str, 
                 target_widget: str = None, position: str = "bottom"):
        self.step_id = step_id
        self.title = title
        self.content = content
        self.target_widget = target_widget  # 目标控件名称
        self.position = position  # 提示位置: top, bottom, left, right
        self.completed = False


class HelpTopic:
    """帮助主题数据结构"""
    
    def __init__(self, topic_id: str, title: str, content: str, 
                 category: str = "general", keywords: List[str] = None):
        self.topic_id = topic_id
        self.title = title
        self.content = content
        self.category = category
        self.keywords = keywords or []


class GuideOverlay(QWidget):
    """引导遮罩层"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.target_rect = QRect()
        self.guide_text = ""
        self.guide_position = "bottom"
        
        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setStyleSheet("background: transparent;")
        
    def set_target(self, rect: QRect, text: str, position: str = "bottom"):
        """设置引导目标"""
        self.target_rect = rect
        self.guide_text = text
        self.guide_position = position
        self.update()
    
    def paintEvent(self, event):
        """绘制遮罩和引导框"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制半透明遮罩
        painter.fillRect(self.rect(), QColor(0, 0, 0, 100))
        
        # 挖空目标区域
        if not self.target_rect.isEmpty():
            painter.setCompositionMode(QPainter.CompositionMode_Clear)
            painter.fillRect(self.target_rect, Qt.transparent)
            
            # 绘制目标区域边框
            painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
            pen = QPen(QColor("#3498db"), 3)
            painter.setPen(pen)
            painter.drawRoundedRect(self.target_rect, 5, 5)
        
        # 绘制引导文本框
        if self.guide_text:
            self._draw_guide_text(painter)
    
    def _draw_guide_text(self, painter):
        """绘制引导文本"""
        # 计算文本框位置
        text_rect = self._calculate_text_position()
        
        # 绘制文本背景
        painter.fillRect(text_rect, QColor(255, 255, 255, 240))
        painter.setPen(QPen(QColor("#3498db"), 2))
        painter.drawRoundedRect(text_rect, 8, 8)
        
        # 绘制文本
        painter.setPen(QColor("#2c3e50"))
        font = QFont("Microsoft YaHei", 12)
        painter.setFont(font)
        
        text_rect_inner = text_rect.adjusted(10, 10, -10, -10)
        painter.drawText(text_rect_inner, Qt.TextWordWrap, self.guide_text)
    
    def _calculate_text_position(self) -> QRect:
        """计算文本框位置"""
        text_width = 300
        text_height = 120
        
        if self.target_rect.isEmpty():
            # 居中显示
            x = (self.width() - text_width) // 2
            y = (self.height() - text_height) // 2
        else:
            if self.guide_position == "bottom":
                x = max(10, min(self.target_rect.x(), self.width() - text_width - 10))
                y = min(self.target_rect.bottom() + 20, self.height() - text_height - 10)
            elif self.guide_position == "top":
                x = max(10, min(self.target_rect.x(), self.width() - text_width - 10))
                y = max(10, self.target_rect.top() - text_height - 20)
            elif self.guide_position == "right":
                x = min(self.target_rect.right() + 20, self.width() - text_width - 10)
                y = max(10, min(self.target_rect.y(), self.height() - text_height - 10))
            else:  # left
                x = max(10, self.target_rect.left() - text_width - 20)
                y = max(10, min(self.target_rect.y(), self.height() - text_height - 10))
        
        return QRect(x, y, text_width, text_height)


class FirstTimeGuideDialog(QDialog):
    """首次使用引导对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.current_step = 0
        self.guide_steps = self._create_guide_steps()
        
        self._init_ui()
        self._show_current_step()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🎯 统一配置界面使用引导")
        self.setMinimumSize(600, 400)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("欢迎使用统一配置界面！")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 20px;")
        layout.addWidget(title_label)
        
        # 进度指示
        self.progress_label = QLabel()
        self.progress_label.setAlignment(Qt.AlignCenter)
        self.progress_label.setStyleSheet("color: #7f8c8d; margin-bottom: 10px;")
        layout.addWidget(self.progress_label)
        
        # 内容区域
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        content_layout = QVBoxLayout(content_frame)
        
        # 步骤标题
        self.step_title = QLabel()
        self.step_title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        self.step_title.setStyleSheet("color: #2980b9; margin-bottom: 10px;")
        content_layout.addWidget(self.step_title)
        
        # 步骤内容
        self.step_content = QTextEdit()
        self.step_content.setReadOnly(True)
        self.step_content.setMaximumHeight(200)
        self.step_content.setStyleSheet("""
            QTextEdit {
                border: none;
                background: transparent;
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        content_layout.addWidget(self.step_content)
        
        layout.addWidget(content_frame)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.skip_btn = QPushButton("跳过引导")
        self.skip_btn.clicked.connect(self.reject)
        
        self.prev_btn = QPushButton("上一步")
        self.prev_btn.clicked.connect(self._prev_step)
        
        self.next_btn = QPushButton("下一步")
        self.next_btn.clicked.connect(self._next_step)
        
        self.finish_btn = QPushButton("完成")
        self.finish_btn.clicked.connect(self.accept)
        self.finish_btn.setVisible(False)
        
        button_layout.addWidget(self.skip_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.prev_btn)
        button_layout.addWidget(self.next_btn)
        button_layout.addWidget(self.finish_btn)
        
        layout.addLayout(button_layout)
        
        # 不再显示选项
        self.dont_show_again_check = QCheckBox("不再显示此引导")
        self.dont_show_again_check.setStyleSheet("margin-top: 10px;")
        layout.addWidget(self.dont_show_again_check)
    
    def _create_guide_steps(self) -> List[GuideStep]:
        """创建引导步骤"""
        steps = [
            GuideStep(
                "welcome",
                "欢迎使用统一配置界面",
                """
                🎉 欢迎使用全新的统一配置界面！
                
                这个界面整合了原有的数据导入和字段配置功能，让您可以在一个地方完成所有配置工作。
                
                主要特色：
                • 一个界面完成所有配置
                • 智能字段映射推荐
                • 可视化配置来源指示
                • 实时冲突检测和解决
                • 配置预览和验证
                """
            ),
            GuideStep(
                "interface_layout",
                "界面布局介绍",
                """
                📱 界面采用左右分栏设计：
                
                📍 左侧面板：
                • 文件信息 - 显示当前选择的Excel文件信息
                • Sheet列表 - 管理和选择要处理的工作表
                • 配置概览 - 查看当前配置状态
                • 快速操作 - 常用功能快捷入口
                
                📍 右侧主面板：
                • Sheet管理 - 批量管理工作表设置
                • 字段映射 - 核心的字段配置功能
                • 高级配置 - 详细的类型和格式设置
                • 预览验证 - 实时预览和配置检查
                """
            ),
            GuideStep(
                "smart_mapping",
                "智能字段映射",
                """
                🤖 智能映射是核心功能：
                
                • 自动识别字段类型（工资、姓名、编号等）
                • 基于数据内容智能推荐映射
                • 提供映射置信度指示
                • 支持一键批量映射
                
                🎯 使用方法：
                1. 选择Excel文件
                2. 点击"智能自动映射"按钮
                3. 查看推荐结果
                4. 手动调整不准确的映射
                5. 保存配置
                """
            ),
            GuideStep(
                "visual_indicators",
                "可视化配置来源",
                """
                🎨 配置来源一目了然：
                
                🟢 绿色 - 用户自定义配置（最高优先级）
                🔴 红色 - 临时覆盖配置
                🟡 橙色 - 表模板配置
                🔵 蓝色 - 系统默认配置（最低优先级）
                
                💡 优势：
                • 清楚知道配置的来源
                • 了解配置的优先级关系
                • 发现和解决配置冲突
                • 追踪配置变更历史
                """
            ),
            GuideStep(
                "conflict_resolution",
                "冲突检测与解决",
                """
                🔍 自动冲突检测：
                
                系统会自动检测以下冲突：
                • 重复的字段映射
                • 数据类型不匹配
                • 必填字段缺失
                • 配置逻辑错误
                
                🔧 解决方案：
                • 自动应用优先级规则
                • 提供冲突解决建议
                • 显示详细的冲突报告
                • 支持手动冲突处理
                """
            ),
            GuideStep(
                "preview_validation",
                "预览与验证",
                """
                👁️ 实时预览功能：
                
                • 查看配置应用后的数据效果
                • 格式化规则实时预览
                • 数据类型转换效果展示
                • 发现潜在的数据问题
                
                ✅ 配置验证：
                • 完整性检查
                • 逻辑一致性验证
                • 数据质量评估
                • 导入准备度检查
                """
            ),
            GuideStep(
                "getting_started",
                "开始使用",
                """
                🚀 现在您可以开始使用了！
                
                📝 建议的操作流程：
                1. 点击"浏览"选择Excel文件
                2. 在左侧面板查看Sheet列表
                3. 使用"智能自动映射"快速配置
                4. 在"字段映射"选项卡中调整配置
                5. 切换到"预览验证"选项卡检查结果
                6. 点击"确定导入"完成数据导入
                
                💡 提示：您可以随时点击"?"按钮获取帮助，或使用右键菜单查看上下文帮助。
                """
            )
        ]
        
        return steps
    
    def _show_current_step(self):
        """显示当前步骤"""
        if 0 <= self.current_step < len(self.guide_steps):
            step = self.guide_steps[self.current_step]
            
            # 更新进度
            progress_text = f"第 {self.current_step + 1} 步，共 {len(self.guide_steps)} 步"
            self.progress_label.setText(progress_text)
            
            # 更新内容
            self.step_title.setText(step.title)
            self.step_content.setText(step.content.strip())
            
            # 更新按钮状态
            self.prev_btn.setEnabled(self.current_step > 0)
            
            is_last_step = self.current_step == len(self.guide_steps) - 1
            self.next_btn.setVisible(not is_last_step)
            self.finish_btn.setVisible(is_last_step)
    
    def _prev_step(self):
        """上一步"""
        if self.current_step > 0:
            self.current_step -= 1
            self._show_current_step()
    
    def _next_step(self):
        """下一步"""
        if self.current_step < len(self.guide_steps) - 1:
            self.current_step += 1
            self._show_current_step()
    
    def should_show_again(self) -> bool:
        """是否应该再次显示"""
        return not self.dont_show_again_check.isChecked()


class HelpDialog(QDialog):
    """帮助对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.help_topics = self._create_help_topics()
        
        self._init_ui()
        self._populate_help_content()
    
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("📖 统一配置界面帮助")
        self.setMinimumSize(800, 600)
        
        layout = QHBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧目录
        left_panel = self._create_topic_list()
        splitter.addWidget(left_panel)
        
        # 右侧内容
        right_panel = self._create_content_area()
        splitter.addWidget(right_panel)
        
        # 设置分割比例
        splitter.setSizes([200, 600])
    
    def _create_topic_list(self) -> QWidget:
        """创建主题列表"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 搜索框
        # search_edit = QLineEdit()
        # search_edit.setPlaceholderText("搜索帮助主题...")
        # layout.addWidget(search_edit)
        
        # 主题列表
        self.topic_list = QListWidget()
        self.topic_list.currentItemChanged.connect(self._on_topic_selected)
        layout.addWidget(self.topic_list)
        
        return widget
    
    def _create_content_area(self) -> QWidget:
        """创建内容区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        self.content_title = QLabel("选择左侧主题查看帮助内容")
        self.content_title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        self.content_title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(self.content_title)
        
        # 内容
        self.content_text = QTextEdit()
        self.content_text.setReadOnly(True)
        layout.addWidget(self.content_text)
        
        return widget
    
    def _create_help_topics(self) -> List[HelpTopic]:
        """创建帮助主题"""
        topics = [
            HelpTopic(
                "getting_started",
                "🚀 快速开始",
                """
                <h3>快速开始使用统一配置界面</h3>
                
                <h4>1. 选择Excel文件</h4>
                <p>点击左侧面板的"浏览"按钮，选择要导入的Excel文件。系统会自动检测文件类型（工资表或异动表）。</p>
                
                <h4>2. 查看Sheet列表</h4>
                <p>在左侧的Sheet列表中，您可以看到所有工作表。勾选要导入的Sheet，取消不需要的Sheet。</p>
                
                <h4>3. 智能字段映射</h4>
                <p>点击"智能自动映射"按钮，系统会自动分析Excel字段并推荐最佳的映射关系。</p>
                
                <h4>4. 检查和调整</h4>
                <p>在"字段映射"选项卡中检查映射结果，手动调整不准确的映射。</p>
                
                <h4>5. 预览验证</h4>
                <p>切换到"预览验证"选项卡，查看配置应用后的数据效果。</p>
                
                <h4>6. 完成导入</h4>
                <p>确认配置无误后，点击"确定导入"按钮完成数据导入。</p>
                """,
                "basic"
            ),
            HelpTopic(
                "smart_mapping",
                "🤖 智能自动映射",
                """
                <h3>智能自动映射功能详解</h3>
                
                <h4>工作原理</h4>
                <p>智能映射系统会分析Excel列名和数据内容，自动推荐最佳的字段映射关系。</p>
                
                <h4>识别规则</h4>
                <ul>
                <li><strong>基本信息字段</strong>：员工编号、姓名、部门、职位等</li>
                <li><strong>工资字段</strong>：基本工资、绩效工资、加班费、补贴等</li>
                <li><strong>扣除字段</strong>：个人所得税、社保、公积金等</li>
                <li><strong>异动字段</strong>：异动类型、原因、日期、金额等</li>
                </ul>
                
                <h4>置信度等级</h4>
                <ul>
                <li><strong>高置信度 (80%+)</strong>：绿色显示，可直接使用</li>
                <li><strong>中置信度 (60-80%)</strong>：橙色显示，建议确认</li>
                <li><strong>低置信度 (<60%)</strong>：蓝色显示，需要检查</li>
                </ul>
                
                <h4>使用建议</h4>
                <p>智能映射后，请务必检查低置信度的映射，确保准确性。</p>
                """,
                "features"
            ),
            HelpTopic(
                "visual_indicators",
                "🎨 可视化配置来源",
                """
                <h3>配置来源可视化指示系统</h3>
                
                <h4>颜色编码</h4>
                <ul>
                <li><strong>🟢 绿色</strong>：用户自定义配置（优先级最高）</li>
                <li><strong>🔴 红色</strong>：临时覆盖配置（高优先级）</li>
                <li><strong>🟡 橙色</strong>：表模板配置（中优先级）</li>
                <li><strong>🔵 蓝色</strong>：系统默认配置（低优先级）</li>
                </ul>
                
                <h4>优先级规则</h4>
                <p>当存在多个配置时，系统按照优先级自动选择生效的配置：</p>
                <ol>
                <li>用户自定义配置</li>
                <li>临时覆盖配置</li>
                <li>表模板配置</li>
                <li>系统默认配置</li>
                </ol>
                
                <h4>图标含义</h4>
                <ul>
                <li><strong>👤</strong>：用户配置</li>
                <li><strong>⚡</strong>：临时覆盖</li>
                <li><strong>📋</strong>：表模板</li>
                <li><strong>🏭</strong>：系统默认</li>
                </ul>
                """,
                "features"
            ),
            HelpTopic(
                "field_types",
                "🏷️ 字段类型说明",
                """
                <h3>字段类型和格式化规则</h3>
                
                <h4>预定义字段类型</h4>
                <ul>
                <li><strong>salary_float</strong>：货币金额，支持千分位和小数位</li>
                <li><strong>name_string</strong>：姓名文本，自动去除空格</li>
                <li><strong>employee_id</strong>：员工编号，自动转大写</li>
                <li><strong>date_field</strong>：日期字段，标准日期格式</li>
                <li><strong>percentage</strong>：百分比，支持百分号显示</li>
                </ul>
                
                <h4>格式化规则</h4>
                <ul>
                <li><strong>千分位</strong>：数值显示千分位分隔符</li>
                <li><strong>小数位</strong>：控制小数点后位数</li>
                <li><strong>去除空格</strong>：自动清理前后空格</li>
                <li><strong>大写转换</strong>：文本自动转为大写</li>
                </ul>
                
                <h4>验证规则</h4>
                <ul>
                <li><strong>非空验证</strong>：必填字段不能为空</li>
                <li><strong>唯一性验证</strong>：员工编号等必须唯一</li>
                <li><strong>数值范围</strong>：工资金额必须为非负数</li>
                <li><strong>日期有效性</strong>：日期字段必须是有效日期</li>
                </ul>
                """,
                "reference"
            ),
            HelpTopic(
                "troubleshooting",
                "🔧 常见问题解决",
                """
                <h3>常见问题及解决方案</h3>
                
                <h4>Q: 智能映射结果不准确怎么办？</h4>
                <p><strong>A:</strong> 可以在字段映射表格中手动调整。点击系统字段名列进行修改，或使用右侧的配置按钮进行详细设置。</p>
                
                <h4>Q: 配置冲突如何解决？</h4>
                <p><strong>A:</strong> 系统会自动按优先级解决大部分冲突。对于无法自动解决的冲突，会在预览验证中显示详细信息和解决建议。</p>
                
                <h4>Q: 如何保存配置为模板？</h4>
                <p><strong>A:</strong> 配置完成后，点击"保存为模板"按钮，输入模板名称即可保存。下次导入相似文件时可以直接使用模板。</p>
                
                <h4>Q: 预览数据为空是什么原因？</h4>
                <p><strong>A:</strong> 可能原因：1) Sheet中确实没有数据；2) 字段映射不正确；3) 数据格式有问题。请检查Excel文件和字段映射配置。</p>
                
                <h4>Q: 如何切换回传统界面？</h4>
                <p><strong>A:</strong> 在用户偏好设置中可以选择界面模式，或者在导入对话框选择界面时选择"传统界面"。</p>
                
                <h4>Q: 导入后数据显示异常？</h4>
                <p><strong>A:</strong> 请检查：1) 字段类型设置是否正确；2) 格式化规则是否合适；3) 源数据是否存在问题。</p>
                """,
                "troubleshooting"
            ),
            HelpTopic(
                "shortcuts",
                "⌨️ 快捷键说明",
                """
                <h3>快捷键和操作技巧</h3>
                
                <h4>常用快捷键</h4>
                <ul>
                <li><strong>Ctrl + O</strong>：打开文件</li>
                <li><strong>Ctrl + S</strong>：保存配置</li>
                <li><strong>Ctrl + A</strong>：智能自动映射</li>
                <li><strong>Ctrl + P</strong>：预览数据</li>
                <li><strong>Ctrl + R</strong>：刷新</li>
                <li><strong>F1</strong>：显示帮助</li>
                <li><strong>F5</strong>：运行验证</li>
                <li><strong>Escape</strong>：取消当前操作</li>
                </ul>
                
                <h4>鼠标操作</h4>
                <ul>
                <li><strong>右键点击字段</strong>：显示字段配置菜单</li>
                <li><strong>双击字段映射行</strong>：快速编辑映射</li>
                <li><strong>悬停在配置来源</strong>：显示详细信息</li>
                </ul>
                
                <h4>操作技巧</h4>
                <ul>
                <li>使用搜索框快速定位字段</li>
                <li>批量选择Sheet进行操作</li>
                <li>利用配置模板提高效率</li>
                <li>定期运行配置验证检查问题</li>
                </ul>
                """,
                "reference"
            )
        ]
        
        return topics
    
    def _populate_help_content(self):
        """填充帮助内容"""
        # 按类别分组
        categories = {}
        for topic in self.help_topics:
            if topic.category not in categories:
                categories[topic.category] = []
            categories[topic.category].append(topic)
        
        # 添加到列表
        category_names = {
            "basic": "🚀 基础使用",
            "features": "✨ 功能特性", 
            "reference": "📚 参考资料",
            "troubleshooting": "🔧 问题解决"
        }
        
        for category, topics in categories.items():
            # 添加类别标题
            category_item = QListWidgetItem(category_names.get(category, category))
            category_item.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
            category_item.setBackground(QColor("#ecf0f1"))
            category_item.setFlags(category_item.flags() & ~Qt.ItemIsSelectable)
            self.topic_list.addItem(category_item)
            
            # 添加主题
            for topic in topics:
                topic_item = QListWidgetItem(f"  {topic.title}")
                topic_item.setData(Qt.UserRole, topic)
                self.topic_list.addItem(topic_item)
    
    def _on_topic_selected(self, current, previous):
        """主题选择事件"""
        if current:
            topic = current.data(Qt.UserRole)
            if topic:
                self.content_title.setText(topic.title)
                self.content_text.setHtml(topic.content)


class UserGuideSystem:
    """用户引导系统管理器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.settings_file = "state/user/guide_settings.json"
        self.settings = self._load_settings()
    
    def should_show_first_time_guide(self) -> bool:
        """是否应该显示首次引导"""
        return self.settings.get("show_first_time_guide", True)
    
    def show_first_time_guide(self, parent=None) -> bool:
        """显示首次引导"""
        try:
            dialog = FirstTimeGuideDialog(parent)
            result = dialog.exec_()
            
            if result == QDialog.Accepted or not dialog.should_show_again():
                self.settings["show_first_time_guide"] = False
                self._save_settings()
            
            return result == QDialog.Accepted
            
        except Exception as e:
            self.logger.error(f"❌ 显示首次引导失败: {e}")
            return False
    
    def show_help_dialog(self, parent=None):
        """显示帮助对话框"""
        try:
            dialog = HelpDialog(parent)
            dialog.exec_()
            
        except Exception as e:
            self.logger.error(f"❌ 显示帮助对话框失败: {e}")
    
    def show_context_help(self, parent, widget_name: str, position: QPoint = None):
        """显示上下文帮助"""
        # TODO: 实现上下文帮助
        help_text = self._get_context_help_text(widget_name)
        if help_text:
            QToolTip.showText(position or QPoint(), help_text, parent)
    
    def _get_context_help_text(self, widget_name: str) -> str:
        """获取上下文帮助文本"""
        help_texts = {
            "file_path_edit": "选择要导入的Excel文件。支持.xlsx和.xls格式。",
            "smart_auto_mapping_btn": "智能分析Excel字段并自动推荐最佳映射关系。",
            "field_mapping_table": "字段映射配置表。左侧是Excel字段名，右侧是系统字段名。",
            "preview_table": "数据预览区域。显示配置应用后的数据效果。",
            "validation_results_text": "配置验证结果。显示错误、警告和优化建议。"
        }
        
        return help_texts.get(widget_name, "")
    
    def _load_settings(self) -> Dict[str, Any]:
        """加载设置"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"⚠️ 加载引导设置失败: {e}")
        
        return {"show_first_time_guide": True}
    
    def _save_settings(self):
        """保存设置"""
        try:
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"❌ 保存引导设置失败: {e}")


# 全局用户引导系统实例
_user_guide_system = None

def get_user_guide_system() -> UserGuideSystem:
    """获取全局用户引导系统实例"""
    global _user_guide_system
    if _user_guide_system is None:
        _user_guide_system = UserGuideSystem()
    return _user_guide_system


if __name__ == "__main__":
    """测试用户引导系统"""
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication([])
    
    # 测试首次引导
    guide_system = UserGuideSystem()
    if guide_system.should_show_first_time_guide():
        guide_system.show_first_time_guide()
    
    # 测试帮助对话框
    guide_system.show_help_dialog()
    
    app.quit()
