"""
批量操作功能组件
P3-001-4: 实现高级的批量数据处理和操作功能

功能特性:
- 批量编辑和更新
- 批量导入/导出
- 批量验证和清理
- 操作历史和撤销
- 进度跟踪和反馈
"""

import sys
import json
import csv
from typing import List, Dict, Any, Optional, Callable, Union
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel, QProgressBar,
    QTextEdit, QTabWidget, QGroupBox, QCheckBox, QSpinBox, QComboBox,
    QFileDialog, QMessageBox, QDialog, QDialogButtonBox, QFormLayout,
    QLineEdit, QFrame, QSplitter, QTreeWidget, QTreeWidgetItem,
    QHeaderView, QAbstractItemView, QToolBar, QAction, QMenu
)
from PyQt5.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QMutex, QWaitCondition,
    QObject, QPropertyAnimation, QEasingCurve
)
from PyQt5.QtGui import (
    QFont, QColor, QPalette, QIcon, QPixmap, QPainter, QBrush
)
import logging
from src.utils.log_config import setup_logger

# 设置日志
logger = setup_logger(__name__)


class BatchOperation:
    """批量操作定义"""
    
    def __init__(self, operation_id: str, name: str, description: str, 
                 operation_func: Callable, validation_func: Optional[Callable] = None):
        self.operation_id = operation_id
        self.name = name
        self.description = description
        self.operation_func = operation_func
        self.validation_func = validation_func
        self.created_time = datetime.now()
        self.executed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_execution = None
        
    def execute(self, data: Any) -> Dict[str, Any]:
        """执行操作"""
        try:
            self.executed_count += 1
            
            # 验证数据
            if self.validation_func and not self.validation_func(data):
                self.error_count += 1
                return {
                    'success': False,
                    'error': '数据验证失败',
                    'data': data
                }
            
            # 执行操作
            result = self.operation_func(data)
            self.success_count += 1
            self.last_execution = datetime.now()
            
            return {
                'success': True,
                'result': result,
                'data': data
            }
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"批量操作执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'data': data
            }


class BatchWorker(QThread):
    """批量操作工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, int, str)  # current, total, message
    operation_completed = pyqtSignal(dict)  # result
    batch_completed = pyqtSignal(dict)  # summary
    error_occurred = pyqtSignal(str)  # error message
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.operations = []
        self.data_items = []
        self.current_operation = None
        self.is_paused = False
        self.is_cancelled = False
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        
        logger.info("批量操作工作线程初始化完成")
    
    def set_batch_data(self, operations: List[BatchOperation], data_items: List[Any]):
        """设置批量数据"""
        self.operations = operations
        self.data_items = data_items
        self.is_cancelled = False
        self.is_paused = False
    
    def pause(self):
        """暂停执行"""
        self.is_paused = True
        logger.info("批量操作已暂停")
    
    def resume(self):
        """恢复执行"""
        self.mutex.lock()
        self.is_paused = False
        self.condition.wakeAll()
        self.mutex.unlock()
        logger.info("批量操作已恢复")
    
    def cancel(self):
        """取消执行"""
        self.is_cancelled = True
        self.resume()  # 确保线程能够退出
        logger.info("批量操作已取消")
    
    def run(self):
        """执行批量操作"""
        try:
            total_operations = len(self.operations) * len(self.data_items)
            current_count = 0
            results = []
            
            for operation in self.operations:
                if self.is_cancelled:
                    break
                
                self.current_operation = operation
                operation_results = []
                
                for data_item in self.data_items:
                    if self.is_cancelled:
                        break
                    
                    # 检查暂停状态
                    if self.is_paused:
                        self.mutex.lock()
                        self.condition.wait(self.mutex)
                        self.mutex.unlock()
                    
                    if self.is_cancelled:
                        break
                    
                    # 执行操作
                    current_count += 1
                    message = f"执行操作: {operation.name} ({current_count}/{total_operations})"
                    self.progress_updated.emit(current_count, total_operations, message)
                    
                    result = operation.execute(data_item)
                    operation_results.append(result)
                    
                    self.operation_completed.emit({
                        'operation': operation.name,
                        'result': result,
                        'progress': current_count / total_operations * 100
                    })
                    
                    # 短暂延迟，避免界面卡顿
                    self.msleep(10)
                
                results.append({
                    'operation': operation,
                    'results': operation_results
                })
            
            # 发送完成信号
            if not self.is_cancelled:
                summary = self.generate_summary(results)
                self.batch_completed.emit(summary)
                logger.info(f"批量操作完成: {summary}")
            
        except Exception as e:
            error_msg = f"批量操作执行失败: {e}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
    
    def generate_summary(self, results: List[Dict]) -> Dict[str, Any]:
        """生成执行摘要"""
        total_operations = 0
        total_success = 0
        total_errors = 0
        operation_summaries = []
        
        for result_group in results:
            operation = result_group['operation']
            operation_results = result_group['results']
            
            success_count = sum(1 for r in operation_results if r['success'])
            error_count = len(operation_results) - success_count
            
            total_operations += len(operation_results)
            total_success += success_count
            total_errors += error_count
            
            operation_summaries.append({
                'name': operation.name,
                'total': len(operation_results),
                'success': success_count,
                'errors': error_count,
                'success_rate': success_count / len(operation_results) * 100 if operation_results else 0
            })
        
        return {
            'total_operations': total_operations,
            'total_success': total_success,
            'total_errors': total_errors,
            'success_rate': total_success / total_operations * 100 if total_operations else 0,
            'operation_summaries': operation_summaries,
            'execution_time': datetime.now(),
            'cancelled': self.is_cancelled
        }


class BatchOperationDialog(QDialog):
    """批量操作配置对话框"""
    
    def __init__(self, available_operations: List[BatchOperation], parent=None):
        super().__init__(parent)
        self.available_operations = available_operations
        self.selected_operations = []
        self.setup_ui()
        logger.info("批量操作配置对话框初始化完成")
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("批量操作配置")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🔧 配置批量操作")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #1976d2;
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 6px;
                margin-bottom: 10px;
            }
        """)
        
        # 操作选择
        operations_group = QGroupBox("选择要执行的操作")
        operations_layout = QVBoxLayout(operations_group)
        
        self.operation_checkboxes = {}
        for operation in self.available_operations:
            checkbox = QCheckBox(f"{operation.name} - {operation.description}")
            checkbox.setChecked(False)
            self.operation_checkboxes[operation.operation_id] = checkbox
            operations_layout.addWidget(checkbox)
        
        # 操作参数
        params_group = QGroupBox("操作参数")
        params_layout = QFormLayout(params_group)
        
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 1000)
        self.batch_size_spin.setValue(100)
        self.batch_size_spin.setSuffix(" 条/批")
        
        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(0, 5000)
        self.delay_spin.setValue(10)
        self.delay_spin.setSuffix(" ms")
        
        self.error_handling = QComboBox()
        self.error_handling.addItems(["跳过错误继续", "遇到错误停止", "重试3次后跳过"])
        
        params_layout.addRow("批次大小:", self.batch_size_spin)
        params_layout.addRow("操作延迟:", self.delay_spin)
        params_layout.addRow("错误处理:", self.error_handling)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # 布局
        layout.addWidget(title_label)
        layout.addWidget(operations_group)
        layout.addWidget(params_group)
        layout.addWidget(button_box)
    
    def get_selected_operations(self) -> List[BatchOperation]:
        """获取选中的操作"""
        selected = []
        for operation in self.available_operations:
            checkbox = self.operation_checkboxes[operation.operation_id]
            if checkbox.isChecked():
                selected.append(operation)
        return selected
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取操作参数"""
        return {
            'batch_size': self.batch_size_spin.value(),
            'delay': self.delay_spin.value(),
            'error_handling': self.error_handling.currentText()
        }


class BatchProgressWidget(QWidget):
    """批量操作进度显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.animation = None
        logger.info("批量操作进度组件初始化完成")
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 总体进度
        self.overall_progress = QProgressBar()
        self.overall_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: #f0f0f0;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #4caf50, stop: 1 #8bc34a
                );
                border-radius: 6px;
            }
        """)
        
        # 当前操作信息
        self.current_operation_label = QLabel("等待开始...")
        self.current_operation_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #333;
                padding: 5px;
            }
        """)
        
        # 统计信息
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Box)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        stats_layout = QHBoxLayout(stats_frame)
        
        self.success_label = QLabel("成功: 0")
        self.error_label = QLabel("错误: 0")
        self.total_label = QLabel("总计: 0")
        self.rate_label = QLabel("成功率: 0%")
        
        for label in [self.success_label, self.error_label, self.total_label, self.rate_label]:
            label.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    padding: 5px 10px;
                    border-radius: 4px;
                    background-color: white;
                }
            """)
        
        self.success_label.setStyleSheet(self.success_label.styleSheet() + "color: #28a745;")
        self.error_label.setStyleSheet(self.error_label.styleSheet() + "color: #dc3545;")
        
        stats_layout.addWidget(self.success_label)
        stats_layout.addWidget(self.error_label)
        stats_layout.addWidget(self.total_label)
        stats_layout.addWidget(self.rate_label)
        stats_layout.addStretch()
        
        # 操作日志
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #fafafa;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        
        # 布局
        layout.addWidget(QLabel("批量操作进度"))
        layout.addWidget(self.overall_progress)
        layout.addWidget(self.current_operation_label)
        layout.addWidget(stats_frame)
        layout.addWidget(QLabel("操作日志"))
        layout.addWidget(self.log_text)
    
    def update_progress(self, current: int, total: int, message: str):
        """更新进度"""
        if total > 0:
            progress = int(current / total * 100)
            self.overall_progress.setValue(progress)
            self.overall_progress.setFormat(f"{current}/{total} ({progress}%)")
        
        self.current_operation_label.setText(message)
        
        # 添加日志
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
    
    def update_statistics(self, success: int, errors: int, total: int):
        """更新统计信息"""
        self.success_label.setText(f"成功: {success}")
        self.error_label.setText(f"错误: {errors}")
        self.total_label.setText(f"总计: {total}")
        
        if total > 0:
            rate = success / total * 100
            self.rate_label.setText(f"成功率: {rate:.1f}%")
        else:
            self.rate_label.setText("成功率: 0%")
    
    def reset(self):
        """重置进度"""
        self.overall_progress.setValue(0)
        self.overall_progress.setFormat("0/0 (0%)")
        self.current_operation_label.setText("等待开始...")
        self.update_statistics(0, 0, 0)
        self.log_text.clear()
    
    def complete(self, summary: Dict[str, Any]):
        """显示完成状态"""
        self.overall_progress.setValue(100)
        
        if summary.get('cancelled', False):
            self.current_operation_label.setText("❌ 操作已取消")
            self.overall_progress.setStyleSheet("""
                QProgressBar::chunk {
                    background-color: #ffc107;
                }
            """)
        else:
            self.current_operation_label.setText("✅ 批量操作完成")
            if summary.get('success_rate', 0) >= 95:
                self.overall_progress.setStyleSheet("""
                    QProgressBar::chunk {
                        background-color: #28a745;
                    }
                """)
        
        # 更新最终统计
        self.update_statistics(
            summary.get('total_success', 0),
            summary.get('total_errors', 0),
            summary.get('total_operations', 0)
        )
        
        # 添加完成日志
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"\n[{timestamp}] === 批量操作完成 ===")
        for op_summary in summary.get('operation_summaries', []):
            self.log_text.append(
                f"[{timestamp}] {op_summary['name']}: "
                f"{op_summary['success']}/{op_summary['total']} "
                f"({op_summary['success_rate']:.1f}%)"
            )


class BatchOperationsManager(QWidget):
    """批量操作管理器"""
    
    # 信号定义
    operation_started = pyqtSignal()
    operation_completed = pyqtSignal(dict)
    operation_cancelled = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.available_operations = []
        self.worker = None
        self.setup_ui()
        self.setup_default_operations()
        logger.info("批量操作管理器初始化完成")
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = QToolBar()
        toolbar.setStyleSheet("""
            QToolBar {
                border: none;
                background-color: #f8f9fa;
                spacing: 5px;
            }
            QToolBar QToolButton {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 12px;
            }
            QToolBar QToolButton:hover {
                background-color: #e9ecef;
            }
        """)
        
        self.start_action = QAction("▶️ 开始批量操作", self)
        self.pause_action = QAction("⏸️ 暂停", self)
        self.resume_action = QAction("▶️ 恢复", self)
        self.cancel_action = QAction("⏹️ 取消", self)
        self.export_action = QAction("📤 导出结果", self)
        
        toolbar.addAction(self.start_action)
        toolbar.addSeparator()
        toolbar.addAction(self.pause_action)
        toolbar.addAction(self.resume_action)
        toolbar.addAction(self.cancel_action)
        toolbar.addSeparator()
        toolbar.addAction(self.export_action)
        
        # 连接信号
        self.start_action.triggered.connect(self.start_batch_operation)
        self.pause_action.triggered.connect(self.pause_operation)
        self.resume_action.triggered.connect(self.resume_operation)
        self.cancel_action.triggered.connect(self.cancel_operation)
        self.export_action.triggered.connect(self.export_results)
        
        # 设置初始状态
        self.pause_action.setEnabled(False)
        self.resume_action.setEnabled(False)
        self.cancel_action.setEnabled(False)
        self.export_action.setEnabled(False)
        
        # 主要内容
        splitter = QSplitter(Qt.Vertical)
        
        # 进度显示
        self.progress_widget = BatchProgressWidget()
        
        # 数据表格
        self.data_table = QTableWidget()
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.data_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 6px;
                gridline-color: #eee;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                border: none;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        splitter.addWidget(self.progress_widget)
        splitter.addWidget(self.data_table)
        splitter.setSizes([200, 400])
        
        # 布局
        layout.addWidget(toolbar)
        layout.addWidget(splitter)
    
    def setup_default_operations(self):
        """设置默认操作"""
        # 数据清理操作
        self.available_operations.append(BatchOperation(
            "clean_data",
            "数据清理",
            "清理空值、重复值和格式错误",
            self.clean_data_operation
        ))
        
        # 数据验证操作
        self.available_operations.append(BatchOperation(
            "validate_data",
            "数据验证",
            "验证数据完整性和格式正确性",
            self.validate_data_operation,
            self.validate_data_validation
        ))
        
        # 数据转换操作
        self.available_operations.append(BatchOperation(
            "transform_data",
            "数据转换",
            "转换数据格式和类型",
            self.transform_data_operation
        ))
        
        # 数据计算操作
        self.available_operations.append(BatchOperation(
            "calculate_data",
            "数据计算",
            "执行数据计算和统计",
            self.calculate_data_operation
        ))
        
        logger.info(f"默认操作设置完成: {len(self.available_operations)}个操作")
    
    def clean_data_operation(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """数据清理操作"""
        cleaned_data = data.copy()
        
        # 清理空值
        for key, value in cleaned_data.items():
            if value is None or value == "":
                cleaned_data[key] = "N/A"
            elif isinstance(value, str):
                cleaned_data[key] = value.strip()
        
        return cleaned_data
    
    def validate_data_operation(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """数据验证操作"""
        validation_result = {
            'original_data': data,
            'is_valid': True,
            'errors': []
        }
        
        # 验证必填字段
        required_fields = ['name', 'department']
        for field in required_fields:
            if field not in data or not data[field]:
                validation_result['is_valid'] = False
                validation_result['errors'].append(f"缺少必填字段: {field}")
        
        return validation_result
    
    def validate_data_validation(self, data: Dict[str, Any]) -> bool:
        """数据验证的验证函数"""
        return isinstance(data, dict) and len(data) > 0
    
    def transform_data_operation(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """数据转换操作"""
        transformed_data = data.copy()
        
        # 转换数值字段
        if 'salary' in transformed_data:
            try:
                salary = str(transformed_data['salary']).replace(',', '')
                transformed_data['salary'] = float(salary)
            except (ValueError, TypeError):
                transformed_data['salary'] = 0.0
        
        # 标准化文本字段
        if 'name' in transformed_data:
            transformed_data['name'] = str(transformed_data['name']).title()
        
        return transformed_data
    
    def calculate_data_operation(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """数据计算操作"""
        calculated_data = data.copy()
        
        # 计算年薪
        if 'salary' in data:
            try:
                monthly_salary = float(data['salary'])
                calculated_data['annual_salary'] = monthly_salary * 12
            except (ValueError, TypeError):
                calculated_data['annual_salary'] = 0.0
        
        # 计算数据完整度
        total_fields = len(data)
        filled_fields = sum(1 for v in data.values() if v not in [None, "", "N/A"])
        calculated_data['completeness'] = filled_fields / total_fields * 100 if total_fields > 0 else 0
        
        return calculated_data
    
    def load_data(self, data: List[Dict[str, Any]]):
        """加载数据到表格"""
        if not data:
            return
        
        # 设置表格
        self.data_table.setRowCount(len(data))
        
        # 获取所有字段
        all_fields = set()
        for item in data:
            all_fields.update(item.keys())
        
        fields = sorted(list(all_fields))
        self.data_table.setColumnCount(len(fields))
        self.data_table.setHorizontalHeaderLabels(fields)
        
        # 填充数据
        for row, item in enumerate(data):
            for col, field in enumerate(fields):
                value = item.get(field, "")
                self.data_table.setItem(row, col, QTableWidgetItem(str(value)))
        
        # 调整列宽
        self.data_table.resizeColumnsToContents()
        
        logger.info(f"数据加载完成: {len(data)}行 x {len(fields)}列")
    
    def get_table_data(self) -> List[Dict[str, Any]]:
        """从表格获取数据"""
        data = []
        
        if self.data_table.rowCount() == 0:
            return data
        
        # 获取表头
        headers = []
        for col in range(self.data_table.columnCount()):
            header_item = self.data_table.horizontalHeaderItem(col)
            headers.append(header_item.text() if header_item else f"Column_{col}")
        
        # 获取数据
        for row in range(self.data_table.rowCount()):
            row_data = {}
            for col in range(self.data_table.columnCount()):
                item = self.data_table.item(row, col)
                row_data[headers[col]] = item.text() if item else ""
            data.append(row_data)
        
        return data
    
    def start_batch_operation(self):
        """开始批量操作"""
        try:
            # 获取数据
            data = self.get_table_data()
            if not data:
                QMessageBox.warning(self, "警告", "没有可处理的数据")
                return
            
            # 显示配置对话框
            dialog = BatchOperationDialog(self.available_operations, self)
            if dialog.exec_() != QDialog.Accepted:
                return
            
            selected_operations = dialog.get_selected_operations()
            if not selected_operations:
                QMessageBox.warning(self, "警告", "请至少选择一个操作")
                return
            
            parameters = dialog.get_parameters()
            
            # 创建工作线程
            self.worker = BatchWorker()
            self.worker.set_batch_data(selected_operations, data)
            
            # 连接信号
            self.worker.progress_updated.connect(self.progress_widget.update_progress)
            self.worker.operation_completed.connect(self.on_operation_completed)
            self.worker.batch_completed.connect(self.on_batch_completed)
            self.worker.error_occurred.connect(self.on_error_occurred)
            
            # 更新UI状态
            self.start_action.setEnabled(False)
            self.pause_action.setEnabled(True)
            self.cancel_action.setEnabled(True)
            
            # 重置进度
            self.progress_widget.reset()
            
            # 启动线程
            self.worker.start()
            self.operation_started.emit()
            
            logger.info(f"批量操作开始: {len(selected_operations)}个操作, {len(data)}条数据")
            
        except Exception as e:
            error_msg = f"启动批量操作失败: {e}"
            logger.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def pause_operation(self):
        """暂停操作"""
        if self.worker:
            self.worker.pause()
            self.pause_action.setEnabled(False)
            self.resume_action.setEnabled(True)
    
    def resume_operation(self):
        """恢复操作"""
        if self.worker:
            self.worker.resume()
            self.pause_action.setEnabled(True)
            self.resume_action.setEnabled(False)
    
    def cancel_operation(self):
        """取消操作"""
        if self.worker:
            self.worker.cancel()
            self.worker.wait()  # 等待线程结束
            self.reset_ui_state()
            self.operation_cancelled.emit()
    
    def on_operation_completed(self, result: Dict[str, Any]):
        """单个操作完成处理"""
        # 更新统计信息
        # 这里可以添加更详细的处理逻辑
        pass
    
    def on_batch_completed(self, summary: Dict[str, Any]):
        """批量操作完成处理"""
        self.progress_widget.complete(summary)
        self.reset_ui_state()
        self.export_action.setEnabled(True)
        self.operation_completed.emit(summary)
        
        # 显示完成消息
        QMessageBox.information(
            self, "完成",
            f"批量操作完成！\n"
            f"总操作数: {summary['total_operations']}\n"
            f"成功: {summary['total_success']}\n"
            f"错误: {summary['total_errors']}\n"
            f"成功率: {summary['success_rate']:.1f}%"
        )
    
    def on_error_occurred(self, error_msg: str):
        """错误处理"""
        self.reset_ui_state()
        QMessageBox.critical(self, "错误", f"批量操作出现错误:\n{error_msg}")
    
    def reset_ui_state(self):
        """重置UI状态"""
        self.start_action.setEnabled(True)
        self.pause_action.setEnabled(False)
        self.resume_action.setEnabled(False)
        self.cancel_action.setEnabled(False)
    
    def export_results(self):
        """导出结果"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出结果", "batch_results.csv", "CSV文件 (*.csv)"
            )
            
            if file_path:
                data = self.get_table_data()
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    if data:
                        fieldnames = data[0].keys()
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(data)
                
                QMessageBox.information(self, "成功", f"结果已导出到:\n{file_path}")
                logger.info(f"批量操作结果已导出: {file_path}")
                
        except Exception as e:
            error_msg = f"导出结果失败: {e}"
            logger.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)


class BatchOperationsDemo(QMainWindow):
    """批量操作功能演示"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_demo_data()
        logger.info("批量操作功能演示启动")
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("批量操作功能演示 - P3-001-4")
        self.setGeometry(100, 100, 1000, 700)
        
        # 中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🎯 P3-001-4: 批量操作功能演示")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        
        # 操作说明
        info_label = QLabel("""
        📋 功能特性:
        • 支持多种批量操作: 数据清理、验证、转换、计算
        • 可配置的批量处理参数和错误处理策略
        • 实时进度跟踪和详细的操作日志
        • 支持暂停、恢复、取消操作
        • 操作结果导出和统计分析
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 1px solid #c8e6c9;
                border-radius: 6px;
                padding: 10px;
                font-size: 12px;
                color: #2e7d32;
            }
        """)
        
        # 批量操作管理器
        self.batch_manager = BatchOperationsManager()
        
        # 布局
        layout.addWidget(title_label)
        layout.addWidget(info_label)
        layout.addWidget(self.batch_manager)
    
    def setup_demo_data(self):
        """设置演示数据"""
        demo_data = [
            {"name": "张三", "department": "技术部", "position": "工程师", "salary": "15000"},
            {"name": "李四", "department": "产品部", "position": "产品经理", "salary": "12,000"},
            {"name": "王五", "department": "", "position": "设计师", "salary": "10000"},
            {"name": "", "department": "运营部", "position": "专员", "salary": "8000"},
            {"name": "钱七", "department": "人事部", "position": "HR", "salary": ""},
            {"name": "孙八", "department": "财务部", "position": "会计", "salary": "9000"},
            {"name": "周九", "department": "市场部", "position": "市场专员", "salary": "8,500"},
            {"name": "吴十", "department": "技术部", "position": "前端工程师", "salary": "13000"},
        ]
        
        self.batch_manager.load_data(demo_data)
        logger.info(f"演示数据加载完成: {len(demo_data)}条记录")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建演示窗口
    demo = BatchOperationsDemo()
    demo.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 