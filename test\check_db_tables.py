#!/usr/bin/env python3
"""
检查数据库表结构
"""

import sys
sys.path.append('.')
import sqlite3
import os

def check_database_tables():
    """检查数据库表结构"""
    # 查找数据库文件
    db_paths = [
        'state/data/salary_data.db',
        'data/db/salary_system.db',
        'state/salary_data.db'
    ]

    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f'\n找到数据库: {db_path}')

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查询所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            all_tables = cursor.fetchall()
            print(f'总表数量: {len(all_tables)}')

            # 查询所有异动表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'change_data%'")
            change_tables = cursor.fetchall()
            print(f'异动表数量: {len(change_tables)}')

            # 查询所有工资表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data%'")
            salary_tables = cursor.fetchall()
            print(f'工资表数量: {len(salary_tables)}')

            # 显示异动表
            if change_tables:
                print('\n=== 异动表 ===')
                for table_name, in change_tables:
                    cursor.execute(f'PRAGMA table_info({table_name})')
                    columns = cursor.fetchall()
                    print(f'\n--- {table_name} ---')
                    print(f'数据库字段数量: {len(columns)}')

                    if columns:
                        print('字段列表:')
                        for i, col in enumerate(columns, 1):
                            print(f'  {i:2d}. {col[1]} ({col[2]})')
                    else:
                        print('  (表为空或不存在)')

            # 显示工资表
            if salary_tables:
                print('\n=== 工资表 ===')
                for table_name, in salary_tables:
                    cursor.execute(f'PRAGMA table_info({table_name})')
                    columns = cursor.fetchall()
                    print(f'\n--- {table_name} ---')
                    print(f'数据库字段数量: {len(columns)}')

                    if columns:
                        print('字段列表:')
                        system_fields = []
                        business_fields = []

                        for i, col in enumerate(columns, 1):
                            field_name = col[1]
                            field_type = col[2]

                            # 分类系统字段和业务字段
                            if field_name in ['id', 'employee_id', 'sequence_number', 'month', 'year', 'created_at', 'updated_at', 'data_source', 'import_time']:
                                system_fields.append(f'  {i:2d}. {field_name} ({field_type}) [系统字段]')
                            else:
                                business_fields.append(f'  {i:2d}. {field_name} ({field_type}) [业务字段]')

                        print('系统字段:')
                        for field in system_fields:
                            print(field)
                        print(f'业务字段: ({len(business_fields)}个)')
                        for field in business_fields[:5]:  # 只显示前5个业务字段
                            print(field)
                        if len(business_fields) > 5:
                            print(f'  ... 还有{len(business_fields)-5}个业务字段')
                    else:
                        print('  (表为空或不存在)')

            if not change_tables and not salary_tables:
                print('未找到异动表和工资表，显示所有表:')
                for table_name, in all_tables:
                    print(f'  - {table_name}')

            conn.close()

    print('\n检查完成')

if __name__ == '__main__':
    check_database_tables()
