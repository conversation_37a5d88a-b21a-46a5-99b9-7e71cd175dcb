#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的错误处理机制

提供详细的错误信息、恢复策略和用户友好的错误反馈
"""

import traceback
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
from loguru import logger


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"           # 轻微错误，不影响主要功能
    MEDIUM = "medium"     # 中等错误，影响部分功能
    HIGH = "high"         # 严重错误，影响主要功能
    CRITICAL = "critical" # 致命错误，系统无法继续运行


class ErrorCategory(Enum):
    """错误类别"""
    CONFIG = "config"           # 配置相关错误
    DATA = "data"              # 数据处理错误
    UI = "ui"                  # 界面相关错误
    NETWORK = "network"        # 网络相关错误
    FILE = "file"              # 文件操作错误
    VALIDATION = "validation"   # 数据验证错误
    SYSTEM = "system"          # 系统级错误


@dataclass
class ErrorContext:
    """错误上下文信息"""
    operation: str = ""                    # 执行的操作
    component: str = ""                    # 出错的组件
    user_action: str = ""                  # 用户执行的动作
    data_context: Dict[str, Any] = field(default_factory=dict)  # 数据上下文
    system_context: Dict[str, Any] = field(default_factory=dict)  # 系统上下文


@dataclass
class RecoveryAction:
    """恢复动作"""
    name: str                              # 动作名称
    description: str                       # 动作描述
    action: Callable[[], bool]            # 恢复函数
    auto_execute: bool = False            # 是否自动执行
    priority: int = 1                     # 优先级（1-10，数字越大优先级越高）


@dataclass
class ErrorInfo:
    """错误信息"""
    error_id: str                         # 错误ID
    severity: ErrorSeverity               # 严重程度
    category: ErrorCategory               # 错误类别
    title: str                           # 错误标题
    message: str                         # 错误消息
    technical_details: str = ""          # 技术细节
    user_message: str = ""               # 用户友好消息
    context: ErrorContext = field(default_factory=ErrorContext)  # 错误上下文
    recovery_actions: List[RecoveryAction] = field(default_factory=list)  # 恢复动作
    timestamp: datetime = field(default_factory=datetime.now)  # 时间戳
    resolved: bool = False               # 是否已解决


class EnhancedErrorHandler:
    """增强的错误处理器"""
    
    def __init__(self):
        self.error_history: List[ErrorInfo] = []
        self.recovery_strategies: Dict[str, List[RecoveryAction]] = {}
        self.error_patterns: Dict[str, ErrorInfo] = {}
        self.auto_recovery_enabled = True
        
    def register_error_pattern(self, pattern_id: str, error_template: ErrorInfo):
        """注册错误模式"""
        self.error_patterns[pattern_id] = error_template
        logger.debug(f"注册错误模式: {pattern_id}")
    
    def register_recovery_strategy(self, error_category: str, actions: List[RecoveryAction]):
        """注册恢复策略"""
        if error_category not in self.recovery_strategies:
            self.recovery_strategies[error_category] = []
        self.recovery_strategies[error_category].extend(actions)
        logger.debug(f"注册恢复策略: {error_category}, {len(actions)} 个动作")
    
    def handle_error(self, 
                    exception: Exception,
                    context: Optional[ErrorContext] = None,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    category: ErrorCategory = ErrorCategory.SYSTEM,
                    custom_message: str = "") -> ErrorInfo:
        """处理错误"""
        
        # 生成错误ID
        error_id = f"{category.value}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # 获取技术细节
        technical_details = self._get_technical_details(exception)
        
        # 生成用户友好消息
        user_message = custom_message or self._generate_user_message(exception, category)
        
        # 创建错误信息
        error_info = ErrorInfo(
            error_id=error_id,
            severity=severity,
            category=category,
            title=f"{category.value.title()} Error",
            message=str(exception),
            technical_details=technical_details,
            user_message=user_message,
            context=context or ErrorContext()
        )
        
        # 查找恢复动作
        error_info.recovery_actions = self._find_recovery_actions(category, exception)
        
        # 记录错误
        self.error_history.append(error_info)
        logger.error(f"错误处理: {error_id} - {error_info.title}: {error_info.message}")
        
        # 尝试自动恢复
        if self.auto_recovery_enabled:
            self._attempt_auto_recovery(error_info)
        
        return error_info
    
    def handle_config_error(self, 
                           exception: Exception,
                           config_name: str = "",
                           operation: str = "") -> ErrorInfo:
        """处理配置相关错误"""
        context = ErrorContext(
            operation=operation,
            component="ConfigManager",
            data_context={"config_name": config_name}
        )
        
        return self.handle_error(
            exception=exception,
            context=context,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.CONFIG,
            custom_message=f"配置 '{config_name}' 处理失败"
        )
    
    def handle_data_error(self,
                         exception: Exception,
                         data_source: str = "",
                         operation: str = "") -> ErrorInfo:
        """处理数据相关错误"""
        context = ErrorContext(
            operation=operation,
            component="DataProcessor",
            data_context={"data_source": data_source}
        )
        
        return self.handle_error(
            exception=exception,
            context=context,
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.DATA,
            custom_message=f"数据处理失败: {data_source}"
        )
    
    def handle_ui_error(self,
                       exception: Exception,
                       widget_name: str = "",
                       user_action: str = "") -> ErrorInfo:
        """处理UI相关错误"""
        context = ErrorContext(
            operation="UI操作",
            component=widget_name,
            user_action=user_action
        )
        
        return self.handle_error(
            exception=exception,
            context=context,
            severity=ErrorSeverity.LOW,
            category=ErrorCategory.UI,
            custom_message=f"界面操作失败: {user_action}"
        )
    
    def _get_technical_details(self, exception: Exception) -> str:
        """获取技术细节"""
        return traceback.format_exc()
    
    def _generate_user_message(self, exception: Exception, category: ErrorCategory) -> str:
        """生成用户友好消息"""
        messages = {
            ErrorCategory.CONFIG: "配置文件可能损坏或格式不正确，请检查配置设置",
            ErrorCategory.DATA: "数据处理过程中出现问题，请检查数据格式和内容",
            ErrorCategory.UI: "界面操作出现问题，请尝试重新操作或重启应用",
            ErrorCategory.FILE: "文件操作失败，请检查文件权限和磁盘空间",
            ErrorCategory.NETWORK: "网络连接出现问题，请检查网络设置",
            ErrorCategory.VALIDATION: "数据验证失败，请检查输入数据的格式和内容",
            ErrorCategory.SYSTEM: "系统出现问题，请联系技术支持"
        }
        
        base_message = messages.get(category, "出现未知错误")
        
        # 根据异常类型提供更具体的建议
        if isinstance(exception, FileNotFoundError):
            return f"{base_message}。文件未找到，请确认文件路径是否正确。"
        elif isinstance(exception, PermissionError):
            return f"{base_message}。权限不足，请以管理员身份运行或检查文件权限。"
        elif isinstance(exception, ValueError):
            return f"{base_message}。数据值不正确，请检查输入数据的格式。"
        elif isinstance(exception, KeyError):
            return f"{base_message}。缺少必要的配置项或数据字段。"
        else:
            return base_message
    
    def _find_recovery_actions(self, category: ErrorCategory, exception: Exception) -> List[RecoveryAction]:
        """查找恢复动作"""
        actions = []
        
        # 从注册的策略中查找
        category_key = category.value
        if category_key in self.recovery_strategies:
            actions.extend(self.recovery_strategies[category_key])
        
        # 根据异常类型添加通用恢复动作
        if isinstance(exception, FileNotFoundError):
            actions.append(RecoveryAction(
                name="重新选择文件",
                description="选择正确的文件路径",
                action=lambda: True,  # 占位符
                priority=8
            ))
        elif isinstance(exception, PermissionError):
            actions.append(RecoveryAction(
                name="检查权限",
                description="以管理员身份运行或修改文件权限",
                action=lambda: True,  # 占位符
                priority=7
            ))
        
        # 通用恢复动作
        actions.append(RecoveryAction(
            name="重试操作",
            description="重新执行失败的操作",
            action=lambda: True,  # 占位符
            priority=5
        ))
        
        actions.append(RecoveryAction(
            name="重置配置",
            description="恢复默认配置设置",
            action=lambda: True,  # 占位符
            priority=3
        ))
        
        # 按优先级排序
        actions.sort(key=lambda x: x.priority, reverse=True)
        
        return actions
    
    def _attempt_auto_recovery(self, error_info: ErrorInfo):
        """尝试自动恢复"""
        for action in error_info.recovery_actions:
            if action.auto_execute:
                try:
                    logger.info(f"尝试自动恢复: {action.name}")
                    if action.action():
                        error_info.resolved = True
                        logger.info(f"自动恢复成功: {action.name}")
                        break
                except Exception as e:
                    logger.warning(f"自动恢复失败: {action.name} - {e}")
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误摘要"""
        cutoff_time = datetime.now().timestamp() - (hours * 3600)
        recent_errors = [
            error for error in self.error_history
            if error.timestamp.timestamp() > cutoff_time
        ]
        
        summary = {
            "total_errors": len(recent_errors),
            "by_severity": {},
            "by_category": {},
            "resolved_count": sum(1 for e in recent_errors if e.resolved),
            "unresolved_count": sum(1 for e in recent_errors if not e.resolved)
        }
        
        for error in recent_errors:
            # 按严重程度统计
            severity = error.severity.value
            summary["by_severity"][severity] = summary["by_severity"].get(severity, 0) + 1
            
            # 按类别统计
            category = error.category.value
            summary["by_category"][category] = summary["by_category"].get(category, 0) + 1
        
        return summary
    
    def clear_resolved_errors(self):
        """清除已解决的错误"""
        self.error_history = [error for error in self.error_history if not error.resolved]
        logger.info("已清除已解决的错误记录")


# 全局错误处理器实例
_global_error_handler = None

def get_error_handler() -> EnhancedErrorHandler:
    """获取全局错误处理器实例"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = EnhancedErrorHandler()
    return _global_error_handler


# 导出的类和函数
__all__ = [
    "ErrorSeverity",
    "ErrorCategory", 
    "ErrorContext",
    "RecoveryAction",
    "ErrorInfo",
    "EnhancedErrorHandler",
    "get_error_handler"
]
