#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复字段映射不一致问题
统一字段映射格式：中文字段名 -> 英文字段名
"""

import json
from pathlib import Path
from datetime import datetime

def fix_field_mappings():
    """修复字段映射不一致"""
    
    # 读取现有映射文件
    mapping_file = Path("state/data/field_mappings.json")
    
    with open(mapping_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("="*60)
    print("字段映射修复工具")
    print("="*60)
    
    # 需要修复的模板（这些模板目前是英文->中文，需要改为中文->英文）
    templates_to_fix = ["全部在职人员工资表", "A岗职工"]
    
    fixed_count = 0
    
    for template_name in templates_to_fix:
        if template_name in data["field_templates"]:
            old_template = data["field_templates"][template_name]
            new_template = {}
            
            print(f"\n修复模板: {template_name}")
            print("-"*40)
            
            # 反转映射方向
            for eng_key, cn_value in old_template.items():
                new_template[cn_value] = eng_key
                print(f"  {cn_value} -> {eng_key}")
                fixed_count += 1
            
            # 更新模板
            data["field_templates"][template_name] = new_template
    
    # 添加更多常用字段映射到table_mappings
    common_mappings = {
        "change_data_2025_12_全部在职人员工资表": {
            "序号": "sequence_number",
            "工号": "employee_id",
            "姓名": "employee_name",
            "部门名称": "department",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",
            "2025年岗位工资": "position_salary_2025",
            "2025年薪级工资": "grade_salary_2025",
            "津贴": "allowance",
            "结余津贴": "balance_allowance",
            "2025年基础性绩效": "basic_performance_2025",
            "卫生费": "health_fee",
            "交通补贴": "transport_allowance",
            "物业补贴": "property_allowance",
            "住房补贴": "housing_allowance",
            "车补": "car_allowance",
            "通讯补贴": "communication_allowance",
            "2025年奖励性绩效预发": "performance_bonus_2025",
            "补发": "supplement",
            "借支": "advance",
            "应发工资": "total_salary",
            "2025公积金": "provident_fund_2025",
            "代扣代存养老保险": "pension_insurance",
            "年份": "year",
            "月份": "month"
        },
        "change_data_2025_12_A岗职工": {
            "序号": "sequence_number",
            "工号": "employee_id",
            "姓名": "employee_name",
            "部门名称": "department",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",
            "2025年岗位工资": "position_salary_2025",
            "2025年校龄工资": "seniority_salary_2025",
            "津贴": "allowance",
            "结余津贴": "balance_allowance",
            "2025年基础性绩效": "basic_performance_2025",
            "卫生费": "health_fee",
            "2025年生活补贴": "living_allowance_2025",
            "车补": "car_allowance",
            "2025年奖励性绩效预发": "performance_bonus_2025",
            "补发": "supplement",
            "借支": "advance",
            "应发工资": "total_salary",
            "2025公积金": "provident_fund_2025",
            "保险扣款": "insurance_deduction",
            "代扣代存养老保险": "pension_insurance",
            "年份": "year",
            "月份": "month"
        }
    }
    
    # 更新table_mappings
    for table_name, mappings in common_mappings.items():
        if table_name not in data["table_mappings"]:
            data["table_mappings"][table_name] = mappings
            print(f"\n添加表映射: {table_name}")
            fixed_count += len(mappings)
    
    # 更新时间戳和版本
    data["last_updated"] = datetime.now().isoformat()
    data["version"] = "2.1"
    
    # 备份原文件
    backup_file = mapping_file.with_suffix('.json.bak')
    with open(backup_file, 'w', encoding='utf-8') as f:
        with open(mapping_file, 'r', encoding='utf-8') as orig:
            f.write(orig.read())
    print(f"\n已备份原文件到: {backup_file}")
    
    # 写入修复后的内容
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"\n修复完成！共修复/添加 {fixed_count} 个字段映射")
    print(f"文件已更新: {mapping_file}")
    
    return fixed_count

if __name__ == "__main__":
    fix_field_mappings()