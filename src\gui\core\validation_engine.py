"""
验证引擎
用于验证数据导入配置和数据有效性
"""

import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from src.modules.logging.setup_logger import setup_logger


class ValidationLevel(Enum):
    """验证级别"""
    ERROR = "error"       # 错误，必须修复
    WARNING = "warning"   # 警告，建议修复
    INFO = "info"         # 信息，参考性质


@dataclass
class ValidationIssue:
    """验证问题"""
    level: ValidationLevel     # 问题级别
    field: str                # 相关字段
    message: str              # 问题描述
    suggestion: str = ""      # 修复建议
    row_number: Optional[int] = None  # 行号（如果适用）


@dataclass
class ValidationReport:
    """验证报告"""
    is_valid: bool                    # 是否通过验证
    total_issues: int                 # 问题总数
    error_count: int                  # 错误数量
    warning_count: int                # 警告数量
    info_count: int                   # 信息数量
    issues: List[ValidationIssue]     # 问题列表
    summary: str                      # 验证摘要


class MappingValidator:
    """映射配置验证器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
    
    def validate_mapping_config(self, mapping_config: Dict[str, Dict], 
                               table_type: str) -> List[ValidationIssue]:
        """验证映射配置"""
        issues = []
        
        # 检查必需字段
        issues.extend(self._check_required_fields(mapping_config, table_type))
        
        # 检查字段重复
        issues.extend(self._check_duplicate_mappings(mapping_config))
        
        # 检查字段名规范
        issues.extend(self._check_field_naming(mapping_config))
        
        # 检查数据类型
        issues.extend(self._check_data_types(mapping_config))
        
        return issues
    
    def _check_required_fields(self, mapping_config: Dict[str, Dict], 
                              table_type: str) -> List[ValidationIssue]:
        """检查必需字段"""
        issues = []
        
        # 定义必需字段
        if table_type == "💰 工资表":
            required_fields = ['姓名', '工号', '应发合计', '实发合计']
        elif table_type == "🔄 异动表":
            required_fields = ['姓名', '工号', '异动类型', '异动日期']
        else:
            required_fields = ['姓名', '工号']
        
        # 获取已映射的目标字段
        mapped_targets = set()
        for config in mapping_config.values():
            target = config.get('target_field', '').strip()
            if target:
                mapped_targets.add(target)
        
        # 检查缺失的必需字段
        for required in required_fields:
            if required not in mapped_targets:
                issues.append(ValidationIssue(
                    level=ValidationLevel.ERROR,
                    field=required,
                    message=f"缺少必需字段: {required}",
                    suggestion=f"请为字段 '{required}' 创建映射关系"
                ))
        
        return issues
    
    def _check_duplicate_mappings(self, mapping_config: Dict[str, Dict]) -> List[ValidationIssue]:
        """检查重复映射"""
        issues = []
        target_count = {}
        
        # 统计目标字段使用次数
        for source_field, config in mapping_config.items():
            target = config.get('target_field', '').strip()
            if target:
                if target not in target_count:
                    target_count[target] = []
                target_count[target].append(source_field)
        
        # 检查重复
        for target, sources in target_count.items():
            if len(sources) > 1:
                issues.append(ValidationIssue(
                    level=ValidationLevel.ERROR,
                    field=target,
                    message=f"字段 '{target}' 被多个源字段映射: {', '.join(sources)}",
                    suggestion="请确保每个目标字段只被映射一次"
                ))
        
        return issues
    
    def _check_field_naming(self, mapping_config: Dict[str, Dict]) -> List[ValidationIssue]:
        """检查字段命名规范"""
        issues = []
        
        # 命名规范模式
        valid_pattern = re.compile(r'^[a-zA-Z\u4e00-\u9fff][a-zA-Z0-9\u4e00-\u9fff_]*$')
        
        for source_field, config in mapping_config.items():
            target = config.get('target_field', '').strip()
            
            if not target:
                issues.append(ValidationIssue(
                    level=ValidationLevel.WARNING,
                    field=source_field,
                    message=f"源字段 '{source_field}' 没有设置目标字段",
                    suggestion="请为此字段设置合适的目标字段名"
                ))
                continue
            
            # 检查命名规范
            if not valid_pattern.match(target):
                issues.append(ValidationIssue(
                    level=ValidationLevel.WARNING,
                    field=target,
                    message=f"字段名 '{target}' 不符合命名规范",
                    suggestion="字段名应以字母或中文开头，只包含字母、数字、中文和下划线"
                ))
            
            # 检查长度
            if len(target) > 50:
                issues.append(ValidationIssue(
                    level=ValidationLevel.WARNING,
                    field=target,
                    message=f"字段名 '{target}' 过长 ({len(target)} 字符)",
                    suggestion="建议字段名长度不超过50个字符"
                ))
            
            # 检查保留字
            reserved_words = ['select', 'from', 'where', 'insert', 'update', 'delete', 'table']
            if target.lower() in reserved_words:
                issues.append(ValidationIssue(
                    level=ValidationLevel.WARNING,
                    field=target,
                    message=f"字段名 '{target}' 是保留字",
                    suggestion="请使用其他字段名避免潜在冲突"
                ))
        
        return issues
    
    def _check_data_types(self, mapping_config: Dict[str, Dict]) -> List[ValidationIssue]:
        """检查数据类型"""
        issues = []
        
        # 有效的数据类型
        valid_types = {
            'VARCHAR': r'^VARCHAR\(\d+\)$',
            'INT': r'^INT$',
            'DECIMAL': r'^DECIMAL\(\d+,\d+\)$',
            'DATE': r'^DATE$',
            'DATETIME': r'^DATETIME$',
            'TEXT': r'^TEXT$',
            'BOOLEAN': r'^BOOLEAN$',
        }
        
        for source_field, config in mapping_config.items():
            data_type = config.get('data_type', '').strip().upper()
            
            if not data_type:
                issues.append(ValidationIssue(
                    level=ValidationLevel.WARNING,
                    field=source_field,
                    message=f"字段 '{source_field}' 没有设置数据类型",
                    suggestion="请为此字段设置合适的数据类型"
                ))
                continue
            
            # 检查数据类型格式
            is_valid_type = False
            for type_name, pattern in valid_types.items():
                if re.match(pattern, data_type):
                    is_valid_type = True
                    break
            
            if not is_valid_type:
                issues.append(ValidationIssue(
                    level=ValidationLevel.ERROR,
                    field=source_field,
                    message=f"字段 '{source_field}' 的数据类型 '{data_type}' 无效",
                    suggestion=f"请使用有效的数据类型: {', '.join(valid_types.keys())}"
                ))
        
        return issues


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
    
    def validate_data_values(self, data: List[Dict], mapping_config: Dict[str, Dict]) -> List[ValidationIssue]:
        """验证数据值"""
        issues = []
        
        for row_index, row_data in enumerate(data, start=1):
            row_issues = self._validate_row_data(row_data, mapping_config, row_index)
            issues.extend(row_issues)
        
        return issues
    
    def _validate_row_data(self, row_data: Dict, mapping_config: Dict[str, Dict], 
                          row_number: int) -> List[ValidationIssue]:
        """验证单行数据"""
        issues = []
        
        for source_field, config in mapping_config.items():
            if source_field not in row_data:
                continue
            
            value = row_data[source_field]
            field_issues = self._validate_field_value(
                source_field, value, config, row_number
            )
            issues.extend(field_issues)
        
        return issues
    
    def _validate_field_value(self, field_name: str, value: Any, 
                             config: Dict, row_number: int) -> List[ValidationIssue]:
        """验证字段值"""
        issues = []
        
        # 检查必需字段
        if config.get('is_required', False) and (value is None or str(value).strip() == ''):
            issues.append(ValidationIssue(
                level=ValidationLevel.ERROR,
                field=field_name,
                message=f"必需字段 '{field_name}' 的值为空",
                row_number=row_number,
                suggestion="请为此必需字段提供有效值"
            ))
            return issues
        
        # 如果值为空且非必需，跳过其他验证
        if value is None or str(value).strip() == '':
            return issues
        
        # 根据数据类型验证
        data_type = config.get('data_type', '').upper()
        
        if data_type.startswith('VARCHAR'):
            issues.extend(self._validate_varchar(field_name, value, data_type, row_number))
        elif data_type == 'INT':
            issues.extend(self._validate_int(field_name, value, row_number))
        elif data_type.startswith('DECIMAL'):
            issues.extend(self._validate_decimal(field_name, value, data_type, row_number))
        elif data_type == 'DATE':
            issues.extend(self._validate_date(field_name, value, row_number))
        
        return issues
    
    def _validate_varchar(self, field_name: str, value: Any, data_type: str, 
                         row_number: int) -> List[ValidationIssue]:
        """验证VARCHAR类型"""
        issues = []
        value_str = str(value)
        
        # 提取长度限制
        match = re.match(r'VARCHAR\((\d+)\)', data_type)
        if match:
            max_length = int(match.group(1))
            if len(value_str) > max_length:
                issues.append(ValidationIssue(
                    level=ValidationLevel.ERROR,
                    field=field_name,
                    message=f"字段 '{field_name}' 的值长度 ({len(value_str)}) 超过限制 ({max_length})",
                    row_number=row_number,
                    suggestion=f"请确保值的长度不超过 {max_length} 个字符"
                ))
        
        return issues
    
    def _validate_int(self, field_name: str, value: Any, row_number: int) -> List[ValidationIssue]:
        """验证INT类型"""
        issues = []
        
        try:
            # 尝试转换为整数
            if isinstance(value, str):
                value = value.strip()
            int(float(value))  # 先转float再转int，处理类似"123.0"的情况
        except (ValueError, TypeError):
            issues.append(ValidationIssue(
                level=ValidationLevel.ERROR,
                field=field_name,
                message=f"字段 '{field_name}' 的值 '{value}' 不是有效的整数",
                row_number=row_number,
                suggestion="请提供有效的整数值"
            ))
        
        return issues
    
    def _validate_decimal(self, field_name: str, value: Any, data_type: str, 
                         row_number: int) -> List[ValidationIssue]:
        """验证DECIMAL类型"""
        issues = []
        
        try:
            # 尝试转换为浮点数
            if isinstance(value, str):
                value = value.strip().replace(',', '')  # 移除千分位逗号
            float_value = float(value)
            
            # 检查精度
            match = re.match(r'DECIMAL\((\d+),(\d+)\)', data_type)
            if match:
                precision = int(match.group(1))  # 总位数
                scale = int(match.group(2))      # 小数位数
                
                value_str = str(float_value)
                if '.' in value_str:
                    integer_part, decimal_part = value_str.split('.')
                    if len(decimal_part) > scale:
                        issues.append(ValidationIssue(
                            level=ValidationLevel.WARNING,
                            field=field_name,
                            message=f"字段 '{field_name}' 的小数位数 ({len(decimal_part)}) 超过限制 ({scale})",
                            row_number=row_number,
                            suggestion=f"建议小数位数不超过 {scale} 位"
                        ))
                
        except (ValueError, TypeError):
            issues.append(ValidationIssue(
                level=ValidationLevel.ERROR,
                field=field_name,
                message=f"字段 '{field_name}' 的值 '{value}' 不是有效的数字",
                row_number=row_number,
                suggestion="请提供有效的数字值"
            ))
        
        return issues
    
    def _validate_date(self, field_name: str, value: Any, row_number: int) -> List[ValidationIssue]:
        """验证DATE类型"""
        issues = []
        
        if isinstance(value, datetime):
            return issues  # 已经是datetime对象，有效
        
        value_str = str(value).strip()
        
        # 尝试多种日期格式
        date_formats = [
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%m/%d/%Y',
            '%d/%m/%Y',
            '%Y%m%d',
        ]
        
        is_valid_date = False
        for fmt in date_formats:
            try:
                datetime.strptime(value_str, fmt)
                is_valid_date = True
                break
            except ValueError:
                continue
        
        if not is_valid_date:
            issues.append(ValidationIssue(
                level=ValidationLevel.ERROR,
                field=field_name,
                message=f"字段 '{field_name}' 的值 '{value}' 不是有效的日期格式",
                row_number=row_number,
                suggestion="请使用有效的日期格式，如 YYYY-MM-DD"
            ))
        
        return issues


class ValidationEngine:
    """验证引擎"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.mapping_validator = MappingValidator()
        self.data_validator = DataValidator()
        
        self.logger.info("验证引擎初始化完成")
    
    def validate_import_configuration(self, mapping_config: Dict[str, Dict], 
                                    table_type: str) -> ValidationReport:
        """验证导入配置"""
        try:
            self.logger.info(f"开始验证导入配置: {table_type}")
            
            # 验证映射配置
            issues = self.mapping_validator.validate_mapping_config(mapping_config, table_type)
            
            # 生成报告
            report = self._generate_report(issues, "配置验证")
            
            self.logger.info(f"配置验证完成: 有效={report.is_valid}, 问题数={report.total_issues}")
            return report
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            error_issue = ValidationIssue(
                level=ValidationLevel.ERROR,
                field="system",
                message=f"验证过程出错: {e}",
                suggestion="请检查配置并重试"
            )
            return self._generate_report([error_issue], "验证失败")
    
    def validate_import_data(self, data: List[Dict], mapping_config: Dict[str, Dict]) -> ValidationReport:
        """验证导入数据"""
        try:
            self.logger.info(f"开始验证导入数据: {len(data)} 行")
            
            # 验证数据值
            issues = self.data_validator.validate_data_values(data, mapping_config)
            
            # 生成报告
            report = self._generate_report(issues, f"数据验证 ({len(data)} 行)")
            
            self.logger.info(f"数据验证完成: 有效={report.is_valid}, 问题数={report.total_issues}")
            return report
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            error_issue = ValidationIssue(
                level=ValidationLevel.ERROR,
                field="system",
                message=f"验证过程出错: {e}",
                suggestion="请检查数据并重试"
            )
            return self._generate_report([error_issue], "验证失败")
    
    def validate_complete_import(self, data: List[Dict], mapping_config: Dict[str, Dict], 
                               table_type: str) -> ValidationReport:
        """完整的导入验证（配置+数据）"""
        try:
            self.logger.info(f"开始完整导入验证: {table_type}, {len(data)} 行数据")
            
            all_issues = []
            
            # 验证配置
            config_issues = self.mapping_validator.validate_mapping_config(mapping_config, table_type)
            all_issues.extend(config_issues)
            
            # 只有配置无严重错误时才验证数据
            config_errors = [issue for issue in config_issues if issue.level == ValidationLevel.ERROR]
            if not config_errors:
                data_issues = self.data_validator.validate_data_values(data, mapping_config)
                all_issues.extend(data_issues)
            else:
                self.logger.warning("配置存在错误，跳过数据验证")
            
            # 生成综合报告
            report = self._generate_report(all_issues, f"完整验证 ({table_type})")
            
            self.logger.info(f"完整验证完成: 有效={report.is_valid}, 问题数={report.total_issues}")
            return report
            
        except Exception as e:
            self.logger.error(f"完整验证失败: {e}")
            error_issue = ValidationIssue(
                level=ValidationLevel.ERROR,
                field="system",
                message=f"验证过程出错: {e}",
                suggestion="请检查配置和数据并重试"
            )
            return self._generate_report([error_issue], "验证失败")
    
    def _generate_report(self, issues: List[ValidationIssue], context: str) -> ValidationReport:
        """生成验证报告"""
        error_count = len([issue for issue in issues if issue.level == ValidationLevel.ERROR])
        warning_count = len([issue for issue in issues if issue.level == ValidationLevel.WARNING])
        info_count = len([issue for issue in issues if issue.level == ValidationLevel.INFO])
        
        is_valid = error_count == 0
        total_issues = len(issues)
        
        # 生成摘要
        if is_valid:
            if total_issues == 0:
                summary = f"{context} 完全通过验证"
            else:
                summary = f"{context} 通过验证，但有 {warning_count} 个警告和 {info_count} 个提示"
        else:
            summary = f"{context} 验证失败，发现 {error_count} 个错误"
        
        return ValidationReport(
            is_valid=is_valid,
            total_issues=total_issues,
            error_count=error_count,
            warning_count=warning_count,
            info_count=info_count,
            issues=issues,
            summary=summary
        )
