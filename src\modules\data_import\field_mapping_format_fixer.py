"""
🔧 [P1-重要修复] 字段映射格式修复工具

功能说明:
- 统一字段映射配置格式
- 将旧格式 {Excel列名: 数据库字段名} 转换为新格式 {数据库字段名: 显示名}
- 验证和修复配置文件一致性
- 生成修复报告

创建时间: 2025-08-18
作者: P1级问题修复团队
"""

import json
import re
from typing import Dict, List, Any, Tuple
from pathlib import Path
from datetime import datetime

from src.utils.log_config import setup_logger


class FieldMappingFormatFixer:
    """字段映射格式修复器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.config_path = Path("state/data/field_mappings.json")
        self.backup_path = Path("state/data/field_mappings_backup.json")
        
        # 修复统计
        self.fix_stats = {
            "tables_processed": 0,
            "format_fixes": 0,
            "validation_errors": 0,
            "warnings": 0
        }
        
    def fix_all_mappings(self) -> Dict[str, Any]:
        """
        🔧 [P1-重要修复] 修复所有表的字段映射格式
        
        Returns:
            修复结果报告
        """
        try:
            self.logger.info("🔧 [P1-重要修复] 开始字段映射格式修复...")
            
            # 1. 备份原配置
            self._backup_config()
            
            # 2. 加载配置
            config = self._load_config()
            if not config:
                return {"success": False, "error": "配置文件加载失败"}
            
            # 3. 修复表映射
            fixed_config = self._fix_table_mappings(config)
            
            # 4. 验证修复结果
            validation_result = self._validate_fixed_config(fixed_config)
            
            # 5. 保存修复后的配置
            if validation_result["is_valid"]:
                self._save_config(fixed_config)
                self.logger.info("🔧 [P1-重要修复] 配置文件修复完成并保存")
            else:
                self.logger.error("🔧 [P1-重要修复] 修复后配置验证失败，未保存")
            
            # 6. 生成修复报告
            report = self._generate_fix_report(validation_result)
            
            return report
            
        except Exception as e:
            self.logger.error(f"🔧 [P1-重要修复] 字段映射格式修复失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _backup_config(self):
        """备份原配置文件"""
        try:
            if self.config_path.exists():
                import shutil
                shutil.copy2(self.config_path, self.backup_path)
                self.logger.info(f"🔧 [P1-重要修复] 配置文件已备份: {self.backup_path}")
        except Exception as e:
            self.logger.warning(f"🔧 [P1-重要修复] 配置备份失败: {e}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                self.logger.error("🔧 [P1-重要修复] 配置文件不存在")
                return {}
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"🔧 [P1-重要修复] 配置文件加载失败: {e}")
            return {}
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        try:
            # 更新时间戳
            config["last_updated"] = datetime.now().isoformat()
            config["metadata"]["last_modified"] = datetime.now().isoformat()
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info("🔧 [P1-重要修复] 配置文件保存成功")
        except Exception as e:
            self.logger.error(f"🔧 [P1-重要修复] 配置文件保存失败: {e}")
    
    def _fix_table_mappings(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """修复表映射格式"""
        try:
            table_mappings = config.get("table_mappings", {})
            fixed_mappings = {}
            
            for table_name, table_config in table_mappings.items():
                self.fix_stats["tables_processed"] += 1
                
                # 检查是否需要修复
                if self._needs_format_fix(table_config):
                    fixed_config = self._fix_single_table_mapping(table_name, table_config)
                    fixed_mappings[table_name] = fixed_config
                    self.fix_stats["format_fixes"] += 1
                else:
                    fixed_mappings[table_name] = table_config
            
            config["table_mappings"] = fixed_mappings
            return config
            
        except Exception as e:
            self.logger.error(f"🔧 [P1-重要修复] 表映射修复失败: {e}")
            return config
    
    def _needs_format_fix(self, table_config: Dict[str, Any]) -> bool:
        """检查表配置是否需要格式修复"""
        try:
            # 检查是否有直接的中文键（旧格式）
            for key in table_config.keys():
                if re.match(r'.*[\u4e00-\u9fff].*', key):
                    return True
            
            # 检查field_mappings是否存在且格式正确
            field_mappings = table_config.get("field_mappings", {})
            if field_mappings:
                first_key = next(iter(field_mappings.keys()))
                # 如果第一个键不是数据库字段名格式，需要修复
                if not re.match(r'^[a-z_][a-z0-9_]*$', first_key):
                    return True
            
            return False
            
        except Exception as e:
            self.logger.warning(f"🔧 [P1-重要修复] 格式检查失败: {e}")
            return False
    
    def _fix_single_table_mapping(self, table_name: str, table_config: Dict[str, Any]) -> Dict[str, Any]:
        """修复单个表的映射格式"""
        try:
            fixed_config = {}
            
            # 1. 提取旧格式的直接映射
            legacy_mappings = {}
            for key, value in table_config.items():
                if re.match(r'.*[\u4e00-\u9fff].*', key) and isinstance(value, str):
                    legacy_mappings[key] = value
                else:
                    # 保留非映射配置
                    fixed_config[key] = value
            
            # 2. 转换格式
            if legacy_mappings:
                new_field_mappings = {}
                for chinese_name, db_field in legacy_mappings.items():
                    if re.match(r'^[a-z_][a-z0-9_]*$', db_field):
                        new_field_mappings[db_field] = chinese_name
                
                fixed_config["field_mappings"] = new_field_mappings
                self.logger.info(f"🔧 [P1-重要修复] 表 {table_name} 格式转换: {len(legacy_mappings)} 个字段")
            
            # 3. 确保必要的配置项存在
            if "field_mappings" not in fixed_config:
                fixed_config["field_mappings"] = {}
            
            if "metadata" not in fixed_config:
                fixed_config["metadata"] = {
                    "format_fixed": True,
                    "fix_time": datetime.now().isoformat()
                }
            else:
                fixed_config["metadata"]["format_fixed"] = True
                fixed_config["metadata"]["fix_time"] = datetime.now().isoformat()
            
            return fixed_config
            
        except Exception as e:
            self.logger.error(f"🔧 [P1-重要修复] 表 {table_name} 格式修复失败: {e}")
            return table_config
    
    def _validate_fixed_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证修复后的配置"""
        try:
            validation_result = {
                "is_valid": True,
                "errors": [],
                "warnings": [],
                "summary": ""
            }
            
            table_mappings = config.get("table_mappings", {})
            
            for table_name, table_config in table_mappings.items():
                # 检查field_mappings格式
                field_mappings = table_config.get("field_mappings", {})
                
                for db_field, display_name in field_mappings.items():
                    # 验证数据库字段名格式
                    if not re.match(r'^[a-z_][a-z0-9_]*$', db_field):
                        validation_result["errors"].append(f"表 {table_name} 字段名格式错误: {db_field}")
                        validation_result["is_valid"] = False
                    
                    # 验证显示名不为空
                    if not display_name or not isinstance(display_name, str):
                        validation_result["warnings"].append(f"表 {table_name} 显示名为空: {db_field}")
            
            # 生成验证摘要
            error_count = len(validation_result["errors"])
            warning_count = len(validation_result["warnings"])
            table_count = len(table_mappings)
            
            validation_result["summary"] = f"验证完成: {table_count}个表，{error_count}个错误，{warning_count}个警告"
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"🔧 [P1-重要修复] 配置验证失败: {e}")
            return {"is_valid": False, "errors": [str(e)], "warnings": [], "summary": "验证失败"}
    
    def _generate_fix_report(self, validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成修复报告"""
        try:
            report = {
                "success": validation_result["is_valid"],
                "timestamp": datetime.now().isoformat(),
                "statistics": self.fix_stats.copy(),
                "validation": validation_result,
                "recommendations": []
            }
            
            # 添加建议
            if self.fix_stats["format_fixes"] > 0:
                report["recommendations"].append("建议重启系统以应用配置更改")
            
            if validation_result["warnings"]:
                report["recommendations"].append("建议检查并修复警告项")
            
            if not validation_result["is_valid"]:
                report["recommendations"].append("建议检查错误日志并手动修复配置")
            
            self.logger.info(f"🔧 [P1-重要修复] 修复报告: {report['statistics']}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"🔧 [P1-重要修复] 修复报告生成失败: {e}")
            return {"success": False, "error": str(e)}
