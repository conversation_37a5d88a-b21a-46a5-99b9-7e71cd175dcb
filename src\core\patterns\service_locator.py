"""
服务定位器模式 - P3级架构规范化
提供统一的服务注册和获取机制
"""

from typing import Dict, Any, Type, Optional, Callable
from abc import ABC, abstractmethod
from enum import Enum
from loguru import logger
import threading


class ServiceLifetime(Enum):
    """服务生命周期"""
    SINGLETON = "singleton"      # 单例
    TRANSIENT = "transient"      # 瞬态
    SCOPED = "scoped"           # 作用域


class IService(ABC):
    """服务接口基类"""
    
    @abstractmethod
    def initialize(self):
        """初始化服务"""
        pass
    
    @abstractmethod
    def shutdown(self):
        """关闭服务"""
        pass


class ServiceDescriptor:
    """服务描述符"""
    
    def __init__(
        self,
        service_type: Type,
        implementation: Optional[Type] = None,
        factory: Optional[Callable] = None,
        lifetime: ServiceLifetime = ServiceLifetime.SINGLETON,
        instance: Optional[Any] = None
    ):
        """
        初始化服务描述符
        
        Args:
            service_type: 服务类型
            implementation: 实现类型
            factory: 工厂函数
            lifetime: 生命周期
            instance: 实例（用于单例）
        """
        self.service_type = service_type
        self.implementation = implementation
        self.factory = factory
        self.lifetime = lifetime
        self.instance = instance


class ServiceLocator:
    """
    服务定位器
    
    提供服务的注册、解析和生命周期管理
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._scoped_services: Dict[Type, Any] = {}
        self.logger = logger
        
        self.logger.info("ServiceLocator 初始化完成")
    
    def register(
        self,
        service_type: Type,
        implementation: Optional[Type] = None,
        factory: Optional[Callable] = None,
        lifetime: ServiceLifetime = ServiceLifetime.SINGLETON,
        replace: bool = False
    ):
        """
        注册服务
        
        Args:
            service_type: 服务类型（接口）
            implementation: 实现类型
            factory: 工厂函数
            lifetime: 生命周期
            replace: 是否替换已存在的服务
        """
        if service_type in self._services and not replace:
            self.logger.warning(f"服务已存在: {service_type.__name__}")
            return
        
        if not implementation and not factory:
            implementation = service_type
        
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation=implementation,
            factory=factory,
            lifetime=lifetime
        )
        
        self._services[service_type] = descriptor
        self.logger.debug(f"注册服务: {service_type.__name__}, 生命周期: {lifetime.value}")
    
    def register_singleton(self, service_type: Type, instance: Any):
        """
        注册单例实例
        
        Args:
            service_type: 服务类型
            instance: 服务实例
        """
        descriptor = ServiceDescriptor(
            service_type=service_type,
            lifetime=ServiceLifetime.SINGLETON,
            instance=instance
        )
        
        self._services[service_type] = descriptor
        self.logger.debug(f"注册单例: {service_type.__name__}")
    
    def resolve(self, service_type: Type) -> Any:
        """
        解析服务
        
        Args:
            service_type: 服务类型
        
        Returns:
            服务实例
        
        Raises:
            ValueError: 服务未注册
        """
        if service_type not in self._services:
            raise ValueError(f"服务未注册: {service_type.__name__}")
        
        descriptor = self._services[service_type]
        
        # 根据生命周期返回实例
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            return self._get_singleton(descriptor)
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            return self._get_scoped(descriptor)
        else:  # TRANSIENT
            return self._create_instance(descriptor)
    
    def _get_singleton(self, descriptor: ServiceDescriptor) -> Any:
        """获取单例实例"""
        if descriptor.instance is None:
            with self._lock:
                if descriptor.instance is None:
                    descriptor.instance = self._create_instance(descriptor)
                    self._initialize_service(descriptor.instance)
        
        return descriptor.instance
    
    def _get_scoped(self, descriptor: ServiceDescriptor) -> Any:
        """获取作用域实例"""
        service_type = descriptor.service_type
        
        if service_type not in self._scoped_services:
            instance = self._create_instance(descriptor)
            self._initialize_service(instance)
            self._scoped_services[service_type] = instance
        
        return self._scoped_services[service_type]
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """创建服务实例"""
        try:
            if descriptor.factory:
                # 使用工厂函数创建
                instance = descriptor.factory()
            elif descriptor.implementation:
                # 使用实现类型创建
                instance = descriptor.implementation()
            else:
                # 使用服务类型创建
                instance = descriptor.service_type()
            
            return instance
            
        except Exception as e:
            self.logger.error(f"创建服务失败: {descriptor.service_type.__name__} - {e}")
            raise
    
    def _initialize_service(self, instance: Any):
        """初始化服务"""
        if isinstance(instance, IService):
            try:
                instance.initialize()
                self.logger.debug(f"服务初始化: {instance.__class__.__name__}")
            except Exception as e:
                self.logger.error(f"服务初始化失败: {instance.__class__.__name__} - {e}")
                raise
    
    def resolve_all(self, service_type: Type) -> list:
        """
        解析所有实现了指定类型的服务
        
        Args:
            service_type: 服务类型
        
        Returns:
            服务实例列表
        """
        instances = []
        
        for registered_type, descriptor in self._services.items():
            if issubclass(registered_type, service_type):
                instances.append(self.resolve(registered_type))
        
        return instances
    
    def clear_scoped(self):
        """清除作用域服务"""
        for service in self._scoped_services.values():
            if isinstance(service, IService):
                try:
                    service.shutdown()
                except Exception as e:
                    self.logger.error(f"服务关闭失败: {service.__class__.__name__} - {e}")
        
        self._scoped_services.clear()
        self.logger.debug("清除作用域服务")
    
    def shutdown_all(self):
        """关闭所有服务"""
        # 关闭单例服务
        for descriptor in self._services.values():
            if descriptor.instance and isinstance(descriptor.instance, IService):
                try:
                    descriptor.instance.shutdown()
                    self.logger.debug(f"关闭服务: {descriptor.instance.__class__.__name__}")
                except Exception as e:
                    self.logger.error(f"服务关闭失败: {e}")
        
        # 清除作用域服务
        self.clear_scoped()
        
        # 清除所有注册
        self._services.clear()
        self.logger.info("所有服务已关闭")
    
    def is_registered(self, service_type: Type) -> bool:
        """
        检查服务是否已注册
        
        Args:
            service_type: 服务类型
        
        Returns:
            是否已注册
        """
        return service_type in self._services
    
    def get_service_info(self) -> Dict[str, str]:
        """
        获取服务信息
        
        Returns:
            服务信息字典
        """
        info = {}
        for service_type, descriptor in self._services.items():
            info[service_type.__name__] = {
                'lifetime': descriptor.lifetime.value,
                'has_instance': descriptor.instance is not None,
                'implementation': descriptor.implementation.__name__ if descriptor.implementation else None
            }
        return info


# 全局服务定位器实例
_service_locator = None

def get_service_locator() -> ServiceLocator:
    """获取服务定位器实例"""
    global _service_locator
    if _service_locator is None:
        _service_locator = ServiceLocator()
    return _service_locator


# 便捷函数
def register_service(
    service_type: Type,
    implementation: Optional[Type] = None,
    factory: Optional[Callable] = None,
    lifetime: ServiceLifetime = ServiceLifetime.SINGLETON
):
    """注册服务的便捷函数"""
    locator = get_service_locator()
    locator.register(service_type, implementation, factory, lifetime)


def resolve_service(service_type: Type) -> Any:
    """解析服务的便捷函数"""
    locator = get_service_locator()
    return locator.resolve(service_type)