#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
字段映射单元测试
"""

import unittest
import json
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestFieldMapping(unittest.TestCase):
    """字段映射测试"""
    
    def setUp(self):
        """测试初始化"""
        self.mapping_file = Path("state/data/field_mappings.json")
        
    def test_mapping_file_exists(self):
        """测试映射文件存在"""
        self.assertTrue(self.mapping_file.exists())
        
    def test_mapping_format(self):
        """测试映射格式"""
        with open(self.mapping_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 验证必要字段
        self.assertIn('version', data)
        self.assertIn('table_mappings', data)
        self.assertIn('field_templates', data)
        
    def test_mapping_consistency(self):
        """测试映射一致性"""
        with open(self.mapping_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查所有表映射格式（应该是中文->英文）
        for table_name, mappings in data['table_mappings'].items():
            if mappings:
                # 取第一个映射检查格式
                first_key = list(mappings.keys())[0]
                first_value = mappings[first_key]
                
                # 中文key
                self.assertTrue(any(ord(c) > 127 for c in first_key), 
                               f"表 {table_name} 的key应该是中文: {first_key}")
                
                # 英文value
                if first_value:  # 可能有空值
                    self.assertTrue(all(ord(c) < 128 for c in first_value.replace('_', '')), 
                                   f"表 {table_name} 的value应该是英文: {first_value}")
    
    def test_common_fields(self):
        """测试常用字段映射"""
        with open(self.mapping_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 常用字段
        common_fields = {
            '工号': 'employee_id',
            '姓名': 'employee_name',
            '部门名称': 'department'
        }
        
        # 检查至少一个表包含这些映射
        found = {field: False for field in common_fields}
        
        for table_name, mappings in data['table_mappings'].items():
            for cn_field, en_field in common_fields.items():
                if mappings.get(cn_field) == en_field:
                    found[cn_field] = True
        
        for field, was_found in found.items():
            self.assertTrue(was_found, f"常用字段 {field} 未找到正确映射")

if __name__ == '__main__':
    unittest.main()
