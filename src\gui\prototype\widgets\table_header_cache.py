#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
表头缓存管理器 - P1级优化
用于缓存和管理表格的表头信息，避免重复创建和累积
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import hashlib
import json
from loguru import logger


@dataclass
class HeaderCacheEntry:
    """表头缓存条目"""
    table_name: str
    headers: List[str]
    display_headers: List[str]
    column_count: int
    version: int
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    checksum: str = ""
    
    def __post_init__(self):
        """计算表头的校验和"""
        if not self.checksum:
            header_str = json.dumps(self.headers, sort_keys=True)
            self.checksum = hashlib.md5(header_str.encode()).hexdigest()
    
    def is_valid(self, headers: List[str]) -> bool:
        """验证缓存是否有效"""
        if len(headers) != len(self.headers):
            return False
        header_str = json.dumps(headers, sort_keys=True)
        current_checksum = hashlib.md5(header_str.encode()).hexdigest()
        return current_checksum == self.checksum


class TableHeaderCache:
    """
    表头缓存管理器 - P1级优化组件
    
    功能：
    1. 缓存表头信息，避免重复处理
    2. 版本控制，追踪表头变化
    3. 一致性校验，防止表头累积
    4. 性能优化，减少内存分配
    """
    
    def __init__(self, max_cache_size: int = 100):
        """
        初始化表头缓存管理器
        
        Args:
            max_cache_size: 最大缓存条目数
        """
        self._cache: Dict[str, HeaderCacheEntry] = {}
        self._max_cache_size = max_cache_size
        self._version_counter: Dict[str, int] = {}
        self._header_history: Dict[str, List[Tuple[int, str]]] = {}  # 表名 -> [(版本, 校验和)]
        
        logger.info(f"🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: {max_cache_size}")
    
    def get_cached_headers(self, table_name: str, 
                          current_headers: Optional[List[str]] = None) -> Optional[HeaderCacheEntry]:
        """
        获取缓存的表头信息
        
        Args:
            table_name: 表名
            current_headers: 当前表头（用于验证）
            
        Returns:
            缓存条目或None
        """
        if table_name not in self._cache:
            return None
        
        entry = self._cache[table_name]
        
        # 如果提供了当前表头，验证一致性
        if current_headers and not entry.is_valid(current_headers):
            logger.warning(f"🔧 [P1优化] 表 {table_name} 的缓存表头已失效")
            return None
        
        # 更新访问信息
        entry.last_accessed = datetime.now()
        entry.access_count += 1
        
        logger.debug(f"🔧 [P1优化] 命中缓存: {table_name}, 访问次数: {entry.access_count}")
        return entry
    
    def cache_headers(self, table_name: str, headers: List[str], 
                     display_headers: Optional[List[str]] = None) -> HeaderCacheEntry:
        """
        缓存表头信息
        
        Args:
            table_name: 表名
            headers: 原始表头
            display_headers: 显示表头
            
        Returns:
            缓存条目
        """
        # 获取或初始化版本号
        if table_name not in self._version_counter:
            self._version_counter[table_name] = 0
        
        # 检查是否需要更新版本
        need_new_version = False
        if table_name in self._cache:
            old_entry = self._cache[table_name]
            if not old_entry.is_valid(headers):
                need_new_version = True
                self._version_counter[table_name] += 1
        
        # 创建新条目
        version = self._version_counter[table_name]
        entry = HeaderCacheEntry(
            table_name=table_name,
            headers=headers.copy(),
            display_headers=display_headers.copy() if display_headers else headers.copy(),
            column_count=len(headers),
            version=version,
            created_at=datetime.now(),
            last_accessed=datetime.now()
        )
        
        # 存储到缓存
        self._cache[table_name] = entry
        
        # 记录历史
        if table_name not in self._header_history:
            self._header_history[table_name] = []
        self._header_history[table_name].append((version, entry.checksum))
        
        # 检查缓存大小
        if len(self._cache) > self._max_cache_size:
            self._evict_least_used()
        
        if need_new_version:
            logger.info(f"🔧 [P1优化] 表 {table_name} 表头已更新，版本: {version}")
        else:
            logger.debug(f"🔧 [P1优化] 缓存表头: {table_name}, 列数: {len(headers)}")
        
        return entry
    
    def validate_headers(self, table_name: str, headers: List[str]) -> Tuple[bool, Optional[str]]:
        """
        验证表头一致性
        
        Args:
            table_name: 表名
            headers: 待验证的表头
            
        Returns:
            (是否有效, 错误信息)
        """
        # 检查重复表头
        header_counts = {}
        for header in headers:
            header_counts[header] = header_counts.get(header, 0) + 1
        
        duplicates = [h for h, count in header_counts.items() if count > 1]
        if duplicates:
            max_dup = max(header_counts.values())
            if max_dup > 5:  # 严重重复
                return False, f"检测到严重的表头重复: {duplicates[:3]}... (最多重复{max_dup}次)"
            elif max_dup > 2:  # 轻微重复
                logger.warning(f"🔧 [P1优化] 表 {table_name} 存在轻微表头重复: {duplicates}")
        
        # 检查列数异常
        if len(headers) > 100:
            return False, f"列数异常: {len(headers)} (超过100列)"
        
        # 检查与缓存的一致性
        if table_name in self._cache:
            cached = self._cache[table_name]
            if len(headers) != cached.column_count:
                # 检查是否为累积问题
                if len(headers) > cached.column_count * 2:
                    return False, f"检测到表头累积: 当前{len(headers)}列, 应为{cached.column_count}列"
        
        return True, None
    
    def clear_cache(self, table_name: Optional[str] = None):
        """
        清理缓存
        
        Args:
            table_name: 指定表名，None则清理全部
        """
        if table_name:
            if table_name in self._cache:
                del self._cache[table_name]
                logger.info(f"🔧 [P1优化] 清理表 {table_name} 的缓存")
        else:
            self._cache.clear()
            self._version_counter.clear()
            self._header_history.clear()
            logger.info("🔧 [P1优化] 清理所有表头缓存")
    
    def get_header_version(self, table_name: str) -> int:
        """获取表头版本号"""
        return self._version_counter.get(table_name, 0)
    
    def get_header_history(self, table_name: str) -> List[Tuple[int, str]]:
        """获取表头变更历史"""
        return self._header_history.get(table_name, [])
    
    def _evict_least_used(self):
        """淘汰最少使用的缓存条目"""
        if not self._cache:
            return
        
        # 按访问次数和最后访问时间排序
        sorted_entries = sorted(
            self._cache.items(),
            key=lambda x: (x[1].access_count, x[1].last_accessed)
        )
        
        # 淘汰前10%
        evict_count = max(1, len(sorted_entries) // 10)
        for table_name, _ in sorted_entries[:evict_count]:
            del self._cache[table_name]
            logger.debug(f"🔧 [P1优化] 淘汰缓存: {table_name}")
    
    def get_statistics(self) -> Dict:
        """获取缓存统计信息"""
        total_entries = len(self._cache)
        total_accesses = sum(e.access_count for e in self._cache.values())
        avg_accesses = total_accesses / total_entries if total_entries > 0 else 0
        
        return {
            'total_entries': total_entries,
            'total_accesses': total_accesses,
            'average_accesses': avg_accesses,
            'max_cache_size': self._max_cache_size,
            'version_counts': dict(self._version_counter)
        }


# 单例实例
_header_cache_instance: Optional[TableHeaderCache] = None


def get_header_cache() -> TableHeaderCache:
    """获取表头缓存单例"""
    global _header_cache_instance
    if _header_cache_instance is None:
        _header_cache_instance = TableHeaderCache()
    return _header_cache_instance