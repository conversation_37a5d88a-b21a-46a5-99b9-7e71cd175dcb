"""
Sheet配置管理器测试脚本

测试Sheet级别配置管理的核心功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.sheet_config_manager import SheetConfigManager, SheetImportConfig


def test_sheet_config_manager():
    """测试Sheet配置管理器"""
    print("🧪 测试Sheet配置管理器...")
    
    # 创建配置管理器
    manager = SheetConfigManager()
    print("✅ Sheet配置管理器创建成功")
    
    # 设置Excel文件
    test_file = "test_excel_file.xlsx"
    manager.set_excel_file(test_file)
    print(f"✅ 设置Excel文件: {test_file}")
    
    # 创建Sheet配置
    sheet_name = "2024年1月"
    config = manager.get_or_create_config(sheet_name)
    print(f"✅ 创建Sheet配置: {sheet_name}")
    
    # 验证默认配置
    assert config.sheet_name == sheet_name
    assert config.header_row == 1
    assert config.data_start_row == 2
    assert config.has_header == True
    print("✅ 默认配置验证通过")
    
    # 更新配置
    success = manager.update_config(sheet_name, 
                                   header_row=2, 
                                   data_start_row=3,
                                   remove_summary_rows=True)
    assert success == True
    print("✅ 配置更新成功")
    
    # 验证更新后的配置
    updated_config = manager.get_or_create_config(sheet_name)
    assert updated_config.header_row == 2
    assert updated_config.data_start_row == 3
    assert updated_config.remove_summary_rows == True
    print("✅ 更新后配置验证通过")
    
    # 测试切换Sheet
    sheet2_name = "2024年2月"
    config2 = manager.switch_sheet(sheet2_name)
    assert manager.current_sheet == sheet2_name
    print(f"✅ 切换到Sheet: {sheet2_name}")
    
    # 测试智能配置推断
    summary_sheet = "汇总统计表"
    summary_config = manager.get_or_create_config(summary_sheet)
    assert summary_config.is_enabled == False  # 汇总表默认不启用
    assert summary_config.remove_summary_rows == True
    print("✅ 智能配置推断测试通过")
    
    # 测试获取所有配置
    all_configs = manager.get_all_configs()
    assert len(all_configs) == 3  # 应该有3个Sheet配置
    print(f"✅ 获取所有配置: {len(all_configs)} 个")
    
    print("🎉 Sheet配置管理器测试全部通过！")


def test_sheet_import_config():
    """测试Sheet导入配置数据结构"""
    print("\n🧪 测试Sheet导入配置数据结构...")
    
    # 创建配置对象
    config = SheetImportConfig(
        sheet_name="测试Sheet",
        header_row=2,
        data_start_row=3,
        data_end_row=100,
        remove_summary_rows=True,
        summary_keywords=["合计", "小计", "总计"]
    )
    print("✅ Sheet配置对象创建成功")
    
    # 测试转换为字典
    config_dict = config.to_dict()
    assert config_dict['sheet_name'] == "测试Sheet"
    assert config_dict['header_row'] == 2
    assert config_dict['data_start_row'] == 3
    assert config_dict['data_end_row'] == 100
    print("✅ 配置转换为字典成功")
    
    # 测试从字典创建
    new_config = SheetImportConfig.from_dict(config_dict)
    assert new_config.sheet_name == config.sheet_name
    assert new_config.header_row == config.header_row
    assert new_config.data_start_row == config.data_start_row
    assert new_config.remove_summary_rows == config.remove_summary_rows
    print("✅ 从字典创建配置成功")
    
    # 测试更新修改时间
    original_time = config.modified_time
    config.update_modified_time()
    assert config.modified_time > original_time
    print("✅ 修改时间更新成功")
    
    print("🎉 Sheet导入配置测试全部通过！")


def test_config_persistence():
    """测试配置持久化"""
    print("\n🧪 测试配置持久化...")
    
    # 创建临时配置目录
    temp_dir = Path("temp/test_configs")
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建配置管理器
    manager = SheetConfigManager(config_dir=temp_dir)
    
    # 设置测试文件
    test_file = "test_persistence.xlsx"
    manager.set_excel_file(test_file)
    
    # 创建并配置多个Sheet
    sheets_config = {
        "Sheet1": {"header_row": 1, "data_start_row": 2},
        "Sheet2": {"header_row": 2, "data_start_row": 3, "remove_summary_rows": True},
        "汇总表": {"is_enabled": False}
    }
    
    for sheet_name, config_updates in sheets_config.items():
        manager.update_config(sheet_name, **config_updates)
    
    print(f"✅ 创建了 {len(sheets_config)} 个Sheet配置")
    
    # 创建新的管理器实例，测试配置加载
    manager2 = SheetConfigManager(config_dir=temp_dir)
    manager2.set_excel_file(test_file)
    
    # 验证配置是否正确加载
    loaded_configs = manager2.get_all_configs()
    assert len(loaded_configs) == len(sheets_config)
    
    # 验证具体配置
    sheet1_config = manager2.get_or_create_config("Sheet1")
    assert sheet1_config.header_row == 1
    assert sheet1_config.data_start_row == 2
    
    sheet2_config = manager2.get_or_create_config("Sheet2")
    assert sheet2_config.header_row == 2
    assert sheet2_config.remove_summary_rows == True
    
    summary_config = manager2.get_or_create_config("汇总表")
    assert summary_config.is_enabled == False
    
    print("✅ 配置持久化和加载验证通过")
    
    # 清理临时文件
    import shutil
    shutil.rmtree(temp_dir, ignore_errors=True)
    print("✅ 临时文件清理完成")
    
    print("🎉 配置持久化测试全部通过！")


def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 开始Sheet级别配置管理功能测试")
    print("=" * 60)
    
    try:
        # 运行各项测试
        test_sheet_config_manager()
        test_sheet_import_config()
        test_config_persistence()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！Sheet级别配置管理功能实现成功！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
