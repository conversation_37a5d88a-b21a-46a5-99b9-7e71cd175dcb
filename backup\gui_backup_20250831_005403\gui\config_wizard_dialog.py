#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置向导对话框
提供分步骤的配置向导，帮助用户快速完成异动表配置
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QStackedWidget, QWidget, QRadioButton, QButtonGroup,
    QListWidget, QListWidgetItem, QTextEdit, QMessageBox,
    QWizard, QWizardPage
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from typing import Dict, Any, List
from loguru import logger


class ConfigWizardDialog(QWizard):
    """配置向导对话框"""
    
    # 配置完成信号
    config_completed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("异动表配置向导")
        self.setMinimumSize(800, 600)
        
        # 设置向导样式
        self.setWizardStyle(QWizard.ModernStyle)
        self.setOption(QWizard.HaveHelpButton, False)
        
        # 添加向导页面
        self.addPage(WelcomePage())
        self.addPage(DataTypePage())
        self.addPage(FieldSelectionPage())
        self.addPage(FormattingRulePage())
        self.addPage(SummaryPage())
        
        # 连接完成信号
        self.finished.connect(self.on_wizard_finished)
    
    def on_wizard_finished(self, result: int):
        """向导完成时的处理"""
        if result == QDialog.Accepted:
            # 收集所有页面的配置
            config = self.collect_configuration()
            self.config_completed.emit(config)
            logger.info("配置向导完成，配置已生成")
    
    def collect_configuration(self) -> Dict[str, Any]:
        """收集所有页面的配置"""
        config = {}
        
        # 获取数据类型
        data_type_page = self.page(1)
        if hasattr(data_type_page, 'get_selected_type'):
            config['data_type'] = data_type_page.get_selected_type()
        
        # 获取选中的字段
        field_page = self.page(2)
        if hasattr(field_page, 'get_selected_fields'):
            config['selected_fields'] = field_page.get_selected_fields()
        
        # 获取格式化规则
        format_page = self.page(3)
        if hasattr(format_page, 'get_formatting_rules'):
            config['formatting_rules'] = format_page.get_formatting_rules()
        
        return config


class WelcomePage(QWizardPage):
    """欢迎页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("欢迎使用异动表配置向导")
        self.setSubTitle("本向导将帮助您快速配置异动表的字段映射和格式化规则")
        
        layout = QVBoxLayout()
        
        # 欢迎信息
        welcome_text = QTextEdit()
        welcome_text.setReadOnly(True)
        welcome_text.setHtml("""
        <h3>配置向导功能说明</h3>
        <p>通过本向导，您可以：</p>
        <ul>
            <li>选择数据类型（工资表、异动表等）</li>
            <li>配置需要导入的字段</li>
            <li>设置字段的格式化规则</li>
            <li>预览并保存配置</li>
        </ul>
        <p><b>提示：</b>您可以随时点击"上一步"返回修改配置。</p>
        """)
        layout.addWidget(welcome_text)
        
        self.setLayout(layout)


class DataTypePage(QWizardPage):
    """数据类型选择页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("选择数据类型")
        self.setSubTitle("请选择您要配置的数据类型")
        
        layout = QVBoxLayout()
        
        # 数据类型选项
        self.button_group = QButtonGroup()
        
        self.salary_radio = QRadioButton("工资表")
        self.salary_radio.setChecked(True)
        self.button_group.addButton(self.salary_radio, 1)
        layout.addWidget(self.salary_radio)
        
        self.change_radio = QRadioButton("异动表")
        self.button_group.addButton(self.change_radio, 2)
        layout.addWidget(self.change_radio)
        
        self.summary_radio = QRadioButton("汇总表")
        self.button_group.addButton(self.summary_radio, 3)
        layout.addWidget(self.summary_radio)
        
        self.custom_radio = QRadioButton("自定义")
        self.button_group.addButton(self.custom_radio, 4)
        layout.addWidget(self.custom_radio)
        
        layout.addStretch()
        
        # 说明文本
        info_label = QLabel("根据选择的数据类型，系统将提供相应的字段模板和格式化规则。")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        self.setLayout(layout)
    
    def get_selected_type(self) -> str:
        """获取选中的数据类型"""
        checked_id = self.button_group.checkedId()
        type_map = {
            1: "salary_table",
            2: "change_table",
            3: "summary_table",
            4: "custom"
        }
        return type_map.get(checked_id, "custom")


class FieldSelectionPage(QWizardPage):
    """字段选择页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("选择需要的字段")
        self.setSubTitle("请选择需要导入和配置的字段")
        
        layout = QVBoxLayout()
        
        # 字段列表
        self.field_list = QListWidget()
        self.field_list.setSelectionMode(QListWidget.MultiSelection)
        
        # 添加常用字段
        common_fields = [
            "工号", "姓名", "部门", "职务",
            "岗位工资", "薪级工资", "津贴", "补贴",
            "奖金", "绩效", "扣款", "实发工资",
            "入职日期", "身份证号", "银行账号"
        ]
        
        for field in common_fields:
            item = QListWidgetItem(field)
            item.setCheckState(Qt.Checked)  # 默认选中
            self.field_list.addItem(item)
        
        layout.addWidget(self.field_list)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all_fields)
        button_layout.addWidget(select_all_btn)
        
        deselect_all_btn = QPushButton("全不选")
        deselect_all_btn.clicked.connect(self.deselect_all_fields)
        button_layout.addWidget(deselect_all_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def select_all_fields(self):
        """全选所有字段"""
        for i in range(self.field_list.count()):
            self.field_list.item(i).setCheckState(Qt.Checked)
    
    def deselect_all_fields(self):
        """取消选择所有字段"""
        for i in range(self.field_list.count()):
            self.field_list.item(i).setCheckState(Qt.Unchecked)
    
    def get_selected_fields(self) -> List[str]:
        """获取选中的字段"""
        selected = []
        for i in range(self.field_list.count()):
            item = self.field_list.item(i)
            if item.checkState() == Qt.Checked:
                selected.append(item.text())
        return selected


class FormattingRulePage(QWizardPage):
    """格式化规则页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("设置格式化规则")
        self.setSubTitle("为字段类型设置格式化规则")
        
        layout = QVBoxLayout()
        
        # 快速模板选择
        template_label = QLabel("快速应用模板:")
        layout.addWidget(template_label)
        
        template_layout = QHBoxLayout()
        
        self.standard_btn = QPushButton("标准模板")
        self.standard_btn.clicked.connect(lambda: self.apply_template("standard"))
        template_layout.addWidget(self.standard_btn)
        
        self.precise_btn = QPushButton("精确计算")
        self.precise_btn.clicked.connect(lambda: self.apply_template("precise"))
        template_layout.addWidget(self.precise_btn)
        
        self.international_btn = QPushButton("国际格式")
        self.international_btn.clicked.connect(lambda: self.apply_template("international"))
        template_layout.addWidget(self.international_btn)
        
        template_layout.addStretch()
        layout.addLayout(template_layout)
        
        # 规则预览
        self.rules_text = QTextEdit()
        self.rules_text.setReadOnly(True)
        layout.addWidget(self.rules_text)
        
        # 默认应用标准模板
        self.apply_template("standard")
        
        self.setLayout(layout)
    
    def apply_template(self, template_type: str):
        """应用格式化模板"""
        templates = {
            "standard": """
标准格式化规则：
- 工资金额：保留2位小数，使用千位分隔符
- 工号：保留前导零，最小长度6位
- 日期：格式 YYYY-MM-DD
- 负数：使用负号表示
            """,
            "precise": """
精确计算规则：
- 工资金额：保留4位小数，不使用千位分隔符
- 数值：不使用科学计数法
- 计算精度：高精度模式
            """,
            "international": """
国际格式规则：
- 工资金额：保留2位小数，使用千位分隔符
- 日期：格式 DD/MM/YYYY
- 小数点：使用点号(.)
- 千位分隔符：使用逗号(,)
            """
        }
        
        self.rules_text.setPlainText(templates.get(template_type, ""))
        self.current_template = template_type
    
    def get_formatting_rules(self) -> Dict[str, Any]:
        """获取格式化规则"""
        # 根据选择的模板返回相应的规则
        if hasattr(self, 'current_template'):
            return {"template": self.current_template}
        return {}


class SummaryPage(QWizardPage):
    """配置总结页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("配置总结")
        self.setSubTitle("请确认您的配置")
        
        layout = QVBoxLayout()
        
        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        layout.addWidget(self.summary_text)
        
        info_label = QLabel('点击"完成"保存配置，点击"上一步"返回修改。')
        layout.addWidget(info_label)
        
        self.setLayout(layout)
    
    def initializePage(self):
        """页面初始化时更新总结"""
        wizard = self.wizard()
        
        # 收集配置信息
        summary = "<h3>配置总结</h3>"
        
        # 数据类型
        data_type_page = wizard.page(1)
        if hasattr(data_type_page, 'get_selected_type'):
            data_type = data_type_page.get_selected_type()
            summary += f"<p><b>数据类型:</b> {data_type}</p>"
        
        # 选中的字段
        field_page = wizard.page(2)
        if hasattr(field_page, 'get_selected_fields'):
            fields = field_page.get_selected_fields()
            summary += f"<p><b>选中字段数:</b> {len(fields)}</p>"
            if fields:
                summary += "<ul>"
                for field in fields[:5]:  # 只显示前5个
                    summary += f"<li>{field}</li>"
                if len(fields) > 5:
                    summary += f"<li>...还有{len(fields)-5}个字段</li>"
                summary += "</ul>"
        
        # 格式化规则
        format_page = wizard.page(3)
        if hasattr(format_page, 'get_formatting_rules'):
            rules = format_page.get_formatting_rules()
            template = rules.get('template', '自定义')
            summary += f"<p><b>格式化模板:</b> {template}</p>"
        
        self.summary_text.setHtml(summary)
