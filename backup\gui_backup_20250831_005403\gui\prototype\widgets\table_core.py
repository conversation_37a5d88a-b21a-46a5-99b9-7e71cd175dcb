"""
虚拟化可扩展表格核心 - 精简版
从原9330行精简到核心功能
"""

import pandas as pd
from typing import Optional, List, Dict, Any
from enum import Enum

from PyQt5.QtWidgets import QTableWidget, QHeaderView, QAbstractItemView
from PyQt5.QtCore import Qt, pyqtSignal, QModelIndex
from loguru import logger

from src.core.unified_state_management import get_unified_state_manager, StateType
from src.core.unified_cache_manager import get_unified_cache_manager


class ExpandState(Enum):
    """展开状态枚举"""
    COLLAPSED = "collapsed"
    EXPANDED = "expanded"
    PARTIAL = "partial"


class VirtualizedExpandableTableCore(QTableWidget):
    """
    虚拟化可扩展表格核心类
    
    精简版表格，只保留核心功能：
    - 数据显示
    - 虚拟化滚动
    - 基础排序
    - 状态管理
    """
    
    # 核心信号
    data_changed = pyqtSignal()
    sort_changed = pyqtSignal(list)  # 排序列变化
    selection_changed = pyqtSignal(list)  # 选择变化
    page_requested = pyqtSignal(int)  # 请求页面
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logger
        
        # 管理器
        self.state_manager = get_unified_state_manager()
        self.cache_manager = get_unified_cache_manager()
        
        # 数据
        self._data_frame: Optional[pd.DataFrame] = None
        self._current_table: str = ""
        self._visible_columns: List[str] = []
        
        # 状态
        self._sort_columns: List[Dict[str, Any]] = []
        self._selected_rows: List[int] = []
        
        # 初始化
        self._init_table()
        self._connect_signals()
        
        self.logger.info("表格核心初始化完成")
    
    def _init_table(self):
        """初始化表格设置"""
        # 基础设置
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        # 表头设置
        self.horizontalHeader().setStretchLastSection(True)
        self.horizontalHeader().setSortIndicatorShown(True)
        self.verticalHeader().setDefaultSectionSize(30)
        
        # 启用排序
        self.setSortingEnabled(True)
        
        # 设置样式
        self._apply_style()
    
    def _connect_signals(self):
        """连接信号"""
        # 表头点击排序
        self.horizontalHeader().sortIndicatorChanged.connect(self._on_sort_changed)
        
        # 选择变化
        self.itemSelectionChanged.connect(self._on_selection_changed)
    
    def set_data(self, df: pd.DataFrame, table_name: str = "",
                preserve_state: bool = True):
        """
        设置数据
        
        Args:
            df: 数据框
            table_name: 表名
            preserve_state: 是否保留状态
        """
        try:
            self._data_frame = df
            self._current_table = table_name
            
            # 保存当前状态
            if preserve_state and table_name:
                self._save_current_state()
            
            # 清空表格
            self.clear()
            self.setRowCount(0)
            self.setColumnCount(0)
            
            if df is None or df.empty:
                self.logger.debug("设置空数据")
                return
            
            # 设置列
            columns = list(df.columns)
            self._visible_columns = columns
            self.setColumnCount(len(columns))
            self.setHorizontalHeaderLabels(columns)
            
            # 设置行
            self.setRowCount(len(df))
            
            # 填充数据（虚拟化：只填充可见部分）
            self._fill_visible_data()
            
            # 恢复状态
            if preserve_state and table_name:
                self._restore_state(table_name)
            
            self.data_changed.emit()
            self.logger.debug(f"设置数据: {len(df)}行 x {len(columns)}列")
            
        except Exception as e:
            self.logger.error(f"设置数据失败: {e}")
    
    def _fill_visible_data(self):
        """填充可见数据（虚拟化）"""
        if self._data_frame is None or self._data_frame.empty:
            return
        
        # 获取可见范围
        first_visible = self.verticalHeader().visualIndexAt(0)
        last_visible = self.verticalHeader().visualIndexAt(self.height())
        
        if first_visible < 0:
            first_visible = 0
        if last_visible < 0 or last_visible >= len(self._data_frame):
            last_visible = min(len(self._data_frame) - 1, first_visible + 100)
        
        # 只填充可见行
        for row in range(first_visible, last_visible + 1):
            self._fill_row(row)
    
    def _fill_row(self, row: int):
        """填充单行数据"""
        if row >= len(self._data_frame):
            return
        
        from PyQt5.QtWidgets import QTableWidgetItem
        
        for col, column_name in enumerate(self._visible_columns):
            try:
                value = self._data_frame.iloc[row, col]
                
                # 处理空值
                if pd.isna(value):
                    display_text = ""
                else:
                    display_text = str(value)
                
                # 创建项
                item = QTableWidgetItem(display_text)
                item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                
                # 设置项
                self.setItem(row, col, item)
                
            except Exception as e:
                self.logger.error(f"填充单元格失败 [{row},{col}]: {e}")
    
    def _on_sort_changed(self, logical_index: int, order: Qt.SortOrder):
        """处理排序变化"""
        if logical_index < 0 or logical_index >= len(self._visible_columns):
            return
        
        column_name = self._visible_columns[logical_index]
        sort_order = "asc" if order == Qt.AscendingOrder else "desc"
        
        # 更新排序列
        self._sort_columns = [{
            'column_name': column_name,
            'sort_order': sort_order
        }]
        
        # 保存排序状态
        if self._current_table:
            self.state_manager.save_sort_state(self._current_table, self._sort_columns)
        
        # 执行排序
        self._apply_sort()
        
        # 发出信号
        self.sort_changed.emit(self._sort_columns)
        
        self.logger.debug(f"排序变化: {column_name} {sort_order}")
    
    def _apply_sort(self):
        """应用排序"""
        if not self._sort_columns or self._data_frame is None or self._data_frame.empty:
            return
        
        try:
            # 使用缓存管理器进行排序
            sorted_df, from_cache = self.cache_manager.get_sorted_data(
                self._current_table,
                self._data_frame,
                self._sort_columns
            )
            
            # 更新数据
            self._data_frame = sorted_df
            
            # 重新填充数据
            self._fill_visible_data()
            
            self.logger.debug(f"排序完成 (缓存: {from_cache})")
            
        except Exception as e:
            self.logger.error(f"排序失败: {e}")
    
    def _on_selection_changed(self):
        """处理选择变化"""
        selected_rows = list(set(item.row() for item in self.selectedItems()))
        self._selected_rows = selected_rows
        
        # 保存选择状态
        if self._current_table:
            self.state_manager.update_state(
                self._current_table,
                StateType.SELECTION,
                selected_rows,
                'table_core'
            )
        
        self.selection_changed.emit(selected_rows)
    
    def _save_current_state(self):
        """保存当前状态"""
        if not self._current_table:
            return
        
        # 保存列宽
        column_widths = {}
        for i in range(self.columnCount()):
            column_widths[self._visible_columns[i]] = self.columnWidth(i)
        
        # 保存滚动位置
        scroll_position = {
            'horizontal': self.horizontalScrollBar().value(),
            'vertical': self.verticalScrollBar().value()
        }
        
        # 更新状态
        self.state_manager.update_state(
            self._current_table,
            StateType.VIEW,
            {
                'column_widths': column_widths,
                'scroll_position': scroll_position
            },
            'table_core'
        )
    
    def _restore_state(self, table_name: str):
        """恢复状态"""
        try:
            state = self.state_manager.get_table_state(table_name)
            
            # 恢复排序
            if state.sort_columns:
                self._sort_columns = state.sort_columns
                # 应用排序到UI
                if self._sort_columns:
                    first_sort = self._sort_columns[0]
                    col_name = first_sort.get('column_name')
                    if col_name in self._visible_columns:
                        col_index = self._visible_columns.index(col_name)
                        order = Qt.AscendingOrder if first_sort.get('sort_order') == 'asc' else Qt.DescendingOrder
                        self.horizontalHeader().setSortIndicator(col_index, order)
            
            # 恢复列宽
            if state.column_widths:
                for col_name, width in state.column_widths.items():
                    if col_name in self._visible_columns:
                        col_index = self._visible_columns.index(col_name)
                        self.setColumnWidth(col_index, width)
            
            # 恢复滚动位置
            if state.scroll_position:
                self.horizontalScrollBar().setValue(state.scroll_position.get('horizontal', 0))
                self.verticalScrollBar().setValue(state.scroll_position.get('vertical', 0))
            
            # 恢复选择
            if state.selected_rows:
                for row in state.selected_rows:
                    if row < self.rowCount():
                        self.selectRow(row)
            
            self.logger.debug(f"恢复状态: {table_name}")
            
        except Exception as e:
            self.logger.error(f"恢复状态失败: {e}")
    
    def _apply_style(self):
        """应用样式"""
        self.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #e0e0e0;
                selection-background-color: #e3f2fd;
                font-size: 12px;
            }
            
            QTableWidget::item {
                padding: 5px;
                border: none;
            }
            
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: black;
            }
            
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 5px;
                border: none;
                border-right: 1px solid #e0e0e0;
                border-bottom: 1px solid #e0e0e0;
                font-weight: bold;
            }
            
            QHeaderView::section:hover {
                background-color: #e8e8e8;
            }
        """)
    
    def get_selected_data(self) -> Optional[pd.DataFrame]:
        """获取选中的数据"""
        if not self._selected_rows or self._data_frame is None:
            return None
        
        return self._data_frame.iloc[self._selected_rows]
    
    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前数据"""
        return self._data_frame
    
    def clear_data(self):
        """清空数据"""
        self.clear()
        self.setRowCount(0)
        self.setColumnCount(0)
        self._data_frame = None
        self._visible_columns = []
        self._sort_columns = []
        self._selected_rows = []
        self.data_changed.emit()
    
    def refresh(self):
        """刷新显示"""
        if self._data_frame is not None:
            self._fill_visible_data()
    
    # 虚拟化滚动优化
    def scrollContentsBy(self, dx: int, dy: int):
        """重写滚动方法以实现虚拟化"""
        super().scrollContentsBy(dx, dy)
        
        # 滚动时更新可见数据
        if dy != 0:  # 垂直滚动
            self._fill_visible_data()