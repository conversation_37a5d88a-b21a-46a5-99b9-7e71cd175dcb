# -*- coding: utf-8 -*-
"""
增强的日志配置 - 添加自动轮转功能
"""

import os
import sys
from pathlib import Path
from loguru import logger

def setup_logger(name: str = None):
    """设置日志记录器（增强版）"""
    
    # 移除默认handler
    logger.remove()
    
    # 日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 日志文件路径
    log_file = log_dir / "salary_system.log"
    
    # 配置控制台输出
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        level="INFO",
        colorize=True
    )
    
    # 配置文件输出 - 带轮转功能
    logger.add(
        log_file,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
        level="INFO",
        rotation="10 MB",  # 当文件达到10MB时轮转
        retention="7 days",  # 保留7天的日志
        compression="zip",  # 压缩旧日志
        enqueue=True,  # 异步写入
        encoding="utf-8",
        backtrace=True,  # 记录异常堆栈
        diagnose=True  # 诊断模式
    )
    
    # 添加错误日志单独文件
    error_log = log_dir / "errors.log"
    logger.add(
        error_log,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
        level="ERROR",
        rotation="5 MB",  # 错误日志5MB轮转
        retention="30 days",  # 错误日志保留30天
        compression="zip",
        enqueue=True,
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )
    
    # 性能日志（可选）
    if os.getenv("ENABLE_PERFORMANCE_LOG", "false").lower() == "true":
        perf_log = log_dir / "performance.log"
        logger.add(
            perf_log,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | PERF | {message}",
            filter=lambda record: "PERF" in record["extra"],
            rotation="20 MB",
            retention="3 days",
            compression="zip",
            enqueue=True,
            encoding="utf-8"
        )
    
    # 创建命名logger
    if name:
        return logger.bind(name=name)
    
    return logger

# 导出logger实例
default_logger = setup_logger()
