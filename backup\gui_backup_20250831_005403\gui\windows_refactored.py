"""
月度工资异动处理系统 - 功能窗口模块 (重构版)

本模块实现系统的各个功能窗口，专注于UI展示和用户交互，
通过应用服务层统一管理业务逻辑，实现高内聚低耦合。

重构改进:
1. 移除直接业务模块依赖，仅依赖应用服务层
2. 专注UI逻辑，业务逻辑通过服务层调用
3. 统一错误处理和状态管理
4. 提高可测试性和可维护性

主要窗口:
1. ChangeDetectionWindow - 异动检测窗口
2. ReportGenerationWindow - 报告生成窗口
"""

from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QTextEdit,
    QSplitter, QGroupBox, QTabWidget, QProgressBar, QComboBox,
    QSpinBox, QCheckBox, QLineEdit, QFileDialog, QMessageBox,
    QHeaderView, QAbstractItemView, QFrame, QScrollArea
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QFont, QColor, QBrush, QIcon

# 仅依赖应用服务层 - 减少耦合
from src.core.application_service import ApplicationService, ServiceResult
from src.utils.log_config import setup_logger


class ChangeDetectionWindow(QMainWindow):
    """
    异动检测窗口 (重构版)
    
    专注于异动检测功能的UI展示和用户交互，
    通过应用服务层访问业务逻辑。
    
    重构原则:
    - 单一职责: 仅负责UI层逻辑
    - 依赖倒置: 依赖抽象的应用服务层
    - 高内聚低耦合: 减少外部依赖
    """
    
    # 信号定义
    detection_completed = pyqtSignal(dict)  # 检测完成信号
    
    def __init__(self, detection_results=None, parent=None):
        """
        初始化异动检测窗口
        
        Args:
            detection_results: 可选的检测结果数据
            parent: 父窗口
        """
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 初始化应用服务层
        self.app_service = ApplicationService()
        self._init_application_service()
        
        # 初始化数据
        self.detection_results = detection_results or {}
        
        # 初始化界面
        self._init_ui()
        
        # 如果有传入的检测结果，则显示
        if self.detection_results:
            self._display_results(self.detection_results)
        
    def _init_application_service(self):
        """初始化应用服务层"""
        try:
            result = self.app_service.initialize()
            if not result.success:
                self.logger.error(f"应用服务初始化失败: {result.message}")
                QMessageBox.critical(self, "错误", f"应用服务初始化失败: {result.message}")
                return
            
            self.logger.info("异动检测窗口的应用服务初始化成功")
            
        except Exception as e:
            self.logger.error(f"应用服务初始化异常: {e}")
            QMessageBox.critical(self, "错误", f"应用服务初始化异常: {e}")
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("异动检测")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # 设置窗口图标
        self.setWindowIcon(self.style().standardIcon(self.style().SP_FileDialogDetailedView))
        
        # 创建中央部件
        self._create_central_widget()
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_tool_bar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 设置样式
        self._set_window_style()
        
        self.logger.info("异动检测窗口初始化完成")
    
    def _create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建控制面板
        self._create_control_panel(main_layout)
        
        # 创建结果显示区域
        self._create_results_area(main_layout)
    
    def _create_control_panel(self, parent_layout):
        """创建控制面板"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.StyledPanel)
        control_frame.setMaximumHeight(120)
        
        control_layout = QHBoxLayout(control_frame)
        
        # 检测参数组
        params_group = QGroupBox("检测参数")
        params_layout = QGridLayout(params_group)
        
        # 检测类型
        params_layout.addWidget(QLabel("检测类型:"), 0, 0)
        self.detection_type_combo = QComboBox()
        self.detection_type_combo.addItems(["全部异动", "工资增加", "工资减少", "新增人员", "离职人员"])
        params_layout.addWidget(self.detection_type_combo, 0, 1)
        
        # 阈值设置
        params_layout.addWidget(QLabel("变化阈值:"), 1, 0)
        self.threshold_spin = QSpinBox()
        self.threshold_spin.setRange(0, 10000)
        self.threshold_spin.setValue(100)
        self.threshold_spin.setSuffix(" 元")
        params_layout.addWidget(self.threshold_spin, 1, 1)
        
        control_layout.addWidget(params_group)
        
        # 操作按钮组
        actions_group = QGroupBox("操作")
        actions_layout = QVBoxLayout(actions_group)
        
        # 开始检测按钮
        self.start_detection_btn = QPushButton("开始检测")
        self.start_detection_btn.setMinimumHeight(35)
        self.start_detection_btn.clicked.connect(self.start_detection)
        actions_layout.addWidget(self.start_detection_btn)
        
        # 导出结果按钮
        self.export_results_btn = QPushButton("导出结果")
        self.export_results_btn.setMinimumHeight(35)
        self.export_results_btn.setEnabled(False)
        self.export_results_btn.clicked.connect(self.export_results)
        actions_layout.addWidget(self.export_results_btn)
        
        control_layout.addWidget(actions_group)
        
        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("等待检测...")
        stats_layout.addWidget(self.stats_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        stats_layout.addWidget(self.progress_bar)
        
        control_layout.addWidget(stats_group)
        
        parent_layout.addWidget(control_frame)
    
    def _create_results_area(self, parent_layout):
        """创建结果显示区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：异动列表
        self._create_changes_list(splitter)
        
        # 右侧：详细信息
        self._create_detail_view(splitter)
        
        # 设置分割比例
        splitter.setSizes([600, 400])
        
        parent_layout.addWidget(splitter)
    
    def _create_changes_list(self, parent_splitter):
        """创建异动列表"""
        changes_widget = QWidget()
        changes_layout = QVBoxLayout(changes_widget)
        
        # 标题
        title_label = QLabel("检测到的异动")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        changes_layout.addWidget(title_label)
        
        # 异动表格
        self.changes_table = QTableWidget()
        self.changes_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.changes_table.setAlternatingRowColors(True)
        
        # 设置列
        columns = ["工号", "姓名", "异动类型", "变化金额", "原因分析", "置信度"]
        self.changes_table.setColumnCount(len(columns))
        self.changes_table.setHorizontalHeaderLabels(columns)
        
        # 设置列宽
        header = self.changes_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 工号
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 姓名
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 异动类型
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 变化金额
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # 原因分析
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 置信度
        
        # 连接选择信号
        self.changes_table.itemSelectionChanged.connect(self._on_selection_changed)
        
        changes_layout.addWidget(self.changes_table)
        parent_splitter.addWidget(changes_widget)
    
    def _create_detail_view(self, parent_splitter):
        """创建详细信息视图"""
        detail_widget = QWidget()
        detail_layout = QVBoxLayout(detail_widget)
        
        # 标题
        title_label = QLabel("详细信息")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        detail_layout.addWidget(title_label)
        
        # 详细信息选项卡
        self.detail_tabs = QTabWidget()
        
        # 基本信息选项卡
        self._create_basic_info_tab()
        
        # 工资明细选项卡
        self._create_salary_detail_tab()
        
        # 历史记录选项卡
        self._create_history_tab()
        
        detail_layout.addWidget(self.detail_tabs)
        parent_splitter.addWidget(detail_widget)
    
    def _create_basic_info_tab(self):
        """创建基本信息选项卡"""
        basic_widget = QWidget()
        basic_layout = QVBoxLayout(basic_widget)
        
        # 基本信息标签
        self.basic_info_label = QLabel("请选择一个异动项目查看详细信息")
        self.basic_info_label.setAlignment(Qt.AlignTop)
        self.basic_info_label.setWordWrap(True)
        basic_layout.addWidget(self.basic_info_label)
        
        basic_layout.addStretch()
        self.detail_tabs.addTab(basic_widget, "基本信息")
    
    def _create_salary_detail_tab(self):
        """创建工资明细选项卡"""
        salary_widget = QWidget()
        salary_layout = QVBoxLayout(salary_widget)
        
        # 工资明细表格
        self.salary_detail_table = QTableWidget()
        self.salary_detail_table.setColumnCount(3)
        self.salary_detail_table.setHorizontalHeaderLabels(["项目", "变更前", "变更后"])
        
        salary_layout.addWidget(self.salary_detail_table)
        self.detail_tabs.addTab(salary_widget, "工资明细")
    
    def _create_history_tab(self):
        """创建历史记录选项卡"""
        history_widget = QWidget()
        history_layout = QVBoxLayout(history_widget)
        
        # 历史记录文本
        self.history_text = QTextEdit()
        self.history_text.setReadOnly(True)
        history_layout.addWidget(self.history_text)
        
        self.detail_tabs.addTab(history_widget, "历史记录")
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 检测菜单
        detection_menu = menubar.addMenu('检测(&D)')
        
        # 开始检测
        start_action = detection_menu.addAction('开始检测(&S)')
        start_action.setShortcut('F5')
        start_action.triggered.connect(self.start_detection)
        
        # 刷新结果
        refresh_action = detection_menu.addAction('刷新结果(&R)')
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_data)
        
        detection_menu.addSeparator()
        
        # 导出结果
        export_action = detection_menu.addAction('导出结果(&E)')
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_results)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        # 关闭窗口
        close_action = view_menu.addAction('关闭窗口(&C)')
        close_action.setShortcut('Ctrl+W')
        close_action.triggered.connect(self.close)
    
    def _create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('检测工具栏')
        
        # 开始检测
        start_action = toolbar.addAction('开始检测')
        start_action.triggered.connect(self.start_detection)
        
        # 导出结果
        export_action = toolbar.addAction('导出结果')
        export_action.triggered.connect(self.export_results)
        
        toolbar.addSeparator()
        
        # 刷新
        refresh_action = toolbar.addAction('刷新')
        refresh_action.triggered.connect(self.refresh_data)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")
    
    def _set_window_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin: 5px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
            QTableWidget {
                gridline-color: #d0d0d0;
                selection-background-color: #0078d4;
            }
        """)
    
    # ==================== 业务逻辑处理方法 ====================
    
    def start_detection(self):
        """开始异动检测"""
        try:
            self.status_bar.showMessage("正在进行异动检测...")
            self.start_detection_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 通过应用服务层执行异动检测
            result = self.app_service.detect_changes()
            
            if result.success:
                self.detection_results = result.data
                self._display_results(self.detection_results)
                self.status_bar.showMessage(f"检测完成: {result.message}")
                self.export_results_btn.setEnabled(True)
                
                # 发送检测完成信号
                self.detection_completed.emit(self.detection_results)
                
            else:
                QMessageBox.critical(self, "错误", f"异动检测失败: {result.message}")
                self.status_bar.showMessage("检测失败")
                
        except Exception as e:
            self.logger.error(f"异动检测失败: {e}")
            QMessageBox.critical(self, "错误", f"异动检测失败: {e}")
            self.status_bar.showMessage("检测失败")
            
        finally:
            self.start_detection_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
    
    def _display_results(self, results):
        """显示检测结果"""
        try:
            if not results or not hasattr(results, 'changes'):
                self.stats_label.setText("没有检测到异动")
                return
                
            changes = results.changes if hasattr(results, 'changes') else []
            
            # 更新统计信息
            total_changes = len(changes)
            self.stats_label.setText(f"检测到 {total_changes} 个异动")
            
            # 填充结果表格
            self.changes_table.setRowCount(total_changes)
            
            for row, change in enumerate(changes):
                # 工号
                self.changes_table.setItem(row, 0, QTableWidgetItem(str(change.get('employee_id', ''))))
                
                # 姓名
                self.changes_table.setItem(row, 1, QTableWidgetItem(str(change.get('name', ''))))
                
                # 异动类型
                self.changes_table.setItem(row, 2, QTableWidgetItem(str(change.get('change_type', ''))))
                
                # 变化金额
                amount = change.get('amount_change', 0)
                amount_text = f"{amount:+.2f}" if isinstance(amount, (int, float)) else str(amount)
                self.changes_table.setItem(row, 3, QTableWidgetItem(amount_text))
                
                # 原因分析
                self.changes_table.setItem(row, 4, QTableWidgetItem(str(change.get('reason', ''))))
                
                # 置信度
                confidence = change.get('confidence', 0)
                confidence_text = f"{confidence:.1%}" if isinstance(confidence, (int, float)) else str(confidence)
                self.changes_table.setItem(row, 5, QTableWidgetItem(confidence_text))
            
            self.logger.info(f"显示了 {total_changes} 个异动检测结果")
            
        except Exception as e:
            self.logger.error(f"显示检测结果失败: {e}")
            QMessageBox.warning(self, "警告", f"显示检测结果失败: {e}")
    
    def _on_selection_changed(self):
        """选择变化处理"""
        try:
            current_row = self.changes_table.currentRow()
            if current_row >= 0 and hasattr(self.detection_results, 'changes'):
                changes = self.detection_results.changes
                if current_row < len(changes):
                    change_data = changes[current_row]
                    self._show_detail_info(change_data)
        except Exception as e:
            self.logger.error(f"选择变化处理失败: {e}")
    
    def _show_detail_info(self, change_data: Dict[str, Any]):
        """显示详细信息"""
        try:
            # 更新基本信息
            basic_info = f"""
            工号: {change_data.get('employee_id', '未知')}
            姓名: {change_data.get('name', '未知')}
            异动类型: {change_data.get('change_type', '未知')}
            变化金额: {change_data.get('amount_change', 0):+.2f} 元
            检测时间: {change_data.get('detection_time', '未知')}
            置信度: {change_data.get('confidence', 0):.1%}
            
            原因分析:
            {change_data.get('reason', '无详细分析')}
            """
            self.basic_info_label.setText(basic_info)
            
            # 更新工资明细
            salary_details = change_data.get('salary_details', {})
            self.salary_detail_table.setRowCount(len(salary_details))
            
            for row, (item, details) in enumerate(salary_details.items()):
                self.salary_detail_table.setItem(row, 0, QTableWidgetItem(str(item)))
                self.salary_detail_table.setItem(row, 1, QTableWidgetItem(str(details.get('before', ''))))
                self.salary_detail_table.setItem(row, 2, QTableWidgetItem(str(details.get('after', ''))))
            
            # 更新历史记录
            history = change_data.get('history', '暂无历史记录')
            self.history_text.setPlainText(str(history))
            
        except Exception as e:
            self.logger.error(f"显示详细信息失败: {e}")
    
    def export_results(self):
        """导出结果"""
        try:
            if not self.detection_results:
                QMessageBox.information(self, "提示", "没有可导出的检测结果")
                return
                
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出异动检测结果", "", "Excel文件 (*.xlsx)"
            )
            
            if file_path:
                # 通过应用服务层导出数据
                result = self.app_service.export_change_data(file_path)
                
                if result.success:
                    QMessageBox.information(self, "成功", result.message)
                    self.status_bar.showMessage(f"结果已导出: {file_path}")
                else:
                    QMessageBox.critical(self, "错误", f"导出失败: {result.message}")
                    
        except Exception as e:
            self.logger.error(f"导出结果失败: {e}")
            QMessageBox.critical(self, "错误", f"导出结果失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            # 通过应用服务层获取最新检测结果
            result = self.app_service.get_detection_results()
            
            if result.success and result.data:
                self.detection_results = result.data
                self._display_results(self.detection_results)
                self.status_bar.showMessage("数据已刷新")
            else:
                self.status_bar.showMessage("没有可刷新的数据")
                
        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
            QMessageBox.warning(self, "警告", f"刷新数据失败: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 清理应用服务资源
            if hasattr(self, 'app_service'):
                self.app_service.cleanup()
                
            self.logger.info("异动检测窗口正常关闭")
            event.accept()
            
        except Exception as e:
            self.logger.error(f"窗口关闭事件处理失败: {e}")
            event.accept()


class ReportGenerationWindow(QMainWindow):
    """
    报告生成窗口 (重构版)
    
    专注于报告生成功能的UI展示和用户交互，
    通过应用服务层访问业务逻辑。
    """
    
    # 信号定义
    report_generated = pyqtSignal(str)  # 报告生成完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 初始化应用服务层
        self.app_service = ApplicationService()
        self._init_application_service()
        
        # 初始化界面
        self._init_ui()
    
    def _init_application_service(self):
        """初始化应用服务层"""
        try:
            result = self.app_service.initialize()
            if not result.success:
                self.logger.error(f"应用服务初始化失败: {result.message}")
                QMessageBox.critical(self, "错误", f"应用服务初始化失败: {result.message}")
                return
            
            self.logger.info("报告生成窗口的应用服务初始化成功")
            
        except Exception as e:
            self.logger.error(f"应用服务初始化异常: {e}")
            QMessageBox.critical(self, "错误", f"应用服务初始化异常: {e}")
    
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("报告生成")
        self.setMinimumSize(900, 600)
        self.resize(1100, 700)
        
        # 设置窗口图标
        self.setWindowIcon(self.style().standardIcon(self.style().SP_FileDialogDetailedView))
        
        # 创建中央部件
        self._create_central_widget()
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_tool_bar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 设置样式
        self._set_window_style()
        
        self.logger.info("报告生成窗口初始化完成")
    
    def _create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：配置面板
        self._create_config_panel(splitter)
        
        # 右侧：预览区域
        self._create_preview_area(splitter)
        
        # 设置分割比例
        splitter.setSizes([400, 500])
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.addWidget(splitter)
    
    def _create_config_panel(self, parent_splitter):
        """创建配置面板"""
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        
        # 报告类型组
        type_group = QGroupBox("报告类型")
        type_layout = QVBoxLayout(type_group)
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems(["汇总报告", "个人明细报告", "部门报告"])
        self.report_type_combo.currentTextChanged.connect(self.preview_report)
        type_layout.addWidget(self.report_type_combo)
        
        config_layout.addWidget(type_group)
        
        # 输出格式组
        format_group = QGroupBox("输出格式")
        format_layout = QVBoxLayout(format_group)
        
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["Word文档", "Excel表格", "PDF文档"])
        format_layout.addWidget(self.output_format_combo)
        
        config_layout.addWidget(format_group)
        
        # 操作按钮
        self.preview_btn = QPushButton("预览报告")
        self.preview_btn.clicked.connect(self.preview_report)
        config_layout.addWidget(self.preview_btn)
        
        self.generate_btn = QPushButton("生成报告")
        self.generate_btn.clicked.connect(self.generate_report)
        config_layout.addWidget(self.generate_btn)
        
        config_layout.addStretch()
        parent_splitter.addWidget(config_widget)
    
    def _create_preview_area(self, parent_splitter):
        """创建预览区域"""
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        
        # 预览标题
        title_label = QLabel("报告预览")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        preview_layout.addWidget(title_label)
        
        # 预览文本
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)
        
        parent_splitter.addWidget(preview_widget)
        
        # 设置默认预览
        self._set_default_preview()
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 生成报告
        generate_action = file_menu.addAction('生成报告(&G)')
        generate_action.setShortcut('Ctrl+G')
        generate_action.triggered.connect(self.generate_report)
        
        file_menu.addSeparator()
        
        # 关闭窗口
        close_action = file_menu.addAction('关闭(&C)')
        close_action.setShortcut('Ctrl+W')
        close_action.triggered.connect(self.close)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        # 预览报告
        preview_action = view_menu.addAction('预览报告(&P)')
        preview_action.setShortcut('F5')
        preview_action.triggered.connect(self.preview_report)
    
    def _create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('报告工具栏')
        
        # 预览报告
        preview_action = toolbar.addAction('预览报告')
        preview_action.triggered.connect(self.preview_report)
        
        # 生成报告
        generate_action = toolbar.addAction('生成报告')
        generate_action.triggered.connect(self.generate_report)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")
    
    def _set_window_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin: 5px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 3px;
                min-height: 25px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
    
    def _set_default_preview(self):
        """设置默认预览"""
        default_text = """
        报告预览
        
        请选择报告类型并点击"预览报告"按钮查看报告内容。
        
        支持的报告类型：
        • 汇总报告 - 显示整体异动统计信息
        • 个人明细报告 - 显示每个人员的详细异动信息
        • 部门报告 - 按部门分组显示异动情况
        
        支持的输出格式：
        • Word文档 (.docx)
        • Excel表格 (.xlsx)
        • PDF文档 (.pdf)
        """
        self.preview_text.setPlainText(default_text)
    
    def preview_report(self):
        """预览报告"""
        try:
            report_type = self.report_type_combo.currentText()
            self.status_bar.showMessage(f"正在预览{report_type}...")
            
            # 这里可以通过应用服务获取数据来生成预览
            preview_content = f"""
            {report_type} - 预览
            
            生成时间: 当前时间
            报告类型: {report_type}
            输出格式: {self.output_format_combo.currentText()}
            
            [这里将显示实际的报告内容]
            
            注意：这只是预览，实际报告将包含完整的数据和格式。
            """
            
            self.preview_text.setPlainText(preview_content)
            self.status_bar.showMessage("预览完成")
            
        except Exception as e:
            self.logger.error(f"预览报告失败: {e}")
            QMessageBox.warning(self, "警告", f"预览报告失败: {e}")
    
    def generate_report(self):
        """生成报告"""
        try:
            output_format = self.output_format_combo.currentText()
            self.status_bar.showMessage("正在生成报告...")
            self.generate_btn.setEnabled(False)
            
            # 根据格式选择生成方法
            if output_format == "Word文档":
                result = self.app_service.generate_word_report()
            elif output_format == "Excel表格":
                result = self.app_service.generate_excel_report()
            else:
                QMessageBox.information(self, "提示", "PDF格式暂未实现")
                return
            
            if result.success:
                QMessageBox.information(self, "成功", result.message)
                self.status_bar.showMessage("报告生成完成")
                
                # 发送报告生成完成信号
                if result.data and 'report_path' in result.data:
                    self.report_generated.emit(result.data['report_path'])
            else:
                QMessageBox.critical(self, "错误", f"报告生成失败: {result.message}")
                self.status_bar.showMessage("报告生成失败")
                
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            QMessageBox.critical(self, "错误", f"生成报告失败: {e}")
            self.status_bar.showMessage("报告生成失败")
            
        finally:
            self.generate_btn.setEnabled(True)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 清理应用服务资源
            if hasattr(self, 'app_service'):
                self.app_service.cleanup()
                
            self.logger.info("报告生成窗口正常关闭")
            event.accept()
            
        except Exception as e:
            self.logger.error(f"窗口关闭事件处理失败: {e}")
            event.accept() 