"""
优化的主窗口 - P2级性能优化版本
实现延迟加载和启动优化
"""

from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QApplication
from PyQt5.QtCore import QTimer, Qt, pyqtSignal
from loguru import logger

from src.core.performance_profiler import get_performance_profiler, profile_startup, profile_function
from src.core.lazy_loader import (
    get_lazy_loader, 
    get_component_loader,
    LazyProperty,
    lazy_import_property
)
from src.gui.prototype.main_window_core import PrototypeMainWindowCore


class OptimizedMainWindow(PrototypeMainWindowCore):
    """
    优化的主窗口
    
    性能优化特性：
    - 延迟加载重量级组件
    - 异步初始化
    - 按需导入模块
    - 启动时间优化
    """
    
    # 信号
    initialization_progress = pyqtSignal(int)  # 初始化进度
    
    def __init__(self):
        # 记录启动开始
        self.profiler = get_performance_profiler()
        profile_startup("主窗口创建开始")
        
        # 基础初始化（快速）
        super().__init__()
        
        # 延迟加载器
        self.lazy_loader = get_lazy_loader()
        self.component_loader = get_component_loader()
        
        # 注册延迟组件
        self._register_lazy_components()
        
        # 启动异步初始化
        QTimer.singleShot(0, self._async_initialization)
        
        profile_startup("主窗口基础初始化完成")
        self.logger.info("OptimizedMainWindow 初始化完成")
    
    def _register_lazy_components(self):
        """注册延迟加载组件"""
        # 导航面板
        self.component_loader.register_component(
            'navigation_panel',
            self._create_navigation_panel
        )
        
        # 工作区
        self.component_loader.register_component(
            'workspace',
            self._create_workspace
        )
        
        # 分页控件
        self.component_loader.register_component(
            'pagination',
            self._create_pagination_widget
        )
        
        # 菜单栏
        self.component_loader.register_component(
            'menubar',
            self._create_menubar
        )
        
        # 工具栏
        self.component_loader.register_component(
            'toolbar',
            self._create_toolbar
        )
    
    def _async_initialization(self):
        """异步初始化（在事件循环中执行）"""
        profile_startup("异步初始化开始")
        
        # 创建初始化步骤
        init_steps = [
            ("加载UI组件", self._load_ui_components),
            ("加载数据服务", self._load_data_services),
            ("恢复会话状态", self._restore_session),
            ("完成初始化", self._finalize_initialization)
        ]
        
        # 使用定时器逐步执行
        self._init_steps = init_steps
        self._current_step = 0
        self._execute_next_step()
    
    def _execute_next_step(self):
        """执行下一个初始化步骤"""
        if self._current_step >= len(self._init_steps):
            return
        
        step_name, step_func = self._init_steps[self._current_step]
        
        try:
            profile_startup(f"执行: {step_name}")
            step_func()
            
            # 更新进度
            progress = int((self._current_step + 1) / len(self._init_steps) * 100)
            self.initialization_progress.emit(progress)
            
        except Exception as e:
            self.logger.error(f"初始化步骤失败 [{step_name}]: {e}")
        
        self._current_step += 1
        
        # 继续下一步
        if self._current_step < len(self._init_steps):
            QTimer.singleShot(10, self._execute_next_step)
    
    @profile_function
    def _load_ui_components(self):
        """加载UI组件（延迟）"""
        # 只加载立即需要的组件
        if not hasattr(self, 'main_workspace'):
            self.main_workspace = self.component_loader.get_component('workspace')
    
    @profile_function
    def _load_data_services(self):
        """加载数据服务（延迟）"""
        # 数据服务已在基类中初始化
        pass
    
    @profile_function
    def _restore_session(self):
        """恢复会话状态"""
        try:
            # 恢复上次打开的表
            last_table = self.state_manager.get_global_state('last_table')
            if last_table:
                # 延迟加载表数据
                QTimer.singleShot(100, lambda: self.set_current_table(last_table))
        except Exception as e:
            self.logger.error(f"恢复会话失败: {e}")
    
    @profile_function
    def _finalize_initialization(self):
        """完成初始化"""
        profile_startup("初始化完成")
        
        # 打印性能报告
        if self.logger.level <= 10:  # DEBUG级别
            self.profiler.print_report()
        
        # 发送完成信号
        self.initialization_progress.emit(100)
        self.status_updated.emit("应用就绪")
    
    # 延迟属性
    @LazyProperty
    def navigation_panel(self):
        """导航面板（延迟创建）"""
        return self.component_loader.get_component('navigation_panel')
    
    @LazyProperty
    def pagination_widget(self):
        """分页控件（延迟创建）"""
        return self.component_loader.get_component('pagination')
    
    @LazyProperty
    def menubar_manager(self):
        """菜单栏管理器（延迟创建）"""
        return self.component_loader.get_component('menubar')
    
    @LazyProperty
    def toolbar(self):
        """工具栏（延迟创建）"""
        return self.component_loader.get_component('toolbar')
    
    # 延迟创建方法
    def _create_navigation_panel(self):
        """创建导航面板"""
        try:
            from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
            panel = EnhancedNavigationPanel(self)
            # 添加到UI
            if hasattr(self, 'main_layout'):
                self.main_layout.insertWidget(0, panel)
            return panel
        except ImportError:
            self.logger.warning("导航面板模块未找到")
            return None
    
    def _create_workspace(self):
        """创建工作区"""
        from src.gui.prototype.prototype_main_window_adapter import MainWorkspaceArea
        workspace = MainWorkspaceArea(self)
        if hasattr(self, 'main_layout'):
            self.main_layout.addWidget(workspace)
        return workspace
    
    def _create_pagination_widget(self):
        """创建分页控件"""
        try:
            from src.gui.prototype.widgets.pagination_widget import PaginationWidget
            widget = PaginationWidget(self)
            if hasattr(self, 'main_layout'):
                self.main_layout.addWidget(widget)
            return widget
        except ImportError:
            self.logger.warning("分页控件模块未找到")
            return None
    
    def _create_menubar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        self._setup_menus(menubar)
        return menubar
    
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        self._setup_toolbar_actions(toolbar)
        return toolbar
    
    def _setup_menus(self, menubar):
        """设置菜单（简化版）"""
        file_menu = menubar.addMenu("文件(&F)")
        file_menu.addAction("导入数据", self._on_import_data)
        file_menu.addAction("导出数据", self._on_export_data)
        file_menu.addSeparator()
        file_menu.addAction("退出", self.close)
        
        view_menu = menubar.addMenu("视图(&V)")
        view_menu.addAction("刷新", self.refresh_current_view)
        view_menu.addAction("清除缓存", self.clear_cache)
    
    def _setup_toolbar_actions(self, toolbar):
        """设置工具栏动作（简化版）"""
        toolbar.addAction("导入", self._on_import_data)
        toolbar.addAction("导出", self._on_export_data)
        toolbar.addSeparator()
        toolbar.addAction("刷新", self.refresh_current_view)
    
    def _on_import_data(self):
        """处理导入数据"""
        # 延迟导入对话框
        dialog_module = self.lazy_loader.lazy_import('src.gui.main_dialogs')
        dialog_class = getattr(dialog_module, 'DataImportDialog', None)
        
        if dialog_class:
            dialog = dialog_class(self)
            dialog.exec_()
    
    def _on_export_data(self):
        """处理导出数据"""
        # 延迟导入对话框
        from PyQt5.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出数据", "", "Excel文件 (*.xlsx);;CSV文件 (*.csv)"
        )
        
        if file_path:
            table_name = self.get_current_table()
            if table_name:
                self.export_data(table_name, file_path)


def create_optimized_app():
    """
    创建优化的应用
    
    Returns:
        (app, window) 元组
    """
    import sys
    
    # 性能分析开始
    profiler = get_performance_profiler()
    profile_startup("应用启动")
    
    # 创建应用
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    profile_startup("QApplication创建")
    
    # 设置应用属性
    app.setApplicationName("月度工资异动处理系统")
    app.setOrganizationName("优化版")
    
    # 创建主窗口
    window = OptimizedMainWindow()
    
    profile_startup("主窗口创建")
    
    # 显示窗口
    window.show()
    
    profile_startup("窗口显示")
    
    return app, window