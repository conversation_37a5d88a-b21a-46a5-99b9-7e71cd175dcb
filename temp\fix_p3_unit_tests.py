#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
P3级问题修复 - 添加单元测试
增加关键模块的单元测试覆盖
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_cache_manager_tests():
    """创建缓存管理器测试"""
    
    test_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存管理器单元测试
"""

import unittest
import pandas as pd
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.gui.widgets.pagination_cache_manager import PaginationCacheManager, CacheEntry

class TestPaginationCacheManager(unittest.TestCase):
    """分页缓存管理器测试"""
    
    def setUp(self):
        """测试初始化"""
        self.cache_manager = PaginationCacheManager(max_cache_entries=10, max_memory_mb=10)
        
    def tearDown(self):
        """测试清理"""
        self.cache_manager.clear_cache()
        
    def test_put_and_get_page_data(self):
        """测试缓存存取"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'id': [1, 2, 3],
            'name': ['Alice', 'Bob', 'Charlie']
        })
        
        # 存储数据
        self.cache_manager.put_page_data('test_table', 1, 10, test_data)
        
        # 获取数据
        cached_data = self.cache_manager.get_page_data('test_table', 1, 10)
        
        # 验证
        self.assertIsNotNone(cached_data)
        pd.testing.assert_frame_equal(cached_data, test_data)
        
    def test_cache_expiration(self):
        """测试缓存过期"""
        # 设置短TTL
        self.cache_manager.ttl_seconds = 0.1
        
        # 存储数据
        test_data = pd.DataFrame({'id': [1]})
        self.cache_manager.put_page_data('test_table', 1, 10, test_data)
        
        # 等待过期
        import time
        time.sleep(0.2)
        
        # 验证已过期
        cached_data = self.cache_manager.get_page_data('test_table', 1, 10)
        self.assertIsNone(cached_data)
        
    def test_cache_statistics(self):
        """测试缓存统计"""
        # 添加一些数据
        test_data = pd.DataFrame({'id': [1]})
        self.cache_manager.put_page_data('test_table', 1, 10, test_data)
        
        # 命中
        self.cache_manager.get_page_data('test_table', 1, 10)
        
        # 未命中
        self.cache_manager.get_page_data('test_table', 2, 10)
        
        # 获取统计
        stats = self.cache_manager.get_statistics()
        
        # 验证
        self.assertEqual(stats['hits'], 1)
        self.assertEqual(stats['misses'], 1)
        self.assertEqual(stats['hit_rate'], 0.5)
        
    def test_lru_eviction(self):
        """测试LRU淘汰"""
        # 设置小容量
        self.cache_manager = PaginationCacheManager(max_cache_entries=2)
        
        # 添加3个条目（触发淘汰）
        for i in range(3):
            data = pd.DataFrame({'id': [i]})
            self.cache_manager.put_page_data('test_table', i, 10, data)
        
        # 验证第一个被淘汰
        cached_data = self.cache_manager.get_page_data('test_table', 0, 10)
        self.assertIsNone(cached_data)
        
        # 验证后两个还在
        cached_data = self.cache_manager.get_page_data('test_table', 1, 10)
        self.assertIsNotNone(cached_data)
        cached_data = self.cache_manager.get_page_data('test_table', 2, 10)
        self.assertIsNotNone(cached_data)

if __name__ == '__main__':
    unittest.main()
'''
    
    # 保存测试文件
    test_file = Path("test/test_cache_manager.py")
    test_file.parent.mkdir(exist_ok=True)
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print(f"缓存管理器测试已创建: {test_file}")
    return test_file

def create_field_mapping_tests():
    """创建字段映射测试"""
    
    test_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
字段映射单元测试
"""

import unittest
import json
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestFieldMapping(unittest.TestCase):
    """字段映射测试"""
    
    def setUp(self):
        """测试初始化"""
        self.mapping_file = Path("state/data/field_mappings.json")
        
    def test_mapping_file_exists(self):
        """测试映射文件存在"""
        self.assertTrue(self.mapping_file.exists())
        
    def test_mapping_format(self):
        """测试映射格式"""
        with open(self.mapping_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 验证必要字段
        self.assertIn('version', data)
        self.assertIn('table_mappings', data)
        self.assertIn('field_templates', data)
        
    def test_mapping_consistency(self):
        """测试映射一致性"""
        with open(self.mapping_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查所有表映射格式（应该是中文->英文）
        for table_name, mappings in data['table_mappings'].items():
            if mappings:
                # 取第一个映射检查格式
                first_key = list(mappings.keys())[0]
                first_value = mappings[first_key]
                
                # 中文key
                self.assertTrue(any(ord(c) > 127 for c in first_key), 
                               f"表 {table_name} 的key应该是中文: {first_key}")
                
                # 英文value
                if first_value:  # 可能有空值
                    self.assertTrue(all(ord(c) < 128 for c in first_value.replace('_', '')), 
                                   f"表 {table_name} 的value应该是英文: {first_value}")
    
    def test_common_fields(self):
        """测试常用字段映射"""
        with open(self.mapping_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 常用字段
        common_fields = {
            '工号': 'employee_id',
            '姓名': 'employee_name',
            '部门名称': 'department'
        }
        
        # 检查至少一个表包含这些映射
        found = {field: False for field in common_fields}
        
        for table_name, mappings in data['table_mappings'].items():
            for cn_field, en_field in common_fields.items():
                if mappings.get(cn_field) == en_field:
                    found[cn_field] = True
        
        for field, was_found in found.items():
            self.assertTrue(was_found, f"常用字段 {field} 未找到正确映射")

if __name__ == '__main__':
    unittest.main()
'''
    
    # 保存测试文件
    test_file = Path("test/test_field_mapping.py")
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print(f"字段映射测试已创建: {test_file}")
    return test_file

def create_config_tests():
    """创建配置测试"""
    
    test_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置文件单元测试
"""

import unittest
import json
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestConfiguration(unittest.TestCase):
    """配置文件测试"""
    
    def setUp(self):
        """测试初始化"""
        self.config_file = Path("config.json")
        
    def test_config_file_exists(self):
        """测试配置文件存在"""
        self.assertTrue(self.config_file.exists())
        
    def test_config_valid_json(self):
        """测试配置文件格式"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.assertIsInstance(config, dict)
        except json.JSONDecodeError:
            self.fail("配置文件不是有效的JSON")
    
    def test_cache_config(self):
        """测试缓存配置"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        cache_config = config.get('cache', {})
        
        # 验证缓存配置
        self.assertIn('enabled', cache_config)
        self.assertIn('ttl_seconds', cache_config)
        self.assertIn('max_entries', cache_config)
        self.assertIn('max_memory_mb', cache_config)
        
        # 验证值的合理性
        self.assertIsInstance(cache_config['enabled'], bool)
        self.assertGreater(cache_config['ttl_seconds'], 0)
        self.assertGreater(cache_config['max_entries'], 0)
        self.assertGreater(cache_config['max_memory_mb'], 0)
    
    def test_database_config(self):
        """测试数据库配置"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        db_config = config.get('database', {})
        
        # 验证数据库配置
        self.assertIn('type', db_config)
        self.assertEqual(db_config['type'], 'sqlite')
        
        if 'path' in db_config:
            # 验证路径格式
            self.assertTrue(db_config['path'].endswith('.db'))

if __name__ == '__main__':
    unittest.main()
'''
    
    # 保存测试文件  
    test_file = Path("test/test_configuration.py")
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print(f"配置测试已创建: {test_file}")
    return test_file

def create_test_runner():
    """创建测试运行器"""
    
    runner_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试运行器 - 运行所有单元测试
"""

import unittest
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_all_tests():
    """运行所有测试"""
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试模块
    test_modules = [
        'test.test_cache_manager',
        'test.test_field_mapping',
        'test.test_configuration'
    ]
    
    for module_name in test_modules:
        try:
            module = __import__(module_name, fromlist=[''])
            suite.addTests(loader.loadTestsFromModule(module))
            print(f"已加载测试模块: {module_name}")
        except ImportError as e:
            print(f"无法加载测试模块 {module_name}: {e}")
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出统计
    print("\\n" + "="*60)
    print("测试统计")
    print("="*60)
    print(f"运行测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    # 计算覆盖率（如果安装了coverage）
    try:
        import coverage
        print("\\n提示：使用以下命令运行带覆盖率的测试：")
        print("coverage run -m unittest discover test/")
        print("coverage report")
        print("coverage html")
    except ImportError:
        print("\\n提示：安装coverage以获取测试覆盖率：")
        print("pip install coverage")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
'''
    
    # 保存运行器
    runner_file = Path("test/run_tests.py")
    
    with open(runner_file, 'w', encoding='utf-8') as f:
        f.write(runner_code)
    
    print(f"测试运行器已创建: {runner_file}")
    return runner_file

def main():
    """主函数"""
    print("\n" + "="*70)
    print("P3级问题修复 - 单元测试")
    print("="*70)
    
    print("\n创建单元测试文件...")
    
    # 1. 创建缓存管理器测试
    cache_test = create_cache_manager_tests()
    
    # 2. 创建字段映射测试
    mapping_test = create_field_mapping_tests()
    
    # 3. 创建配置测试
    config_test = create_config_tests()
    
    # 4. 创建测试运行器
    runner = create_test_runner()
    
    print("\n" + "="*70)
    print("单元测试创建完成")
    print("="*70)
    print("\n已创建测试文件：")
    print(f"1. {cache_test}")
    print(f"2. {mapping_test}")
    print(f"3. {config_test}")
    print(f"4. {runner}")
    print("\n运行测试：")
    print("python test/run_tests.py")
    print("\n或使用pytest：")
    print("pytest test/ -v")

if __name__ == "__main__":
    main()