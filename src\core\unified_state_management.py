"""
统一状态管理器 - P0级优化
整合所有分散的状态管理器功能
"""

import json
import os
from typing import Dict, Any, Optional, List, Callable, Set
from datetime import datetime
from dataclasses import dataclass, field, asdict
from enum import Enum
from pathlib import Path
from loguru import logger


class StateType(Enum):
    """状态类型枚举"""
    SORT = "sort"
    PAGINATION = "pagination"
    FILTER = "filter"
    SELECTION = "selection"
    VIEW = "view"
    NAVIGATION = "navigation"
    UI = "ui"
    DATA = "data"
    CACHE = "cache"
    CONFIG = "config"


@dataclass
class StateChange:
    """状态变更记录"""
    timestamp: str
    state_type: StateType
    old_value: Any
    new_value: Any
    source: str
    table_name: Optional[str] = None


@dataclass
class TableState:
    """表格完整状态"""
    table_name: str
    sort_columns: List[Dict[str, Any]] = field(default_factory=list)
    current_page: int = 1
    page_size: int = 100
    filters: Dict[str, Any] = field(default_factory=dict)
    selected_rows: List[int] = field(default_factory=list)
    column_widths: Dict[str, int] = field(default_factory=dict)
    scroll_position: Dict[str, int] = field(default_factory=dict)
    expanded_rows: Set[int] = field(default_factory=set)
    last_updated: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # Set不能直接序列化，转换为list
        if 'expanded_rows' in data:
            data['expanded_rows'] = list(self.expanded_rows)
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TableState':
        """从字典创建"""
        # 将list转回set
        if 'expanded_rows' in data:
            data['expanded_rows'] = set(data['expanded_rows'])
        return cls(**data)


class UnifiedStateManagement:
    """
    统一状态管理器
    
    整合功能：
    1. 排序状态管理（原enhanced_sort_state_manager）
    2. 分页状态管理（原pagination_state_manager）
    3. 表格状态管理（原table_state_manager）
    4. UI状态管理（原unified_state_manager）
    5. 导航状态管理
    6. 缓存状态管理
    """
    
    _instance = None
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化管理器"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.logger = logger
        
        # 状态存储
        self._states: Dict[str, TableState] = {}
        self._global_state: Dict[str, Any] = {}
        
        # 变更历史
        self._change_history: List[StateChange] = []
        self._max_history = 100
        
        # 监听器
        self._listeners: Dict[StateType, List[Callable]] = {
            state_type: [] for state_type in StateType
        }
        
        # 持久化配置
        self._state_dir = Path("state/unified")
        self._state_dir.mkdir(parents=True, exist_ok=True)
        self._state_file = self._state_dir / "unified_state.json"
        
        # 加载持久化状态
        self._load_states()
        
        self.logger.info("UnifiedStateManagement 初始化完成")
    
    def get_table_state(self, table_name: str) -> TableState:
        """
        获取表格状态
        
        Args:
            table_name: 表格名称
        
        Returns:
            表格状态对象
        """
        if table_name not in self._states:
            self._states[table_name] = TableState(table_name=table_name)
        return self._states[table_name]
    
    def update_state(self, table_name: str, state_type: StateType, 
                    value: Any, source: str = "unknown") -> bool:
        """
        更新状态
        
        Args:
            table_name: 表格名称
            state_type: 状态类型
            value: 新值
            source: 更新来源
        
        Returns:
            是否更新成功
        """
        try:
            state = self.get_table_state(table_name)
            old_value = None
            
            # 根据状态类型更新对应字段
            if state_type == StateType.SORT:
                old_value = state.sort_columns
                state.sort_columns = value
            elif state_type == StateType.PAGINATION:
                if isinstance(value, dict):
                    old_value = {
                        'current_page': state.current_page,
                        'page_size': state.page_size
                    }
                    state.current_page = value.get('current_page', state.current_page)
                    state.page_size = value.get('page_size', state.page_size)
            elif state_type == StateType.FILTER:
                old_value = state.filters
                state.filters = value
            elif state_type == StateType.SELECTION:
                old_value = state.selected_rows
                state.selected_rows = value
            elif state_type == StateType.VIEW:
                if isinstance(value, dict):
                    if 'column_widths' in value:
                        old_value = state.column_widths
                        state.column_widths = value['column_widths']
                    if 'scroll_position' in value:
                        state.scroll_position = value['scroll_position']
            
            # 更新时间戳
            state.last_updated = datetime.now().isoformat()
            
            # 记录变更
            self._record_change(
                table_name=table_name,
                state_type=state_type,
                old_value=old_value,
                new_value=value,
                source=source
            )
            
            # 通知监听器
            self._notify_listeners(state_type, table_name, value)
            
            # 自动持久化
            self._save_states()
            
            self.logger.debug(f"状态更新: {table_name}.{state_type.value} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")
            return False
    
    def get_sort_state(self, table_name: str) -> List[Dict[str, Any]]:
        """获取排序状态（兼容旧接口）"""
        state = self.get_table_state(table_name)
        return state.sort_columns
    
    def save_sort_state(self, table_name: str, sort_columns: List[Dict[str, Any]]) -> bool:
        """保存排序状态（兼容旧接口）"""
        return self.update_state(table_name, StateType.SORT, sort_columns, "sort_manager")
    
    def get_pagination_state(self, table_name: str) -> Dict[str, Any]:
        """获取分页状态（兼容旧接口）"""
        state = self.get_table_state(table_name)
        return {
            'current_page': state.current_page,
            'page_size': state.page_size
        }
    
    def save_pagination_state(self, table_name: str, page: int, page_size: int) -> bool:
        """保存分页状态（兼容旧接口）"""
        return self.update_state(
            table_name, 
            StateType.PAGINATION,
            {'current_page': page, 'page_size': page_size},
            "pagination_manager"
        )
    
    def clear_table_state(self, table_name: str):
        """清除表格状态"""
        if table_name in self._states:
            del self._states[table_name]
            self._save_states()
            self.logger.info(f"清除表格状态: {table_name}")
    
    def clear_all_states(self):
        """清除所有状态"""
        self._states.clear()
        self._global_state.clear()
        self._change_history.clear()
        self._save_states()
        self.logger.info("清除所有状态")
    
    def register_listener(self, state_type: StateType, callback: Callable):
        """
        注册状态变更监听器
        
        Args:
            state_type: 监听的状态类型
            callback: 回调函数
        """
        if callback not in self._listeners[state_type]:
            self._listeners[state_type].append(callback)
            self.logger.debug(f"注册监听器: {state_type.value}")
    
    def unregister_listener(self, state_type: StateType, callback: Callable):
        """
        取消注册监听器
        
        Args:
            state_type: 状态类型
            callback: 回调函数
        """
        if callback in self._listeners[state_type]:
            self._listeners[state_type].remove(callback)
            self.logger.debug(f"取消监听器: {state_type.value}")
    
    def get_change_history(self, table_name: Optional[str] = None,
                          state_type: Optional[StateType] = None,
                          limit: int = 10) -> List[StateChange]:
        """
        获取变更历史
        
        Args:
            table_name: 表格名称（可选）
            state_type: 状态类型（可选）
            limit: 返回数量限制
        
        Returns:
            变更历史列表
        """
        history = self._change_history
        
        if table_name:
            history = [h for h in history if h.table_name == table_name]
        
        if state_type:
            history = [h for h in history if h.state_type == state_type]
        
        return history[-limit:]
    
    def get_global_state(self, key: str, default: Any = None) -> Any:
        """获取全局状态"""
        return self._global_state.get(key, default)
    
    def set_global_state(self, key: str, value: Any):
        """设置全局状态"""
        old_value = self._global_state.get(key)
        self._global_state[key] = value
        
        # 记录变更
        self._record_change(
            table_name=None,
            state_type=StateType.CONFIG,
            old_value=old_value,
            new_value=value,
            source="global"
        )
        
        self._save_states()
    
    def _record_change(self, table_name: Optional[str], state_type: StateType,
                       old_value: Any, new_value: Any, source: str):
        """记录状态变更"""
        change = StateChange(
            timestamp=datetime.now().isoformat(),
            state_type=state_type,
            old_value=old_value,
            new_value=new_value,
            source=source,
            table_name=table_name
        )
        
        self._change_history.append(change)
        
        # 限制历史记录数量
        if len(self._change_history) > self._max_history:
            self._change_history = self._change_history[-self._max_history:]
    
    def _notify_listeners(self, state_type: StateType, table_name: str, value: Any):
        """通知监听器"""
        for callback in self._listeners[state_type]:
            try:
                callback(table_name, value)
            except Exception as e:
                self.logger.error(f"监听器回调失败: {e}")
    
    def _save_states(self):
        """持久化状态"""
        try:
            data = {
                'tables': {
                    name: state.to_dict()
                    for name, state in self._states.items()
                },
                'global': self._global_state,
                'last_saved': datetime.now().isoformat()
            }
            
            with open(self._state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
                
        except Exception as e:
            self.logger.error(f"保存状态失败: {e}")
    
    def _load_states(self):
        """加载持久化状态"""
        try:
            if self._state_file.exists():
                with open(self._state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 加载表格状态
                for name, state_data in data.get('tables', {}).items():
                    self._states[name] = TableState.from_dict(state_data)
                
                # 加载全局状态
                self._global_state = data.get('global', {})
                
                self.logger.info(f"加载状态: {len(self._states)}个表格")
                
        except Exception as e:
            self.logger.warning(f"加载状态失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取状态统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'table_count': len(self._states),
            'global_keys': len(self._global_state),
            'history_count': len(self._change_history),
            'listener_counts': {
                st.value: len(listeners)
                for st, listeners in self._listeners.items()
            },
            'memory_usage': self._estimate_memory_usage()
        }
    
    def _estimate_memory_usage(self) -> str:
        """估算内存使用"""
        import sys
        
        total_size = 0
        total_size += sys.getsizeof(self._states)
        total_size += sys.getsizeof(self._global_state)
        total_size += sys.getsizeof(self._change_history)
        
        # 转换为人类可读格式
        if total_size < 1024:
            return f"{total_size} B"
        elif total_size < 1024 * 1024:
            return f"{total_size / 1024:.2f} KB"
        else:
            return f"{total_size / (1024 * 1024):.2f} MB"


# 创建全局实例
_unified_state_manager = None

def get_unified_state_manager() -> UnifiedStateManagement:
    """获取统一状态管理器的全局实例"""
    global _unified_state_manager
    if _unified_state_manager is None:
        _unified_state_manager = UnifiedStateManagement()
    return _unified_state_manager


# 兼容性别名（便于迁移）
UnifiedStateManager = UnifiedStateManagement
EnhancedUnifiedStateManager = UnifiedStateManagement
TableSortStateManager = UnifiedStateManagement
PaginationStateManager = UnifiedStateManagement