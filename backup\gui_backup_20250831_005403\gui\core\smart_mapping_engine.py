"""
智能映射引擎
基于表类型、历史配置、字段名语义和数据内容进行智能映射推荐
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from src.modules.logging.setup_logger import setup_logger


class MappingConfidence(Enum):
    """映射置信度级别"""
    HIGH = "high"       # 高置信度 (>0.8)
    MEDIUM = "medium"   # 中等置信度 (0.5-0.8)
    LOW = "low"         # 低置信度 (<0.5)


@dataclass
class MappingResult:
    """映射结果"""
    source_field: str           # Excel源字段名
    target_field: str           # 目标数据库字段名
    display_name: str           # 用户友好的显示名称
    data_type: str             # 数据类型
    confidence: float          # 置信度 (0-1)
    confidence_level: MappingConfidence  # 置信度级别
    reasoning: str             # 推荐理由
    is_required: bool          # 是否必需


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool             # 是否有效
    errors: List[str]          # 错误列表
    warnings: List[str]        # 警告列表
    suggestions: List[str]     # 建议列表


class TemplateMatcher:
    """模板匹配器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        
        # 工资表标准字段模板
        self.salary_templates = {
            '姓名': ['姓名', 'name', '员工姓名', '人员姓名'],
            '工号': ['工号', '员工编号', '编号', 'id', 'employee_id'],
            '部门': ['部门', '科室', 'department', '所属部门'],
            '职位': ['职位', '岗位', 'position', 'job_title'],
            '基本工资': ['基本工资', '基础工资', 'basic_salary', '底薪'],
            '津贴': ['津贴', '补贴', 'allowance', '岗位津贴'],
            '奖金': ['奖金', 'bonus', '绩效奖金', '奖励'],
            '扣款': ['扣款', '扣除', 'deduction', '各项扣除'],
            '应发合计': ['应发合计', '应发总额', 'gross_pay', '税前合计'],
            '实发合计': ['实发合计', '实发总额', 'net_pay', '税后合计'],
        }
        
        # 异动表标准字段模板
        self.change_templates = {
            '姓名': ['姓名', 'name', '员工姓名', '人员姓名'],
            '工号': ['工号', '员工编号', '编号', 'id', 'employee_id'],
            '异动类型': ['异动类型', '变动类型', 'change_type', '操作类型'],
            '异动原因': ['异动原因', '变动原因', 'change_reason', '原因'],
            '异动日期': ['异动日期', '变动日期', 'change_date', '生效日期'],
            '原部门': ['原部门', '调出部门', 'from_department', '前部门'],
            '新部门': ['新部门', '调入部门', 'to_department', '现部门'],
            '原职位': ['原职位', '原岗位', 'from_position', '前职位'],
            '新职位': ['新职位', '新岗位', 'to_position', '现职位'],
        }
    
    def match_template(self, excel_headers: List[str], table_type: str) -> Dict[str, str]:
        """基于模板匹配字段"""
        template = self.salary_templates if table_type == "💰 工资表" else self.change_templates
        
        mappings = {}
        used_headers = set()
        
        for db_field, patterns in template.items():
            best_match = None
            best_score = 0
            
            for header in excel_headers:
                if header in used_headers:
                    continue
                    
                score = self._calculate_similarity(header, patterns)
                if score > best_score and score > 0.3:  # 最小匹配阈值
                    best_score = score
                    best_match = header
            
            if best_match:
                mappings[best_match] = db_field
                used_headers.add(best_match)
        
        return mappings
    
    def _calculate_similarity(self, header: str, patterns: List[str]) -> float:
        """计算字段相似度"""
        header_cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', header.lower())
        
        max_similarity = 0
        for pattern in patterns:
            pattern_cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', pattern.lower())
            
            # 完全匹配
            if header_cleaned == pattern_cleaned:
                return 1.0
            
            # 包含匹配
            if pattern_cleaned in header_cleaned or header_cleaned in pattern_cleaned:
                similarity = min(len(pattern_cleaned), len(header_cleaned)) / max(len(pattern_cleaned), len(header_cleaned))
                max_similarity = max(max_similarity, similarity * 0.8)
            
            # 模糊匹配（简单版本）
            common_chars = set(header_cleaned) & set(pattern_cleaned)
            if common_chars:
                similarity = len(common_chars) / max(len(header_cleaned), len(pattern_cleaned))
                max_similarity = max(max_similarity, similarity * 0.5)
        
        return max_similarity


class SemanticAnalyzer:
    """增强语义分析器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        
        # 扩展语义关键词库
        self.semantic_keywords = {
            'personal_info': {
                'primary': ['姓名', '工号', '身份证', '员工编号', 'ID'],
                'secondary': ['电话', '手机', '地址', '邮箱', '联系方式'],
                'patterns': [r'.*姓名.*', r'.*工号.*', r'.*编号.*', r'.*ID.*', r'.*号码.*']
            },
            'organization': {
                'primary': ['部门', '科室', '单位', '组织', '公司'],
                'secondary': ['分公司', '事业部', '中心', '处', '室'],
                'patterns': [r'.*部门.*', r'.*科室.*', r'.*单位.*', r'.*公司.*']
            },
            'position': {
                'primary': ['职位', '岗位', '职务', '级别', '职称'],
                'secondary': ['角色', '头衔', '等级', '序列', '岗位级别'],
                'patterns': [r'.*职位.*', r'.*岗位.*', r'.*职务.*', r'.*级别.*']
            },
            'money': {
                'primary': ['工资', '薪资', '薪酬', '收入', '报酬'],
                'basic': ['基本工资', '基础工资', '底薪', '基薪'],
                'bonus': ['奖金', '绩效', '提成', '分红', '激励'],
                'allowance': ['津贴', '补贴', '补助', '福利', '补偿'],
                'deduction': ['扣款', '扣除', '代扣', '税费', '保险'],
                'total': ['合计', '总额', '总计', '汇总', '应发', '实发'],
                'patterns': [r'.*工资.*', r'.*薪.*', r'.*金.*', r'.*费.*', r'.*补.*', r'.*扣.*']
            },
            'time': {
                'primary': ['日期', '时间', '年份', '月份'],
                'specific': ['入职时间', '离职时间', '生效日期', '截止日期'],
                'patterns': [r'.*日期.*', r'.*时间.*', r'\d{4}年.*', r'.*月.*']
            },
            'change': {
                'primary': ['异动', '变动', '调整', '变化'],
                'types': ['转岗', '升职', '降职', '调动', '离职', '入职'],
                'reasons': ['原因', '说明', '备注', '描述'],
                'patterns': [r'.*异动.*', r'.*变动.*', r'.*调.*', r'.*转.*']
            },
            'status': {
                'primary': ['状态', '标志', '标识', '类型'],
                'values': ['在职', '离职', '休假', '停薪', '试用'],
                'patterns': [r'.*状态.*', r'.*标.*', r'.*类型.*']
            }
        }
        
        # 字段权重配置
        self.field_weights = {
            'exact_match': 1.0,      # 完全匹配
            'primary_match': 0.9,    # 主要关键词匹配
            'secondary_match': 0.7,  # 次要关键词匹配  
            'pattern_match': 0.6,    # 正则模式匹配
            'partial_match': 0.4,    # 部分匹配
            'context_bonus': 0.2     # 上下文加分
        }
    
    def analyze_semantic_type(self, field_name: str) -> Tuple[str, float]:
        """增强语义类型分析，返回类型和置信度"""
        import re
        
        field_cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', field_name.lower())
        best_type = 'unknown'
        max_score = 0.0
        
        for semantic_type, type_data in self.semantic_keywords.items():
            score = 0.0
            
            # 检查主要关键词
            if 'primary' in type_data:
                for keyword in type_data['primary']:
                    if keyword.lower() in field_cleaned:
                        score = max(score, self.field_weights['primary_match'])
                        if field_cleaned == keyword.lower():
                            score = self.field_weights['exact_match']  # 完全匹配
            
            # 检查次要关键词
            if 'secondary' in type_data and score < self.field_weights['primary_match']:
                for keyword in type_data['secondary']:
                    if keyword.lower() in field_cleaned:
                        score = max(score, self.field_weights['secondary_match'])
            
            # 检查具体子类别
            for sub_category in ['basic', 'bonus', 'allowance', 'deduction', 'total', 'types', 'specific', 'values']:
                if sub_category in type_data and score < self.field_weights['primary_match']:
                    for keyword in type_data[sub_category]:
                        if keyword.lower() in field_cleaned:
                            score = max(score, self.field_weights['secondary_match'])
            
            # 检查正则模式
            if 'patterns' in type_data and score < self.field_weights['secondary_match']:
                for pattern in type_data['patterns']:
                    if re.search(pattern, field_name, re.IGNORECASE):
                        score = max(score, self.field_weights['pattern_match'])
            
            # 部分匹配检查
            if score == 0.0:
                if 'primary' in type_data:
                    for keyword in type_data['primary']:
                        # 检查是否有共同字符
                        common_chars = set(field_cleaned) & set(keyword.lower())
                        if len(common_chars) >= 2:  # 至少2个共同字符
                            similarity = len(common_chars) / max(len(field_cleaned), len(keyword))
                            if similarity > 0.3:
                                score = max(score, self.field_weights['partial_match'] * similarity)
            
            if score > max_score:
                max_score = score
                best_type = semantic_type
        
        return best_type, max_score
    
    def get_detailed_semantic_info(self, field_name: str) -> Dict[str, Any]:
        """获取详细的语义信息"""
        semantic_type, confidence = self.analyze_semantic_type(field_name)
        
        info = {
            'semantic_type': semantic_type,
            'confidence': confidence,
            'suggestions': [],
            'data_type_recommendation': 'VARCHAR(100)',
            'is_required_suggestion': False
        }
        
        # 基于语义类型提供具体建议
        if semantic_type == 'personal_info':
            info['data_type_recommendation'] = 'VARCHAR(100)'
            info['is_required_suggestion'] = '姓名' in field_name or '工号' in field_name
            info['suggestions'] = ['这是个人信息字段，建议设置为必填项' if info['is_required_suggestion'] else '这是个人信息字段']
            
        elif semantic_type == 'money':
            info['data_type_recommendation'] = 'DECIMAL(10,2)'
            info['is_required_suggestion'] = any(word in field_name for word in ['工资', '薪资', '合计', '总额'])
            if '合计' in field_name or '总额' in field_name:
                info['suggestions'] = ['这是金额汇总字段，建议设置为必填项且使用DECIMAL类型']
            else:
                info['suggestions'] = ['这是金额字段，建议使用DECIMAL类型保证精度']
                
        elif semantic_type == 'time':
            info['data_type_recommendation'] = 'DATE'
            info['is_required_suggestion'] = '日期' in field_name
            info['suggestions'] = ['这是时间字段，建议使用DATE类型']
            
        elif semantic_type == 'organization':
            info['data_type_recommendation'] = 'VARCHAR(100)'
            info['is_required_suggestion'] = '部门' in field_name
            info['suggestions'] = ['这是组织架构字段，可考虑关联组织架构表']
            
        elif semantic_type == 'position':
            info['data_type_recommendation'] = 'VARCHAR(100)'
            info['is_required_suggestion'] = False
            info['suggestions'] = ['这是职位字段，可考虑关联职位字典表']
            
        elif semantic_type == 'change':
            info['data_type_recommendation'] = 'VARCHAR(50)'
            info['is_required_suggestion'] = '异动类型' in field_name
            info['suggestions'] = ['这是异动相关字段，建议使用枚举值限制输入']
            
        return info
    
    def suggest_data_type(self, field_name: str, sample_values: List[Any] = None) -> str:
        """基于字段名和样本数据推荐数据类型"""
        semantic_type = self.analyze_semantic_type(field_name)
        
        # 基于语义类型的默认推荐
        type_mapping = {
            'personal_info': 'VARCHAR(100)',
            'organization': 'VARCHAR(100)',
            'position': 'VARCHAR(100)',
            'money': 'DECIMAL(10,2)',
            'time': 'DATE',
            'change': 'VARCHAR(50)',
        }
        
        # 如果有样本数据，进行更精确的推断
        if sample_values:
            return self._infer_from_samples(sample_values, type_mapping.get(semantic_type, 'VARCHAR(255)'))
        
        return type_mapping.get(semantic_type, 'VARCHAR(255)')
    
    def _infer_from_samples(self, samples: List[Any], default_type: str) -> str:
        """基于样本数据推断类型"""
        if not samples:
            return default_type
        
        # 简单的类型推断逻辑
        numeric_count = 0
        date_count = 0
        
        for sample in samples[:10]:  # 只检查前10个样本
            if sample is None:
                continue
            
            sample_str = str(sample).strip()
            if not sample_str:
                continue
            
            # 检查数字
            try:
                float(sample_str.replace(',', ''))
                numeric_count += 1
                continue
            except ValueError:
                pass
            
            # 检查日期
            date_patterns = [
                r'\d{4}-\d{1,2}-\d{1,2}',
                r'\d{4}/\d{1,2}/\d{1,2}',
                r'\d{1,2}-\d{1,2}-\d{4}',
            ]
            
            for pattern in date_patterns:
                if re.match(pattern, sample_str):
                    date_count += 1
                    break
        
        sample_count = len([s for s in samples if s is not None])
        if sample_count == 0:
            return default_type
        
        # 如果大部分是数字，推荐数字类型
        if numeric_count / sample_count > 0.7:
            return 'DECIMAL(10,2)' if '.' in str(samples[0]) else 'INT'
        
        # 如果大部分是日期，推荐日期类型
        if date_count / sample_count > 0.7:
            return 'DATE'
        
        return default_type


class HistoryAnalyzer:
    """增强历史配置分析器"""
    
    def __init__(self, history_file: str = "config/mapping_history.json"):
        self.logger = setup_logger(__name__)
        self.history_file = history_file
        self.history_cache = {}
        self.field_frequency = {}
        self.mapping_patterns = {}
        
        # 确保历史文件目录存在
        import os
        if os.path.dirname(history_file):  # 只有当目录不为空时才创建
            os.makedirs(os.path.dirname(history_file), exist_ok=True)
        
        # 加载历史数据
        self._load_history()
    
    def _load_history(self):
        """加载历史配置"""
        import json
        import os
        
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.history_cache = data.get('mappings', {})
                    self.field_frequency = data.get('frequency', {})
                    self.mapping_patterns = data.get('patterns', {})
                    
                self.logger.info(f"成功加载历史配置: {len(self.history_cache)} 个配置")
                
            except Exception as e:
                self.logger.error(f"加载历史配置失败: {e}")
                self._init_empty_history()
        else:
            self._init_empty_history()
    
    def _init_empty_history(self):
        """初始化空的历史配置"""
        self.history_cache = {}
        self.field_frequency = {}
        self.mapping_patterns = {}
    
    def _save_history(self):
        """保存历史配置到文件"""
        import json
        
        try:
            data = {
                'mappings': self.history_cache,
                'frequency': self.field_frequency,
                'patterns': self.mapping_patterns,
                'last_updated': self._get_current_timestamp()
            }
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            self.logger.info("历史配置保存成功")
            
        except Exception as e:
            self.logger.error(f"保存历史配置失败: {e}")
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_historical_mapping(self, table_type: str) -> Dict[str, str]:
        """获取历史映射配置"""
        return self.history_cache.get(table_type, {})
    
    def save_mapping_history(self, table_type: str, mapping: Dict[str, str]):
        """保存映射历史"""
        # 保存完整映射
        session_id = f"{table_type}_{self._get_current_timestamp()}"
        self.history_cache[session_id] = mapping
        
        # 更新字段频率统计
        for excel_field, db_field in mapping.items():
            field_key = f"{excel_field}->{db_field}"
            self.field_frequency[field_key] = self.field_frequency.get(field_key, 0) + 1
        
        # 更新映射模式
        self._update_mapping_patterns(table_type, mapping)
        
        # 持久化保存
        self._save_history()
        
        self.logger.info(f"保存映射历史: {table_type} - {len(mapping)} 个字段")
    
    def _update_mapping_patterns(self, table_type: str, mapping: Dict[str, str]):
        """更新映射模式"""
        if table_type not in self.mapping_patterns:
            self.mapping_patterns[table_type] = {}
        
        for excel_field, db_field in mapping.items():
            # 记录字段映射模式
            if excel_field not in self.mapping_patterns[table_type]:
                self.mapping_patterns[table_type][excel_field] = {}
            
            target_fields = self.mapping_patterns[table_type][excel_field]
            target_fields[db_field] = target_fields.get(db_field, 0) + 1
    
    def get_frequency_based_suggestions(self, excel_headers: List[str], table_type: str = None) -> Dict[str, Tuple[str, float]]:
        """基于历史频率的建议"""
        suggestions = {}
        
        for excel_field in excel_headers:
            best_target = None
            max_frequency = 0
            
            # 1. 直接查找字段频率
            for field_mapping, frequency in self.field_frequency.items():
                if field_mapping.startswith(f"{excel_field}->"):
                    target_field = field_mapping.split('->', 1)[1]
                    if frequency > max_frequency:
                        max_frequency = frequency
                        best_target = target_field
            
            # 2. 查找模式匹配
            if table_type and table_type in self.mapping_patterns:
                if excel_field in self.mapping_patterns[table_type]:
                    pattern_targets = self.mapping_patterns[table_type][excel_field]
                    pattern_best = max(pattern_targets.items(), key=lambda x: x[1], default=(None, 0))
                    if pattern_best[1] > max_frequency:
                        best_target = pattern_best[0]
                        max_frequency = pattern_best[1]
            
            # 3. 模糊匹配
            if not best_target:
                best_target, max_frequency = self._fuzzy_match_history(excel_field)
            
            if best_target and max_frequency > 0:
                # 计算置信度（基于频次）
                total_mappings = sum(self.field_frequency.values()) or 1
                confidence = min(0.9, max_frequency / total_mappings * 10)  # 最高0.9
                suggestions[excel_field] = (best_target, confidence)
        
        return suggestions
    
    def _fuzzy_match_history(self, excel_field: str) -> Tuple[str, int]:
        """模糊匹配历史字段"""
        best_target = None
        max_frequency = 0
        
        excel_cleaned = excel_field.lower().replace(' ', '')
        
        for field_mapping, frequency in self.field_frequency.items():
            historical_excel = field_mapping.split('->', 1)[0].lower().replace(' ', '')
            
            # 计算相似度
            similarity = self._calculate_field_similarity(excel_cleaned, historical_excel)
            
            if similarity > 0.7:  # 相似度阈值
                target_field = field_mapping.split('->', 1)[1]
                adjusted_frequency = int(frequency * similarity)
                
                if adjusted_frequency > max_frequency:
                    max_frequency = adjusted_frequency
                    best_target = target_field
        
        return best_target or "", max_frequency
    
    def _calculate_field_similarity(self, field1: str, field2: str) -> float:
        """计算字段相似度"""
        if field1 == field2:
            return 1.0
        
        # 包含关系
        if field1 in field2 or field2 in field1:
            return 0.8
        
        # 共同字符比例
        set1, set2 = set(field1), set(field2)
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """获取映射统计信息"""
        total_mappings = len(self.field_frequency)
        most_common = sorted(self.field_frequency.items(), key=lambda x: x[1], reverse=True)[:10]
        
        table_stats = {}
        for table_type, patterns in self.mapping_patterns.items():
            table_stats[table_type] = {
                'field_count': len(patterns),
                'most_mapped_fields': list(patterns.keys())[:5]
            }
        
        return {
            'total_mappings': total_mappings,
            'most_common_mappings': most_common,
            'table_statistics': table_stats,
            'history_sessions': len(self.history_cache)
        }
    
    def cleanup_old_history(self, keep_sessions: int = 50):
        """清理旧的历史记录"""
        if len(self.history_cache) > keep_sessions:
            # 按时间戳排序，保留最新的记录
            sorted_sessions = sorted(self.history_cache.items(), 
                                   key=lambda x: x[0].split('_')[-1], reverse=True)
            
            # 保留最新的记录
            self.history_cache = dict(sorted_sessions[:keep_sessions])
            
            # 重新计算字段频率
            self._recalculate_frequency()
            
            # 保存清理后的数据
            self._save_history()
            
            self.logger.info(f"清理历史记录，保留最新 {keep_sessions} 个会话")
    
    def _recalculate_frequency(self):
        """重新计算字段频率"""
        self.field_frequency = {}
        self.mapping_patterns = {}
        
        for session_mapping in self.history_cache.values():
            for excel_field, db_field in session_mapping.items():
                field_key = f"{excel_field}->{db_field}"
                self.field_frequency[field_key] = self.field_frequency.get(field_key, 0) + 1


class SmartMappingEngine:
    """智能映射引擎"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        
        # 初始化子组件
        self.template_matcher = TemplateMatcher()
        self.semantic_analyzer = SemanticAnalyzer()
        self.history_analyzer = HistoryAnalyzer()
        
        self.logger.info("智能映射引擎初始化完成")
    
    def generate_smart_mapping(self, excel_headers: List[str], table_type: str, 
                             sample_data: Dict[str, List[Any]] = None) -> List[MappingResult]:
        """增强智能映射生成"""
        try:
            self.logger.info(f"开始生成智能映射: 类型={table_type}, 字段数={len(excel_headers)}")
            
            results = []
            
            # 1. 基于模板匹配
            template_mappings = self.template_matcher.match_template(excel_headers, table_type)
            
            # 2. 基于历史频率建议 (增强)
            historical_suggestions = self.history_analyzer.get_frequency_based_suggestions(excel_headers, table_type)
            
            # 3. 基于语义分析 (增强)
            semantic_suggestions = {}
            for header in excel_headers:
                semantic_info = self.semantic_analyzer.get_detailed_semantic_info(header)
                semantic_suggestions[header] = semantic_info
            
            # 为每个Excel字段生成映射建议
            for header in excel_headers:
                mapping_result = self._generate_enhanced_field_mapping(
                    header, template_mappings, historical_suggestions, 
                    semantic_suggestions, sample_data, table_type
                )
                results.append(mapping_result)
            
            # 按置信度排序
            results.sort(key=lambda x: x.confidence, reverse=True)
            
            high_confidence_count = len([r for r in results if r.confidence > 0.8])
            medium_confidence_count = len([r for r in results if 0.5 <= r.confidence <= 0.8])
            
            self.logger.info(f"智能映射生成完成: 高置信度 {high_confidence_count} 个, 中等置信度 {medium_confidence_count} 个")
            return results
            
        except Exception as e:
            self.logger.error(f"生成智能映射失败: {e}")
            raise
    
    def _generate_enhanced_field_mapping(self, header: str, template_mappings: Dict[str, str],
                                       historical_suggestions: Dict[str, Tuple[str, float]], 
                                       semantic_suggestions: Dict[str, Dict],
                                       sample_data: Dict[str, List[Any]] = None,
                                       table_type: str = None) -> MappingResult:
        """增强字段映射生成"""
        
        confidence = 0.0
        target_field = header  # 默认使用原字段名
        reasoning_parts = []
        
        # 优先级1: 历史配置匹配 (最高优先级)
        if header in historical_suggestions:
            hist_target, hist_confidence = historical_suggestions[header]
            if hist_confidence > confidence:
                target_field = hist_target
                confidence = hist_confidence
                reasoning_parts.append(f"历史频率匹配(置信度:{hist_confidence:.1%})")
        
        # 优先级2: 模板匹配
        if header in template_mappings:
            template_confidence = 0.85
            if template_confidence > confidence:
                target_field = template_mappings[header]
                confidence = template_confidence
                reasoning_parts.append("模板精确匹配")
        
        # 优先级3: 语义分析
        semantic_info = semantic_suggestions.get(header, {})
        semantic_confidence = semantic_info.get('confidence', 0.0)
        if semantic_confidence > 0.6 and semantic_confidence > confidence * 0.8:
            # 如果语义分析置信度较高，可以作为补充
            if not reasoning_parts:  # 如果还没有其他匹配
                target_field = semantic_info.get('semantic_type', header)
                confidence = semantic_confidence * 0.7  # 语义分析稍微降权
                reasoning_parts.append(f"语义分析({semantic_info.get('semantic_type', 'unknown')})")
            else:
                # 作为置信度加成
                confidence = min(0.95, confidence + semantic_confidence * 0.1)
                reasoning_parts.append(f"语义验证({semantic_info.get('semantic_type', 'unknown')})")
        
        # 样本数据验证
        sample_values = sample_data.get(header, []) if sample_data else []
        data_type = semantic_info.get('data_type_recommendation', 'VARCHAR(100)')
        if sample_values:
            # 基于样本数据调整数据类型
            inferred_type = self.semantic_analyzer.suggest_data_type(header, sample_values)
            if inferred_type != data_type:
                data_type = inferred_type
                reasoning_parts.append("基于样本数据调整类型")
        
        # 必需字段判断 (综合考虑)
        is_required = semantic_info.get('is_required_suggestion', False)
        
        # 基于表类型的特殊规则
        if table_type:
            if table_type == "💰 工资表":
                required_keywords = ['姓名', '工号', '应发', '实发', '合计']
                is_required = any(keyword in header for keyword in required_keywords)
            elif table_type == "🔄 异动表":
                required_keywords = ['姓名', '工号', '异动类型', '异动日期']
                is_required = any(keyword in header for keyword in required_keywords)
        
        # 如果没有任何匹配，使用字段名自身
        if confidence == 0.0:
            confidence = 0.3  # 基础置信度
            reasoning_parts.append("使用原字段名")
        
        # 生成推理说明
        reasoning = " + ".join(reasoning_parts) if reasoning_parts else "默认映射"
        
        # 确定置信度级别
        if confidence >= 0.8:
            confidence_level = MappingConfidence.HIGH
        elif confidence >= 0.5:
            confidence_level = MappingConfidence.MEDIUM
        else:
            confidence_level = MappingConfidence.LOW
        
        return MappingResult(
            source_field=header,
            target_field=target_field,
            display_name=header,
            data_type=data_type,
            confidence=confidence,
            confidence_level=confidence_level,
            reasoning=reasoning,
            is_required=is_required
        )
    
    def save_user_mapping(self, table_type: str, mapping_config: Dict[str, Dict]):
        """保存用户的映射配置用于学习"""
        try:
            # 转换格式
            simple_mapping = {}
            for excel_field, config in mapping_config.items():
                target_field = config.get('target_field', excel_field)
                simple_mapping[excel_field] = target_field
            
            # 保存到历史分析器
            self.history_analyzer.save_mapping_history(table_type, simple_mapping)
            
            self.logger.info(f"用户映射配置已保存用于学习: {table_type} - {len(simple_mapping)} 个字段")
            
        except Exception as e:
            self.logger.error(f"保存用户映射配置失败: {e}")
    
    def get_mapping_insights(self) -> Dict[str, Any]:
        """获取映射洞察信息"""
        try:
            history_stats = self.history_analyzer.get_mapping_statistics()
            
            insights = {
                'total_learned_mappings': history_stats.get('total_mappings', 0),
                'most_common_mappings': history_stats.get('most_common_mappings', [])[:5],
                'table_statistics': history_stats.get('table_statistics', {}),
                'learning_sessions': history_stats.get('history_sessions', 0),
                'recommendation_accuracy': self._estimate_accuracy(),
                'suggestions': self._generate_insights_suggestions(history_stats)
            }
            
            return insights
            
        except Exception as e:
            self.logger.error(f"获取映射洞察失败: {e}")
            return {}
    
    def _estimate_accuracy(self) -> float:
        """估算推荐准确率"""
        # 基于历史数据量和模板覆盖度估算
        total_mappings = len(self.history_analyzer.field_frequency)
        if total_mappings == 0:
            return 0.6  # 默认准确率
        elif total_mappings < 10:
            return 0.7  # 学习初期
        elif total_mappings < 50:
            return 0.8  # 学习中期  
        else:
            return 0.9  # 学习后期
    
    def _generate_insights_suggestions(self, history_stats: Dict) -> List[str]:
        """生成洞察建议"""
        suggestions = []
        
        total_mappings = history_stats.get('total_mappings', 0)
        if total_mappings < 10:
            suggestions.append("建议多使用智能映射功能以提升推荐准确度")
        
        most_common = history_stats.get('most_common_mappings', [])
        if most_common:
            top_mapping = most_common[0]
            suggestions.append(f"最常用的映射是: {top_mapping[0].replace('->', ' → ')}")
        
        table_stats = history_stats.get('table_statistics', {})
        if len(table_stats) > 1:
            suggestions.append("系统已学习多种表类型的映射模式")
        
        return suggestions
    
    def _generate_field_mapping(self, header: str, template_mappings: Dict[str, str],
                               historical_mappings: Dict[str, str], 
                               sample_data: Dict[str, List[Any]] = None) -> MappingResult:
        """为单个字段生成映射"""
        
        confidence = 0.0
        target_field = header  # 默认使用原字段名
        reasoning = "无匹配规则"
        
        # 检查模板匹配
        if header in template_mappings:
            target_field = template_mappings[header]
            confidence = 0.8
            reasoning = "基于模板匹配"
        
        # 检查历史匹配（优先级更高）
        elif header in historical_mappings:
            target_field = historical_mappings[header]
            confidence = 0.9
            reasoning = "基于历史配置"
        
        # 语义分析
        else:
            semantic_type = self.semantic_analyzer.analyze_semantic_type(header)
            if semantic_type != 'unknown':
                confidence = 0.4
                reasoning = f"基于语义分析({semantic_type})"
        
        # 推荐数据类型
        sample_values = sample_data.get(header, []) if sample_data else []
        data_type = self.semantic_analyzer.suggest_data_type(header, sample_values)
        
        # 确定置信度级别
        if confidence >= 0.8:
            confidence_level = MappingConfidence.HIGH
        elif confidence >= 0.5:
            confidence_level = MappingConfidence.MEDIUM
        else:
            confidence_level = MappingConfidence.LOW
        
        # 判断是否必需（基于常见的必需字段）
        required_fields = ['姓名', '工号', 'name', 'id', 'employee_id']
        is_required = any(req in header.lower() for req in required_fields)
        
        return MappingResult(
            source_field=header,
            target_field=target_field,
            display_name=header,  # 默认使用原名称作为显示名
            data_type=data_type,
            confidence=confidence,
            confidence_level=confidence_level,
            reasoning=reasoning,
            is_required=is_required
        )
    
    def validate_mapping(self, mappings: Dict[str, str], table_type: str) -> ValidationResult:
        """验证映射配置"""
        try:
            errors = []
            warnings = []
            suggestions = []
            
            # 检查必需字段
            required_fields = ['姓名', '工号'] if table_type == "💰 工资表" else ['姓名', '工号', '异动类型']
            
            for required in required_fields:
                if required not in mappings.values():
                    errors.append(f"缺少必需字段: {required}")
            
            # 检查重复映射
            target_fields = list(mappings.values())
            duplicates = [field for field in set(target_fields) if target_fields.count(field) > 1]
            if duplicates:
                errors.append(f"存在重复映射的字段: {', '.join(duplicates)}")
            
            # 检查字段名规范
            for source, target in mappings.items():
                if not target.strip():
                    warnings.append(f"字段 '{source}' 的目标字段名为空")
                
                # 建议使用标准字段名
                if target.lower() in ['name', 'id']:
                    suggestions.append(f"建议将 '{target}' 改为更具体的名称，如 'employee_name' 或 'employee_id'")
            
            is_valid = len(errors) == 0
            
            self.logger.info(f"映射验证完成: 有效={is_valid}, 错误={len(errors)}, 警告={len(warnings)}")
            
            return ValidationResult(
                is_valid=is_valid,
                errors=errors,
                warnings=warnings,
                suggestions=suggestions
            )
            
        except Exception as e:
            self.logger.error(f"映射验证失败: {e}")
            return ValidationResult(
                is_valid=False,
                errors=[f"验证过程出错: {e}"],
                warnings=[],
                suggestions=[]
            )
    
    def optimize_mapping(self, mappings: Dict[str, str], table_type: str) -> Dict[str, str]:
        """优化映射配置"""
        optimized = mappings.copy()
        
        # 应用标准化规则
        standardization_rules = {
            '员工姓名': '姓名',
            '人员姓名': '姓名',
            '员工编号': '工号',
            '人员编号': '工号',
        }
        
        for source, target in list(optimized.items()):
            if target in standardization_rules:
                optimized[source] = standardization_rules[target]
        
        self.logger.info("映射配置优化完成")
        return optimized
