#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置冲突检测分析器

负责检测、分析和解决配置系统中的冲突，包括：
- 配置冲突检测
- 冲突影响分析
- 冲突解决建议
- 自动冲突解决

创建时间: 2025-01-20
方案: 统一配置界面详细设计（方案3）
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.utils.log_config import setup_logger
from src.gui.unified_config_manager import (
    ConfigurationSource, ConfigurationItem, ConfigurationPriority,
    ConflictReport, FieldMapping, SheetConfiguration
)


class ConflictType(Enum):
    """冲突类型枚举"""
    VALUE_CONFLICT = "VALUE_CONFLICT"           # 值冲突
    TYPE_CONFLICT = "TYPE_CONFLICT"             # 类型冲突
    SCOPE_CONFLICT = "SCOPE_CONFLICT"           # 作用域冲突
    TIMING_CONFLICT = "TIMING_CONFLICT"         # 应用时机冲突
    DEPENDENCY_CONFLICT = "DEPENDENCY_CONFLICT" # 依赖冲突


class ConflictSeverity(Enum):
    """冲突严重程度"""
    LOW = "LOW"           # 低：不影响功能，仅影响显示
    MEDIUM = "MEDIUM"     # 中：可能影响部分功能
    HIGH = "HIGH"         # 高：严重影响功能
    CRITICAL = "CRITICAL" # 严重：可能导致系统错误


@dataclass
class ConflictAnalysis:
    """冲突分析结果"""
    conflict_type: ConflictType
    severity: ConflictSeverity
    field_key: str
    conflicting_configs: List[ConfigurationItem]
    winning_config: ConfigurationItem
    impact_description: str
    resolution_suggestions: List[str]
    auto_resolvable: bool = False
    resolution_confidence: float = 0.0  # 自动解决的置信度 (0-1)


@dataclass
class ResolutionStrategy:
    """冲突解决策略"""
    strategy_id: str
    name: str
    description: str
    priority: int
    applicable_conflict_types: List[ConflictType]
    
    def is_applicable(self, conflict_type: ConflictType) -> bool:
        """检查策略是否适用于指定冲突类型"""
        return conflict_type in self.applicable_conflict_types


class ConflictAnalyzer:
    """冲突检测分析器"""
    
    def __init__(self):
        """初始化冲突分析器"""
        self.logger = setup_logger(__name__)
        
        # 冲突分析历史
        self.analysis_history: List[ConflictAnalysis] = []
        
        # 解决策略
        self.resolution_strategies = self._init_resolution_strategies()
        
        # 字段类型兼容性映射
        self.type_compatibility = self._init_type_compatibility()
        
        # 冲突模式缓存
        self._conflict_patterns_cache = {}
        
        self.logger.info("🔍 冲突分析器初始化完成")
    
    def analyze_conflicts(self, configurations: Dict[str, List[ConfigurationItem]]) -> List[ConflictAnalysis]:
        """分析配置冲突
        
        Args:
            configurations: 配置字典，key为配置键，value为配置项列表
            
        Returns:
            冲突分析结果列表
        """
        analyses = []
        
        for field_key, config_items in configurations.items():
            if len(config_items) <= 1:
                continue
            
            # 检测各种类型的冲突
            analysis = self._analyze_field_conflicts(field_key, config_items)
            if analysis:
                analyses.append(analysis)
        
        # 保存分析历史
        self.analysis_history.extend(analyses)
        
        self.logger.info(f"🔍 分析完成，发现 {len(analyses)} 个冲突")
        return analyses
    
    def _analyze_field_conflicts(self, field_key: str, config_items: List[ConfigurationItem]) -> Optional[ConflictAnalysis]:
        """分析单个字段的冲突"""
        if len(config_items) <= 1:
            return None
        
        # 检测冲突类型
        conflict_type = self._detect_conflict_type(config_items)
        
        # 评估冲突严重程度
        severity = self._assess_conflict_severity(field_key, config_items, conflict_type)
        
        # 确定获胜配置
        winning_config = self._determine_winning_config(config_items)
        
        # 生成影响描述
        impact_description = self._generate_impact_description(field_key, config_items, conflict_type)
        
        # 生成解决建议
        resolution_suggestions = self._generate_resolution_suggestions(field_key, config_items, conflict_type)
        
        # 评估自动解决可能性
        auto_resolvable, confidence = self._assess_auto_resolution(config_items, conflict_type)
        
        return ConflictAnalysis(
            conflict_type=conflict_type,
            severity=severity,
            field_key=field_key,
            conflicting_configs=config_items,
            winning_config=winning_config,
            impact_description=impact_description,
            resolution_suggestions=resolution_suggestions,
            auto_resolvable=auto_resolvable,
            resolution_confidence=confidence
        )
    
    def _detect_conflict_type(self, config_items: List[ConfigurationItem]) -> ConflictType:
        """检测冲突类型"""
        # 检查值冲突
        values = [item.value for item in config_items]
        unique_values = set()
        for value in values:
            if isinstance(value, dict):
                unique_values.add(json.dumps(value, sort_keys=True))
            else:
                unique_values.add(str(value))
        
        if len(unique_values) > 1:
            # 进一步分析冲突类型
            if self._is_type_conflict(config_items):
                return ConflictType.TYPE_CONFLICT
            elif self._is_scope_conflict(config_items):
                return ConflictType.SCOPE_CONFLICT
            elif self._is_timing_conflict(config_items):
                return ConflictType.TIMING_CONFLICT
            else:
                return ConflictType.VALUE_CONFLICT
        
        return ConflictType.VALUE_CONFLICT
    
    def _is_type_conflict(self, config_items: List[ConfigurationItem]) -> bool:
        """检测是否为类型冲突"""
        # 检查配置值的数据类型是否不同
        types = set(type(item.value).__name__ for item in config_items)
        return len(types) > 1
    
    def _is_scope_conflict(self, config_items: List[ConfigurationItem]) -> bool:
        """检测是否为作用域冲突"""
        # 检查元数据中的作用域信息
        scopes = set()
        for item in config_items:
            scope = item.metadata.get('scope', 'default')
            scopes.add(scope)
        return len(scopes) > 1
    
    def _is_timing_conflict(self, config_items: List[ConfigurationItem]) -> bool:
        """检测是否为应用时机冲突"""
        # 检查元数据中的应用时机信息
        timings = set()
        for item in config_items:
            timing = item.metadata.get('apply_timing', 'default')
            timings.add(timing)
        return len(timings) > 1
    
    def _assess_conflict_severity(self, field_key: str, config_items: List[ConfigurationItem], 
                                conflict_type: ConflictType) -> ConflictSeverity:
        """评估冲突严重程度"""
        # 基于字段重要性评估
        critical_fields = ['employee_id', 'salary_base', 'tax_amount']
        important_fields = ['name', 'department', 'position']
        
        if field_key in critical_fields:
            base_severity = ConflictSeverity.HIGH
        elif field_key in important_fields:
            base_severity = ConflictSeverity.MEDIUM
        else:
            base_severity = ConflictSeverity.LOW
        
        # 基于冲突类型调整严重程度
        if conflict_type == ConflictType.TYPE_CONFLICT:
            # 类型冲突通常比值冲突更严重
            if base_severity == ConflictSeverity.LOW:
                return ConflictSeverity.MEDIUM
            elif base_severity == ConflictSeverity.MEDIUM:
                return ConflictSeverity.HIGH
            else:
                return ConflictSeverity.CRITICAL
        
        return base_severity
    
    def _determine_winning_config(self, config_items: List[ConfigurationItem]) -> ConfigurationItem:
        """确定获胜的配置（基于优先级）"""
        return min(config_items, key=lambda item: ConfigurationPriority.get_priority(item.source))
    
    def _generate_impact_description(self, field_key: str, config_items: List[ConfigurationItem], 
                                   conflict_type: ConflictType) -> str:
        """生成冲突影响描述"""
        sources = [item.source.value for item in config_items]
        values = [str(item.value) for item in config_items]
        
        descriptions = {
            ConflictType.VALUE_CONFLICT: f"字段 '{field_key}' 在不同配置源中有不同的值：{', '.join(values)}",
            ConflictType.TYPE_CONFLICT: f"字段 '{field_key}' 在不同配置源中有不同的数据类型",
            ConflictType.SCOPE_CONFLICT: f"字段 '{field_key}' 在不同配置源中有不同的作用域",
            ConflictType.TIMING_CONFLICT: f"字段 '{field_key}' 在不同配置源中有不同的应用时机",
            ConflictType.DEPENDENCY_CONFLICT: f"字段 '{field_key}' 的依赖关系在不同配置源中存在冲突"
        }
        
        base_description = descriptions.get(conflict_type, f"字段 '{field_key}' 存在未知类型的冲突")
        
        # 添加来源信息
        sources_desc = f"涉及的配置源：{', '.join(sources)}"
        
        return f"{base_description}。{sources_desc}"
    
    def _generate_resolution_suggestions(self, field_key: str, config_items: List[ConfigurationItem], 
                                       conflict_type: ConflictType) -> List[str]:
        """生成解决建议"""
        suggestions = []
        
        # 基础建议
        winning_config = self._determine_winning_config(config_items)
        suggestions.append(f"采用优先级最高的配置：{winning_config.value} (来源: {winning_config.source.value})")
        
        # 基于冲突类型的具体建议
        if conflict_type == ConflictType.VALUE_CONFLICT:
            suggestions.extend([
                "检查配置值是否正确，可能需要人工确认",
                "考虑创建新的用户配置覆盖默认设置",
                "如果是临时需求，可使用临时覆盖功能"
            ])
        
        elif conflict_type == ConflictType.TYPE_CONFLICT:
            suggestions.extend([
                "统一字段的数据类型定义",
                "检查是否需要类型转换",
                "考虑修改字段类型配置"
            ])
        
        elif conflict_type == ConflictType.SCOPE_CONFLICT:
            suggestions.extend([
                "明确字段的作用域范围",
                "考虑为不同作用域创建独立的配置",
                "检查作用域配置是否正确"
            ])
        
        elif conflict_type == ConflictType.TIMING_CONFLICT:
            suggestions.extend([
                "统一配置的应用时机",
                "检查是否需要分阶段应用配置",
                "考虑调整配置的生效时机"
            ])
        
        # 查找适用的解决策略
        applicable_strategies = [
            strategy for strategy in self.resolution_strategies.values()
            if strategy.is_applicable(conflict_type)
        ]
        
        for strategy in sorted(applicable_strategies, key=lambda s: s.priority):
            suggestions.append(f"策略建议：{strategy.description}")
        
        return suggestions
    
    def _assess_auto_resolution(self, config_items: List[ConfigurationItem], 
                              conflict_type: ConflictType) -> Tuple[bool, float]:
        """评估自动解决可能性
        
        Returns:
            (是否可自动解决, 置信度)
        """
        # 简单的优先级冲突通常可以自动解决
        if conflict_type == ConflictType.VALUE_CONFLICT:
            # 检查是否有明确的优先级差异
            priorities = [ConfigurationPriority.get_priority(item.source) for item in config_items]
            if len(set(priorities)) == len(priorities):  # 所有优先级都不同
                return True, 0.9
        
        # 类型冲突需要更谨慎的处理
        if conflict_type == ConflictType.TYPE_CONFLICT:
            return False, 0.0
        
        # 其他冲突类型的自动解决置信度较低
        return True, 0.5
    
    def auto_resolve_conflicts(self, analyses: List[ConflictAnalysis]) -> Dict[str, ConfigurationItem]:
        """自动解决冲突
        
        Args:
            analyses: 冲突分析结果列表
            
        Returns:
            解决后的配置字典
        """
        resolved_configs = {}
        
        for analysis in analyses:
            if analysis.auto_resolvable and analysis.resolution_confidence >= 0.7:
                # 自动采用获胜配置
                resolved_configs[analysis.field_key] = analysis.winning_config
                self.logger.info(f"✅ 自动解决冲突: {analysis.field_key} -> {analysis.winning_config.value}")
            else:
                # 记录无法自动解决的冲突
                self.logger.warning(f"⚠️ 无法自动解决冲突: {analysis.field_key}")
        
        return resolved_configs
    
    def generate_conflict_report(self, analyses: List[ConflictAnalysis]) -> str:
        """生成冲突报告
        
        Args:
            analyses: 冲突分析结果列表
            
        Returns:
            格式化的冲突报告
        """
        if not analyses:
            return "✅ 未检测到配置冲突"
        
        report_lines = [
            "📊 配置冲突分析报告",
            "=" * 50,
            f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"冲突总数: {len(analyses)}",
            ""
        ]
        
        # 按严重程度分组
        by_severity = {}
        for analysis in analyses:
            severity = analysis.severity
            if severity not in by_severity:
                by_severity[severity] = []
            by_severity[severity].append(analysis)
        
        # 生成分组报告
        severity_order = [ConflictSeverity.CRITICAL, ConflictSeverity.HIGH, 
                         ConflictSeverity.MEDIUM, ConflictSeverity.LOW]
        
        for severity in severity_order:
            if severity not in by_severity:
                continue
            
            severity_analyses = by_severity[severity]
            severity_icon = {
                ConflictSeverity.CRITICAL: "🚨",
                ConflictSeverity.HIGH: "⚠️",
                ConflictSeverity.MEDIUM: "⚡",
                ConflictSeverity.LOW: "ℹ️"
            }[severity]
            
            report_lines.append(f"{severity_icon} {severity.value} 级别冲突 ({len(severity_analyses)} 个)")
            report_lines.append("-" * 30)
            
            for i, analysis in enumerate(severity_analyses, 1):
                report_lines.append(f"{i}. 字段: {analysis.field_key}")
                report_lines.append(f"   类型: {analysis.conflict_type.value}")
                report_lines.append(f"   影响: {analysis.impact_description}")
                report_lines.append(f"   获胜配置: {analysis.winning_config.value} ({analysis.winning_config.source.value})")
                
                if analysis.auto_resolvable:
                    report_lines.append(f"   🤖 可自动解决 (置信度: {analysis.resolution_confidence:.1%})")
                else:
                    report_lines.append(f"   👤 需要人工处理")
                
                report_lines.append(f"   💡 建议: {analysis.resolution_suggestions[0] if analysis.resolution_suggestions else '无'}")
                report_lines.append("")
        
        # 总结统计
        auto_resolvable_count = sum(1 for a in analyses if a.auto_resolvable)
        report_lines.extend([
            "📈 统计摘要",
            "-" * 20,
            f"可自动解决: {auto_resolvable_count}/{len(analyses)} ({auto_resolvable_count/len(analyses):.1%})",
            f"需要人工处理: {len(analyses) - auto_resolvable_count}/{len(analyses)}",
        ])
        
        return "\n".join(report_lines)
    
    def get_conflict_statistics(self) -> Dict[str, Any]:
        """获取冲突统计信息"""
        if not self.analysis_history:
            return {"total_conflicts": 0}
        
        # 统计各种冲突类型
        type_counts = {}
        severity_counts = {}
        auto_resolvable_count = 0
        
        for analysis in self.analysis_history:
            # 冲突类型统计
            conflict_type = analysis.conflict_type.value
            type_counts[conflict_type] = type_counts.get(conflict_type, 0) + 1
            
            # 严重程度统计
            severity = analysis.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # 自动解决统计
            if analysis.auto_resolvable:
                auto_resolvable_count += 1
        
        return {
            "total_conflicts": len(self.analysis_history),
            "by_type": type_counts,
            "by_severity": severity_counts,
            "auto_resolvable": auto_resolvable_count,
            "auto_resolvable_rate": auto_resolvable_count / len(self.analysis_history)
        }
    
    def _init_resolution_strategies(self) -> Dict[str, ResolutionStrategy]:
        """初始化解决策略"""
        strategies = {
            "priority_based": ResolutionStrategy(
                strategy_id="priority_based",
                name="基于优先级解决",
                description="根据配置来源的优先级自动选择最高优先级的配置",
                priority=1,
                applicable_conflict_types=[
                    ConflictType.VALUE_CONFLICT,
                    ConflictType.SCOPE_CONFLICT
                ]
            ),
            "type_conversion": ResolutionStrategy(
                strategy_id="type_conversion",
                name="类型转换解决",
                description="尝试将冲突的配置值转换为兼容的类型",
                priority=2,
                applicable_conflict_types=[ConflictType.TYPE_CONFLICT]
            ),
            "merge_configs": ResolutionStrategy(
                strategy_id="merge_configs",
                name="配置合并解决",
                description="将多个配置合并为一个综合配置",
                priority=3,
                applicable_conflict_types=[
                    ConflictType.VALUE_CONFLICT,
                    ConflictType.DEPENDENCY_CONFLICT
                ]
            ),
            "temporal_resolution": ResolutionStrategy(
                strategy_id="temporal_resolution",
                name="时间顺序解决",
                description="根据配置的时间戳选择最新的配置",
                priority=4,
                applicable_conflict_types=[ConflictType.TIMING_CONFLICT]
            )
        }
        
        return strategies
    
    def _init_type_compatibility(self) -> Dict[str, List[str]]:
        """初始化类型兼容性映射"""
        return {
            "string": ["str", "unicode", "text"],
            "integer": ["int", "long", "number"],
            "float": ["float", "double", "decimal", "number"],
            "boolean": ["bool", "checkbox", "flag"],
            "date": ["datetime", "timestamp", "date"],
            "currency": ["money", "decimal", "float"]
        }


if __name__ == "__main__":
    """测试冲突分析器"""
    import logging
    logging.basicConfig(level=logging.DEBUG)
    
    # 创建测试配置项
    config1 = ConfigurationItem(
        key="employee_id",
        value="员工编号",
        source=ConfigurationSource.SYSTEM_DEFAULT,
        timestamp=datetime.now(),
        description="系统默认配置"
    )
    
    config2 = ConfigurationItem(
        key="employee_id", 
        value="人员编号",
        source=ConfigurationSource.USER_CONFIG,
        timestamp=datetime.now(),
        description="用户自定义配置"
    )
    
    config3 = ConfigurationItem(
        key="salary_base",
        value=5000.0,
        source=ConfigurationSource.TABLE_TEMPLATE,
        timestamp=datetime.now(),
        description="模板配置"
    )
    
    config4 = ConfigurationItem(
        key="salary_base",
        value="5000",  # 字符串类型，与上面的float冲突
        source=ConfigurationSource.USER_CONFIG,
        timestamp=datetime.now(),
        description="用户配置"
    )
    
    # 创建冲突分析器
    analyzer = ConflictAnalyzer()
    
    # 分析冲突
    configurations = {
        "employee_id": [config1, config2],
        "salary_base": [config3, config4]
    }
    
    analyses = analyzer.analyze_conflicts(configurations)
    
    print("\n冲突分析结果:")
    for analysis in analyses:
        print(f"字段: {analysis.field_key}")
        print(f"类型: {analysis.conflict_type.value}")
        print(f"严重程度: {analysis.severity.value}")
        print(f"影响: {analysis.impact_description}")
        print(f"可自动解决: {analysis.auto_resolvable} (置信度: {analysis.resolution_confidence:.1%})")
        print("-" * 50)
    
    # 生成报告
    report = analyzer.generate_conflict_report(analyses)
    print("\n" + report)
    
    # 获取统计信息
    stats = analyzer.get_conflict_statistics()
    print(f"\n统计信息: {json.dumps(stats, indent=2, ensure_ascii=False)}")
