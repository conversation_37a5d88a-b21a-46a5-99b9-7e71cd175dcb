"""
测试排序状态持久化
验证P0修复是否成功
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.enhanced_sort_state_manager import get_sort_state_manager
import time

def test_sort_persistence():
    """测试排序状态持久化"""
    print("=" * 60)
    print("测试排序状态持久化")
    print("=" * 60)
    
    manager = get_sort_state_manager()
    
    # 1. 清空所有状态
    print("\n1. 清空所有状态...")
    manager.clear_all_states()
    
    # 2. 测试保存排序状态
    print("\n2. 测试保存排序状态...")
    test_table = "salary_2025_01_active"
    
    sort_columns = [
        {'column_name': 'employee_name', 'sort_order': 'asc', 'priority': 0},
        {'column_name': 'total_salary', 'sort_order': 'desc', 'priority': 1}
    ]
    
    success = manager.save_sort_state(test_table, sort_columns)
    print(f"   保存状态: {'成功' if success else '失败'}")
    
    # 3. 测试获取排序状态
    print("\n3. 测试获取排序状态...")
    retrieved = manager.get_sort_state(test_table)
    
    if retrieved:
        print(f"   获取成功，列数: {len(retrieved)}")
        for col in retrieved:
            print(f"   - {col['column_name']}: {col['sort_order']} (优先级={col['priority']})")
    else:
        print("   获取失败")
    
    # 4. 测试状态持久化（模拟重启）
    print("\n4. 测试状态持久化（模拟重启）...")
    
    # 创建新实例（模拟程序重启）
    del manager
    manager2 = get_sort_state_manager()
    
    retrieved2 = manager2.get_sort_state(test_table)
    if retrieved2:
        print("   [PASS] 状态持久化成功")
        print(f"   恢复了 {len(retrieved2)} 个排序列")
    else:
        print("   [FAIL] 状态持久化失败")
    
    # 5. 测试更新单个列排序
    print("\n5. 测试更新单个列排序...")
    manager2.update_sort_column(test_table, 'department', 'asc')
    
    updated = manager2.get_sort_state(test_table)
    if updated and len(updated) == 3:
        print("   [PASS] 添加新排序列成功")
    
    # 切换排序顺序
    manager2.update_sort_column(test_table, 'employee_name', None)  # 切换
    updated = manager2.get_sort_state(test_table)
    
    for col in updated:
        if col['column_name'] == 'employee_name':
            if col['sort_order'] == 'desc':
                print("   [PASS] 切换排序顺序成功")
            else:
                print("   [FAIL] 切换排序顺序失败")
            break
    
    # 6. 测试清除排序状态
    print("\n6. 测试清除排序状态...")
    manager2.clear_sort_state(test_table)
    
    cleared = manager2.get_sort_state(test_table)
    if cleared is None or len(cleared) == 0:
        print("   [PASS] 清除排序状态成功")
    else:
        print("   [FAIL] 清除排序状态失败")
    
    # 7. 测试多表排序状态
    print("\n7. 测试多表排序状态...")
    
    tables = [
        "salary_2025_01_active",
        "salary_2025_01_retired",
        "change_data_2025_01"
    ]
    
    for table in tables:
        manager2.save_sort_state(table, [
            {'column_name': 'employee_id', 'sort_order': 'asc'}
        ])
    
    active_tables = manager2.get_active_tables()
    print(f"   活跃排序表数: {len(active_tables)}")
    print(f"   表列表: {active_tables}")
    
    # 8. 获取统计信息
    print("\n8. 统计信息...")
    stats = manager2.get_statistics()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_sort_persistence()