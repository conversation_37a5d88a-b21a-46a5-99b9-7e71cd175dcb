#!/usr/bin/env python3
"""
测试另存配置修复是否有效

验证修复后的逻辑：
1. 用户配置了字段类型
2. 点击"另存配置"按钮
3. 系统应该能正确保存配置，而不是提示错误
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger

def test_fixed_save_logic():
    """测试修复后的保存逻辑"""
    logger.info("=== 测试修复后的另存配置逻辑 ===")
    
    class FixedConfigDialog:
        def __init__(self):
            self.current_sheet_name = 'A岗职工'  # 模拟初始化
            self.all_sheets_configs = {}  # 空字典
            
        def get_current_configuration(self):
            """模拟获取当前配置"""
            return {
                'field_mapping': {
                    '序号': '序号',
                    '工号': '工号', 
                    '姓名': '姓名',
                    '基本工资': '基本工资',
                    '津贴': '津贴'
                },
                'field_types': {
                    '序号': 'integer',
                    '工号': 'employee_id_string',
                    '姓名': 'name_string',
                    '基本工资': 'salary_float',
                    '津贴': 'salary_float'
                }
            }
        
        def save_configuration(self):
            """修复后的另存配置逻辑"""
            logger.info("开始执行修复后的另存配置逻辑...")
            logger.info(f"当前工作表: {self.current_sheet_name}")
            logger.info(f"all_sheets_configs 初始状态: {self.all_sheets_configs}")
            
            # 修复后的逻辑：先保存当前工作表的配置，确保不遗漏
            if hasattr(self, 'current_sheet_name') and self.current_sheet_name != 'unknown':
                try:
                    current_config = self.get_current_configuration()
                    if current_config and current_config.get('field_mapping'):
                        self.all_sheets_configs[self.current_sheet_name] = current_config
                        logger.info(f"已保存当前工作表 '{self.current_sheet_name}' 配置到待保存列表")
                        logger.info(f"配置包含 {len(current_config.get('field_mapping', {}))} 个字段")
                except Exception as e:
                    logger.warning(f"保存当前工作表配置时出错: {e}")
            
            logger.info(f"保存后 all_sheets_configs 状态: {bool(self.all_sheets_configs)}")
            logger.info(f"包含的工作表: {list(self.all_sheets_configs.keys())}")
            
            # 检查是否有配置需要保存
            if not self.all_sheets_configs:
                logger.error("没有找到任何已配置的工作表")
                return False
            else:
                logger.info(f"找到 {len(self.all_sheets_configs)} 个已配置的工作表，可以继续保存")
                
                # 模拟后续保存过程
                total_fields = sum(len(config.get('field_mapping', {})) for config in self.all_sheets_configs.values())
                logger.info(f"总计 {total_fields} 个字段将被保存")
                
                return True
    
    # 1. 测试单个工作表配置保存
    logger.info("\n--- 测试1：单个工作表配置保存 ---")
    dialog1 = FixedConfigDialog()
    result1 = dialog1.save_configuration()
    
    # 2. 测试多个工作表的情况
    logger.info("\n--- 测试2：模拟多个工作表配置保存 ---")
    dialog2 = FixedConfigDialog()
    
    # 模拟用户先配置了其他工作表
    dialog2.all_sheets_configs = {
        '退休人员工资表': {
            'field_mapping': {'序号': '序号', '姓名': '姓名', '退休费': '退休费'},
            'field_types': {'序号': 'integer', '姓名': 'name_string', '退休费': 'salary_float'}
        }
    }
    dialog2.current_sheet_name = 'A岗职工'
    
    result2 = dialog2.save_configuration()
    
    # 3. 验证边界情况：没有有效配置
    logger.info("\n--- 测试3：无有效配置的情况 ---")
    
    class EmptyConfigDialog(FixedConfigDialog):
        def get_current_configuration(self):
            """模拟没有有效配置"""
            return {'field_mapping': {}, 'field_types': {}}
    
    dialog3 = EmptyConfigDialog()
    result3 = dialog3.save_configuration()
    
    # 总结测试结果
    logger.info("\n=== 测试结果总结 ===")
    results = {
        '单表配置': result1,
        '多表配置': result2, 
        '无效配置': result3
    }
    
    for test_name, result in results.items():
        status = "通过" if result else "失败"
        logger.info(f"{test_name}: {status}")
    
    return results

def main():
    """主函数"""
    try:
        results = test_fixed_save_logic()
        
        # 预期结果
        expected = {
            '单表配置': True,   # 应该成功
            '多表配置': True,   # 应该成功
            '无效配置': False   # 应该失败
        }
        
        success = all(results[key] == expected[key] for key in expected)
        
        print(f"\n>>> 修复测试结果: {'成功' if success else '失败'}")
        if success:
            print(">>> 修复已生效：另存配置现在能正确识别已配置的工作表")
        else:
            print(">>> 修复存在问题，需要进一步调试")
            
        return 0 if success else 1
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())