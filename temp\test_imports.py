"""
测试新模块的导入是否正确
"""

import sys
import os
import io

# 设置输出编码为UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """测试所有新模块的导入"""
    errors = []
    
    # 测试格式化引擎
    try:
        from src.modules.data_import.formatting_engine import get_formatting_engine
        engine = get_formatting_engine()
        print("✓ formatting_engine 导入成功")
        print(f"  - 注册的字段类型数: {len(engine.get_field_types())}")
    except Exception as e:
        errors.append(f"✗ formatting_engine 导入失败: {e}")
        print(f"✗ formatting_engine 导入失败: {e}")
    
    # 测试字段类型管理器
    try:
        from src.modules.data_import.field_type_manager import FieldTypeManager
        manager = FieldTypeManager()
        print("✓ field_type_manager 导入成功")
        print(f"  - 自定义字段类型数: {len(manager.list_custom_field_types())}")
    except Exception as e:
        errors.append(f"✗ field_type_manager 导入失败: {e}")
        print(f"✗ field_type_manager 导入失败: {e}")
    
    # 测试字段类型编辑对话框
    try:
        from src.gui.field_type_editor_dialog import FieldTypeEditorDialog
        print("✓ field_type_editor_dialog 导入成功")
    except Exception as e:
        errors.append(f"✗ field_type_editor_dialog 导入失败: {e}")
        print(f"✗ field_type_editor_dialog 导入失败: {e}")
    
    # 测试字段类型管理对话框
    try:
        from src.gui.field_type_manager_dialog import FieldTypeManagerDialog
        print("✓ field_type_manager_dialog 导入成功")
    except Exception as e:
        errors.append(f"✗ field_type_manager_dialog 导入失败: {e}")
        print(f"✗ field_type_manager_dialog 导入失败: {e}")
    
    # 测试修复后的change_data_config_dialog
    try:
        from src.gui.change_data_config_dialog import ChangeDataConfigDialog
        print("✓ change_data_config_dialog 导入成功")
    except Exception as e:
        errors.append(f"✗ change_data_config_dialog 导入失败: {e}")
        print(f"✗ change_data_config_dialog 导入失败: {e}")
    
    print("\n" + "="*50)
    if errors:
        print("发现以下错误:")
        for error in errors:
            print(f"  {error}")
    else:
        print("所有模块导入成功！")
    
    return len(errors) == 0

if __name__ == "__main__":
    success = test_imports()
    exit(0 if success else 1)