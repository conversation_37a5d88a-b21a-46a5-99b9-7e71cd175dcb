#!/usr/bin/env python3
"""
测试统一数据导入窗口按钮功能
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.gui.unified_data_import_window import UnifiedDataImportWindow
from src.utils.log_config import setup_logger

def test_button_functionality():
    """测试按钮功能"""
    logger = setup_logger(__name__)
    
    app = QApplication(sys.argv)
    
    try:
        # 创建窗口
        window = UnifiedDataImportWindow()
        window.show()
        
        # 测试按钮点击
        def test_buttons():
            logger.info("开始测试按钮功能...")
            
            # 测试Sheet管理按钮
            try:
                logger.info("测试全选按钮...")
                window.sheet_management_widget.select_all_btn.click()
                logger.info("全选按钮测试完成")
            except Exception as e:
                logger.error(f"全选按钮测试失败: {e}")
            
            try:
                logger.info("测试刷新按钮...")
                window.sheet_management_widget.refresh_btn.click()
                logger.info("刷新按钮测试完成")
            except Exception as e:
                logger.error(f"刷新按钮测试失败: {e}")
            
            # 测试映射配置按钮
            try:
                logger.info("测试智能映射按钮...")
                window.mapping_tab.smart_mapping_btn.click()
                logger.info("智能映射按钮测试完成")
            except Exception as e:
                logger.error(f"智能映射按钮测试失败: {e}")
            
            try:
                logger.info("测试验证配置按钮...")
                window.mapping_tab.validate_btn.click()
                logger.info("验证配置按钮测试完成")
            except Exception as e:
                logger.error(f"验证配置按钮测试失败: {e}")
            
            # 测试顶部工具栏按钮
            try:
                logger.info("测试帮助按钮...")
                window.help_btn.click()
                logger.info("帮助按钮测试完成")
            except Exception as e:
                logger.error(f"帮助按钮测试失败: {e}")
            
            logger.info("按钮功能测试完成")
            
            # 3秒后关闭
            QTimer.singleShot(3000, app.quit)
        
        # 1秒后开始测试
        QTimer.singleShot(1000, test_buttons)
        
        app.exec_()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_button_functionality()
