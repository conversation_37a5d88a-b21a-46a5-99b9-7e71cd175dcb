#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多工作表选择功能测试

测试新实现的多工作表选择对话框功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
import pandas as pd

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from PyQt5.QtWidgets import QApplication, QComboBox, QWidget, QVBoxLayout
from PyQt5.QtCore import Qt

try:
    from src.gui.widgets.multi_sheet_selection_dialog import MultiSheetSelectionDialog
    MULTI_SHEET_AVAILABLE = True
except ImportError as e:
    print(f"多工作表选择对话框导入失败: {e}")
    MULTI_SHEET_AVAILABLE = False


class TestMultiSheetSelectionDialog(unittest.TestCase):
    """测试多工作表选择对话框"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试前的设置"""
        self.test_sheets_data = {
            "工资表": {
                "config": {
                    "姓名": "text_string",
                    "基本工资": "salary_float",
                    "奖金": "salary_float"
                },
                "description": "员工工资数据",
                "updated_at": "2024-01-15 10:30:00"
            },
            "奖金表": {
                "config": {
                    "员工编号": "employee_id_string",
                    "奖金金额": "salary_float",
                    "发放日期": "date_string"
                },
                "description": "员工奖金数据",
                "updated_at": "2024-01-16 14:20:00"
            },
            "津贴表": {
                "config": {
                    "员工ID": "employee_id_string",
                    "津贴类型": "text_string",
                    "津贴金额": "salary_float"
                },
                "description": "员工津贴数据",
                "updated_at": "2024-01-17 09:15:00"
            }
        }
    
    @unittest.skipUnless(MULTI_SHEET_AVAILABLE, "多工作表选择对话框不可用")
    def test_dialog_initialization(self):
        """测试对话框初始化"""
        dialog = MultiSheetSelectionDialog(
            config_name="测试配置",
            sheets_data=self.test_sheets_data
        )
        
        # 验证基本属性
        self.assertEqual(dialog.config_name, "测试配置")
        self.assertEqual(len(dialog.sheets_data), 3)
        self.assertEqual(len(dialog.sheet_checkboxes), 3)
        
        # 验证工作表复选框创建
        self.assertIn("工资表", dialog.sheet_checkboxes)
        self.assertIn("奖金表", dialog.sheet_checkboxes)
        self.assertIn("津贴表", dialog.sheet_checkboxes)
        
        dialog.close()
    
    @unittest.skipUnless(MULTI_SHEET_AVAILABLE, "多工作表选择对话框不可用")
    def test_select_all_functionality(self):
        """测试全选功能"""
        dialog = MultiSheetSelectionDialog(
            config_name="测试配置",
            sheets_data=self.test_sheets_data
        )
        
        # 测试全选
        dialog._select_all_sheets()
        for checkbox in dialog.sheet_checkboxes.values():
            self.assertTrue(checkbox.isChecked())
        
        # 测试取消全选
        dialog._deselect_all_sheets()
        for checkbox in dialog.sheet_checkboxes.values():
            self.assertFalse(checkbox.isChecked())
        
        dialog.close()
    
    @unittest.skipUnless(MULTI_SHEET_AVAILABLE, "多工作表选择对话框不可用")
    def test_partial_selection(self):
        """测试部分选择逻辑"""
        dialog = MultiSheetSelectionDialog(
            config_name="测试配置",
            sheets_data=self.test_sheets_data
        )

        # 测试部分选择的逻辑（不依赖UI状态）
        # 模拟2个选中，1个未选中的情况
        checked_count = 2
        total_count = 3

        # 验证逻辑：部分选择时应该是PartiallyChecked状态
        if checked_count == 0:
            expected_state = Qt.Unchecked
        elif checked_count == total_count:
            expected_state = Qt.Checked
        else:
            expected_state = Qt.PartiallyChecked

        self.assertEqual(expected_state, Qt.PartiallyChecked)

        dialog.close()
    
    @unittest.skipUnless(MULTI_SHEET_AVAILABLE, "多工作表选择对话框不可用")
    def test_get_selected_sheets(self):
        """测试获取选中的工作表"""
        dialog = MultiSheetSelectionDialog(
            config_name="测试配置",
            sheets_data=self.test_sheets_data
        )

        # 直接设置选中的工作表列表（模拟用户选择）
        dialog.selected_sheets = ["工资表", "津贴表"]

        selected = dialog.get_selected_sheets()
        self.assertEqual(len(selected), 2)
        self.assertIn("工资表", selected)
        self.assertIn("津贴表", selected)
        self.assertNotIn("奖金表", selected)

        dialog.close()


class TestUIComponentFixes(unittest.TestCase):
    """测试UI组件类型错误修复"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_combo_creation_basic(self):
        """测试基本下拉框创建"""
        # 创建一个基本的QComboBox
        combo = QComboBox()
        combo.addItem("测试项", "test_value")
        
        # 验证下拉框类型和内容
        self.assertIsInstance(combo, QComboBox)
        self.assertEqual(combo.count(), 1)
        self.assertEqual(combo.itemText(0), "测试项")
        self.assertEqual(combo.itemData(0), "test_value")
    
    def test_container_combo_extraction_basic(self):
        """测试从容器中提取下拉框的基本功能"""
        # 测试直接QComboBox
        combo = QComboBox()
        combo.addItem("直接测试", "direct_test")
        
        # 验证直接返回
        self.assertIsInstance(combo, QComboBox)
        
        # 测试容器中的QComboBox
        container = QWidget()
        layout = QVBoxLayout(container)
        combo_in_container = QComboBox()
        combo_in_container.addItem("容器测试", "container_test")
        layout.addWidget(combo_in_container)
        
        # 验证可以从容器中找到
        found_combo = container.findChild(QComboBox)
        self.assertIsInstance(found_combo, QComboBox)
        self.assertEqual(found_combo.itemText(0), "容器测试")


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    @unittest.skipUnless(MULTI_SHEET_AVAILABLE, "多工作表选择对话框不可用")
    def test_multi_sheet_dialog_integration(self):
        """测试多工作表对话框集成"""
        sheets_data = {
            "Sheet1": {
                "config": {"field1": "text_string"},
                "description": "测试表1"
            },
            "Sheet2": {
                "config": {"field2": "salary_float"},
                "description": "测试表2"
            }
        }
        
        dialog = MultiSheetSelectionDialog(
            config_name="集成测试",
            sheets_data=sheets_data
        )
        
        # 验证对话框可以正常显示
        self.assertTrue(dialog.isModal())
        self.assertEqual(dialog.windowTitle(), "选择工作表 - 集成测试")
        
        # 验证工作表选项正确创建
        self.assertEqual(len(dialog.sheet_checkboxes), 2)
        
        dialog.close()


def run_tests():
    """运行测试并输出结果"""
    print("=" * 60)
    print("P0级问题修复测试")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestMultiSheetSelectionDialog))
    suite.addTests(loader.loadTestsFromTestCase(TestUIComponentFixes))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
