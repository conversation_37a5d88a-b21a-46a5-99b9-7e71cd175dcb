#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能配置推荐引擎

功能说明:
- 根据Sheet名称和内容自动推荐配置
- 智能检测表头位置和数据范围
- 识别汇总行和无效数据
- 提供配置推荐的置信度评分
"""

import re
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime

from src.utils.log_config import setup_logger
from src.modules.data_import.config_types import ConfigRecommendation


class SmartConfigRecommender:
    """智能配置推荐引擎"""
    
    def __init__(self):
        """初始化推荐引擎"""
        self.logger = setup_logger(__name__)
        
        # 预定义的模式规则
        self._init_pattern_rules()
        
        self.logger.info("智能配置推荐引擎初始化完成")
    
    def _init_pattern_rules(self):
        """初始化模式识别规则"""
        
        # Sheet名称模式
        self.sheet_name_patterns = {
            'summary': {
                'keywords': ['汇总', '统计', '总计', '合计', '小计', 'summary', 'total'],
                'config': {'is_enabled': False, 'remove_summary_rows': True},
                'confidence': 0.9
            },
            'documentation': {
                'keywords': ['说明', '备注', '注释', '帮助', 'readme', 'doc', 'help'],
                'config': {'is_enabled': False},
                'confidence': 0.95
            },
            'template': {
                'keywords': ['模板', '示例', 'template', 'example', 'sample'],
                'config': {'is_enabled': False},
                'confidence': 0.9
            },
            'monthly_data': {
                'keywords': ['月', '月份', 'month'],
                'config': {'header_row': 1, 'data_start_row': 2, 'has_header': True},
                'confidence': 0.8
            },
            'yearly_data': {
                'keywords': ['年', '年度', 'year', 'annual'],
                'config': {'header_row': 1, 'data_start_row': 2, 'has_header': True},
                'confidence': 0.8
            }
        }
        
        # 表头检测模式
        self.header_patterns = [
            r'姓名|工号|员工|编号|name|id|employee',
            r'工资|薪资|salary|wage|pay',
            r'部门|科室|department|dept',
            r'职位|岗位|position|job|title',
            r'日期|时间|date|time'
        ]
        
        # 汇总行检测模式
        self.summary_patterns = [
            r'^(合计|小计|总计|汇总|统计|sum|total|subtotal)',
            r'(合计|小计|总计|汇总|统计|sum|total|subtotal)$',
            r'^\s*(合计|小计|总计|汇总|统计|sum|total|subtotal)\s*[:：]?',
            r'平均|average|avg|mean'
        ]
        
        # 无效行检测模式
        self.invalid_patterns = [
            r'^\s*$',  # 空行
            r'^备注|^说明|^注意|^note|^remark',
            r'制表人|制表日期|审核|批准|approved|reviewed',
            r'第\d+页|page\s*\d+|共\d+页'
        ]
    
    def recommend_config(self, sheet_name: str, file_path: Optional[str] = None,
                        sample_data: Optional[pd.DataFrame] = None) -> ConfigRecommendation:
        """
        为Sheet推荐配置
        
        Args:
            sheet_name: Sheet名称
            file_path: Excel文件路径（可选）
            sample_data: 样本数据（可选，如果提供则不读取文件）
            
        Returns:
            配置推荐结果
        """
        try:
            # 延迟导入避免循环导入
            from src.modules.data_import.sheet_config_manager import SheetImportConfig

            self.logger.info(f"开始为Sheet '{sheet_name}' 推荐配置")

            # 创建基础配置
            config = SheetImportConfig(sheet_name=sheet_name)
            reasons = []
            warnings = []
            suggestions = []
            confidence = 0.5  # 基础置信度
            
            # 1. 基于Sheet名称的推荐
            name_result = self._analyze_sheet_name(sheet_name)
            if name_result:
                for key, value in name_result['config'].items():
                    setattr(config, key, value)
                confidence = max(confidence, name_result['confidence'])
                reasons.extend(name_result['reasons'])
                warnings.extend(name_result.get('warnings', []))
            
            # 2. 基于数据内容的推荐（如果有数据）
            if sample_data is not None or file_path:
                if sample_data is None:
                    # 读取样本数据
                    sample_data = self._read_sample_data(file_path, sheet_name)
                
                if sample_data is not None and not sample_data.empty:
                    content_result = self._analyze_sheet_content(sample_data)
                    if content_result:
                        # 合并配置
                        for key, value in content_result['config'].items():
                            setattr(config, key, value)
                        confidence = max(confidence, content_result['confidence'])
                        reasons.extend(content_result['reasons'])
                        warnings.extend(content_result.get('warnings', []))
                        suggestions.extend(content_result.get('suggestions', []))
            
            # 3. 应用智能默认值
            self._apply_smart_defaults(config, reasons)
            
            # 4. 验证配置合理性
            validation_result = self._validate_config(config)
            if validation_result['warnings']:
                warnings.extend(validation_result['warnings'])
            if validation_result['suggestions']:
                suggestions.extend(validation_result['suggestions'])
            
            recommendation = ConfigRecommendation(
                config=config,
                confidence=min(confidence, 1.0),
                reasons=reasons,
                warnings=warnings,
                suggestions=suggestions
            )
            
            self.logger.info(f"配置推荐完成，置信度: {confidence:.2f}")
            return recommendation
            
        except Exception as e:
            self.logger.error(f"配置推荐失败: {e}")
            # 返回默认配置
            from src.modules.data_import.sheet_config_manager import SheetImportConfig
            return ConfigRecommendation(
                config=SheetImportConfig(sheet_name=sheet_name),
                confidence=0.1,
                reasons=["使用默认配置"],
                warnings=[f"推荐过程出错: {e}"]
            )
    
    def _analyze_sheet_name(self, sheet_name: str) -> Optional[Dict[str, Any]]:
        """分析Sheet名称"""
        sheet_name_lower = sheet_name.lower().strip()
        
        for pattern_name, pattern_info in self.sheet_name_patterns.items():
            for keyword in pattern_info['keywords']:
                if keyword in sheet_name_lower:
                    return {
                        'config': pattern_info['config'].copy(),
                        'confidence': pattern_info['confidence'],
                        'reasons': [f"Sheet名称包含'{keyword}'，识别为{pattern_name}类型"],
                        'warnings': [] if pattern_info['config'].get('is_enabled', True) 
                                   else [f"检测到{pattern_name}类型Sheet，建议不导入"]
                    }
        
        return None
    
    def _read_sample_data(self, file_path: str, sheet_name: str, max_rows: int = 50) -> Optional[pd.DataFrame]:
        """读取样本数据"""
        try:
            from src.modules.data_import.excel_importer import ExcelImporter
            importer = ExcelImporter()
            return importer.import_data(file_path, sheet_name, max_rows=max_rows)
        except Exception as e:
            self.logger.warning(f"读取样本数据失败: {e}")
            return None

    def _analyze_sheet_content(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """分析Sheet内容"""
        try:
            config = {}
            reasons = []
            warnings = []
            suggestions = []
            confidence = 0.6

            # 1. 检测表头位置
            header_result = self._detect_header_position(df)
            if header_result:
                config.update(header_result['config'])
                reasons.extend(header_result['reasons'])
                confidence = max(confidence, header_result['confidence'])

            # 2. 检测数据范围
            range_result = self._detect_data_range(df)
            if range_result:
                config.update(range_result['config'])
                reasons.extend(range_result['reasons'])
                if range_result.get('warnings'):
                    warnings.extend(range_result['warnings'])

            # 3. 检测汇总行
            summary_result = self._detect_summary_rows(df)
            if summary_result:
                config.update(summary_result['config'])
                reasons.extend(summary_result['reasons'])
                if summary_result.get('suggestions'):
                    suggestions.extend(summary_result['suggestions'])

            # 4. 数据质量分析
            quality_result = self._analyze_data_quality(df)
            if quality_result:
                if quality_result.get('warnings'):
                    warnings.extend(quality_result['warnings'])
                if quality_result.get('suggestions'):
                    suggestions.extend(quality_result['suggestions'])

            return {
                'config': config,
                'confidence': confidence,
                'reasons': reasons,
                'warnings': warnings,
                'suggestions': suggestions
            }

        except Exception as e:
            self.logger.error(f"分析Sheet内容失败: {e}")
            return None

    def _detect_header_position(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """检测表头位置"""
        try:
            reasons = []
            confidence = 0.7

            # 检查前几行，寻找最可能的表头行
            for row_idx in range(min(5, len(df))):
                row_data = df.iloc[row_idx].astype(str).str.strip()

                # 计算表头特征得分
                header_score = 0

                # 1. 检查是否包含典型的表头关键词
                for pattern in self.header_patterns:
                    if any(re.search(pattern, str(cell), re.IGNORECASE) for cell in row_data):
                        header_score += 2

                # 2. 检查非空值比例
                non_empty_ratio = (row_data != '').sum() / len(row_data)
                if non_empty_ratio > 0.5:
                    header_score += 1

                # 3. 检查是否包含数字（表头通常不是纯数字）
                numeric_ratio = sum(1 for cell in row_data if str(cell).replace('.', '').replace('-', '').isdigit()) / len(row_data)
                if numeric_ratio < 0.3:  # 表头中数字比例应该较低
                    header_score += 1

                # 如果得分足够高，认为是表头
                if header_score >= 3:
                    return {
                        'config': {
                            'header_row': row_idx + 1,  # 转换为1基索引
                            'data_start_row': row_idx + 2,
                            'has_header': True,
                            'auto_detect_header': True
                        },
                        'confidence': min(0.9, 0.6 + header_score * 0.1),
                        'reasons': [f"在第{row_idx + 1}行检测到表头，得分: {header_score}"]
                    }

            # 如果没有检测到明显的表头，使用默认配置
            return {
                'config': {
                    'header_row': 1,
                    'data_start_row': 2,
                    'has_header': True,
                    'auto_detect_header': True
                },
                'confidence': 0.5,
                'reasons': ["未检测到明显表头，使用默认配置"]
            }

        except Exception as e:
            self.logger.error(f"检测表头位置失败: {e}")
            return None

    def _detect_data_range(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """检测数据范围"""
        try:
            config = {}
            reasons = []
            warnings = []

            # 检测数据结束位置
            total_rows = len(df)

            # 从后往前检查，寻找最后的有效数据行
            last_data_row = total_rows
            empty_row_count = 0

            for i in range(total_rows - 1, -1, -1):
                row_data = df.iloc[i].astype(str).str.strip()

                # 检查是否为空行
                if all(cell == '' or cell == 'nan' for cell in row_data):
                    empty_row_count += 1
                    continue

                # 检查是否为汇总行或无效行
                first_cell = str(row_data.iloc[0]) if len(row_data) > 0 else ''
                if any(re.search(pattern, first_cell, re.IGNORECASE) for pattern in self.summary_patterns + self.invalid_patterns):
                    last_data_row = i
                    continue

                # 找到有效数据行
                break

            if empty_row_count > 0:
                config['data_end_row'] = last_data_row
                reasons.append(f"检测到{empty_row_count}行空行，设置数据结束行为{last_data_row}")

            if empty_row_count > total_rows * 0.2:  # 超过20%的空行
                warnings.append(f"检测到大量空行({empty_row_count}行)，建议检查数据质量")

            # 设置跳过空行
            config['skip_empty_rows'] = True
            reasons.append("启用跳过空行选项")

            return {
                'config': config,
                'reasons': reasons,
                'warnings': warnings
            }

        except Exception as e:
            self.logger.error(f"检测数据范围失败: {e}")
            return None

    def _detect_summary_rows(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """检测汇总行"""
        try:
            config = {}
            reasons = []
            suggestions = []

            summary_rows_found = 0
            summary_keywords_found = set()

            # 检查每一行的第一列是否包含汇总关键词
            for i, row in df.iterrows():
                first_cell = str(row.iloc[0]).strip() if len(row) > 0 else ''

                for pattern in self.summary_patterns:
                    if re.search(pattern, first_cell, re.IGNORECASE):
                        summary_rows_found += 1
                        # 提取匹配的关键词
                        match = re.search(pattern, first_cell, re.IGNORECASE)
                        if match:
                            summary_keywords_found.add(match.group().strip())
                        break

            if summary_rows_found > 0:
                config['remove_summary_rows'] = True
                config['summary_keywords'] = list(summary_keywords_found) + ["合计", "小计", "总计", "汇总"]
                reasons.append(f"检测到{summary_rows_found}行汇总数据，启用汇总行移除")
                suggestions.append(f"发现汇总关键词: {', '.join(summary_keywords_found)}")
            else:
                config['remove_summary_rows'] = False
                reasons.append("未检测到汇总行")

            return {
                'config': config,
                'reasons': reasons,
                'suggestions': suggestions
            }

        except Exception as e:
            self.logger.error(f"检测汇总行失败: {e}")
            return None

    def _analyze_data_quality(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """分析数据质量"""
        try:
            warnings = []
            suggestions = []

            total_cells = df.size

            # 1. 空值分析
            null_count = df.isnull().sum().sum()
            null_ratio = null_count / total_cells if total_cells > 0 else 0

            if null_ratio > 0.3:
                warnings.append(f"数据中空值比例较高({null_ratio:.1%})，建议检查数据完整性")
                suggestions.append("考虑启用空值填充选项")
            elif null_ratio > 0.1:
                suggestions.append(f"数据中有{null_ratio:.1%}的空值，可考虑填充处理")

            # 2. 数据类型一致性检查
            for col in df.columns:
                col_data = df[col].dropna().astype(str)
                if len(col_data) == 0:
                    continue

                # 检查数字列的一致性
                numeric_count = sum(1 for x in col_data if x.replace('.', '').replace('-', '').replace(',', '').isdigit())
                numeric_ratio = numeric_count / len(col_data)

                if 0.3 < numeric_ratio < 0.8:  # 部分是数字，部分不是
                    warnings.append(f"列'{col}'数据类型不一致，{numeric_ratio:.1%}为数字")
                    suggestions.append(f"建议检查列'{col}'的数据格式")

            # 3. 重复数据检查
            duplicate_rows = df.duplicated().sum()
            if duplicate_rows > 0:
                duplicate_ratio = duplicate_rows / len(df)
                if duplicate_ratio > 0.1:
                    warnings.append(f"检测到{duplicate_rows}行重复数据({duplicate_ratio:.1%})")
                    suggestions.append("建议在导入前去除重复数据")

            # 4. 异常值检查（简单的长度检查）
            for col in df.columns:
                col_data = df[col].dropna().astype(str)
                if len(col_data) == 0:
                    continue

                # 检查异常长的文本
                max_length = max(len(x) for x in col_data)
                avg_length = sum(len(x) for x in col_data) / len(col_data)

                if max_length > avg_length * 5 and max_length > 100:
                    suggestions.append(f"列'{col}'存在异常长的文本(最长{max_length}字符)，建议检查")

            return {
                'warnings': warnings,
                'suggestions': suggestions
            }

        except Exception as e:
            self.logger.error(f"数据质量分析失败: {e}")
            return None

    def _apply_smart_defaults(self, config: Any, reasons: List[str]):
        """应用智能默认值"""

        # 如果没有设置数据清洗选项，应用智能默认值
        if not hasattr(config, 'trim_whitespace') or config.trim_whitespace is None:
            config.trim_whitespace = True
            reasons.append("启用去除前后空格")

        if not hasattr(config, 'normalize_numbers') or config.normalize_numbers is None:
            config.normalize_numbers = True
            reasons.append("启用数字格式统一")

        if not hasattr(config, 'handle_merged_cells') or config.handle_merged_cells is None:
            config.handle_merged_cells = True
            reasons.append("启用合并单元格处理")

        # 根据Sheet是否启用设置其他默认值
        if config.is_enabled:
            if not hasattr(config, 'skip_empty_rows') or config.skip_empty_rows is None:
                config.skip_empty_rows = True
                reasons.append("启用跳过空行")

    def _validate_config(self, config: Any) -> Dict[str, List[str]]:
        """验证配置合理性"""
        warnings = []
        suggestions = []

        # 检查行号设置的合理性
        if config.header_row >= config.data_start_row:
            warnings.append("表头行号不应大于等于数据起始行号")

        if config.data_end_row and config.data_end_row <= config.data_start_row:
            warnings.append("数据结束行号不应小于等于数据起始行号")

        # 检查是否启用了相互冲突的选项
        if not config.has_header and config.auto_detect_header:
            suggestions.append("已禁用表头但启用了自动检测表头，建议保持一致")

        if not config.is_enabled:
            suggestions.append("此Sheet已被禁用，不会参与数据导入")

        return {
            'warnings': warnings,
            'suggestions': suggestions
        }
