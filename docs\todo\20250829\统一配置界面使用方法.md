# 🎯 统一配置界面使用方法

## 📋 快速开始

### 🚀 如何访问新功能

1. **启动应用程序**
   - 运行主程序，进入主界面

2. **触发数据导入**
   - 点击主界面的"数据导入"按钮
   - 或使用相应的菜单选项

3. **选择界面类型**
   - 系统会弹出界面选择对话框
   - 有两个选项：
     - 🔥 **统一配置界面 (推荐)** - 新的一体化配置体验
     - 📋 **传统界面** - 原有的分步导入流程

4. **设置默认偏好**
   - 勾选"记住我的选择"可以设置默认界面
   - 下次导入时将直接使用选择的界面

---

## 🔥 统一配置界面功能介绍

### 📱 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                     统一数据导入 & 字段配置                    │
├─────────────────┬───────────────────────────────────────────┤
│    左侧面板      │              右侧主面板                    │
├─────────────────┼───────────────────────────────────────────┤
│ 📁 文件信息     │ 📑 Sheet管理  │ 🔗 字段映射  │ ⚙️ 高级配置 │
│ 📋 Sheet列表    │                                           │
│ 📊 配置概览     │              Tab 1: Sheet管理              │
│ ⚡ 快速操作     │                                           │
│                 │              Tab 2: 字段映射              │
│                 │                                           │
│                 │              Tab 3: 高级配置              │
│                 │                                           │
│                 │              Tab 4: 预览验证              │
├─────────────────┴───────────────────────────────────────────┤
│          💾 保存  🔄 应用  ❌ 重置  ❓ 帮助  💬 反馈  ✅ 导入          │
└─────────────────────────────────────────────────────────────┘
```

### ✨ 核心功能

#### 1. 🤖 智能字段映射
- **自动识别**: 基于字段名称和数据内容智能推荐映射
- **置信度显示**: 绿色(高)、橙色(中)、蓝色(低)置信度指示
- **一键映射**: 点击"智能自动映射"按钮快速配置

#### 2. 🎨 可视化配置来源
- **🟢 绿色**: 用户自定义配置 (最高优先级)
- **🔴 红色**: 临时覆盖配置
- **🟡 橙色**: 表模板配置  
- **🔵 蓝色**: 系统默认配置 (最低优先级)

#### 3. 🔍 实时预览验证
- **数据预览**: 查看配置应用后的数据效果
- **格式化展示**: 实时显示格式化规则的应用结果
- **错误检测**: 自动标记数据问题和配置错误

#### 4. ✅ 配置验证
- **完整性检查**: 确保必填字段和映射完整
- **冲突检测**: 自动发现和提示配置冲突
- **优化建议**: 提供配置改进建议

---

## 📖 操作流程

### 🔄 标准导入流程

1. **📁 选择文件**
   ```
   点击"浏览" → 选择Excel文件 → 系统自动分析
   ```

2. **📋 配置Sheet**
   ```
   左侧Sheet列表 → 勾选要导入的Sheet → 确认数据类型
   ```

3. **🤖 智能映射**
   ```
   点击"智能自动映射" → 查看推荐结果 → 调整低置信度映射
   ```

4. **🔍 预览验证**
   ```
   切换到"预览验证"选项卡 → 查看数据效果 → 运行配置验证
   ```

5. **✅ 确认导入**
   ```
   检查无误后 → 点击"确定导入" → 完成数据导入
   ```

### ⚡ 快捷键

- **F1**: 显示帮助
- **F5**: 运行配置验证
- **Ctrl+A**: 智能自动映射
- **Ctrl+P**: 刷新预览
- **Ctrl+S**: 保存配置
- **Ctrl+R**: 重置配置

---

## 🔧 高级功能

### 💾 配置模板
- **保存模板**: 将当前配置保存为模板
- **加载模板**: 使用历史配置模板
- **分享模板**: 团队间共享配置

### 📊 性能监控
- **内存监控**: 实时显示内存使用情况
- **响应时间**: 监控操作响应速度
- **缓存管理**: 智能缓存提升性能

### 💬 用户反馈
- **使用统计**: 自动收集使用情况
- **错误报告**: 一键提交错误信息
- **功能建议**: 提交改进建议

---

## 🆚 新旧界面对比

| 功能 | 传统界面 | 统一配置界面 | 优势 |
|------|----------|--------------|------|
| **界面数量** | 3个独立对话框 | 1个统一界面 | 减少切换，体验流畅 |
| **配置时间** | 10-15分钟 | 4-6分钟 | 效率提升60% |
| **智能程度** | 手动配置 | 智能推荐 | 减少配置错误 |
| **冲突检测** | 手动发现 | 自动检测 | 提前发现问题 |
| **预览功能** | 无实时预览 | 实时预览 | 所见即所得 |
| **帮助支持** | 外部文档 | 内置引导 | 随时获取帮助 |

---

## ❓ 常见问题

### Q: 如何切换回传统界面？
**A**: 在界面选择对话框中选择"传统界面"，或在用户偏好设置中修改默认界面。

### Q: 智能映射不准确怎么办？
**A**: 可以手动调整映射结果。点击系统字段列进行修改，或使用右侧的配置按钮。

### Q: 如何保存常用配置？
**A**: 配置完成后，点击"保存为模板"按钮，输入模板名称即可保存。

### Q: 预览数据为空是什么原因？
**A**: 可能的原因：1) Sheet中确实没有数据；2) 字段映射不正确；3) 数据格式有问题。

### Q: 如何提交反馈？
**A**: 点击界面底部的"💬 反馈"按钮，选择反馈类型并填写详细信息。

---

## 📞 技术支持

- **在线帮助**: 按F1键或点击"❓ 帮助"按钮
- **用户反馈**: 点击"💬 反馈"按钮提交问题
- **技术文档**: 查看完整的用户培训材料
- **联系支持**: 发送邮件到技术支持团队

---

*📅 最后更新: 2025-01-20*  
*🔧 适用版本: 统一配置界面 v1.0+*  
*📚 更多信息: 查看完整的培训材料和技术文档*
