#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置系统测试脚本

用于测试新创建的统一配置界面系统的各个组件功能。

创建时间: 2025-01-20
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# 导入要测试的模块
from src.gui.unified_config_manager import (
    ConfigurationManager, ConfigurationSource, ConfigurationItem,
    ConfigurationPriority, ConflictReport
)
from src.gui.unified_visual_indicator import VisualSourceIndicator
from src.gui.unified_conflict_analyzer import ConflictAnalyzer, ConflictType, ConflictSeverity
from src.gui.unified_integration_manager import IntegrationManager, InterfaceMode


class TestConfigurationManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        """测试准备"""
        self.manager = ConfigurationManager()
    
    def test_add_configuration(self):
        """测试添加配置"""
        config = self.manager.add_configuration(
            "test_key", "test_value", 
            ConfigurationSource.USER_CONFIG, 
            "测试配置"
        )
        
        self.assertEqual(config.key, "test_key")
        self.assertEqual(config.value, "test_value")
        self.assertEqual(config.source, ConfigurationSource.USER_CONFIG)
    
    def test_get_effective_configuration(self):
        """测试获取生效配置"""
        # 添加多个不同优先级的配置
        self.manager.add_configuration(
            "test_key", "default_value", 
            ConfigurationSource.SYSTEM_DEFAULT
        )
        self.manager.add_configuration(
            "test_key", "user_value", 
            ConfigurationSource.USER_CONFIG
        )
        
        # 应该返回用户配置（优先级更高）
        effective_config = self.manager.get_effective_configuration("test_key")
        self.assertEqual(effective_config.value, "user_value")
        self.assertEqual(effective_config.source, ConfigurationSource.USER_CONFIG)
    
    def test_detect_conflicts(self):
        """测试冲突检测"""
        # 添加冲突配置
        self.manager.add_configuration(
            "conflict_key", "value1", 
            ConfigurationSource.SYSTEM_DEFAULT
        )
        self.manager.add_configuration(
            "conflict_key", "value2", 
            ConfigurationSource.USER_CONFIG
        )
        
        conflicts = self.manager.detect_conflicts()
        self.assertEqual(len(conflicts), 1)
        self.assertEqual(conflicts[0].field_key, "conflict_key")
    
    def test_resolve_conflicts(self):
        """测试冲突解决"""
        # 添加冲突配置
        self.manager.add_configuration(
            "resolve_key", "default_value", 
            ConfigurationSource.SYSTEM_DEFAULT
        )
        self.manager.add_configuration(
            "resolve_key", "user_value", 
            ConfigurationSource.USER_CONFIG
        )
        
        resolved = self.manager.resolve_configuration_conflicts()
        self.assertEqual(resolved["resolve_key"].value, "user_value")


class TestConfigurationPriority(unittest.TestCase):
    """配置优先级测试"""
    
    def test_priority_order(self):
        """测试优先级顺序"""
        user_priority = ConfigurationPriority.get_priority(ConfigurationSource.USER_CONFIG)
        system_priority = ConfigurationPriority.get_priority(ConfigurationSource.SYSTEM_DEFAULT)
        
        # 用户配置优先级应该更高（数字更小）
        self.assertLess(user_priority, system_priority)
    
    def test_compare_priority(self):
        """测试优先级比较"""
        result = ConfigurationPriority.compare_priority(
            ConfigurationSource.USER_CONFIG, 
            ConfigurationSource.SYSTEM_DEFAULT
        )
        
        # 用户配置优先级更高
        self.assertEqual(result, -1)


class TestConflictAnalyzer(unittest.TestCase):
    """冲突分析器测试"""
    
    def setUp(self):
        """测试准备"""
        self.analyzer = ConflictAnalyzer()
    
    def test_analyze_conflicts(self):
        """测试冲突分析"""
        # 创建测试配置项
        config1 = ConfigurationItem(
            key="test_field",
            value="value1",
            source=ConfigurationSource.SYSTEM_DEFAULT,
            timestamp=datetime.now()
        )
        config2 = ConfigurationItem(
            key="test_field",
            value="value2",
            source=ConfigurationSource.USER_CONFIG,
            timestamp=datetime.now()
        )
        
        configurations = {"test_field": [config1, config2]}
        analyses = self.analyzer.analyze_conflicts(configurations)
        
        self.assertEqual(len(analyses), 1)
        self.assertEqual(analyses[0].field_key, "test_field")
        self.assertEqual(analyses[0].conflict_type, ConflictType.VALUE_CONFLICT)
    
    def test_generate_conflict_report(self):
        """测试冲突报告生成"""
        # 模拟冲突分析结果
        config1 = ConfigurationItem(
            key="report_field",
            value="value1",
            source=ConfigurationSource.SYSTEM_DEFAULT,
            timestamp=datetime.now()
        )
        config2 = ConfigurationItem(
            key="report_field",
            value="value2",
            source=ConfigurationSource.USER_CONFIG,
            timestamp=datetime.now()
        )
        
        configurations = {"report_field": [config1, config2]}
        analyses = self.analyzer.analyze_conflicts(configurations)
        
        report = self.analyzer.generate_conflict_report(analyses)
        
        self.assertIn("配置冲突分析报告", report)
        self.assertIn("report_field", report)


class TestVisualSourceIndicator(unittest.TestCase):
    """可视化指示器测试"""
    
    def setUp(self):
        """测试准备"""
        self.indicator = VisualSourceIndicator()
    
    def test_get_source_config(self):
        """测试获取源配置"""
        config = self.indicator.get_source_config(ConfigurationSource.USER_CONFIG)
        
        self.assertEqual(config.color, "#4CAF50")  # 绿色
        self.assertEqual(config.icon, "👤")
        self.assertEqual(config.priority, 1)
    
    def test_create_source_indicator_label(self):
        """测试创建源指示标签"""
        # 这个测试需要QApplication，在GUI测试环境中运行
        pass  # 暂时跳过GUI测试
    
    def test_create_priority_indicator(self):
        """测试创建优先级指示器"""
        # 这个测试需要QApplication，在GUI测试环境中运行
        pass  # 暂时跳过GUI测试


class TestIntegrationManager(unittest.TestCase):
    """集成管理器测试"""
    
    def setUp(self):
        """测试准备"""
        self.manager = IntegrationManager()
    
    def test_determine_interface_mode(self):
        """测试界面模式确定"""
        # 测试自动决定逻辑
        mode = self.manager._auto_decide_interface_mode()
        self.assertIn(mode, [InterfaceMode.UNIFIED, InterfaceMode.LEGACY_SEPARATE])
    
    def test_usage_stats_update(self):
        """测试使用统计更新"""
        initial_unified_count = self.manager.usage_stats.get("unified_usage_count", 0)
        
        self.manager._update_usage_stats(InterfaceMode.UNIFIED)
        
        new_unified_count = self.manager.usage_stats.get("unified_usage_count", 0)
        self.assertEqual(new_unified_count, initial_unified_count + 1)
    
    def test_get_usage_report(self):
        """测试使用报告生成"""
        report = self.manager.get_usage_report()
        self.assertIn("界面使用统计报告", report)


class TestGUIComponents(unittest.TestCase):
    """GUI组件测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置GUI测试环境"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_unified_import_config_dialog_creation(self):
        """测试统一配置对话框创建"""
        try:
            from src.gui.unified_import_config_dialog import UnifiedImportConfigDialog
            
            # 创建对话框
            dialog = UnifiedImportConfigDialog()
            
            # 检查基本属性
            self.assertIsNotNone(dialog)
            self.assertEqual(dialog.windowTitle(), "统一数据导入 & 字段配置")
            
            # 检查主要组件是否创建
            self.assertIsNotNone(dialog.left_panel)
            self.assertIsNotNone(dialog.right_panel)
            self.assertIsNotNone(dialog.tab_widget)
            
            # 检查选项卡数量
            self.assertEqual(dialog.tab_widget.count(), 4)
            
            print("✅ 统一配置对话框创建测试通过")
            
        except Exception as e:
            self.fail(f"统一配置对话框创建失败: {e}")
    
    def test_integration_manager_dialog_creation(self):
        """测试集成管理器对话框创建"""
        try:
            from src.gui.unified_integration_manager import show_unified_import_dialog
            
            # 这个测试可能需要用户交互，在实际环境中需要谨慎使用
            # dialog = show_unified_import_dialog()
            # self.assertIsNotNone(dialog)
            
            print("✅ 集成管理器测试通过（跳过GUI创建）")
            
        except Exception as e:
            print(f"⚠️ 集成管理器测试警告: {e}")


def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 开始统一配置系统综合测试")
    print("=" * 50)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试用例
    suite.addTests(loader.loadTestsFromTestCase(TestConfigurationManager))
    suite.addTests(loader.loadTestsFromTestCase(TestConfigurationPriority))
    suite.addTests(loader.loadTestsFromTestCase(TestConflictAnalyzer))
    suite.addTests(loader.loadTestsFromTestCase(TestVisualSourceIndicator))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegrationManager))
    suite.addTests(loader.loadTestsFromTestCase(TestGUIComponents))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 50)
    print("📊 测试结果摘要:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  • {test}: {traceback}")
    
    if result.errors:
        print("\n⚠️ 错误的测试:")
        for test, traceback in result.errors:
            print(f"  • {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n🎉 所有测试通过！统一配置系统工作正常。")
    else:
        print("\n⚠️ 部分测试未通过，请检查相关组件。")
    
    return result.wasSuccessful()


def manual_integration_test():
    """手动集成测试"""
    print("\n🖱️ 开始手动集成测试")
    print("=" * 30)
    
    try:
        # 创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 测试1: 创建配置管理器
        print("1. 测试配置管理器...")
        manager = ConfigurationManager()
        manager.add_configuration("test", "value", ConfigurationSource.USER_CONFIG)
        effective = manager.get_effective_configuration("test")
        assert effective.value == "value"
        print("   ✅ 配置管理器工作正常")
        
        # 测试2: 测试冲突检测
        print("2. 测试冲突检测...")
        analyzer = ConflictAnalyzer()
        config1 = ConfigurationItem("field", "value1", ConfigurationSource.SYSTEM_DEFAULT, datetime.now())
        config2 = ConfigurationItem("field", "value2", ConfigurationSource.USER_CONFIG, datetime.now())
        analyses = analyzer.analyze_conflicts({"field": [config1, config2]})
        assert len(analyses) == 1
        print("   ✅ 冲突检测工作正常")
        
        # 测试3: 测试可视化指示器
        print("3. 测试可视化指示器...")
        indicator = VisualSourceIndicator()
        config = indicator.get_source_config(ConfigurationSource.USER_CONFIG)
        assert config.color == "#4CAF50"
        print("   ✅ 可视化指示器工作正常")
        
        # 测试4: 测试集成管理器
        print("4. 测试集成管理器...")
        integration_manager = IntegrationManager()
        mode = integration_manager._auto_decide_interface_mode()
        assert mode in [InterfaceMode.UNIFIED, InterfaceMode.LEGACY_SEPARATE]
        print("   ✅ 集成管理器工作正常")
        
        print("\n🎉 手动集成测试全部通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 手动集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    """主测试入口"""
    print("🚀 统一配置系统测试启动")
    print("方案3实施 - 阶段1测试")
    print("=" * 50)
    
    # 首先运行手动集成测试
    manual_success = manual_integration_test()
    
    if manual_success:
        # 运行综合测试
        comprehensive_success = run_comprehensive_test()
        
        if comprehensive_success:
            print("\n🎊 恭喜！统一配置系统阶段1实施成功！")
            print("✅ 所有核心功能正常工作")
            print("✅ 配置管理器运行正常")
            print("✅ 冲突检测功能正常")
            print("✅ 可视化指示器正常")
            print("✅ 集成管理器正常")
            print("\n📋 下一步:")
            print("1. 进行用户验收测试")
            print("2. 收集用户反馈")
            print("3. 优化界面和功能")
            print("4. 准备阶段2实施")
        else:
            print("\n⚠️ 综合测试未完全通过，但核心功能正常。")
            print("建议进一步调试和优化。")
    else:
        print("\n❌ 手动集成测试失败，请检查系统环境和依赖。")
    
    print("\n" + "=" * 50)
    print("测试完成")
