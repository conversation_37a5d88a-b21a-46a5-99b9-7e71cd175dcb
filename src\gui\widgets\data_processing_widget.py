"""
数据处理配置组件

提供Sheet级别的数据处理配置界面，包括：
- 数据范围设置（表头行、起始行、结束行）
- 数据清洗规则配置
- 汇总行处理配置
- 实时预览效果
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout,
    QSpinBox, QCheckBox, QLineEdit, QPushButton, QLabel,
    QTextEdit, QFrame, QScrollArea, QGridLayout, QComboBox,
    QListWidget, QListWidgetItem, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette

from src.utils.log_config import setup_logger


class DataProcessingWidget(QWidget):
    """数据处理配置组件"""
    
    # 信号定义
    config_changed = pyqtSignal(dict)  # 配置变化信号
    preview_requested = pyqtSignal(str)  # 预览请求信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 当前配置
        self.current_sheet_name = ""
        self.current_config = None
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # 添加智能推荐和模板功能区域
        self._create_smart_features_section(layout)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameStyle(QFrame.NoFrame)
        
        # 主内容组件
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)
        
        # 当前Sheet信息
        self.sheet_info_label = QLabel("当前Sheet: 未选择")
        self.sheet_info_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2196F3;
                padding: 8px;
                background-color: #E3F2FD;
                border-radius: 4px;
            }
        """)
        content_layout.addWidget(self.sheet_info_label)
        
        # 数据范围设置
        range_group = self._create_data_range_group()
        content_layout.addWidget(range_group)
        
        # 数据清洗规则
        cleaning_group = self._create_data_cleaning_group()
        content_layout.addWidget(cleaning_group)
        
        # 汇总行处理
        summary_group = self._create_summary_handling_group()
        content_layout.addWidget(summary_group)
        
        # 预览效果
        preview_group = self._create_preview_group()
        content_layout.addWidget(preview_group)
        
        # 设置滚动区域
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # 底部操作按钮
        button_layout = self._create_button_layout()
        layout.addLayout(button_layout)
    
    def _create_data_range_group(self) -> QGroupBox:
        """创建数据范围设置组"""
        group = QGroupBox("📊 数据范围设置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QFormLayout(group)
        layout.setSpacing(10)
        
        # 表头行号
        self.header_row_spin = QSpinBox()
        self.header_row_spin.setRange(1, 1000)
        self.header_row_spin.setValue(1)
        self.header_row_spin.setToolTip("指定表头所在的行号")
        
        header_layout = QHBoxLayout()
        header_layout.addWidget(self.header_row_spin)
        
        self.auto_detect_header_check = QCheckBox("自动检测表头")
        self.auto_detect_header_check.setChecked(True)
        self.auto_detect_header_check.setToolTip("自动检测表头位置")
        header_layout.addWidget(self.auto_detect_header_check)
        header_layout.addStretch()
        
        layout.addRow("表头行号:", header_layout)
        
        # 数据起始行
        self.data_start_spin = QSpinBox()
        self.data_start_spin.setRange(1, 1000)
        self.data_start_spin.setValue(2)
        self.data_start_spin.setToolTip("数据开始的行号")
        layout.addRow("数据起始行:", self.data_start_spin)
        
        # 数据结束行
        end_layout = QHBoxLayout()
        self.data_end_spin = QSpinBox()
        self.data_end_spin.setRange(0, 100000)
        self.data_end_spin.setValue(0)
        self.data_end_spin.setSpecialValueText("到文件末尾")
        self.data_end_spin.setToolTip("数据结束的行号，0表示到文件末尾")
        end_layout.addWidget(self.data_end_spin)
        
        self.skip_empty_check = QCheckBox("跳过空行")
        self.skip_empty_check.setChecked(True)
        self.skip_empty_check.setToolTip("自动跳过空白行")
        end_layout.addWidget(self.skip_empty_check)
        end_layout.addStretch()
        
        layout.addRow("数据结束行:", end_layout)
        
        return group
    
    def _create_data_cleaning_group(self) -> QGroupBox:
        """创建数据清洗规则组"""
        group = QGroupBox("🔧 数据清洗规则")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        # 基本清洗选项
        basic_layout = QGridLayout()
        
        self.trim_whitespace_check = QCheckBox("自动去除前后空格")
        self.trim_whitespace_check.setChecked(True)
        basic_layout.addWidget(self.trim_whitespace_check, 0, 0)
        
        self.normalize_numbers_check = QCheckBox("统一数字格式")
        self.normalize_numbers_check.setChecked(True)
        basic_layout.addWidget(self.normalize_numbers_check, 0, 1)
        
        self.handle_merged_cells_check = QCheckBox("处理合并单元格")
        self.handle_merged_cells_check.setChecked(True)
        basic_layout.addWidget(self.handle_merged_cells_check, 1, 0)
        
        self.fill_empty_values_check = QCheckBox("填充空值")
        self.fill_empty_values_check.setChecked(False)
        basic_layout.addWidget(self.fill_empty_values_check, 1, 1)
        
        layout.addLayout(basic_layout)
        
        return group
    
    def _create_summary_handling_group(self) -> QGroupBox:
        """创建汇总行处理组"""
        group = QGroupBox("📋 汇总行处理")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        # 移除汇总行选项
        self.remove_summary_check = QCheckBox("移除汇总行")
        self.remove_summary_check.setChecked(False)
        self.remove_summary_check.setToolTip("自动识别并移除包含汇总数据的行")
        layout.addWidget(self.remove_summary_check)
        
        # 汇总关键词设置
        keywords_layout = QHBoxLayout()
        keywords_layout.addWidget(QLabel("汇总关键词:"))
        
        self.summary_keywords_list = QListWidget()
        self.summary_keywords_list.setMaximumHeight(80)
        self.summary_keywords_list.setToolTip("包含这些关键词的行将被识别为汇总行")
        
        # 默认关键词
        default_keywords = ["合计", "小计", "总计", "汇总"]
        for keyword in default_keywords:
            item = QListWidgetItem(keyword)
            item.setFlags(item.flags() | Qt.ItemIsEditable)
            self.summary_keywords_list.addItem(item)
        
        keywords_layout.addWidget(self.summary_keywords_list)
        
        # 关键词操作按钮
        keyword_btn_layout = QVBoxLayout()
        self.add_keyword_btn = QPushButton("+ 添加")
        self.add_keyword_btn.setMaximumWidth(60)
        self.remove_keyword_btn = QPushButton("- 删除")
        self.remove_keyword_btn.setMaximumWidth(60)
        
        keyword_btn_layout.addWidget(self.add_keyword_btn)
        keyword_btn_layout.addWidget(self.remove_keyword_btn)
        keyword_btn_layout.addStretch()
        
        keywords_layout.addLayout(keyword_btn_layout)
        layout.addLayout(keywords_layout)
        
        return group
    
    def _create_preview_group(self) -> QGroupBox:
        """创建预览效果组"""
        group = QGroupBox("👁️ 预览效果")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        # 预览信息显示
        self.preview_info_label = QLabel("请选择Sheet后查看预览效果")
        self.preview_info_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 4px;
                color: #666;
            }
        """)
        self.preview_info_label.setWordWrap(True)
        layout.addWidget(self.preview_info_label)
        
        return group
    
    def _create_button_layout(self) -> QHBoxLayout:
        """创建底部按钮布局"""
        layout = QHBoxLayout()
        
        # 预览按钮
        self.preview_btn = QPushButton("🔍 预览效果")
        self.preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        
        # 重置按钮
        self.reset_btn = QPushButton("🔄 重置配置")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        
        # 应用按钮
        self.apply_btn = QPushButton("✅ 应用配置")
        self.apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
        """)
        
        layout.addWidget(self.preview_btn)
        layout.addWidget(self.reset_btn)
        layout.addStretch()
        layout.addWidget(self.apply_btn)
        
        return layout
    
    def _connect_signals(self):
        """连接信号"""
        # 配置变化信号
        self.header_row_spin.valueChanged.connect(self._on_config_changed)
        self.data_start_spin.valueChanged.connect(self._on_config_changed)
        self.data_end_spin.valueChanged.connect(self._on_config_changed)
        self.auto_detect_header_check.toggled.connect(self._on_config_changed)
        self.skip_empty_check.toggled.connect(self._on_config_changed)
        self.trim_whitespace_check.toggled.connect(self._on_config_changed)
        self.normalize_numbers_check.toggled.connect(self._on_config_changed)
        self.handle_merged_cells_check.toggled.connect(self._on_config_changed)
        self.fill_empty_values_check.toggled.connect(self._on_config_changed)
        self.remove_summary_check.toggled.connect(self._on_config_changed)
        
        # 按钮信号
        self.add_keyword_btn.clicked.connect(self._add_keyword)
        self.remove_keyword_btn.clicked.connect(self._remove_keyword)
        self.preview_btn.clicked.connect(self._request_preview)
        self.reset_btn.clicked.connect(self._reset_config)
        self.apply_btn.clicked.connect(self._apply_config)
    
    def update_for_sheet(self, sheet_name: str, sheet_config):
        """
        更新为指定Sheet的配置

        Args:
            sheet_name: Sheet名称
            sheet_config: Sheet配置对象
        """
        self.current_sheet_name = sheet_name
        self.current_config = sheet_config

        # 更新Sheet信息显示
        self.sheet_info_label.setText(f"当前Sheet: {sheet_name}")

        # 尝试从配置管理器加载最新配置
        try:
            sheet_manager = self._get_sheet_config_manager()
            if sheet_manager:
                # 从配置管理器获取最新配置
                latest_config = sheet_manager.get_or_create_config(sheet_name)
                if latest_config:
                    sheet_config = latest_config
                    self.current_config = latest_config
        except Exception as e:
            self.logger.warning(f"从配置管理器加载配置失败，使用传入配置: {e}")

        # 更新UI控件值
        self._update_ui_from_config(sheet_config)

        # 更新预览信息
        self._update_preview_info()

        # 重新加载模板列表（可能有新模板）
        self._load_template_list()

        self.logger.info(f"数据处理配置已切换到Sheet: {sheet_name}")
    
    def _update_ui_from_config(self, config):
        """从配置更新UI控件"""
        if not config:
            return
        
        # 阻止信号发射，避免循环更新
        self.blockSignals(True)
        
        try:
            # 数据范围设置
            self.header_row_spin.setValue(config.header_row)
            self.data_start_spin.setValue(config.data_start_row)
            self.data_end_spin.setValue(config.data_end_row or 0)
            self.auto_detect_header_check.setChecked(config.auto_detect_header)
            self.skip_empty_check.setChecked(config.skip_empty_rows)
            
            # 数据清洗设置
            self.trim_whitespace_check.setChecked(config.trim_whitespace)
            self.normalize_numbers_check.setChecked(config.normalize_numbers)
            self.handle_merged_cells_check.setChecked(config.handle_merged_cells)
            self.fill_empty_values_check.setChecked(config.fill_empty_values)
            
            # 汇总行处理
            self.remove_summary_check.setChecked(config.remove_summary_rows)
            
            # 更新关键词列表
            self.summary_keywords_list.clear()
            for keyword in config.summary_keywords:
                item = QListWidgetItem(keyword)
                item.setFlags(item.flags() | Qt.ItemIsEditable)
                self.summary_keywords_list.addItem(item)
                
        finally:
            self.blockSignals(False)
    
    def _on_config_changed(self):
        """配置变化处理"""
        if not self.current_config:
            return
        
        # 获取当前配置
        config_dict = self._get_current_config_dict()
        
        # 发射配置变化信号
        self.config_changed.emit(config_dict)
        
        # 更新预览信息
        self._update_preview_info()
    
    def _get_current_config_dict(self) -> dict:
        """获取当前配置字典"""
        # 获取汇总关键词
        keywords = []
        for i in range(self.summary_keywords_list.count()):
            item = self.summary_keywords_list.item(i)
            if item and item.text().strip():
                keywords.append(item.text().strip())
        
        return {
            'header_row': self.header_row_spin.value(),
            'data_start_row': self.data_start_spin.value(),
            'data_end_row': self.data_end_spin.value() if self.data_end_spin.value() > 0 else None,
            'auto_detect_header': self.auto_detect_header_check.isChecked(),
            'skip_empty_rows': self.skip_empty_check.isChecked(),
            'trim_whitespace': self.trim_whitespace_check.isChecked(),
            'normalize_numbers': self.normalize_numbers_check.isChecked(),
            'handle_merged_cells': self.handle_merged_cells_check.isChecked(),
            'fill_empty_values': self.fill_empty_values_check.isChecked(),
            'remove_summary_rows': self.remove_summary_check.isChecked(),
            'summary_keywords': keywords
        }
    
    def _add_keyword(self):
        """添加汇总关键词"""
        from PyQt5.QtWidgets import QInputDialog
        
        keyword, ok = QInputDialog.getText(self, "添加关键词", "请输入汇总关键词:")
        if ok and keyword.strip():
            item = QListWidgetItem(keyword.strip())
            item.setFlags(item.flags() | Qt.ItemIsEditable)
            self.summary_keywords_list.addItem(item)
            self._on_config_changed()
    
    def _remove_keyword(self):
        """删除选中的汇总关键词"""
        current_item = self.summary_keywords_list.currentItem()
        if current_item:
            row = self.summary_keywords_list.row(current_item)
            self.summary_keywords_list.takeItem(row)
            self._on_config_changed()
    
    def _request_preview(self):
        """请求预览"""
        if self.current_sheet_name:
            self.preview_requested.emit(self.current_sheet_name)
    
    def _reset_config(self):
        """重置配置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            f"确定要重置Sheet '{self.current_sheet_name}' 的数据处理配置吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 重置为默认配置
            from src.modules.data_import.sheet_config_manager import SheetImportConfig
            default_config = SheetImportConfig(sheet_name=self.current_sheet_name)
            self._update_ui_from_config(default_config)
            self._on_config_changed()
    
    def _apply_config(self):
        """应用配置"""
        if not self.current_config:
            return

        config_dict = self._get_current_config_dict()
        self.config_changed.emit(config_dict)

        # 保存到配置管理器
        self._save_current_config()

        # 显示成功消息
        self.preview_info_label.setText(f"✅ 配置已应用到Sheet '{self.current_sheet_name}'")
        self.preview_info_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #E8F5E8;
                border-radius: 4px;
                color: #2E7D32;
                font-weight: bold;
            }
        """)
    
    def _update_preview_info(self):
        """更新预览信息"""
        if not self.current_sheet_name:
            self.preview_info_label.setText("请选择Sheet后查看预览效果")
            return
        
        # 构建预览信息
        info_lines = [
            f"Sheet: {self.current_sheet_name}",
            f"表头行: 第{self.header_row_spin.value()}行",
            f"数据范围: 第{self.data_start_spin.value()}行开始",
        ]
        
        if self.data_end_spin.value() > 0:
            info_lines.append(f"数据结束: 第{self.data_end_spin.value()}行")
        else:
            info_lines.append("数据结束: 到文件末尾")
        
        if self.remove_summary_check.isChecked():
            keyword_count = self.summary_keywords_list.count()
            info_lines.append(f"将移除汇总行 (关键词: {keyword_count}个)")
        
        self.preview_info_label.setText("\n".join(info_lines))
        self.preview_info_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 4px;
                color: #333;
            }
        """)

    def _create_smart_features_section(self, parent_layout):
        """创建智能功能区域"""
        # 智能功能组
        smart_group = QGroupBox("🧠 智能配置助手")
        smart_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #3498db;
            }
        """)

        smart_layout = QHBoxLayout(smart_group)

        # 智能推荐按钮
        self.smart_recommend_btn = QPushButton("🎯 智能推荐配置")
        self.smart_recommend_btn.setToolTip("根据Sheet名称和内容自动推荐最佳配置")
        self.smart_recommend_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.smart_recommend_btn.clicked.connect(self._apply_smart_recommendation)

        # 模板选择下拉框
        self.template_combo = QComboBox()
        self.template_combo.setToolTip("选择预定义的配置模板")
        self.template_combo.setMinimumWidth(200)
        self.template_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 12px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)

        # 应用模板按钮
        self.apply_template_btn = QPushButton("📋 应用模板")
        self.apply_template_btn.setToolTip("应用选中的配置模板")
        self.apply_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.apply_template_btn.clicked.connect(self._apply_selected_template)

        # 保存为模板按钮
        self.save_template_btn = QPushButton("💾 保存为模板")
        self.save_template_btn.setToolTip("将当前配置保存为模板")
        self.save_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
        """)
        self.save_template_btn.clicked.connect(self._save_as_template)

        # 添加到布局
        smart_layout.addWidget(self.smart_recommend_btn)
        smart_layout.addWidget(QLabel("模板:"))
        smart_layout.addWidget(self.template_combo)
        smart_layout.addWidget(self.apply_template_btn)
        smart_layout.addWidget(self.save_template_btn)
        smart_layout.addStretch()

        parent_layout.addWidget(smart_group)

        # 加载模板列表
        self._load_template_list()

    def _load_template_list(self):
        """加载模板列表"""
        try:
            # 获取Sheet配置管理器
            sheet_manager = self._get_sheet_config_manager()
            if not sheet_manager:
                return

            # 清空现有项目
            self.template_combo.clear()
            self.template_combo.addItem("-- 选择模板 --", None)

            # 获取所有模板
            templates = sheet_manager.template_manager.get_all_templates()

            # 按分类分组
            builtin_templates = [t for t in templates if t.category == 'builtin']
            user_templates = [t for t in templates if t.category == 'user']

            # 添加内置模板
            if builtin_templates:
                self.template_combo.addItem("--- 内置模板 ---", None)
                for template in builtin_templates:
                    display_name = f"📋 {template.name}"
                    self.template_combo.addItem(display_name, template.id)

            # 添加用户模板
            if user_templates:
                self.template_combo.addItem("--- 用户模板 ---", None)
                for template in user_templates:
                    display_name = f"👤 {template.name}"
                    self.template_combo.addItem(display_name, template.id)

            self.logger.info(f"加载了 {len(templates)} 个模板")

        except Exception as e:
            self.logger.error(f"加载模板列表失败: {e}")

    def _get_sheet_config_manager(self):
        """获取Sheet配置管理器"""
        try:
            # 从父窗口获取导入管理器
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'import_manager'):
                parent_window = parent_window.parent()

            if parent_window and hasattr(parent_window, 'import_manager'):
                return getattr(parent_window.import_manager, 'sheet_config_manager', None)

            return None
        except Exception as e:
            self.logger.error(f"获取Sheet配置管理器失败: {e}")
            return None

    def _apply_smart_recommendation(self):
        """应用智能推荐配置"""
        try:
            if not self.current_sheet_name:
                QMessageBox.warning(self, "提示", "请先选择一个Sheet")
                return

            sheet_manager = self._get_sheet_config_manager()
            if not sheet_manager:
                QMessageBox.warning(self, "错误", "无法获取配置管理器")
                return

            # 获取智能推荐
            recommendation = sheet_manager.get_smart_recommendation(self.current_sheet_name)

            # 显示推荐信息
            info_text = f"智能推荐结果 (置信度: {recommendation.confidence:.1%}):\n\n"
            info_text += "推荐理由:\n" + "\n".join(f"• {reason}" for reason in recommendation.reasons)

            if recommendation.warnings:
                info_text += "\n\n⚠️ 警告:\n" + "\n".join(f"• {warning}" for warning in recommendation.warnings)

            if recommendation.suggestions:
                info_text += "\n\n💡 建议:\n" + "\n".join(f"• {suggestion}" for suggestion in recommendation.suggestions)

            # 询问是否应用
            reply = QMessageBox.question(
                self, "智能推荐配置",
                info_text + "\n\n是否应用此推荐配置？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 应用推荐配置
                success = sheet_manager.apply_smart_recommendation(self.current_sheet_name)
                if success:
                    # 刷新界面
                    self._load_config_from_manager()
                    QMessageBox.information(self, "成功", "智能推荐配置已应用")
                else:
                    QMessageBox.warning(self, "失败", "应用智能推荐配置失败")

        except Exception as e:
            self.logger.error(f"应用智能推荐失败: {e}")
            QMessageBox.critical(self, "错误", f"智能推荐功能出错: {e}")

    def _apply_selected_template(self):
        """应用选中的模板"""
        try:
            template_id = self.template_combo.currentData()
            if not template_id:
                QMessageBox.warning(self, "提示", "请先选择一个模板")
                return

            if not self.current_sheet_name:
                QMessageBox.warning(self, "提示", "请先选择一个Sheet")
                return

            sheet_manager = self._get_sheet_config_manager()
            if not sheet_manager:
                QMessageBox.warning(self, "错误", "无法获取配置管理器")
                return

            # 获取模板信息
            template = sheet_manager.template_manager.get_template(template_id)
            if not template:
                QMessageBox.warning(self, "错误", "模板不存在")
                return

            # 询问是否应用
            reply = QMessageBox.question(
                self, "应用模板",
                f"是否将模板 '{template.name}' 应用到Sheet '{self.current_sheet_name}'？\n\n"
                f"模板描述: {template.description}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 应用模板
                success = sheet_manager.apply_template_to_sheet(self.current_sheet_name, template_id)
                if success:
                    # 刷新界面
                    self._load_config_from_manager()
                    QMessageBox.information(self, "成功", f"模板 '{template.name}' 已应用")
                else:
                    QMessageBox.warning(self, "失败", "应用模板失败")

        except Exception as e:
            self.logger.error(f"应用模板失败: {e}")
            QMessageBox.critical(self, "错误", f"应用模板出错: {e}")

    def _save_as_template(self):
        """保存当前配置为模板"""
        try:
            if not self.current_sheet_name:
                QMessageBox.warning(self, "提示", "请先选择一个Sheet")
                return

            sheet_manager = self._get_sheet_config_manager()
            if not sheet_manager:
                QMessageBox.warning(self, "错误", "无法获取配置管理器")
                return

            # 获取模板名称和描述
            from PyQt5.QtWidgets import QInputDialog

            template_name, ok = QInputDialog.getText(
                self, "保存为模板",
                f"请输入模板名称:\n(基于Sheet: {self.current_sheet_name})"
            )

            if not ok or not template_name.strip():
                return

            template_desc, ok = QInputDialog.getText(
                self, "保存为模板",
                "请输入模板描述:"
            )

            if not ok:
                template_desc = f"基于Sheet '{self.current_sheet_name}' 创建的模板"

            # 保存当前配置到管理器
            self._save_current_config()

            # 创建模板
            template_id = sheet_manager.create_template_from_sheet(
                sheet_name=self.current_sheet_name,
                template_name=template_name.strip(),
                description=template_desc.strip(),
                tags=[self.current_sheet_name, "用户创建"]
            )

            if template_id:
                # 重新加载模板列表
                self._load_template_list()
                QMessageBox.information(self, "成功", f"模板 '{template_name}' 已保存")
            else:
                QMessageBox.warning(self, "失败", "保存模板失败")

        except Exception as e:
            self.logger.error(f"保存模板失败: {e}")
            QMessageBox.critical(self, "错误", f"保存模板出错: {e}")

    def _load_config_from_manager(self):
        """从配置管理器加载配置"""
        try:
            if not self.current_sheet_name:
                return

            sheet_manager = self._get_sheet_config_manager()
            if not sheet_manager:
                return

            # 获取配置
            config = sheet_manager.get_or_create_config(self.current_sheet_name)

            # 更新界面
            self.header_row_spin.setValue(config.header_row)
            self.data_start_spin.setValue(config.data_start_row)

            if hasattr(config, 'data_end_row') and config.data_end_row:
                self.data_end_spin.setValue(config.data_end_row)

            self.skip_empty_check.setChecked(config.skip_empty_rows)
            self.remove_summary_check.setChecked(config.remove_summary_rows)

            # 更新汇总关键词
            if hasattr(config, 'summary_keywords') and config.summary_keywords:
                self.summary_keywords_list.clear()
                for keyword in config.summary_keywords:
                    item = QListWidgetItem(keyword)
                    self.summary_keywords_list.addItem(item)

            self.logger.info(f"从配置管理器加载配置: {self.current_sheet_name}")

        except Exception as e:
            self.logger.error(f"从配置管理器加载配置失败: {e}")

    def _save_current_config(self):
        """保存当前配置到管理器"""
        try:
            if not self.current_sheet_name:
                return

            sheet_manager = self._get_sheet_config_manager()
            if not sheet_manager:
                return

            # 收集当前配置
            config_updates = {
                'header_row': self.header_row_spin.value(),
                'data_start_row': self.data_start_spin.value(),
                'data_end_row': self.data_end_spin.value() if self.data_end_spin.value() > 0 else None,
                'skip_empty_rows': self.skip_empty_check.isChecked(),
                'remove_summary_rows': self.remove_summary_check.isChecked(),
                'summary_keywords': [
                    self.summary_keywords_list.item(i).text()
                    for i in range(self.summary_keywords_list.count())
                ]
            }

            # 更新配置
            sheet_manager.update_config(self.current_sheet_name, **config_updates)

            self.logger.info(f"保存配置到管理器: {self.current_sheet_name}")

        except Exception as e:
            self.logger.error(f"保存配置到管理器失败: {e}")
