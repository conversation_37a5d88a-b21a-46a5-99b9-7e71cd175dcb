#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI集成测试脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from src.gui.unified_data_import_window import UnifiedDataImportWindow

def test_gui_integration():
    """测试GUI集成"""
    print("🚀 测试GUI集成")
    
    app = QApplication([])
    
    try:
        # 创建导入窗口
        print("📋 创建统一数据导入窗口...")
        window = UnifiedDataImportWindow()
        
        # 检查导入管理器
        print("🔧 检查导入管理器...")
        if hasattr(window, 'import_manager'):
            print("  ✅ 导入管理器存在")
            
            # 检查Sheet配置管理器
            if hasattr(window.import_manager, 'sheet_config_manager'):
                print("  ✅ Sheet配置管理器已集成")
                
                # 检查第二阶段功能
                sheet_manager = window.import_manager.sheet_config_manager
                
                # 检查智能推荐
                if hasattr(sheet_manager, 'smart_recommender'):
                    print("  ✅ 智能推荐引擎已集成")
                
                # 检查模板管理器
                if hasattr(sheet_manager, 'template_manager'):
                    print("  ✅ 配置模板管理器已集成")
                    templates = sheet_manager.template_manager.get_all_templates()
                    print(f"    📋 可用模板数量: {len(templates)}")
                
                # 检查批量管理器
                if hasattr(sheet_manager, 'batch_manager'):
                    print("  ✅ 批量配置管理器已集成")
            else:
                print("  ❌ Sheet配置管理器未集成")
        else:
            print("  ❌ 导入管理器不存在")
        
        # 检查数据处理组件
        print("🧹 检查数据处理组件...")
        if hasattr(window, 'processing_tab'):
            print("  ✅ 数据处理选项卡存在")
            
            # 检查智能功能按钮
            if hasattr(window.processing_tab, 'smart_recommend_btn'):
                print("  ✅ 智能推荐按钮已添加")
            
            if hasattr(window.processing_tab, 'template_combo'):
                print("  ✅ 模板选择下拉框已添加")
            
            if hasattr(window.processing_tab, 'apply_template_btn'):
                print("  ✅ 应用模板按钮已添加")
            
            if hasattr(window.processing_tab, 'save_template_btn'):
                print("  ✅ 保存模板按钮已添加")
        else:
            print("  ❌ 数据处理选项卡不存在")
        
        print("\n✅ GUI集成测试完成")
        print("💡 现在可以点击'导入数据'按钮测试新功能了！")
        
        # 显示窗口进行手动测试
        window.show()
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_gui_integration())
