"""
数据导入工作线程
实现异步数据导入，避免UI阻塞，提供详细进度反馈
"""

import sys
import logging
from typing import Dict, List, Optional, Any
from PyQt5.QtCore import QThread, pyqtSignal, QObject
from pathlib import Path

from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.utils.log_config import setup_logger


class DataImportWorker(QThread):
    """数据导入工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度值(0-100), 状态描述
    status_updated = pyqtSignal(str)  # 状态更新
    import_completed = pyqtSignal(bool, dict)  # 导入完成(成功标志, 结果数据)
    error_occurred = pyqtSignal(str)  # 错误发生
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 导入参数
        self.file_path = None
        self.year = None
        self.month = None
        self.target_table = None
        self.target_path = None
        self.selected_sheets = []
        self.mapping_config = {}
        self.multi_sheet_importer = None
        
        # 控制标志
        self._should_stop = False
    
    def setup_import(self, file_path: str, year: int, month: int, 
                    target_table: Optional[str] = None,
                    target_path: Optional[str] = None,
                    selected_sheets: List[str] = None,
                    mapping_config: Dict = None,
                    multi_sheet_importer: MultiSheetImporter = None):
        """设置导入参数"""
        self.file_path = file_path
        self.year = year
        self.month = month
        self.target_table = target_table
        self.target_path = target_path
        self.selected_sheets = selected_sheets or []
        self.mapping_config = mapping_config or {}
        self.multi_sheet_importer = multi_sheet_importer
        
        self.logger.info(f"导入工作线程配置完成: {Path(file_path).name}")
    
    def stop_import(self):
        """停止导入"""
        self._should_stop = True
        self.logger.info("收到停止导入请求")
    
    def run(self):
        """执行数据导入"""
        try:
            if not self.multi_sheet_importer:
                self.error_occurred.emit("MultiSheetImporter未初始化")
                return
            
            self.logger.info("开始异步数据导入")
            self.progress_updated.emit(0, "开始导入...")
            
            # 检查是否被取消
            if self._should_stop:
                self.logger.info("导入被用户取消")
                return
            
            # 阶段1：文件验证 (10%)
            self.progress_updated.emit(10, "验证文件...")
            file_path = Path(self.file_path)
            
            if not file_path.exists():
                self.error_occurred.emit(f"文件不存在: {file_path}")
                return
            
            # 检查是否被取消
            if self._should_stop:
                return
            
            # 阶段2：准备导入 (20%)
            self.progress_updated.emit(20, "准备导入数据...")
            
            # 执行导入
            result = self.multi_sheet_importer.import_excel_file(
                file_path=self.file_path,
                year=self.year,
                month=self.month,
                target_table=self.target_table,
                target_path=self.target_path
            )
            
            # 检查是否被取消
            if self._should_stop:
                return
            
            # 阶段3：处理结果 (90%)
            self.progress_updated.emit(90, "处理导入结果...")
            
            if result and result.get('success', False):
                self.progress_updated.emit(100, "导入完成")
                self.import_completed.emit(True, result)
                self.logger.info("异步数据导入成功完成")
            else:
                error_msg = result.get('error', '未知错误') if result else '导入失败'
                self.error_occurred.emit(f"导入失败: {error_msg}")
                self.logger.error(f"异步数据导入失败: {error_msg}")
        
        except Exception as e:
            error_msg = f"导入过程中发生异常: {str(e)}"
            self.error_occurred.emit(error_msg)
            self.logger.error(error_msg)
