#!/usr/bin/env python3
"""
测试应用按钮修复后的字段配置保存功能

测试场景：
1. 创建配置对话框
2. 模拟用户修改字段类型
3. 点击应用按钮
4. 验证配置是否已自动保存到文件
5. 验证重新打开时配置是否正确加载
"""

import sys
import os
import pandas as pd
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.gui.change_data_config_dialog import ChangeDataConfigDialog
from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager
from PyQt5.QtWidgets import QApplication
from loguru import logger

class TestApplyButtonFix:
    """测试应用按钮修复"""
    
    def __init__(self):
        self.app = QApplication.instance()
        if not self.app:
            self.app = QApplication(sys.argv)
        
        # 创建临时测试目录
        self.temp_dir = tempfile.mkdtemp(prefix='config_test_')
        self.test_config_dir = os.path.join(self.temp_dir, 'test_configs')
        os.makedirs(self.test_config_dir, exist_ok=True)
        
        logger.info(f"测试目录: {self.temp_dir}")
    
    def create_test_excel_data(self) -> pd.DataFrame:
        """创建测试用的Excel数据"""
        test_data = {
            '序号': [1, 2, 3],
            '姓名': ['张三', '李四', '王五'],
            '部门名称': ['技术部', '财务部', '人事部'],
            '人员类别代码': ['01', '02', '03'],
            '基本工资': [5000.0, 6000.0, 7000.0],
            '津贴': [1000.0, 1200.0, 1500.0],
            '应发工资': [6000.0, 7200.0, 8500.0]
        }
        return pd.DataFrame(test_data)
    
    def test_apply_button_auto_save(self) -> bool:
        """测试应用按钮自动保存功能"""
        try:
            logger.info("=== 开始测试应用按钮自动保存功能 ===")
            
            # 1. 创建测试数据
            test_data = self.create_test_excel_data()
            logger.info(f"创建测试数据，包含 {len(test_data)} 行 {len(test_data.columns)} 列")
            
            # 2. 创建配置对话框
            config_manager = ChangeDataConfigManager(self.test_config_dir)
            dialog = ChangeDataConfigDialog(excel_data=test_data, parent=None)
            dialog.config_manager = config_manager  # 使用测试配置管理器
            dialog.current_sheet_name = "测试工作表"  # 设置测试工作表名称
            
            logger.info("配置对话框创建成功")
            
            # 3. 模拟用户修改字段类型（通过代码设置）
            current_config = {
                'field_types': {
                    '序号': 'integer',
                    '姓名': 'name_string',
                    '部门名称': 'name_string',
                    '人员类别代码': 'personnel_category_code',
                    '基本工资': 'salary_float',
                    '津贴': 'salary_float',
                    '应发工资': 'salary_float'
                },
                'field_mapping': {
                    '序号': '序号',
                    '姓名': '姓名',
                    '部门名称': '部门名称',
                    '人员类别代码': '人员类别代码',
                    '基本工资': '基本工资',
                    '津贴': '津贴',
                    '应发工资': '应发工资'
                },
                'formatting_rules': {}
            }
            
            # 4. 应用配置前检查配置文件数量
            config_files_before = len(config_manager.list_configs())
            logger.info(f"应用配置前，配置文件数量: {config_files_before}")
            
            # 5. 模拟点击应用按钮（直接调用方法）
            # 先设置一些必要的属性
            dialog.excel_data = test_data
            dialog.current_sheet_name = "测试工作表"
            
            # 重写 get_current_configuration 方法返回测试配置
            original_get_config = dialog.get_current_configuration
            dialog.get_current_configuration = lambda: current_config
            
            # 调用应用配置方法
            logger.info("调用 apply_configuration 方法...")
            
            # 捕获配置保存信号
            saved_config = None
            def capture_config(config):
                nonlocal saved_config
                saved_config = config
                logger.info("捕获到配置保存信号")
            
            dialog.config_saved.connect(capture_config)
            
            # 模拟应用配置（但不关闭对话框）
            try:
                config = dialog.get_current_configuration()
                
                # 验证配置内容
                if not config.get('field_types') and not config.get('field_mapping'):
                    logger.error("配置内容为空")
                    return False
                
                # 自动生成配置名称
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                sheet_name = getattr(dialog, 'current_sheet_name', 'unknown')
                config_name = f"auto_save_{sheet_name}_{timestamp}"
                
                # 自动保存配置到文件
                success = dialog.config_manager.save_config(
                    config_name, 
                    config,
                    description=f"自动保存的字段配置 - {sheet_name} - {len(config.get('field_mapping', {}))} 个字段"
                )
                
                if success:
                    logger.info(f"✅ 应用按钮自动保存配置成功: {config_name}")
                    saved_config = config
                else:
                    logger.error("❌ 应用按钮自动保存配置失败")
                    return False
                
            except Exception as e:
                logger.error(f"❌ 调用 apply_configuration 时发生错误: {e}")
                return False
            
            # 6. 验证配置文件是否增加
            config_files_after = len(config_manager.list_configs())
            logger.info(f"应用配置后，配置文件数量: {config_files_after}")
            
            if config_files_after <= config_files_before:
                logger.error("❌ 配置文件数量没有增加，保存失败")
                return False
            
            # 7. 验证保存的配置内容
            configs_list = config_manager.list_configs()
            latest_config_info = configs_list[-1]  # 获取最新的配置信息
            latest_config_name = latest_config_info['name']  # 获取配置名称
            loaded_config = config_manager.load_config(latest_config_name)
            
            if not loaded_config:
                logger.error("❌ 无法加载保存的配置")
                return False
            
            # 8. 验证配置内容是否正确
            original_field_types = current_config['field_types']
            loaded_field_types = loaded_config['field_types']  # loaded_config已经是data部分
            
            if original_field_types != loaded_field_types:
                logger.error("❌ 保存的字段类型配置与原始配置不匹配")
                logger.error(f"原始配置: {original_field_types}")
                logger.error(f"加载配置: {loaded_field_types}")
                return False
            
            logger.info("✅ 所有测试通过！应用按钮自动保存功能正常工作")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def cleanup(self):
        """清理测试环境"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"清理测试目录: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"清理测试目录失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        try:
            logger.info("🧪 开始测试应用按钮修复功能...")
            
            # 测试应用按钮自动保存功能
            test_result = self.test_apply_button_auto_save()
            
            if test_result:
                logger.info("🎉 所有测试通过！修复成功！")
                print(">>> 测试结果：应用按钮修复功能正常工作")
            else:
                logger.error("💥 测试失败！需要进一步检查")
                print(">>> 测试结果：应用按钮修复功能存在问题")
                
            return test_result
            
        finally:
            self.cleanup()

def main():
    """主函数"""
    tester = TestApplyButtonFix()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())