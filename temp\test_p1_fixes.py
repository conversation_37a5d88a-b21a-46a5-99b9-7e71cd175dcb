#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试P1级问题修复效果
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import pandas as pd
from loguru import logger

def test_config_manager():
    """测试配置管理器"""
    from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager
    
    manager = ChangeDataConfigManager()
    
    # 测试模板列表
    templates = manager.list_templates()
    print(f"✓ 模板数量: {len(templates)}")
    for t in templates:
        print(f"  - {t['name']} ({t['key']})")
    
    # 测试配置列表
    configs = manager.list_configs()
    print(f"\n✓ 配置数量: {len(configs)}")
    for c in configs:
        print(f"  - {c['name']}")
    
    return True

def test_formatting_engine():
    """测试格式化引擎"""
    from src.modules.data_import.formatting_engine import get_formatting_engine
    
    engine = get_formatting_engine()
    
    # 测试字段类型
    field_types = engine.get_field_types()
    print(f"\n✓ 字段类型数量: {len(field_types)}")
    for type_id, info in list(field_types.items())[:5]:  # 只显示前5个
        print(f"  - {info['name']} ({type_id})")
    
    return True

def test_field_type_manager():
    """测试字段类型管理器"""
    from src.modules.data_import.field_type_manager import FieldTypeManager
    
    manager = FieldTypeManager()
    
    # 测试自定义字段类型
    custom_types = manager.list_custom_field_types()
    print(f"\n✓ 自定义字段类型数量: {len(custom_types)}")
    
    # 测试获取所有字段类型
    all_types = manager.get_all_field_types()
    print(f"✓ 所有字段类型数量: {len(all_types)}")
    
    return True

def test_dialog_initialization():
    """测试对话框初始化（不显示GUI）"""
    from PyQt5.QtWidgets import QApplication
    import sys
    
    # 创建应用实例
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    try:
        from src.gui.change_data_config_dialog import ChangeDataConfigDialog
        
        # 创建对话框实例（不显示）
        dialog = ChangeDataConfigDialog()
        
        # 检查下拉框是否已填充
        config_count = dialog.config_combo.count()
        template_count = dialog.template_combo.count()
        
        print(f"\n✓ 配置下拉框项数: {config_count}")
        print(f"✓ 模板下拉框项数: {template_count}")
        
        # 检查字段表格行高
        row_height = dialog.field_table.verticalHeader().defaultSectionSize()
        print(f"✓ 字段表格行高: {row_height}px")
        
        return True
        
    except Exception as e:
        print(f"✗ 对话框初始化失败: {e}")
        return False
    finally:
        if app:
            app.quit()

def main():
    """主测试函数"""
    print("=" * 50)
    print("P1级别修复测试")
    print("=" * 50)
    
    tests = [
        ("配置管理器", test_config_manager),
        ("格式化引擎", test_formatting_engine),
        ("字段类型管理器", test_field_type_manager),
        ("对话框初始化", test_dialog_initialization)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n测试: {name}")
        print("-" * 30)
        try:
            success = test_func()
            results.append((name, success))
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            results.append((name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    failed = 0
    for name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{name}: {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("\n✅ 所有P1级别修复测试通过！")
    else:
        print(f"\n⚠️ {failed} 个测试失败，需要进一步修复")

if __name__ == "__main__":
    main()