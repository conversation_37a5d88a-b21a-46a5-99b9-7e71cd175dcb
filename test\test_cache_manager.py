#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存管理器单元测试
"""

import unittest
import pandas as pd
from pathlib import Path
import sys

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.gui.widgets.pagination_cache_manager import PaginationCacheManager, CacheEntry

class TestPaginationCacheManager(unittest.TestCase):
    """分页缓存管理器测试"""
    
    def setUp(self):
        """测试初始化"""
        self.cache_manager = PaginationCacheManager(max_cache_entries=10, max_memory_mb=10)
        
    def tearDown(self):
        """测试清理"""
        self.cache_manager.clear_cache()
        
    def test_put_and_get_page_data(self):
        """测试缓存存取"""
        # 创建测试数据
        test_data = pd.DataFrame({
            'id': [1, 2, 3],
            'name': ['<PERSON>', '<PERSON>', '<PERSON>']
        })
        
        # 存储数据
        self.cache_manager.put_page_data('test_table', 1, 10, test_data)
        
        # 获取数据
        cached_data = self.cache_manager.get_page_data('test_table', 1, 10)
        
        # 验证
        self.assertIsNotNone(cached_data)
        pd.testing.assert_frame_equal(cached_data, test_data)
        
    def test_cache_expiration(self):
        """测试缓存过期"""
        # 设置短TTL
        self.cache_manager.ttl_seconds = 0.1
        
        # 存储数据
        test_data = pd.DataFrame({'id': [1]})
        self.cache_manager.put_page_data('test_table', 1, 10, test_data)
        
        # 等待过期
        import time
        time.sleep(0.2)
        
        # 验证已过期
        cached_data = self.cache_manager.get_page_data('test_table', 1, 10)
        self.assertIsNone(cached_data)
        
    def test_cache_statistics(self):
        """测试缓存统计"""
        # 添加一些数据
        test_data = pd.DataFrame({'id': [1]})
        self.cache_manager.put_page_data('test_table', 1, 10, test_data)
        
        # 命中
        self.cache_manager.get_page_data('test_table', 1, 10)
        
        # 未命中
        self.cache_manager.get_page_data('test_table', 2, 10)
        
        # 获取统计
        stats = self.cache_manager.get_statistics()
        
        # 验证
        self.assertEqual(stats['hits'], 1)
        self.assertEqual(stats['misses'], 1)
        self.assertEqual(stats['hit_rate'], 0.5)
        
    def test_lru_eviction(self):
        """测试LRU淘汰"""
        # 设置小容量
        self.cache_manager = PaginationCacheManager(max_cache_entries=2)
        
        # 添加3个条目（触发淘汰）
        for i in range(3):
            data = pd.DataFrame({'id': [i]})
            self.cache_manager.put_page_data('test_table', i, 10, data)
        
        # 验证第一个被淘汰
        cached_data = self.cache_manager.get_page_data('test_table', 0, 10)
        self.assertIsNone(cached_data)
        
        # 验证后两个还在
        cached_data = self.cache_manager.get_page_data('test_table', 1, 10)
        self.assertIsNotNone(cached_data)
        cached_data = self.cache_manager.get_page_data('test_table', 2, 10)
        self.assertIsNotNone(cached_data)

if __name__ == '__main__':
    unittest.main()
