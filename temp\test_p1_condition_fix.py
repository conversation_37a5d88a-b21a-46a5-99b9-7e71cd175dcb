"""
测试P1级别条件判断逻辑修复
验证字段格式化不会出现类型转换错误
"""

import sys
import os
import io
import pandas as pd

# 设置输出编码
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_field_formatting():
    """测试字段格式化逻辑"""
    from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager
    from src.modules.data_import.formatting_engine import get_formatting_engine
    from src.modules.data_import.field_type_manager import FieldTypeManager
    
    print("=" * 50)
    print("测试字段格式化逻辑")
    print("=" * 50)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '工号': ['001', '002', '003'],
        '姓名': ['张三', '李四', '王五'],
        '部门名称': ['财务部', '人事部', '技术部'],
        '岗位工资': [5000.0, 6000.0, 7000.0],
        '人员类别': ['管理人员', '技术人员', '行政人员']
    })
    
    # 测试各种字段类型的格式化
    test_cases = [
        ('姓名', 'name_string', '姓名 (name_string)'),
        ('工号', 'employee_id_string', '工号 (employee_id_string)'),
        ('部门名称', 'text_string', '文本字符串 (text_string)'),
        ('岗位工资', 'salary_float', '工资金额 (salary_float)'),
        ('人员类别', 'text_string', '文本字符串 (text_string)')
    ]
    
    errors = []
    success_count = 0
    
    for col_name, field_type, field_type_text in test_cases:
        try:
            formatted_data = test_data.copy()
            
            # 模拟条件判断逻辑
            if field_type == "salary_float" or (field_type_text and "工资" in field_type_text):
                # 金额格式化
                formatted_data[col_name] = formatted_data[col_name].apply(
                    lambda x: f"{float(x):,.2f}" if pd.notna(x) and str(x).strip() else ""
                )
                print(f"✓ {col_name}: 工资格式化成功")
                
            elif field_type == "employee_id_string" or (field_type_text and "工号" in field_type_text):
                # 工号格式化
                formatted_data[col_name] = formatted_data[col_name].apply(
                    lambda x: str(x).zfill(6) if pd.notna(x) else ""
                )
                print(f"✓ {col_name}: 工号格式化成功")
                
            elif field_type == "name_string" or (field_type_text and "姓名" in field_type_text):
                # 姓名格式化
                formatted_data[col_name] = formatted_data[col_name].apply(
                    lambda x: str(x).strip() if pd.notna(x) else ""
                )
                print(f"✓ {col_name}: 姓名格式化成功")
                
            else:
                # 默认文本格式化
                formatted_data[col_name] = formatted_data[col_name].apply(
                    lambda x: str(x).strip() if pd.notna(x) else ""
                )
                print(f"✓ {col_name}: 文本格式化成功")
                
            success_count += 1
            
        except Exception as e:
            error_msg = f"✗ {col_name} ({field_type}): {str(e)}"
            print(error_msg)
            errors.append(error_msg)
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"成功: {success_count}/{len(test_cases)}")
    print(f"失败: {len(errors)}/{len(test_cases)}")
    
    if errors:
        print("\n失败的测试：")
        for error in errors:
            print(f"  {error}")
        return False
    else:
        print("\n✅ 所有字段格式化测试通过！")
        return True

def test_condition_logic():
    """测试条件逻辑是否正确"""
    print("\n" + "=" * 50)
    print("测试条件逻辑")
    print("=" * 50)
    
    # 测试条件判断
    test_cases = [
        # (field_type, field_type_text, expected_branch)
        ("salary_float", None, "salary"),
        ("salary_float", "", "salary"),
        ("other", "工资金额 (salary_float)", "salary"),
        ("name_string", None, "name"),
        ("other", "姓名 (name_string)", "name"),
        ("text_string", "部门名称", "default"),
        ("", None, "default"),
        (None, None, "default")
    ]
    
    passed = 0
    failed = 0
    
    for field_type, field_type_text, expected in test_cases:
        actual = "unknown"
        
        if field_type == "salary_float" or (field_type_text and "工资" in field_type_text):
            actual = "salary"
        elif field_type == "name_string" or (field_type_text and "姓名" in field_type_text):
            actual = "name"
        else:
            actual = "default"
        
        if actual == expected:
            print(f"✓ ({field_type}, {field_type_text}) -> {actual}")
            passed += 1
        else:
            print(f"✗ ({field_type}, {field_type_text}) -> {actual} (期望: {expected})")
            failed += 1
    
    print(f"\n通过: {passed}/{len(test_cases)}")
    print(f"失败: {failed}/{len(test_cases)}")
    
    return failed == 0

def main():
    """主测试函数"""
    print("P1级别条件判断修复测试")
    print("=" * 50)
    
    # 运行测试
    test1_passed = test_field_formatting()
    test2_passed = test_condition_logic()
    
    # 总结
    print("\n" + "=" * 50)
    print("总体测试结果")
    print("=" * 50)
    
    if test1_passed and test2_passed:
        print("✅ P1级别条件判断逻辑修复验证通过！")
        print("说明：条件判断不再错误地将文本字段当作数字处理")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)