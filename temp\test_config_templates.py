#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置模板系统测试脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_template_manager import ConfigTemplateManager
from src.modules.data_import.sheet_config_manager import SheetConfigManager

def test_template_manager():
    """测试配置模板管理器"""
    print("=== 测试配置模板管理器 ===")
    
    manager = ConfigTemplateManager()
    
    # 1. 查看内置模板
    print("\n📋 内置模板:")
    builtin_templates = manager.get_templates_by_category('builtin')
    for template in builtin_templates:
        print(f"  - {template.name}: {template.description}")
        print(f"    标签: {', '.join(template.tags)}")
        print(f"    使用次数: {template.usage_count}")
    
    # 2. 创建用户模板
    print("\n📝 创建用户模板:")
    custom_config = {
        'header_row': 3,
        'data_start_row': 4,
        'has_header': True,
        'remove_summary_rows': True,
        'summary_keywords': ['总计', '合计'],
        'skip_empty_rows': True,
        'is_enabled': True
    }
    
    template_id = manager.create_template(
        name="自定义工资表模板",
        description="第3行为表头，第4行开始为数据的工资表",
        config_data=custom_config,
        tags=["工资表", "自定义", "第3行表头"]
    )
    print(f"  创建模板ID: {template_id}")
    
    # 3. 搜索模板
    print("\n🔍 搜索模板:")
    search_results = manager.search_templates("工资")
    for template in search_results:
        print(f"  - {template.name} (分类: {template.category})")
    
    # 4. 获取热门模板
    print("\n🔥 热门模板:")
    popular = manager.get_popular_templates(5)
    for template in popular:
        print(f"  - {template.name}: {template.usage_count} 次使用")
    
    # 5. 模板统计
    print("\n📊 模板统计:")
    summary = manager.get_template_summary()
    print(f"  总数: {summary['total_count']}")
    print(f"  内置: {summary['builtin_count']}")
    print(f"  用户: {summary['user_count']}")
    print(f"  分类: {', '.join(summary['categories'])}")
    print(f"  所有标签: {', '.join(summary['all_tags'])}")
    
    return manager, template_id

def test_template_application():
    """测试模板应用"""
    print("\n=== 测试模板应用 ===")
    
    # 创建Sheet配置管理器
    sheet_manager = SheetConfigManager()
    
    # 1. 获取推荐模板
    print("\n💡 获取推荐模板:")
    test_sheets = ["2024年1月工资表", "员工信息汇总", "数据说明"]
    
    for sheet_name in test_sheets:
        print(f"\n  Sheet: {sheet_name}")
        recommended = sheet_manager.get_recommended_templates(sheet_name)
        for template in recommended[:3]:  # 显示前3个
            print(f"    - {template.name} (使用次数: {template.usage_count})")
    
    # 2. 应用模板
    print("\n🎯 应用模板:")
    sheet_name = "测试工资表"
    
    # 获取标准工资表模板
    template_manager = sheet_manager.template_manager
    builtin_templates = template_manager.get_templates_by_category('builtin')
    salary_template = next((t for t in builtin_templates if '工资表' in t.name), None)
    
    if salary_template:
        print(f"  应用模板 '{salary_template.name}' 到 '{sheet_name}'")
        success = sheet_manager.apply_template_to_sheet(sheet_name, salary_template.id)
        print(f"  应用结果: {'成功' if success else '失败'}")
        
        # 查看应用后的配置
        config = sheet_manager.get_or_create_config(sheet_name)
        print(f"  配置结果:")
        print(f"    表头行: {config.header_row}")
        print(f"    数据起始行: {config.data_start_row}")
        print(f"    移除汇总行: {config.remove_summary_rows}")
        print(f"    跳过空行: {config.skip_empty_rows}")
    
    # 3. 从配置创建模板
    print("\n📋 从配置创建模板:")
    new_template_id = sheet_manager.create_template_from_sheet(
        sheet_name=sheet_name,
        template_name="测试模板",
        description="从测试工资表创建的模板",
        tags=["测试", "工资表"]
    )
    
    if new_template_id:
        print(f"  创建模板ID: {new_template_id}")
        new_template = template_manager.get_template(new_template_id)
        if new_template:
            print(f"  模板名称: {new_template.name}")
            print(f"  模板描述: {new_template.description}")
            print(f"  模板标签: {', '.join(new_template.tags)}")

def test_template_management():
    """测试模板管理功能"""
    print("\n=== 测试模板管理功能 ===")
    
    manager = ConfigTemplateManager()
    
    # 1. 收藏功能
    print("\n⭐ 测试收藏功能:")
    builtin_templates = manager.get_templates_by_category('builtin')
    if builtin_templates:
        template = builtin_templates[0]
        print(f"  切换模板 '{template.name}' 的收藏状态")
        
        # 切换收藏
        success = manager.toggle_favorite(template.id)
        print(f"  切换结果: {'成功' if success else '失败'}")
        
        # 查看收藏状态
        updated_template = manager.get_template(template.id)
        print(f"  当前收藏状态: {updated_template.is_favorite}")
        
        # 获取收藏列表
        favorites = manager.get_favorite_templates()
        print(f"  收藏模板数量: {len(favorites)}")
    
    # 2. 模板导出导入
    print("\n📤 测试导出导入:")
    if builtin_templates:
        template = builtin_templates[0]
        export_file = "temp/exported_template.json"
        
        # 导出模板
        success = manager.export_template(template.id, export_file)
        print(f"  导出模板 '{template.name}': {'成功' if success else '失败'}")
        
        if success:
            # 导入模板
            imported_id = manager.import_template(export_file)
            if imported_id:
                imported_template = manager.get_template(imported_id)
                print(f"  导入模板: {imported_template.name} (ID: {imported_id})")
                print(f"  导入分类: {imported_template.category}")
            
            # 清理导出文件
            try:
                os.remove(export_file)
            except:
                pass
    
    # 3. 模板更新
    print("\n✏️ 测试模板更新:")
    user_templates = manager.get_templates_by_category('user')
    if user_templates:
        template = user_templates[0]
        old_name = template.name
        
        # 更新模板
        success = manager.update_template(
            template.id,
            name=f"{old_name} (已更新)",
            description="这是一个更新后的模板描述"
        )
        print(f"  更新模板: {'成功' if success else '失败'}")
        
        if success:
            updated_template = manager.get_template(template.id)
            print(f"  新名称: {updated_template.name}")
            print(f"  新描述: {updated_template.description}")

def main():
    """主函数"""
    print("🚀 配置模板系统测试")
    print("=" * 50)
    
    try:
        # 测试模板管理器
        manager, template_id = test_template_manager()
        
        # 测试模板应用
        test_template_application()
        
        # 测试模板管理功能
        test_template_management()
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
