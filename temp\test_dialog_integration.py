"""
测试异动表配置对话框集成功能
验证修复后的功能是否正常工作
"""

import sys
import os
import io
import pandas as pd
from PyQt5.QtWidgets import QApplication

# 设置输出编码
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_dialog_with_data():
    """测试对话框与实际数据"""
    
    # 创建应用实例
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    try:
        from src.gui.change_data_config_dialog import ChangeDataConfigDialog
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '工号': ['001', '002', '003'],
            '姓名': ['张三', '李四', '王五'],
            '部门名称': ['财务部', '人事部', '技术部'],
            '岗位工资': [5000.0, 6000.0, 7000.0],
            '薪级工资': [2000.0, 2500.0, 3000.0],
            '津贴': [500.0, 600.0, 700.0],
            '补贴': [300.0, 400.0, 500.0],
            '奖金': [1000.0, 1500.0, 2000.0],
            '人员类别': ['管理人员', '技术人员', '行政人员']
        })
        
        print("=" * 50)
        print("测试异动表配置对话框集成")
        print("=" * 50)
        
        # 创建对话框实例（不显示）
        dialog = ChangeDataConfigDialog(excel_data=test_data)
        
        # 测试1：检查初始化
        print("\n测试1：对话框初始化")
        print("-" * 30)
        print(f"✓ 对话框创建成功")
        print(f"✓ 配置下拉框项数: {dialog.config_combo.count()}")
        print(f"✓ 模板下拉框项数: {dialog.template_combo.count()}")
        print(f"✓ 字段表格行数: {dialog.field_table.rowCount()}")
        
        # 测试2：测试字段类型下拉框
        print("\n测试2：字段类型下拉框")
        print("-" * 30)
        for i in range(min(3, dialog.field_table.rowCount())):
            field_name = dialog.field_table.item(i, 0).text()
            type_combo = dialog.field_table.cellWidget(i, 1)
            if type_combo:
                item_count = type_combo.count()
                current_text = type_combo.currentText()
                print(f"✓ {field_name}: {item_count}个选项, 当前: {current_text}")
        
        # 测试3：测试刷新预览（这会触发格式化逻辑）
        print("\n测试3：数据格式化预览")
        print("-" * 30)
        
        try:
            # 先设置一些字段类型
            for i in range(dialog.field_table.rowCount()):
                field_name = dialog.field_table.item(i, 0).text()
                type_combo = dialog.field_table.cellWidget(i, 1)
                
                if type_combo:
                    # 根据字段名设置合适的类型
                    if "工号" in field_name:
                        # 查找并设置工号类型
                        for j in range(type_combo.count()):
                            if type_combo.itemData(j) == "employee_id_string":
                                type_combo.setCurrentIndex(j)
                                break
                    elif "姓名" in field_name:
                        # 查找并设置姓名类型
                        for j in range(type_combo.count()):
                            if type_combo.itemData(j) == "name_string":
                                type_combo.setCurrentIndex(j)
                                break
                    elif "工资" in field_name or "津贴" in field_name or "补贴" in field_name or "奖金" in field_name:
                        # 查找并设置工资类型
                        for j in range(type_combo.count()):
                            if type_combo.itemData(j) == "salary_float":
                                type_combo.setCurrentIndex(j)
                                break
            
            # 刷新预览
            dialog.refresh_preview()
            print("✓ 数据格式化预览成功（无错误）")
            
        except Exception as e:
            print(f"✗ 数据格式化失败: {e}")
            return False
        
        # 测试4：测试模板应用
        print("\n测试4：模板应用")
        print("-" * 30)
        
        if dialog.template_combo.count() > 1:
            # 选择第一个模板（跳过默认选项）
            dialog.template_combo.setCurrentIndex(1)
            template_name = dialog.template_combo.currentText()
            print(f"✓ 选择模板: {template_name}")
            
            # 应用模板会自动触发apply_template
            # 由于信号连接，这里不需要手动调用
            print("✓ 模板选择完成")
        
        print("\n" + "=" * 50)
        print("集成测试完成")
        print("=" * 50)
        print("✅ 所有测试通过，对话框功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if app:
            app.quit()

def main():
    """主测试函数"""
    success = test_dialog_with_data()
    
    if success:
        print("\n✅ P1级别修复集成测试通过！")
        print("说明：")
        print("1. 条件判断逻辑已修复，不再出现类型转换错误")
        print("2. 字段格式化功能正常工作")
        print("3. 对话框可以正常初始化和使用")
    else:
        print("\n⚠️ 集成测试失败，需要进一步检查")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)