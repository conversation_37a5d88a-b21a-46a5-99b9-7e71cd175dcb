#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段类型配置组件

在统一数据导入窗口中提供字段类型的集中管理和配置功能。
支持内置类型和自定义类型的查看、编辑、导入导出等操作。
"""

import os
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTreeWidget, QTreeWidgetItem, QGroupBox, QLabel,
    QTextEdit, QLineEdit, QPushButton, QToolBar,
    QAction, QMessageBox, QFileDialog, QScrollArea,
    QFrame, QGridLayout, QFormLayout, QSpacerItem,
    QSizePolicy, QApplication
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QFont, QPalette

from src.modules.data_import.field_type_manager import FieldTypeManager
from src.modules.data_import.formatting_engine import get_formatting_engine
from src.modules.logging.setup_logger import setup_logger

logger = setup_logger(__name__)


class FieldTypeConfigWidget(QWidget):
    """字段类型配置组件"""
    
    # 信号定义
    field_type_changed = pyqtSignal(str)  # 字段类型变更信号
    field_type_deleted = pyqtSignal(str)  # 字段类型删除信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.field_type_manager = FieldTypeManager()
        self.formatting_engine = get_formatting_engine()
        
        # 当前选中的字段类型
        self.current_field_type = None
        self.current_field_data = None
        
        # 缓存机制
        self.field_type_cache = {}
        self.cache_timer = QTimer()
        self.cache_timer.setSingleShot(True)
        self.cache_timer.timeout.connect(self._save_cache)
        
        self._init_ui()
        self._connect_signals()
        self._load_field_types()

        # 延迟加载缓存，确保UI完全初始化
        QTimer.singleShot(100, self._load_cache)

        logger.info("字段类型配置组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 创建工具栏
        self._create_toolbar(layout)
        
        # 创建主要内容区域
        self._create_main_content(layout)
    
    def _create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar = QToolBar()
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        toolbar.setIconSize(toolbar.iconSize() * 0.8)  # 稍小的图标
        
        # 新建按钮
        self.new_action = QAction("➕ 新建", self)
        self.new_action.setToolTip("创建新的字段类型")
        self.new_action.triggered.connect(self._create_field_type)
        toolbar.addAction(self.new_action)
        
        # 编辑按钮
        self.edit_action = QAction("✏️ 编辑", self)
        self.edit_action.setToolTip("编辑选中的字段类型")
        self.edit_action.setEnabled(False)
        self.edit_action.triggered.connect(self._edit_field_type)
        toolbar.addAction(self.edit_action)
        
        # 删除按钮
        self.delete_action = QAction("🗑️ 删除", self)
        self.delete_action.setToolTip("删除选中的字段类型")
        self.delete_action.setEnabled(False)
        self.delete_action.triggered.connect(self._delete_field_type)
        toolbar.addAction(self.delete_action)
        
        toolbar.addSeparator()
        
        # 导入按钮
        self.import_action = QAction("📥 导入", self)
        self.import_action.setToolTip("从文件导入字段类型配置")
        self.import_action.triggered.connect(self._import_field_types)
        toolbar.addAction(self.import_action)
        
        # 导出按钮
        self.export_action = QAction("📤 导出", self)
        self.export_action.setToolTip("导出字段类型配置到文件")
        self.export_action.triggered.connect(self._export_field_types)
        toolbar.addAction(self.export_action)
        
        parent_layout.addWidget(toolbar)
    
    def _create_main_content(self, parent_layout):
        """创建主要内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：类型列表
        self._create_type_list(splitter)
        
        # 右侧：详情面板
        self._create_details_panel(splitter)
        
        # 设置分割比例
        splitter.setSizes([300, 500])
        splitter.setStretchFactor(0, 0)
        splitter.setStretchFactor(1, 1)
        
        parent_layout.addWidget(splitter)
    
    def _create_type_list(self, parent):
        """创建左侧类型列表"""
        # 创建容器
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        list_layout.setContentsMargins(0, 0, 0, 0)
        
        # 搜索框（预留）
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索字段类型...")
        self.search_edit.textChanged.connect(self._filter_field_types)
        list_layout.addWidget(self.search_edit)
        
        # 树形列表
        self.type_tree = QTreeWidget()
        self.type_tree.setHeaderLabel("字段类型")
        self.type_tree.setRootIsDecorated(True)
        self.type_tree.setAlternatingRowColors(True)
        self.type_tree.itemSelectionChanged.connect(self._on_type_selection_changed)
        list_layout.addWidget(self.type_tree)
        
        parent.addWidget(list_widget)
    
    def _create_details_panel(self, parent):
        """创建右侧详情面板"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 详情内容容器
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        self.details_layout.setContentsMargins(10, 10, 10, 10)
        self.details_layout.setSpacing(15)
        
        # 默认显示提示信息
        self._show_empty_details()
        
        scroll_area.setWidget(self.details_widget)
        parent.addWidget(scroll_area)
    
    def _show_empty_details(self):
        """显示空详情提示"""
        # 清空现有内容
        self._clear_details_layout()
        
        # 添加提示标签
        tip_label = QLabel("请从左侧列表中选择一个字段类型以查看详细信息")
        tip_label.setAlignment(Qt.AlignCenter)
        tip_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 14px;
                padding: 50px;
            }
        """)
        
        self.details_layout.addWidget(tip_label)
        self.details_layout.addStretch()
    
    def _clear_details_layout(self):
        """清空详情布局"""
        while self.details_layout.count():
            child = self.details_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def _connect_signals(self):
        """连接信号"""
        # 搜索框变化时触发缓存保存
        self.search_edit.textChanged.connect(self._delayed_save_cache)

        # 树形列表展开/折叠时触发缓存保存
        self.type_tree.itemExpanded.connect(self._delayed_save_cache)
        self.type_tree.itemCollapsed.connect(self._delayed_save_cache)
    
    def _load_field_types(self):
        """加载字段类型列表"""
        self.type_tree.clear()
        
        # 创建根节点
        builtin_root = QTreeWidgetItem(self.type_tree, ["▶ 内置类型"])
        builtin_root.setExpanded(True)
        
        custom_root = QTreeWidgetItem(self.type_tree, ["▶ 自定义类型"])
        custom_root.setExpanded(True)
        
        # 获取使用统计
        usage_stats = self._get_field_type_usage_stats()

        # 加载内置类型
        builtin_types = self.formatting_engine.get_field_types()
        for type_id, type_info in builtin_types.items():
            usage_count = usage_stats.get(type_id, 0)
            display_name = type_info.get("name", type_id)
            if usage_count > 0:
                display_name += f" ({usage_count}次使用)"

            item = QTreeWidgetItem(builtin_root, [display_name])
            item.setData(0, Qt.UserRole, {
                "type_id": type_id,
                "type_info": type_info,
                "is_builtin": True,
                "editable": False,
                "usage_count": usage_count
            })

            # 根据使用频率设置不同的显示样式
            if usage_count > 10:
                item.setForeground(0, item.treeWidget().palette().color(QPalette.Link))
            elif usage_count > 5:
                item.setForeground(0, item.treeWidget().palette().color(QPalette.Text))

        # 加载自定义类型
        custom_types = self.field_type_manager.list_custom_field_types()
        for type_info in custom_types:
            usage_count = usage_stats.get(type_info["id"], 0)
            display_name = type_info.get("name", type_info["id"])
            if usage_count > 0:
                display_name += f" ({usage_count}次使用)"

            item = QTreeWidgetItem(custom_root, [display_name])
            item.setData(0, Qt.UserRole, {
                "type_id": type_info["id"],
                "type_info": type_info,
                "is_builtin": False,
                "editable": True,
                "usage_count": usage_count
            })

            # 自定义类型使用蓝色显示
            item.setForeground(0, item.treeWidget().palette().color(QPalette.Link))
        
        logger.info(f"已加载 {len(builtin_types)} 个内置类型和 {len(custom_types)} 个自定义类型")
    
    def _filter_field_types(self, text):
        """过滤字段类型"""
        text = text.lower().strip()

        # 遍历所有项目进行过滤
        def filter_items(parent_item):
            for i in range(parent_item.childCount()):
                child = parent_item.child(i)
                if child:
                    # 获取类型数据
                    type_data = child.data(0, Qt.UserRole)
                    if type_data:
                        type_id = type_data.get("type_id", "").lower()
                        type_info = type_data.get("type_info", {})
                        type_name = type_info.get("name", "").lower()
                        description = type_info.get("description", "").lower()

                        # 检查是否匹配搜索文本
                        matches = (
                            text in type_id or
                            text in type_name or
                            text in description
                        )

                        # 设置可见性
                        child.setHidden(not matches and text != "")

        # 过滤内置类型和自定义类型
        root = self.type_tree.invisibleRootItem()
        for i in range(root.childCount()):
            category_item = root.child(i)
            if category_item:
                filter_items(category_item)

                # 如果分类下没有可见的子项，隐藏分类
                has_visible_children = False
                for j in range(category_item.childCount()):
                    if not category_item.child(j).isHidden():
                        has_visible_children = True
                        break

                category_item.setHidden(not has_visible_children and text != "")

    def _get_field_type_usage_stats(self):
        """获取字段类型使用统计"""
        usage_stats = {}

        try:
            # 从配置文件中统计使用情况
            # 这里可以扩展为从数据库或配置文件中读取实际使用统计

            # 模拟一些使用统计数据
            default_stats = {
                "salary_float": 15,
                "employee_id_string": 12,
                "name_string": 18,
                "date_string": 8,
                "id_number_string": 5,
                "code_string": 3,
                "text_string": 10,
                "integer": 6,
                "float": 4,
                "personnel_category_code": 2
            }

            # 尝试从实际配置中读取统计
            stats_file = self.field_type_manager.storage_dir / 'usage_stats.json'
            if stats_file.exists():
                with open(stats_file, 'r', encoding='utf-8') as f:
                    saved_stats = json.load(f)
                    usage_stats.update(saved_stats)
            else:
                # 使用默认统计数据
                usage_stats = default_stats

        except Exception as e:
            logger.warning(f"获取字段类型使用统计失败: {e}")
            # 返回空统计
            usage_stats = {}

        return usage_stats

    def _update_field_type_usage(self, type_id: str):
        """更新字段类型使用统计"""
        try:
            stats_file = self.field_type_manager.storage_dir / 'usage_stats.json'

            # 读取现有统计
            usage_stats = {}
            if stats_file.exists():
                with open(stats_file, 'r', encoding='utf-8') as f:
                    usage_stats = json.load(f)

            # 增加使用次数
            usage_stats[type_id] = usage_stats.get(type_id, 0) + 1

            # 保存统计
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(usage_stats, f, ensure_ascii=False, indent=2)

            logger.info(f"字段类型 {type_id} 使用统计已更新")

        except Exception as e:
            logger.warning(f"更新字段类型使用统计失败: {e}")
    
    def _on_type_selection_changed(self):
        """类型选择变更处理"""
        current_item = self.type_tree.currentItem()
        if not current_item or not current_item.data(0, Qt.UserRole):
            self._show_empty_details()
            self._update_toolbar_state(False, False)
            return
        
        # 获取选中的类型数据
        type_data = current_item.data(0, Qt.UserRole)
        self.current_field_type = type_data["type_id"]
        self.current_field_data = type_data
        
        # 显示详情
        self._show_field_type_details(type_data)
        
        # 更新工具栏状态
        is_custom = not type_data["is_builtin"]
        self._update_toolbar_state(is_custom, is_custom)

        # 触发缓存保存
        self._delayed_save_cache()
    
    def _update_toolbar_state(self, can_edit, can_delete):
        """更新工具栏按钮状态"""
        self.edit_action.setEnabled(can_edit)
        self.delete_action.setEnabled(can_delete)
    
    def _show_field_type_details(self, type_data):
        """显示字段类型详情"""
        # 清空现有内容
        self._clear_details_layout()
        
        type_info = type_data["type_info"]
        is_builtin = type_data["is_builtin"]
        
        # 基本信息组
        self._create_basic_info_group(type_info, is_builtin)
        
        # 格式化配置组
        self._create_format_config_group(type_info)
        
        # 验证规则组
        self._create_validation_rules_group(type_info)
        
        # 预览示例组
        self._create_preview_group(type_info)
        
        # 添加弹性空间
        self.details_layout.addStretch()
    
    def _create_basic_info_group(self, type_info, is_builtin):
        """创建基本信息组"""
        group = QGroupBox("基本信息")
        layout = QFormLayout(group)

        # 类型ID
        type_id = type_info.get("id", self.current_field_type)
        id_label = QLabel(type_id)
        id_label.setStyleSheet("font-family: monospace; background: #f5f5f5; padding: 2px 4px; border-radius: 2px;")
        layout.addRow("类型ID:", id_label)

        # 类型名称
        name_label = QLabel(type_info.get("name", "未命名"))
        name_label.setStyleSheet("font-weight: bold;")
        layout.addRow("名称:", name_label)

        # 描述
        description = type_info.get("description", "无描述")
        desc_label = QLabel(description)
        desc_label.setWordWrap(True)
        layout.addRow("描述:", desc_label)

        # 基础规则类型
        rule_type = type_info.get("rule_type", "未知")
        rule_label = QLabel(rule_type)
        layout.addRow("基础规则:", rule_label)

        # 来源标识
        source_text = "内置类型" if is_builtin else "自定义类型"
        source_label = QLabel(source_text)
        source_label.setStyleSheet(f"color: {'#666' if is_builtin else '#0066cc'}; font-weight: bold;")
        layout.addRow("来源:", source_label)

        self.details_layout.addWidget(group)

    def _create_format_config_group(self, type_info):
        """创建格式化配置组"""
        group = QGroupBox("格式化配置")
        layout = QFormLayout(group)

        default_config = type_info.get("default_config", {})
        if not default_config:
            no_config_label = QLabel("无特殊格式化配置")
            no_config_label.setStyleSheet("color: #666; font-style: italic;")
            layout.addRow(no_config_label)
        else:
            for key, value in default_config.items():
                # 格式化显示配置项
                display_value = self._format_config_value(key, value)
                value_label = QLabel(str(display_value))
                value_label.setWordWrap(True)
                layout.addRow(f"{self._format_config_key(key)}:", value_label)

        self.details_layout.addWidget(group)

    def _create_validation_rules_group(self, type_info):
        """创建验证规则组"""
        group = QGroupBox("验证规则")
        layout = QFormLayout(group)

        validation_rules = type_info.get("validation_rules", {})
        if not validation_rules:
            no_rules_label = QLabel("无验证规则")
            no_rules_label.setStyleSheet("color: #666; font-style: italic;")
            layout.addRow(no_rules_label)
        else:
            for key, value in validation_rules.items():
                display_value = self._format_validation_value(key, value)
                value_label = QLabel(str(display_value))
                value_label.setWordWrap(True)
                layout.addRow(f"{self._format_validation_key(key)}:", value_label)

        self.details_layout.addWidget(group)

    def _create_preview_group(self, type_info):
        """创建预览示例组"""
        group = QGroupBox("预览示例")
        layout = QVBoxLayout(group)

        # 输入测试值的输入框
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("测试输入:"))

        self.preview_input = QLineEdit()
        self.preview_input.setPlaceholderText("输入测试值...")
        self.preview_input.textChanged.connect(self._update_preview)
        input_layout.addWidget(self.preview_input)

        layout.addLayout(input_layout)

        # 输出结果显示
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("格式化输出:"))

        self.preview_output = QLabel("请输入测试值")
        self.preview_output.setStyleSheet("""
            QLabel {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px;
                border-radius: 4px;
                font-family: monospace;
            }
        """)
        self.preview_output.setWordWrap(True)
        output_layout.addWidget(self.preview_output)

        layout.addLayout(output_layout)

        # 设置一些默认测试值
        self._set_default_test_value(type_info)

        self.details_layout.addWidget(group)

    def _format_config_key(self, key):
        """格式化配置项键名"""
        key_mapping = {
            "decimal_places": "小数位数",
            "thousands_separator": "千位分隔符",
            "negative_format": "负数格式",
            "min_length": "最小长度",
            "max_length": "最大长度",
            "padding_char": "填充字符",
            "preserve_leading_zeros": "保留前导零",
            "trim_spaces": "去除空格",
            "case": "大小写格式",
            "date_format": "日期格式",
            "time_format": "时间格式"
        }
        return key_mapping.get(key, key)

    def _format_config_value(self, key, value):
        """格式化配置项值"""
        if isinstance(value, bool):
            return "是" if value else "否"
        elif key == "negative_format":
            format_mapping = {"minus": "负号", "parentheses": "括号", "red": "红色显示"}
            return format_mapping.get(value, value)
        elif key == "case":
            case_mapping = {"original": "原样", "upper": "大写", "lower": "小写", "title": "标题格式"}
            return case_mapping.get(value, value)
        return value

    def _format_validation_key(self, key):
        """格式化验证规则键名"""
        key_mapping = {
            "required": "必填",
            "min_length": "最小长度",
            "max_length": "最大长度",
            "min_value": "最小值",
            "max_value": "最大值",
            "pattern": "正则表达式",
            "custom_code": "自定义验证代码"
        }
        return key_mapping.get(key, key)

    def _format_validation_value(self, key, value):
        """格式化验证规则值"""
        if isinstance(value, bool):
            return "是" if value else "否"
        return value

    def _set_default_test_value(self, type_info):
        """设置默认测试值"""
        type_id = type_info.get("id", self.current_field_type)

        # 根据字段类型设置合适的测试值
        test_values = {
            "salary_float": "12345.678",
            "employee_id_string": "123456",
            "name_string": "张三",
            "date_string": "2024-01-15",
            "id_number_string": "110101199001011234",
            "code_string": "A001",
            "text_string": "示例文本",
            "integer": "12345",
            "float": "123.45",
            "personnel_category_code": "1"
        }

        default_value = test_values.get(type_id, "测试值")
        self.preview_input.setText(default_value)

    def _update_preview(self):
        """更新预览输出"""
        if not self.current_field_type or not self.current_field_data:
            return

        input_value = self.preview_input.text().strip()
        if not input_value:
            self.preview_output.setText("请输入测试值")
            return

        try:
            # 使用格式化引擎处理值
            formatted_value = self.formatting_engine.format_value(
                input_value,
                self.current_field_type
            )
            self.preview_output.setText(str(formatted_value))
        except Exception as e:
            self.preview_output.setText(f"格式化错误: {str(e)}")
            logger.warning(f"预览格式化失败: {e}")
    
    def _create_field_type(self):
        """创建新字段类型"""
        try:
            from src.gui.field_type_editor_dialog import FieldTypeEditorDialog

            dialog = FieldTypeEditorDialog(parent=self)
            dialog.field_type_created.connect(self._on_field_type_created)

            if dialog.exec_() == dialog.Accepted:
                self._load_field_types()
                logger.info("新字段类型创建成功")
        except ImportError as e:
            QMessageBox.warning(self, "错误", f"无法加载字段类型编辑器: {e}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建字段类型失败: {e}")
            logger.error(f"创建字段类型失败: {e}")

    def _edit_field_type(self):
        """编辑字段类型"""
        if not self.current_field_type or not self.current_field_data:
            QMessageBox.warning(self, "提示", "请先选择要编辑的字段类型")
            return

        if self.current_field_data.get("is_builtin"):
            QMessageBox.warning(self, "提示", "内置字段类型不可编辑")
            return

        try:
            from src.gui.field_type_editor_dialog import FieldTypeEditorDialog

            dialog = FieldTypeEditorDialog(
                field_type_id=self.current_field_type,
                parent=self
            )
            dialog.field_type_updated.connect(self._on_field_type_updated)

            if dialog.exec_() == dialog.Accepted:
                self._load_field_types()
                logger.info(f"字段类型 {self.current_field_type} 编辑成功")
        except ImportError as e:
            QMessageBox.warning(self, "错误", f"无法加载字段类型编辑器: {e}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑字段类型失败: {e}")
            logger.error(f"编辑字段类型失败: {e}")

    def _delete_field_type(self):
        """删除字段类型"""
        if not self.current_field_type or not self.current_field_data:
            QMessageBox.warning(self, "提示", "请先选择要删除的字段类型")
            return

        if self.current_field_data.get("is_builtin"):
            QMessageBox.warning(self, "提示", "内置字段类型不可删除")
            return

        # 确认删除
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除字段类型 '{self.current_field_data['type_info'].get('name', self.current_field_type)}' 吗？\n\n"
            "此操作不可撤销，请确保该类型未被其他配置使用。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.field_type_manager.delete_field_type(self.current_field_type):
                    self.field_type_deleted.emit(self.current_field_type)
                    self._load_field_types()
                    self._show_empty_details()
                    QMessageBox.information(self, "成功", "字段类型删除成功")
                    logger.info(f"字段类型 {self.current_field_type} 删除成功")
                else:
                    QMessageBox.warning(self, "错误", "字段类型删除失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除字段类型失败: {e}")
                logger.error(f"删除字段类型失败: {e}")

    def _import_field_types(self):
        """导入字段类型"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "导入字段类型配置",
            "",
            "JSON Files (*.json);;All Files (*)"
        )

        if not file_path:
            return

        try:
            # 询问是否覆盖现有类型
            reply = QMessageBox.question(
                self,
                "导入选项",
                "是否覆盖现有的同名字段类型？\n\n"
                "选择'是'将覆盖同名类型\n"
                "选择'否'将跳过同名类型",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.No
            )

            if reply == QMessageBox.Cancel:
                return

            overwrite = reply == QMessageBox.Yes

            if self.field_type_manager.import_field_types(file_path, overwrite):
                self._load_field_types()
                QMessageBox.information(self, "成功", "字段类型配置导入成功")
                logger.info(f"从 {file_path} 导入字段类型配置成功")
            else:
                QMessageBox.warning(self, "错误", "字段类型配置导入失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入失败: {e}")
            logger.error(f"导入字段类型配置失败: {e}")

    def _export_field_types(self):
        """导出字段类型"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出字段类型配置",
            "field_types_config.json",
            "JSON Files (*.json);;All Files (*)"
        )

        if not file_path:
            return

        try:
            if self.field_type_manager.export_field_types(file_path):
                QMessageBox.information(self, "成功", f"字段类型配置已导出到:\n{file_path}")
                logger.info(f"字段类型配置导出到 {file_path} 成功")
            else:
                QMessageBox.warning(self, "错误", "字段类型配置导出失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {e}")
            logger.error(f"导出字段类型配置失败: {e}")

    def _on_field_type_created(self, type_id: str, type_config: dict):
        """字段类型创建完成回调"""
        self.field_type_changed.emit(type_id)
        logger.info(f"字段类型 {type_id} 创建完成")

    def _on_field_type_updated(self, type_id: str, type_config: dict):
        """字段类型更新完成回调"""
        self.field_type_changed.emit(type_id)
        logger.info(f"字段类型 {type_id} 更新完成")

    def _save_cache(self):
        """保存缓存"""
        try:
            cache_file = self.field_type_manager.storage_dir / 'widget_cache.json'
            cache_data = {
                "last_selected_type": self.current_field_type,
                "search_text": self.search_edit.text(),
                "expanded_categories": self._get_expanded_categories(),
                "timestamp": str(datetime.now())
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

            logger.debug("字段类型配置缓存已保存")

        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")

    def _load_cache(self):
        """加载缓存"""
        try:
            cache_file = self.field_type_manager.storage_dir / 'widget_cache.json'
            if not cache_file.exists():
                return

            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            # 恢复搜索文本
            if cache_data.get("search_text"):
                self.search_edit.setText(cache_data["search_text"])

            # 恢复展开状态
            expanded_categories = cache_data.get("expanded_categories", [])
            self._restore_expanded_categories(expanded_categories)

            # 恢复选中的字段类型
            last_selected = cache_data.get("last_selected_type")
            if last_selected:
                QTimer.singleShot(100, lambda: self.select_field_type(last_selected))

            logger.debug("字段类型配置缓存已加载")

        except Exception as e:
            logger.warning(f"加载缓存失败: {e}")

    def _get_expanded_categories(self):
        """获取展开的分类"""
        expanded = []
        root = self.type_tree.invisibleRootItem()
        for i in range(root.childCount()):
            category_item = root.child(i)
            if category_item and category_item.isExpanded():
                expanded.append(category_item.text(0))
        return expanded

    def _restore_expanded_categories(self, expanded_categories):
        """恢复展开的分类"""
        root = self.type_tree.invisibleRootItem()
        for i in range(root.childCount()):
            category_item = root.child(i)
            if category_item and category_item.text(0) in expanded_categories:
                category_item.setExpanded(True)

    def _delayed_save_cache(self):
        """延迟保存缓存"""
        self.cache_timer.start(500)  # 500ms后保存

    def refresh_field_types(self):
        """刷新字段类型列表（供外部调用）"""
        self._load_field_types()

    def get_current_field_type(self):
        """获取当前选中的字段类型"""
        return self.current_field_type

    def select_field_type(self, type_id: str):
        """选中指定的字段类型"""
        def find_and_select_item(parent_item):
            for i in range(parent_item.childCount()):
                child = parent_item.child(i)
                if child:
                    type_data = child.data(0, Qt.UserRole)
                    if type_data and type_data.get("type_id") == type_id:
                        # 展开父节点
                        parent_item.setExpanded(True)
                        # 选中该项
                        self.type_tree.setCurrentItem(child)
                        return True
            return False

        # 在所有分类中查找
        root = self.type_tree.invisibleRootItem()
        for i in range(root.childCount()):
            category_item = root.child(i)
            if category_item and find_and_select_item(category_item):
                break
