#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试编辑器修复效果
验证编辑时不会缩成一条缝
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QTableWidget, QTableWidgetItem, QLabel
)
from PyQt5.QtCore import Qt

class EditorTestWindow(QMainWindow):
    """编辑器测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("编辑器修复测试")
        self.setGeometry(100, 100, 800, 500)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("编辑器修复测试 - 双击数据库字段列测试编辑效果")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px; color: #2c3e50;")
        layout.addWidget(info_label)
        
        # 创建测试表格
        self.table = self._create_test_table()
        layout.addWidget(self.table)
        
        # 添加测试说明
        test_info = QLabel("""
测试步骤：
1. 双击"数据库字段"列的任意单元格
2. 检查编辑器是否正常显示（不应该是一条缝）
3. 检查是否能看到原有内容
4. 尝试输入新内容
5. 按回车或点击其他地方确认

如果编辑器显示正常，说明修复成功。
        """)
        test_info.setStyleSheet("color: #666; font-size: 12px; margin: 10px; background-color: #f8f9fa; padding: 10px; border-radius: 4px;")
        test_info.setWordWrap(True)
        layout.addWidget(test_info)
        
    def _create_test_table(self):
        """创建测试表格"""
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["Excel列名", "数据库字段", "显示名称"])
        
        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.verticalHeader().setVisible(False)
        table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.SelectedClicked | QTableWidget.EditKeyPressed)
        table.verticalHeader().setDefaultSectionSize(35)
        
        # 应用修复后的样式
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #cfe2ff;
                selection-color: #0d6efd;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 12px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
            }
            
            QTableWidget::item:selected {
                background-color: #cfe2ff;
                color: #0d6efd;
            }
            
            QTableWidget::item:hover {
                background-color: #e7f1ff;
            }
            
            /* 表头样式 */
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                padding: 10px 8px;
                border: 1px solid #dee2e6;
                border-radius: 0px;
                font-weight: 600;
                font-size: 12px;
                color: #495057;
            }
            
            QHeaderView::section:first {
                border-top-left-radius: 6px;
            }
            
            QHeaderView::section:last {
                border-top-right-radius: 6px;
            }
            
            /* 关键：确保编辑器正常显示，不能缩成一条缝 */
            QTableWidget::item:edit {
                background-color: #ffffff;
                border: 2px solid #0d6efd;
                border-radius: 4px;
                padding: 4px 8px;
                min-height: 20px;
            }
            
            QLineEdit {
                background-color: #ffffff;
                border: 2px solid #0d6efd;
                border-radius: 4px;
                padding: 6px 10px;
                font-size: 12px;
                color: #495057;
                min-height: 20px;
            }
            
            QLineEdit:focus {
                border-color: #0d6efd;
                outline: none;
                background-color: #ffffff;
            }
        """)
        
        # 添加测试数据
        test_data = [
            ["姓名", "name", "姓名"],
            ["身份证号", "id_card_number", "身份证号"],
            ["基本工资", "basic_salary", "基本工资"],
            ["岗位津贴", "position_allowance", "岗位津贴"],
            ["绩效工资", "performance_bonus", "绩效工资"]
        ]
        
        table.setRowCount(len(test_data))
        for row, (excel_col, db_field, display_name) in enumerate(test_data):
            # Excel列名（只读）
            excel_item = QTableWidgetItem(excel_col)
            excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
            table.setItem(row, 0, excel_item)
            
            # 数据库字段（可编辑）
            db_field_item = QTableWidgetItem(db_field)
            db_field_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable | Qt.ItemIsSelectable)
            table.setItem(row, 1, db_field_item)
            
            # 显示名称（可编辑）
            display_item = QTableWidgetItem(display_name)
            table.setItem(row, 2, display_item)
        
        return table

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = EditorTestWindow()
    window.show()
    
    print("编辑器修复测试启动")
    print("请双击'数据库字段'列测试编辑效果")
    print("检查编辑器是否正常显示，不会缩成一条缝")
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
