# 统一数据导入配置窗口字段类型列添加方案

## 一、需求背景
在"统一数据导入配置"窗口的"字段映射"选项卡中，添加"字段类型"列，以增强字段的业务语义管理能力。

## 二、现状分析

### 2.1 当前表格结构
现有字段映射表格包含5列：
- **Excel字段**：源Excel文件的字段名
- **数据库字段**：目标数据库表的字段名
- **显示名称**：UI界面的显示名称
- **数据类型**：SQL数据类型（VARCHAR、INT、DECIMAL等）
- **是否必需**：字段是否为必填项

### 2.2 已有技术基础
项目中已存在完整的字段类型管理体系：

1. **FieldTypeManager** (`src/modules/data_import/field_type_manager.py`)
   - 支持创建、编辑、删除自定义字段类型
   - 基础规则类型：number、string、date、code、custom
   - 包含验证规则和默认配置

2. **FieldRegistry** (`src/modules/format_management/field_registry.py`)
   - 定义了字段类型映射关系
   - 包含特殊类型：year_string、month_string等
   - 支持字段分类和隐藏字段管理

3. **FieldTypeConfigWidget** (`src/gui/widgets/field_type_config_widget.py`)
   - 提供独立的字段类型配置界面
   - 支持导入/导出字段类型配置

## 三、概念区分

### 3.1 数据类型 vs 字段类型
| 维度 | 数据类型 | 字段类型 |
|-----|---------|---------|
| 定义 | SQL数据库存储类型 | 业务语义类型 |
| 示例 | VARCHAR(100)、DECIMAL(10,2) | 工资金额、员工编号、年份字符串 |
| 作用 | 决定数据库存储格式 | 决定业务处理逻辑和显示格式 |
| 关系 | 底层技术实现 | 上层业务抽象 |

### 3.2 字段类型的价值
1. **语义明确**：清晰表达字段的业务含义
2. **格式化支持**：不同类型应用不同的格式化规则
3. **验证增强**：基于业务规则进行数据验证
4. **模板复用**：作为配置模板的一部分，提高复用性

## 四、实施方案

### 4.1 推荐方案：智能联动模式

#### 界面布局
```
Excel字段 | 数据库字段 | 显示名称 | 字段类型 | 数据类型 | 是否必需 | 验证状态
```

#### 核心特性
1. **智能联动**：选择字段类型后，自动推荐合适的数据类型
2. **手动覆盖**：允许用户根据需要调整数据类型
3. **类型继承**：字段类型可继承已有类型的配置

#### 字段类型示例
- **工资金额**：自动设置为DECIMAL(10,2)，支持千分位显示
- **员工编号**：自动设置为VARCHAR(20)，支持前缀验证
- **年份字符串**：自动设置为VARCHAR(4)，支持年份格式验证
- **月份字符串**：自动设置为VARCHAR(2)，支持月份提取

### 4.2 实施步骤

#### 第一阶段：表格UI调整
1. 修改`unified_data_import_window.py`中的`_create_mapping_table`方法
2. 将列数从6增加到7（包含字段类型列）
3. 在第3列（显示名称后）添加"字段类型"列
4. 调整表格列宽比例

#### 第二阶段：字段类型集成
1. 在`MappingTab`类中集成`FieldTypeManager`
2. 创建字段类型下拉选择组件
3. 实现字段类型与数据类型的联动逻辑
4. 添加字段类型的持久化存储

#### 第三阶段：功能增强
1. 实现字段类型的快速选择功能
2. 添加常用字段类型的预设
3. 支持批量应用字段类型
4. 集成到模板管理系统

## 五、技术实现细节

### 5.1 数据结构扩展
```python
# 映射配置增加field_type字段
mapping_config = {
    "excel_field": "原字段名",
    "db_field": "数据库字段名",
    "display_name": "显示名称",
    "field_type": "salary_amount",  # 新增
    "data_type": "DECIMAL(10,2)",
    "is_required": False
}
```

### 5.2 联动逻辑
```python
# 字段类型与数据类型映射关系
FIELD_TYPE_MAPPING = {
    "salary_amount": {
        "data_type": "DECIMAL(10,2)",
        "format": "currency",
        "validation": "positive_number"
    },
    "employee_id": {
        "data_type": "VARCHAR(20)",
        "format": "code",
        "validation": "alphanumeric"
    },
    "year_string": {
        "data_type": "VARCHAR(4)",
        "format": "year",
        "validation": "year_format"
    }
}
```

### 5.3 UI组件创建
```python
def _create_field_type_combo(self, current_type=None):
    """创建字段类型下拉框"""
    combo = QComboBox()
    
    # 添加内置类型
    builtin_types = [
        ("通用", "general"),
        ("工资金额", "salary_amount"),
        ("员工编号", "employee_id"),
        ("部门名称", "department"),
        ("年份", "year_string"),
        ("月份", "month_string")
    ]
    
    for display_name, type_id in builtin_types:
        combo.addItem(display_name, type_id)
    
    # 添加自定义类型
    custom_types = self.field_type_manager.get_custom_field_types()
    if custom_types:
        combo.insertSeparator(combo.count())
        for type_id, type_info in custom_types.items():
            combo.addItem(type_info['name'], type_id)
    
    if current_type:
        index = combo.findData(current_type)
        if index >= 0:
            combo.setCurrentIndex(index)
    
    return combo
```

## 六、影响评估

### 6.1 正面影响
1. **用户体验提升**：更直观的字段管理
2. **配置效率提高**：自动推荐减少手动配置
3. **数据质量保障**：基于类型的验证规则
4. **模板复用性增强**：字段类型作为模板的一部分

### 6.2 潜在风险
1. **界面复杂度增加**：需要合理的UI设计
2. **学习成本**：用户需要理解字段类型概念
3. **兼容性考虑**：需要处理旧配置的升级

## 七、测试计划

### 7.1 功能测试
- 字段类型选择和切换
- 类型联动效果验证
- 自定义类型创建和使用
- 配置保存和加载

### 7.2 兼容性测试
- 旧配置文件的兼容
- 不同数据源的适配
- 模板系统的集成

### 7.3 性能测试
- 大量字段的处理性能
- 类型切换的响应速度
- 内存占用情况

## 八、实施建议

1. **分阶段实施**：先实现基础功能，再逐步增强
2. **保持兼容**：确保旧配置仍可正常使用
3. **提供引导**：为用户提供字段类型选择指南
4. **收集反馈**：根据用户使用情况持续优化

## 九、总结

添加"字段类型"列是一个有价值的功能增强，项目已具备完整的技术基础。通过智能联动模式，可以在不增加过多复杂度的前提下，显著提升字段配置的效率和准确性。建议采用分阶段实施策略，确保功能的稳定性和用户体验。
