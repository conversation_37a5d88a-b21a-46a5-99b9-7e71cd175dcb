#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一数据导入配置窗口布局修复效果

测试内容：
1. 文件选择区域是否完全显示
2. 工具栏按钮是否合理布局
3. 不同窗口大小下的显示效果
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from gui.unified_data_import_window import UnifiedDataImportWindow


class LayoutTestWindow(QMainWindow):
    """布局测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("统一数据导入配置窗口布局测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        test_normal_btn = QPushButton("测试正常窗口大小 (1400x900)")
        test_normal_btn.clicked.connect(self.test_normal_size)
        layout.addWidget(test_normal_btn)
        
        test_small_btn = QPushButton("测试小窗口大小 (1200x800)")
        test_small_btn.clicked.connect(self.test_small_size)
        layout.addWidget(test_small_btn)
        
        test_large_btn = QPushButton("测试大窗口大小 (1600x1000)")
        test_large_btn.clicked.connect(self.test_large_size)
        layout.addWidget(test_large_btn)
        
        test_responsive_btn = QPushButton("测试响应式调整")
        test_responsive_btn.clicked.connect(self.test_responsive)
        layout.addWidget(test_responsive_btn)
        
        self.dialog = None
    
    def test_normal_size(self):
        """测试正常窗口大小"""
        self._open_dialog(1400, 900, "正常大小测试")
    
    def test_small_size(self):
        """测试小窗口大小"""
        self._open_dialog(1200, 800, "小窗口测试")
    
    def test_large_size(self):
        """测试大窗口大小"""
        self._open_dialog(1600, 1000, "大窗口测试")
    
    def test_responsive(self):
        """测试响应式调整"""
        dialog = UnifiedDataImportWindow(self)
        dialog.setWindowTitle("响应式测试 - 请手动调整窗口大小")
        dialog.show()
        self.dialog = dialog

    def _open_dialog(self, width, height, title_suffix):
        """打开指定大小的对话框"""
        if self.dialog:
            self.dialog.close()

        dialog = UnifiedDataImportWindow(self)
        dialog.setWindowTitle(f"统一数据导入配置 - {title_suffix}")
        dialog.resize(width, height)
        dialog.show()
        self.dialog = dialog
        
        print(f"已打开 {title_suffix} 窗口 ({width}x{height})")
        print("请检查以下内容：")
        print("1. 文件选择区域是否完全显示，没有被遮挡")
        print("2. Sheet工具栏按钮是否分两行显示，文字清晰")
        print("3. 映射工具栏按钮是否分两行显示，文字清晰")
        print("4. 整体布局是否协调美观")
        print("-" * 50)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    test_window = LayoutTestWindow()
    test_window.show()
    
    print("=== 统一数据导入配置窗口布局测试 ===")
    print("修复内容：")
    print("1. 顶部工具栏：增加高度，确保文件选择区域完全显示")
    print("2. Sheet管理工具栏：按钮分两行显示，避免挤压")
    print("3. 映射工具栏：按钮分两行显示，缩短按钮文字")
    print()
    print("请点击测试按钮验证不同窗口大小下的显示效果")
    print("=" * 50)
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
