# 异动表用户配置管理器实现方案

**文档创建时间**: 2025-08-20  
**关联文档**: 异动表处理机制完整改进方案  
**实现目标**: 用户自定义配置的存储、管理和应用机制

## 📋 设计目标

### 核心功能
1. **配置存储**：用户自定义配置的持久化存储
2. **模式匹配**：智能匹配相似的异动表配置
3. **模板管理**：预定义模板的管理和复用
4. **版本控制**：配置的版本管理和回滚
5. **导入导出**：配置的导入导出功能

### 设计原则
- **用户友好**：简单直观的配置界面
- **智能匹配**：自动识别相似的表结构
- **灵活扩展**：支持新的字段类型和格式化规则
- **数据安全**：配置数据的备份和恢复

## 🏗️ 架构设计

### 类结构图

```mermaid
classDiagram
    class ChangeDataConfigManager {
        +config_dir: Path
        +template_dir: Path
        +backup_dir: Path
        +save_user_config(pattern, config)
        +load_user_config(table_name)
        +get_available_templates()
        +create_template_from_config(config, name)
        +delete_config(pattern)
        +backup_configs()
        +restore_configs(backup_file)
    }
    
    class ConfigMatcher {
        +similarity_threshold: float
        +match_config_by_pattern(table_name)
        +match_config_by_similarity(headers)
        +calculate_similarity(config1, config2)
    }
    
    class TemplateManager {
        +predefined_templates: Dict
        +get_template(template_name)
        +create_custom_template(config)
        +update_template(template_name, config)
        +list_templates()
    }
    
    class ConfigValidator {
        +validate_config(config)
        +validate_field_types(field_types)
        +validate_format_rules(format_rules)
        +fix_config_issues(config)
    }
    
    ChangeDataConfigManager --> ConfigMatcher
    ChangeDataConfigManager --> TemplateManager
    ChangeDataConfigManager --> ConfigValidator
```

## 💻 核心实现

### 1. ChangeDataConfigManager 主类

```python
class ChangeDataConfigManager:
    """异动表配置管理器"""
    
    def __init__(self, config_dir: str = "state/data/change_data_configs"):
        self.logger = setup_logger(__name__)
        
        # 目录设置
        self.config_dir = Path(config_dir)
        self.template_dir = self.config_dir / "templates"
        self.backup_dir = self.config_dir / "backups"
        self.user_dir = self.config_dir / "user_configs"
        
        # 创建必要目录
        for directory in [self.config_dir, self.template_dir, self.backup_dir, self.user_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.config_matcher = ConfigMatcher()
        self.template_manager = TemplateManager(self.template_dir)
        self.config_validator = ConfigValidator()
        
        # 配置缓存
        self._config_cache = {}
        self._cache_timestamp = {}
        
        # 初始化预定义模板
        self._initialize_predefined_templates()
    
    def save_user_config(self, table_pattern: str, config: Dict[str, Any]) -> bool:
        """
        保存用户自定义配置
        
        Args:
            table_pattern: 表名模式（支持通配符）
            config: 配置数据
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 验证配置
            validation_result = self.config_validator.validate_config(config)
            if not validation_result['valid']:
                self.logger.error(f"配置验证失败: {validation_result['errors']}")
                return False
            
            # 生成配置文件名
            safe_pattern = self._sanitize_filename(table_pattern)
            config_file = self.user_dir / f"{safe_pattern}.json"
            
            # 准备配置数据
            config_data = {
                'table_pattern': table_pattern,
                'field_mappings': config.get('field_mappings', {}),
                'field_types': config.get('field_types', {}),
                'format_rules': config.get('format_rules', {}),
                'display_order': config.get('display_order', []),
                'hidden_fields': config.get('hidden_fields', []),
                'description': config.get('description', ''),
                'created_time': datetime.now().isoformat(),
                'updated_time': datetime.now().isoformat(),
                'version': config.get('version', '1.0'),
                'user_modified': True,
                'metadata': {
                    'field_count': len(config.get('field_mappings', {})),
                    'creator': config.get('creator', 'user'),
                    'tags': config.get('tags', [])
                }
            }
            
            # 保存配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            # 更新缓存
            self._config_cache[table_pattern] = config_data
            self._cache_timestamp[table_pattern] = time.time()
            
            self.logger.info(f"用户配置保存成功: {table_pattern}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存用户配置失败: {e}")
            return False
    
    def load_user_config(self, table_name: str) -> Optional[Dict[str, Any]]:
        """
        加载用户自定义配置
        
        Args:
            table_name: 表名
            
        Returns:
            Optional[Dict[str, Any]]: 配置数据，如果未找到返回None
        """
        try:
            # 1. 尝试精确匹配
            exact_config = self._load_config_by_exact_match(table_name)
            if exact_config:
                self.logger.info(f"找到精确匹配配置: {table_name}")
                return exact_config
            
            # 2. 尝试模式匹配
            pattern_config = self._load_config_by_pattern_match(table_name)
            if pattern_config:
                self.logger.info(f"找到模式匹配配置: {table_name}")
                return pattern_config
            
            # 3. 尝试相似度匹配
            similarity_config = self._load_config_by_similarity(table_name)
            if similarity_config:
                self.logger.info(f"找到相似度匹配配置: {table_name}")
                return similarity_config
            
            self.logger.debug(f"未找到匹配的用户配置: {table_name}")
            return None
            
        except Exception as e:
            self.logger.error(f"加载用户配置失败: {e}")
            return None
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """
        获取可用的配置模板
        
        Returns:
            List[Dict[str, Any]]: 模板列表
        """
        templates = []
        
        try:
            # 1. 获取预定义模板
            predefined_templates = self.template_manager.list_templates()
            templates.extend(predefined_templates)
            
            # 2. 获取用户自定义配置作为模板
            for config_file in self.user_dir.glob("*.json"):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    template_info = {
                        'name': config.get('table_pattern', config_file.stem),
                        'type': 'user_config',
                        'description': config.get('description', '用户自定义配置'),
                        'field_count': len(config.get('field_mappings', {})),
                        'created_time': config.get('created_time', '未知'),
                        'updated_time': config.get('updated_time', '未知'),
                        'version': config.get('version', '1.0'),
                        'file_path': str(config_file)
                    }
                    templates.append(template_info)
                    
                except Exception as e:
                    self.logger.warning(f"读取用户配置模板失败 {config_file}: {e}")
            
            return templates
            
        except Exception as e:
            self.logger.error(f"获取可用模板失败: {e}")
            return []
    
    def create_template_from_config(self, config: Dict[str, Any], template_name: str) -> bool:
        """
        从配置创建模板
        
        Args:
            config: 配置数据
            template_name: 模板名称
            
        Returns:
            bool: 创建是否成功
        """
        try:
            return self.template_manager.create_custom_template(config, template_name)
        except Exception as e:
            self.logger.error(f"创建模板失败: {e}")
            return False
    
    def delete_config(self, table_pattern: str) -> bool:
        """
        删除用户配置
        
        Args:
            table_pattern: 表名模式
            
        Returns:
            bool: 删除是否成功
        """
        try:
            safe_pattern = self._sanitize_filename(table_pattern)
            config_file = self.user_dir / f"{safe_pattern}.json"
            
            if config_file.exists():
                # 备份后删除
                backup_file = self.backup_dir / f"{safe_pattern}_{int(time.time())}.json"
                shutil.copy2(config_file, backup_file)
                config_file.unlink()
                
                # 清除缓存
                if table_pattern in self._config_cache:
                    del self._config_cache[table_pattern]
                if table_pattern in self._cache_timestamp:
                    del self._cache_timestamp[table_pattern]
                
                self.logger.info(f"用户配置删除成功: {table_pattern}")
                return True
            else:
                self.logger.warning(f"配置文件不存在: {table_pattern}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除用户配置失败: {e}")
            return False
    
    def backup_configs(self) -> str:
        """
        备份所有配置
        
        Returns:
            str: 备份文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"configs_backup_{timestamp}.zip"
            
            with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 备份用户配置
                for config_file in self.user_dir.glob("*.json"):
                    zipf.write(config_file, f"user_configs/{config_file.name}")
                
                # 备份模板
                for template_file in self.template_dir.glob("*.json"):
                    zipf.write(template_file, f"templates/{template_file.name}")
            
            self.logger.info(f"配置备份完成: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            self.logger.error(f"配置备份失败: {e}")
            return ""
    
    def restore_configs(self, backup_file: str) -> bool:
        """
        从备份恢复配置
        
        Args:
            backup_file: 备份文件路径
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                zipf.extractall(self.config_dir)
            
            # 清除缓存
            self._config_cache.clear()
            self._cache_timestamp.clear()
            
            self.logger.info(f"配置恢复完成: {backup_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置恢复失败: {e}")
            return False
    
    def _load_config_by_exact_match(self, table_name: str) -> Optional[Dict[str, Any]]:
        """精确匹配加载配置"""
        safe_name = self._sanitize_filename(table_name)
        config_file = self.user_dir / f"{safe_name}.json"
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"读取配置文件失败 {config_file}: {e}")
        
        return None
    
    def _load_config_by_pattern_match(self, table_name: str) -> Optional[Dict[str, Any]]:
        """模式匹配加载配置"""
        return self.config_matcher.match_config_by_pattern(table_name, self.user_dir)
    
    def _load_config_by_similarity(self, table_name: str) -> Optional[Dict[str, Any]]:
        """相似度匹配加载配置"""
        return self.config_matcher.match_config_by_similarity(table_name, self.user_dir)
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不安全字符"""
        import re
        # 移除或替换不安全字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(safe_name) > 100:
            safe_name = safe_name[:100]
        return safe_name
    
    def _initialize_predefined_templates(self):
        """初始化预定义模板"""
        predefined_templates = {
            'a_grade_employees': {
                'name': 'A岗职工模板',
                'description': '适用于A岗职工异动表',
                'field_mappings': {
                    'employee_id': '工号',
                    'employee_name': '姓名',
                    'department': '部门名称',
                    'position_salary_2025': '2025年岗位工资',
                    'grade_salary_2025': '2025年薪级工资'
                },
                'field_types': {
                    'employee_id': 'employee_id_string',
                    'employee_name': 'name_string',
                    'department': 'text_string',
                    'position_salary_2025': 'salary_float',
                    'grade_salary_2025': 'salary_float'
                }
            },
            'retired_employees': {
                'name': '离休人员模板',
                'description': '适用于离休人员异动表',
                'field_mappings': {
                    'employee_id': '工号',
                    'employee_name': '姓名',
                    'basic_retirement_fee': '基本退休费',
                    'living_allowance': '离退休生活补贴'
                },
                'field_types': {
                    'employee_id': 'employee_id_string',
                    'employee_name': 'name_string',
                    'basic_retirement_fee': 'salary_float',
                    'living_allowance': 'salary_float'
                }
            }
        }
        
        # 保存预定义模板
        for template_key, template_data in predefined_templates.items():
            self.template_manager.save_predefined_template(template_key, template_data)
```

### 2. ConfigMatcher 配置匹配器

```python
class ConfigMatcher:
    """配置匹配器"""
    
    def __init__(self, similarity_threshold: float = 0.7):
        self.logger = setup_logger(__name__)
        self.similarity_threshold = similarity_threshold
    
    def match_config_by_pattern(self, table_name: str, config_dir: Path) -> Optional[Dict[str, Any]]:
        """
        通过模式匹配查找配置
        
        Args:
            table_name: 表名
            config_dir: 配置目录
            
        Returns:
            Optional[Dict[str, Any]]: 匹配的配置
        """
        try:
            for config_file in config_dir.glob("*.json"):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    pattern = config.get('table_pattern', '')
                    if self._match_pattern(table_name, pattern):
                        self.logger.debug(f"模式匹配成功: {table_name} -> {pattern}")
                        return config
                        
                except Exception as e:
                    self.logger.warning(f"读取配置文件失败 {config_file}: {e}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"模式匹配失败: {e}")
            return None
    
    def match_config_by_similarity(self, table_name: str, config_dir: Path) -> Optional[Dict[str, Any]]:
        """
        通过相似度匹配查找配置
        
        Args:
            table_name: 表名
            config_dir: 配置目录
            
        Returns:
            Optional[Dict[str, Any]]: 最相似的配置
        """
        try:
            best_config = None
            best_similarity = 0.0
            
            for config_file in config_dir.glob("*.json"):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    pattern = config.get('table_pattern', '')
                    similarity = self._calculate_string_similarity(table_name, pattern)
                    
                    if similarity > best_similarity and similarity >= self.similarity_threshold:
                        best_similarity = similarity
                        best_config = config
                        
                except Exception as e:
                    self.logger.warning(f"读取配置文件失败 {config_file}: {e}")
            
            if best_config:
                self.logger.debug(f"相似度匹配成功: {table_name} (相似度: {best_similarity:.2f})")
            
            return best_config
            
        except Exception as e:
            self.logger.error(f"相似度匹配失败: {e}")
            return None
    
    def _match_pattern(self, text: str, pattern: str) -> bool:
        """模式匹配（支持通配符）"""
        import fnmatch
        return fnmatch.fnmatch(text, pattern)
    
    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """计算字符串相似度"""
        from difflib import SequenceMatcher
        return SequenceMatcher(None, str1, str2).ratio()
```

## 📁 配置文件格式

### 用户配置文件示例

```json
{
  "table_pattern": "change_data_*_A岗职工",
  "field_mappings": {
    "employee_id": "工号",
    "employee_name": "姓名",
    "department": "部门名称",
    "position_salary_2025": "2025年岗位工资",
    "grade_salary_2025": "2025年薪级工资",
    "allowance": "津贴",
    "performance_bonus": "2025年奖励性绩效预发"
  },
  "field_types": {
    "employee_id": "employee_id_string",
    "employee_name": "name_string",
    "department": "text_string",
    "position_salary_2025": "salary_float",
    "grade_salary_2025": "salary_float",
    "allowance": "salary_float",
    "performance_bonus": "salary_float"
  },
  "format_rules": {
    "employee_id": "remove_decimal_keep_string",
    "position_salary_2025": "two_decimal_places",
    "grade_salary_2025": "two_decimal_places",
    "allowance": "two_decimal_places",
    "performance_bonus": "two_decimal_places"
  },
  "display_order": [
    "employee_id",
    "employee_name", 
    "department",
    "position_salary_2025",
    "grade_salary_2025",
    "allowance",
    "performance_bonus"
  ],
  "hidden_fields": [
    "id",
    "created_at",
    "updated_at",
    "sequence_number"
  ],
  "description": "A岗职工异动表配置，包含基本工资和绩效字段",
  "created_time": "2025-08-20T10:00:00",
  "updated_time": "2025-08-20T10:00:00",
  "version": "1.0",
  "user_modified": true,
  "metadata": {
    "field_count": 7,
    "creator": "user",
    "tags": ["A岗", "职工", "工资", "绩效"]
  }
}
```

## 🔧 使用示例

```python
# 创建配置管理器
config_manager = ChangeDataConfigManager()

# 保存用户配置
user_config = {
    'field_mappings': {'employee_id': '工号', 'employee_name': '姓名'},
    'field_types': {'employee_id': 'employee_id_string', 'employee_name': 'name_string'},
    'format_rules': {'employee_id': 'remove_decimal_keep_string'},
    'display_order': ['employee_id', 'employee_name']
}

success = config_manager.save_user_config('change_data_*_测试表', user_config)
print(f"配置保存: {'成功' if success else '失败'}")

# 加载配置
loaded_config = config_manager.load_user_config('change_data_2025_12_测试表')
if loaded_config:
    print("找到匹配配置")
else:
    print("未找到匹配配置")

# 获取可用模板
templates = config_manager.get_available_templates()
print(f"可用模板数量: {len(templates)}")
```

---

**文档版本**: v1.0  
**最后更新**: 2025-08-20  
**状态**: 实现方案
