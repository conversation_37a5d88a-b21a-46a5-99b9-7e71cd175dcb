#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分页状态管理器 - P1级优化
专门管理分页状态，避免表切换和分页切换的状态混淆
"""

from typing import Dict, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
from loguru import logger
import json


@dataclass
class PaginationState:
    """分页状态数据"""
    table_name: str
    current_page: int = 1
    page_size: int = 50
    total_records: int = 0
    total_pages: int = 1
    sort_columns: list = field(default_factory=list)
    filter_conditions: dict = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)
    is_paginating: bool = False  # 是否正在分页操作中
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'table_name': self.table_name,
            'current_page': self.current_page,
            'page_size': self.page_size,
            'total_records': self.total_records,
            'total_pages': self.total_pages,
            'sort_columns': self.sort_columns,
            'filter_conditions': self.filter_conditions,
            'last_updated': self.last_updated.isoformat(),
            'is_paginating': self.is_paginating
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'PaginationState':
        """从字典创建"""
        data = data.copy()
        if 'last_updated' in data and isinstance(data['last_updated'], str):
            data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        return cls(**data)


class PaginationStateManager:
    """
    分页状态管理器 - P1级优化组件
    
    功能：
    1. 维护每个表的独立分页状态
    2. 区分表切换和分页切换
    3. 防止状态污染和混淆
    4. 提供状态持久化支持
    """
    
    def __init__(self, state_file: Optional[str] = None):
        """
        初始化分页状态管理器
        
        Args:
            state_file: 状态持久化文件路径
        """
        self._states: Dict[str, PaginationState] = {}
        self._current_table: Optional[str] = None
        self._state_file = state_file
        self._transition_history: list = []  # 记录状态转换历史
        
        # 加载持久化状态
        if state_file:
            self._load_states()
        
        logger.info("🔧 [P1优化] 分页状态管理器初始化完成")
    
    def get_state(self, table_name: str) -> Optional[PaginationState]:
        """
        获取指定表的分页状态
        
        Args:
            table_name: 表名
            
        Returns:
            分页状态或None
        """
        return self._states.get(table_name)
    
    def set_state(self, table_name: str, **kwargs) -> PaginationState:
        """
        设置分页状态
        
        Args:
            table_name: 表名
            **kwargs: 状态参数
            
        Returns:
            更新后的状态
        """
        if table_name not in self._states:
            self._states[table_name] = PaginationState(table_name=table_name)
        
        state = self._states[table_name]
        
        # 更新状态字段
        for key, value in kwargs.items():
            if hasattr(state, key):
                setattr(state, key, value)
        
        # 计算总页数
        if 'total_records' in kwargs or 'page_size' in kwargs:
            state.total_pages = max(1, (state.total_records + state.page_size - 1) // state.page_size)
        
        state.last_updated = datetime.now()
        
        # 保存状态
        if self._state_file:
            self._save_states()
        
        logger.debug(f"🔧 [P1优化] 更新分页状态: {table_name}, 页码: {state.current_page}/{state.total_pages}")
        
        return state
    
    def begin_pagination(self, table_name: str) -> bool:
        """
        开始分页操作
        
        Args:
            table_name: 表名
            
        Returns:
            是否成功开始（False表示已经在分页中）
        """
        state = self.get_state(table_name)
        if state and state.is_paginating:
            logger.warning(f"🔧 [P1优化] 表 {table_name} 已经在分页操作中")
            return False
        
        if table_name not in self._states:
            self._states[table_name] = PaginationState(table_name=table_name)
        
        self._states[table_name].is_paginating = True
        self._record_transition('begin_pagination', table_name)
        
        logger.info(f"🔧 [P1优化] 开始分页操作: {table_name}")
        return True
    
    def end_pagination(self, table_name: str):
        """
        结束分页操作
        
        Args:
            table_name: 表名
        """
        if table_name in self._states:
            self._states[table_name].is_paginating = False
            self._record_transition('end_pagination', table_name)
            logger.info(f"🔧 [P1优化] 结束分页操作: {table_name}")
    
    def is_table_switch(self, from_table: Optional[str], to_table: str) -> bool:
        """
        判断是否为表切换
        
        Args:
            from_table: 源表名
            to_table: 目标表名
            
        Returns:
            是否为表切换
        """
        if from_table is None:
            # 首次加载
            return True
        
        if from_table == to_table:
            # 同表操作，可能是分页
            return False
        
        # 不同表，是表切换
        self._record_transition('table_switch', f"{from_table} -> {to_table}")
        return True
    
    def is_pagination(self, table_name: str, new_page: int) -> bool:
        """
        判断是否为分页操作
        
        Args:
            table_name: 表名
            new_page: 新页码
            
        Returns:
            是否为分页
        """
        state = self.get_state(table_name)
        if not state:
            return False
        
        if state.current_page != new_page:
            self._record_transition('pagination', f"{table_name}: {state.current_page} -> {new_page}")
            return True
        
        return False
    
    def reset_state(self, table_name: str):
        """
        重置指定表的分页状态
        
        Args:
            table_name: 表名
        """
        if table_name in self._states:
            self._states[table_name] = PaginationState(
                table_name=table_name,
                page_size=self._states[table_name].page_size  # 保留页大小设置
            )
            logger.info(f"🔧 [P1优化] 重置分页状态: {table_name}")
    
    def clear_all_states(self):
        """清除所有分页状态"""
        self._states.clear()
        self._current_table = None
        self._transition_history.clear()
        logger.info("🔧 [P1优化] 清除所有分页状态")
    
    def get_current_table(self) -> Optional[str]:
        """获取当前表名"""
        return self._current_table
    
    def set_current_table(self, table_name: str):
        """设置当前表名"""
        old_table = self._current_table
        self._current_table = table_name
        
        if old_table != table_name:
            self._record_transition('current_table_changed', f"{old_table} -> {table_name}")
    
    def _record_transition(self, transition_type: str, details: str):
        """记录状态转换"""
        self._transition_history.append({
            'type': transition_type,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
        
        # 限制历史记录大小
        if len(self._transition_history) > 100:
            self._transition_history = self._transition_history[-50:]
    
    def get_transition_history(self) -> list:
        """获取状态转换历史"""
        return self._transition_history.copy()
    
    def _load_states(self):
        """从文件加载状态"""
        try:
            import os
            if os.path.exists(self._state_file):
                with open(self._state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for table_name, state_dict in data.get('states', {}).items():
                        self._states[table_name] = PaginationState.from_dict(state_dict)
                    self._current_table = data.get('current_table')
                    logger.info(f"🔧 [P1优化] 加载分页状态: {len(self._states)}个表")
        except Exception as e:
            logger.error(f"🔧 [P1优化] 加载分页状态失败: {e}")
    
    def _save_states(self):
        """保存状态到文件"""
        try:
            data = {
                'states': {name: state.to_dict() for name, state in self._states.items()},
                'current_table': self._current_table,
                'saved_at': datetime.now().isoformat()
            }
            
            import os
            os.makedirs(os.path.dirname(self._state_file), exist_ok=True)
            
            with open(self._state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.debug(f"🔧 [P1优化] 保存分页状态: {len(self._states)}个表")
        except Exception as e:
            logger.error(f"🔧 [P1优化] 保存分页状态失败: {e}")
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_tables = len(self._states)
        paginating_tables = sum(1 for s in self._states.values() if s.is_paginating)
        
        return {
            'total_tables': total_tables,
            'paginating_tables': paginating_tables,
            'current_table': self._current_table,
            'transition_count': len(self._transition_history)
        }


# 单例实例
_pagination_manager_instance: Optional[PaginationStateManager] = None


def get_pagination_manager() -> PaginationStateManager:
    """获取分页状态管理器单例"""
    global _pagination_manager_instance
    if _pagination_manager_instance is None:
        # 默认使用状态文件
        state_file = "state/pagination_states.json"
        _pagination_manager_instance = PaginationStateManager(state_file)
    return _pagination_manager_instance