#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试表格样式统一效果
对比修改前后的样式差异，验证与系统其他表格的一致性
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QTableWidget, QTableWidgetItem, QLabel, QPushButton,
    QHBoxLayout, QSplitter
)
from PyQt5.QtCore import Qt

class StyleComparisonWindow(QMainWindow):
    """样式对比测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("表格样式统一测试")
        self.setGeometry(100, 100, 1400, 700)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("表格样式统一测试 - 对比系统标准样式与字段映射表格样式")
        info_label.setStyleSheet("font-weight: bold; font-size: 16px; margin: 10px; color: #2c3e50;")
        layout.addWidget(info_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：系统标准样式表格
        left_widget = self._create_standard_table_widget()
        splitter.addWidget(left_widget)
        
        # 右侧：字段映射样式表格
        right_widget = self._create_mapping_table_widget()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([700, 700])
        
        # 添加说明
        instruction_label = QLabel("""
对比要点：
1. 表格边框和圆角是否一致
2. 表头样式（背景色、字体、边框）是否统一
3. 单元格内边距是否相同
4. 选中和悬停效果是否一致
5. 编辑器样式是否与系统输入框风格协调

如果两个表格看起来风格一致，说明样式统一成功。
        """)
        instruction_label.setStyleSheet("color: #666; font-size: 12px; margin: 10px; background-color: #f8f9fa; padding: 10px; border-radius: 4px;")
        instruction_label.setWordWrap(True)
        layout.addWidget(instruction_label)
        
    def _create_standard_table_widget(self):
        """创建系统标准样式的表格"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title = QLabel("系统标准样式表格")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #495057; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 创建表格
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["字段名", "类型", "说明"])
        
        # 应用系统标准样式（来自 main_dialogs.py）
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #cfe2ff;
                selection-color: #0d6efd;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 12px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
            }
            
            QTableWidget::item:selected {
                background-color: #cfe2ff;
                color: #0d6efd;
            }
            
            QTableWidget::item:hover {
                background-color: #e7f1ff;
            }
            
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                padding: 10px 8px;
                border: 1px solid #dee2e6;
                border-radius: 0px;
                font-weight: 600;
                font-size: 12px;
                color: #495057;
            }
            
            QHeaderView::section:first {
                border-top-left-radius: 6px;
            }
            
            QHeaderView::section:last {
                border-top-right-radius: 6px;
            }
        """)
        
        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.verticalHeader().setVisible(False)
        table.verticalHeader().setDefaultSectionSize(35)
        
        # 添加测试数据
        test_data = [
            ["用户ID", "VARCHAR(50)", "用户唯一标识"],
            ["用户名", "VARCHAR(100)", "用户登录名"],
            ["邮箱", "VARCHAR(255)", "用户邮箱地址"],
            ["创建时间", "DATETIME", "账户创建时间"],
            ["状态", "INT", "账户状态"]
        ]
        
        table.setRowCount(len(test_data))
        for row, (field, type_val, desc) in enumerate(test_data):
            table.setItem(row, 0, QTableWidgetItem(field))
            table.setItem(row, 1, QTableWidgetItem(type_val))
            table.setItem(row, 2, QTableWidgetItem(desc))
        
        layout.addWidget(table)
        return widget
        
    def _create_mapping_table_widget(self):
        """创建字段映射样式的表格"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title = QLabel("字段映射表格（统一后样式）")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #495057; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 创建表格
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["Excel列名", "数据库字段", "显示名称"])
        
        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.verticalHeader().setVisible(False)
        table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.SelectedClicked | QTableWidget.EditKeyPressed)
        table.verticalHeader().setDefaultSectionSize(35)
        
        # 应用统一后的样式
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #cfe2ff;
                selection-color: #0d6efd;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 12px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
            }
            
            QTableWidget::item:selected {
                background-color: #cfe2ff;
                color: #0d6efd;
            }
            
            QTableWidget::item:hover {
                background-color: #e7f1ff;
            }
            
            /* 表头样式 - 与系统统一 */
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                padding: 10px 8px;
                border: 1px solid #dee2e6;
                border-radius: 0px;
                font-weight: 600;
                font-size: 12px;
                color: #495057;
            }
            
            QHeaderView::section:first {
                border-top-left-radius: 6px;
            }
            
            QHeaderView::section:last {
                border-top-right-radius: 6px;
            }
            
            /* 编辑器样式 - 保持功能性的同时与系统风格协调 */
            QTableWidget::item:edit {
                background-color: #ffffff;
                border: 1px solid #86b7fe;
                border-radius: 4px;
                padding: 6px 8px;
            }
            
            QLineEdit {
                background-color: #ffffff;
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 13px;
                color: #495057;
            }
            
            QLineEdit:focus {
                border-color: #86b7fe;
                outline: none;
            }
            
            QLineEdit:hover {
                border-color: #b6d7ff;
            }
        """)
        
        # 添加测试数据
        test_data = [
            ["姓名", "name", "姓名"],
            ["身份证号", "id_card", "身份证号"],
            ["基本工资", "basic_salary", "基本工资"],
            ["岗位津贴", "position_allowance", "岗位津贴"],
            ["绩效工资", "performance_salary", "绩效工资"]
        ]
        
        table.setRowCount(len(test_data))
        for row, (excel_col, db_field, display_name) in enumerate(test_data):
            # Excel列名（只读）
            excel_item = QTableWidgetItem(excel_col)
            excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
            table.setItem(row, 0, excel_item)
            
            # 数据库字段（可编辑）
            db_field_item = QTableWidgetItem(db_field)
            db_field_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable | Qt.ItemIsSelectable)
            table.setItem(row, 1, db_field_item)
            
            # 显示名称（可编辑）
            display_item = QTableWidgetItem(display_name)
            table.setItem(row, 2, display_item)
        
        layout.addWidget(table)
        
        # 添加编辑提示
        edit_hint = QLabel("💡 双击"数据库字段"列可以编辑，测试编辑器样式")
        edit_hint.setStyleSheet("color: #28a745; font-size: 12px; margin-top: 5px;")
        layout.addWidget(edit_hint)
        
        return widget

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = StyleComparisonWindow()
    window.show()
    
    print("表格样式统一测试启动")
    print("请对比两个表格的样式是否一致")
    print("特别注意表头、边框、内边距、选中效果等")
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
