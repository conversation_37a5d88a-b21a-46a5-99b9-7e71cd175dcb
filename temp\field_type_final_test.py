#!/usr/bin/env python3
"""
字段类型列添加方案最终验证脚本
全面测试所有实施的功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始字段类型列添加方案最终验证...")
    print("="*60)
    
    test_results = []
    
    # 测试1：代码结构检查（不创建GUI组件）
    print("\n📋 测试1: 代码结构检查")
    try:
        # 检查_create_mapping_table方法的源码
        import inspect
        from src.gui.unified_data_import_window import UnifiedMappingConfigWidget

        # 获取方法源码
        source = inspect.getsource(UnifiedMappingConfigWidget._create_mapping_table)

        # 验证关键代码存在
        assert "setColumnCount(7)" in source, "表格列数未设置为7"
        assert "字段类型" in source, "缺少字段类型列标题"
        assert '"Excel列名", "数据库字段", "显示名称", "字段类型", "数据类型", "是否必需", "验证状态"' in source, "列标题不正确"

        print("✅ 代码结构检查通过")
        test_results.append(("代码结构检查", True))

    except Exception as e:
        print(f"❌ 代码结构检查失败: {e}")
        test_results.append(("代码结构检查", False))
    
    # 测试2：字段类型方法
    print("\n📋 测试2: 字段类型方法")
    try:
        from src.gui.unified_data_import_window import UnifiedMappingConfigWidget
        
        # 创建实例
        widget = UnifiedMappingConfigWidget.__new__(UnifiedMappingConfigWidget)
        widget._get_recommended_field_type = UnifiedMappingConfigWidget._get_recommended_field_type.__get__(widget)
        widget._get_recommended_data_type = UnifiedMappingConfigWidget._get_recommended_data_type.__get__(widget)
        
        # 测试字段类型推荐
        test_cases = [
            ("工资", "salary_amount"),
            ("工号", "employee_id"),
            ("部门", "department"),
            ("年份", "year_string"),
            ("月份", "month_string"),
            ("其他", "general")
        ]
        
        for field_name, expected_type in test_cases:
            actual_type = widget._get_recommended_field_type(field_name)
            assert actual_type == expected_type, f"字段类型推荐错误：{field_name} -> 期望{expected_type}，实际{actual_type}"
        
        # 测试数据类型推荐
        data_type_cases = [
            ("salary_amount", "DECIMAL(10,2)"),
            ("employee_id", "VARCHAR(20)"),
            ("department", "VARCHAR(100)"),
            ("general", "VARCHAR(100)")
        ]
        
        for field_type, expected_data_type in data_type_cases:
            actual_data_type = widget._get_recommended_data_type(field_type)
            assert actual_data_type == expected_data_type, f"数据类型推荐错误：{field_type} -> 期望{expected_data_type}，实际{actual_data_type}"
        
        print("✅ 字段类型方法测试通过")
        test_results.append(("字段类型方法", True))
        
    except Exception as e:
        print(f"❌ 字段类型方法测试失败: {e}")
        test_results.append(("字段类型方法", False))
    
    # 测试3：方法存在性
    print("\n📋 测试3: 方法存在性")
    try:
        from src.gui.unified_data_import_window import UnifiedMappingConfigWidget
        
        required_methods = [
            '_create_field_type_combo',
            '_on_field_type_changed',
            '_get_recommended_field_type',
            '_get_recommended_data_type',
            '_apply_enhanced_template',
            '_apply_legacy_template'
        ]
        
        for method_name in required_methods:
            assert hasattr(UnifiedMappingConfigWidget, method_name), f"缺少方法: {method_name}"
        
        print("✅ 方法存在性测试通过")
        test_results.append(("方法存在性", True))
        
    except Exception as e:
        print(f"❌ 方法存在性测试失败: {e}")
        test_results.append(("方法存在性", False))
    
    # 测试4：FieldTypeManager集成
    print("\n📋 测试4: FieldTypeManager集成")
    try:
        from src.modules.data_import.field_type_manager import FieldTypeManager
        
        # 创建FieldTypeManager实例
        manager = FieldTypeManager()
        
        # 验证关键方法存在
        assert hasattr(manager, 'list_custom_field_types'), "FieldTypeManager缺少list_custom_field_types方法"
        assert hasattr(manager, 'get_all_field_types'), "FieldTypeManager缺少get_all_field_types方法"
        
        # 测试方法调用
        custom_types = manager.list_custom_field_types()
        all_types = manager.get_all_field_types()
        
        assert isinstance(custom_types, list), "list_custom_field_types应返回列表"
        assert isinstance(all_types, dict), "get_all_field_types应返回字典"
        
        print("✅ FieldTypeManager集成测试通过")
        test_results.append(("FieldTypeManager集成", True))
        
    except Exception as e:
        print(f"❌ FieldTypeManager集成测试失败: {e}")
        test_results.append(("FieldTypeManager集成", False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总:")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！字段类型列添加方案实施成功！")
        print("\n✨ 实施亮点:")
        print("   - 表格结构正确扩展到7列")
        print("   - 字段类型智能推荐功能正常")
        print("   - 数据类型联动机制工作正常")
        print("   - 所有必需方法已实现")
        print("   - FieldTypeManager集成成功")
        return True
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，需要修复后再验证")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
