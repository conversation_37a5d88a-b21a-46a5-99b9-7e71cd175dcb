#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
P2级问题修复 - 日志自动轮转机制
防止日志文件过大，自动归档历史日志
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_log_rotation_config():
    """创建日志轮转配置"""
    
    print("="*60)
    print("日志轮转配置")
    print("="*60)
    
    # 生成loguru的轮转配置代码
    rotation_code = '''# -*- coding: utf-8 -*-
"""
增强的日志配置 - 添加自动轮转功能
"""

import os
import sys
from pathlib import Path
from loguru import logger

def setup_logger(name: str = None):
    """设置日志记录器（增强版）"""
    
    # 移除默认handler
    logger.remove()
    
    # 日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 日志文件路径
    log_file = log_dir / "salary_system.log"
    
    # 配置控制台输出
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        level="INFO",
        colorize=True
    )
    
    # 配置文件输出 - 带轮转功能
    logger.add(
        log_file,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
        level="INFO",
        rotation="10 MB",  # 当文件达到10MB时轮转
        retention="7 days",  # 保留7天的日志
        compression="zip",  # 压缩旧日志
        enqueue=True,  # 异步写入
        encoding="utf-8",
        backtrace=True,  # 记录异常堆栈
        diagnose=True  # 诊断模式
    )
    
    # 添加错误日志单独文件
    error_log = log_dir / "errors.log"
    logger.add(
        error_log,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
        level="ERROR",
        rotation="5 MB",  # 错误日志5MB轮转
        retention="30 days",  # 错误日志保留30天
        compression="zip",
        enqueue=True,
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )
    
    # 性能日志（可选）
    if os.getenv("ENABLE_PERFORMANCE_LOG", "false").lower() == "true":
        perf_log = log_dir / "performance.log"
        logger.add(
            perf_log,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | PERF | {message}",
            filter=lambda record: "PERF" in record["extra"],
            rotation="20 MB",
            retention="3 days",
            compression="zip",
            enqueue=True,
            encoding="utf-8"
        )
    
    # 创建命名logger
    if name:
        return logger.bind(name=name)
    
    return logger

# 导出logger实例
default_logger = setup_logger()
'''
    
    # 保存增强的日志配置
    enhanced_log_config = Path("src/utils/log_config_enhanced.py")
    with open(enhanced_log_config, 'w', encoding='utf-8') as f:
        f.write(rotation_code)
    
    print(f"增强日志配置已创建: {enhanced_log_config}")
    
    return enhanced_log_config

def update_log_config_imports():
    """更新日志配置导入"""
    
    print("\n" + "="*60)
    print("更新日志配置导入")
    print("="*60)
    
    # 修改log_config.py以支持轮转
    log_config_file = Path("src/utils/log_config.py")
    
    if log_config_file.exists():
        with open(log_config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已有轮转配置
        if "rotation=" in content:
            print("[INFO] 日志配置已包含轮转设置")
        else:
            print("[WARN] 当前日志配置不包含轮转设置")
            print("建议在logger.add()中添加以下参数：")
            print('  rotation="10 MB"  # 10MB时轮转')
            print('  retention="7 days"  # 保留7天')
            print('  compression="zip"  # 压缩旧日志')
    
    return log_config_file

def clean_old_logs():
    """清理旧日志文件"""
    
    print("\n" + "="*60)
    print("清理旧日志文件")
    print("="*60)
    
    log_dir = Path("logs")
    
    if not log_dir.exists():
        print("[INFO] 日志目录不存在")
        return
    
    # 统计日志文件
    log_files = list(log_dir.glob("*.log*"))
    total_size = sum(f.stat().st_size for f in log_files) / (1024 * 1024)  # MB
    
    print(f"日志文件数量: {len(log_files)}")
    print(f"总大小: {total_size:.2f} MB")
    
    # 列出大于1MB的日志文件
    large_files = []
    for f in log_files:
        size_mb = f.stat().st_size / (1024 * 1024)
        if size_mb > 1:
            large_files.append((f, size_mb))
            print(f"  {f.name}: {size_mb:.2f} MB")
    
    if large_files:
        print(f"\n发现 {len(large_files)} 个大于1MB的日志文件")
        
        # 创建归档目录
        archive_dir = log_dir / "archive"
        archive_dir.mkdir(exist_ok=True)
        
        # 移动旧日志到归档目录
        from datetime import datetime
        import shutil
        
        for log_file, size in large_files:
            if ".log" in log_file.name and not log_file.name.endswith(".log"):
                # 已经是归档文件，移动到archive目录
                archive_path = archive_dir / log_file.name
                try:
                    shutil.move(str(log_file), str(archive_path))
                    print(f"  已归档: {log_file.name}")
                except Exception as e:
                    print(f"  归档失败: {log_file.name} - {e}")
    
    return log_dir

def create_log_maintenance_script():
    """创建日志维护脚本"""
    
    print("\n" + "="*60)
    print("创建日志维护脚本")
    print("="*60)
    
    maintenance_script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志维护脚本 - 定期清理和归档日志
可以通过Windows任务计划程序定期运行
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
import zipfile
import shutil

def archive_old_logs(log_dir="logs", days_to_keep=7):
    """归档旧日志文件"""
    
    log_path = Path(log_dir)
    if not log_path.exists():
        return
    
    archive_dir = log_path / "archive"
    archive_dir.mkdir(exist_ok=True)
    
    # 当前时间
    now = datetime.now()
    cutoff_time = now - timedelta(days=days_to_keep)
    
    # 遍历日志文件
    for log_file in log_path.glob("*.log*"):
        # 跳过当前活动日志
        if log_file.name == "salary_system.log":
            continue
        
        # 检查文件修改时间
        mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
        if mtime < cutoff_time:
            # 创建压缩文件名
            archive_name = f"{log_file.stem}_{mtime.strftime('%Y%m%d')}.zip"
            archive_path = archive_dir / archive_name
            
            # 压缩并归档
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                zf.write(log_file, log_file.name)
            
            # 删除原文件
            log_file.unlink()
            print(f"已归档: {log_file.name} -> {archive_name}")

def clean_large_logs(log_dir="logs", max_size_mb=10):
    """清理过大的日志文件"""
    
    log_path = Path(log_dir)
    if not log_path.exists():
        return
    
    for log_file in log_path.glob("*.log"):
        size_mb = log_file.stat().st_size / (1024 * 1024)
        if size_mb > max_size_mb:
            # 备份当前日志
            backup_name = f"{log_file.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            backup_path = log_path / "archive" / backup_name
            backup_path.parent.mkdir(exist_ok=True)
            
            shutil.copy2(log_file, backup_path)
            
            # 清空当前日志
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"Log rotated at {datetime.now()}\\n")
            
            print(f"已轮转: {log_file.name} ({size_mb:.1f}MB)")

if __name__ == "__main__":
    print("开始日志维护...")
    archive_old_logs()
    clean_large_logs()
    print("日志维护完成")
'''
    
    # 保存维护脚本
    script_file = Path("maintenance/log_maintenance.py")
    script_file.parent.mkdir(exist_ok=True)
    
    with open(script_file, 'w', encoding='utf-8') as f:
        f.write(maintenance_script)
    
    print(f"日志维护脚本已创建: {script_file}")
    
    # 创建批处理文件（Windows）
    batch_file = Path("maintenance/log_maintenance.bat")
    with open(batch_file, 'w') as f:
        f.write('@echo off\n')
        f.write('python log_maintenance.py\n')
        f.write('pause\n')
    
    print(f"批处理文件已创建: {batch_file}")
    
    return script_file

def main():
    """主函数"""
    print("\n" + "="*70)
    print("P2级问题修复 - 日志自动轮转")
    print("="*70)
    
    # 1. 创建增强的日志配置
    config_file = create_log_rotation_config()
    
    # 2. 更新现有配置
    update_log_config_imports()
    
    # 3. 清理旧日志
    clean_old_logs()
    
    # 4. 创建维护脚本
    maintenance_script = create_log_maintenance_script()
    
    print("\n" + "="*70)
    print("日志轮转配置完成")
    print("="*70)
    print("\n建议：")
    print("1. 使用增强的日志配置替换原配置")
    print("2. 设置Windows任务计划定期运行维护脚本")
    print("3. 监控日志目录大小变化")

if __name__ == "__main__":
    main()