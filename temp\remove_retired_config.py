"""
删除JSON文件中的retired_staff_format_config配置段
"""
import json
import os

def remove_retired_config(file_path):
    """删除retired_staff_format_config配置段"""
    
    # 读取JSON文件
    with open(file_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 删除retired_staff_format_config配置段
    if 'retired_staff_format_config' in config:
        del config['retired_staff_format_config']
        print(f"已删除 {file_path} 中的 retired_staff_format_config 配置段")
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    else:
        print(f"{file_path} 中没有找到 retired_staff_format_config 配置段")
        return False

# 处理所有相关配置文件
files_to_process = [
    'config/format_config.json',
    'src/modules/format_management/format_config.json',
    'state/format_config.json',
    'state/data/format_config.json'
]

for file_path in files_to_process:
    if os.path.exists(file_path):
        print(f"\n处理文件: {file_path}")
        remove_retired_config(file_path)
    else:
        print(f"\n文件不存在: {file_path}")

print("\n完成！")