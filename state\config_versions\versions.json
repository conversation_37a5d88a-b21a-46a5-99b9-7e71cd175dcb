{"histories": {"测试工资表": {"sheet_name": "测试工资表", "current_version_id": "b6687a90-15e3-4ac6-be2d-6e0870ce1c10", "versions": [{"version_id": "998df204-d883-4da5-8b32-8b54003c3d5b", "sheet_name": "测试工资表", "version_number": 1, "version_type": "manual", "config_data": {"header_row": 1, "data_start_row": 2, "is_enabled": true, "remove_summary_rows": false}, "changes": [{"field_name": "header_row", "change_type": "create", "old_value": null, "new_value": "1"}, {"field_name": "data_start_row", "change_type": "create", "old_value": null, "new_value": "2"}, {"field_name": "is_enabled", "change_type": "create", "old_value": null, "new_value": "True"}, {"field_name": "remove_summary_rows", "change_type": "create", "old_value": null, "new_value": "False"}], "created_time": "2025-08-31T22:16:09.873224", "created_by": "user", "description": "初始配置版本", "tags": ["initial"], "parent_version_id": null, "is_current": false}, {"version_id": "755b64de-9e6d-4761-bf89-c73cfc9bacba", "sheet_name": "测试工资表", "version_number": 2, "version_type": "auto", "config_data": {"header_row": 1, "data_start_row": 2, "is_enabled": true, "remove_summary_rows": true, "summary_keywords": ["合计", "小计"]}, "changes": [{"field_name": "remove_summary_rows", "change_type": "update", "old_value": "False", "new_value": "True"}, {"field_name": "summary_keywords", "change_type": "create", "old_value": null, "new_value": "['合计', '小计']"}], "created_time": "2025-08-31T22:16:09.876162", "created_by": "system", "description": "启用汇总行移除", "tags": [], "parent_version_id": "998df204-d883-4da5-8b32-8b54003c3d5b", "is_current": false}, {"version_id": "94ee1c0c-4f6a-4a4f-afc4-0b0ebc590f2c", "sheet_name": "测试工资表", "version_number": 3, "version_type": "milestone", "config_data": {"header_row": 1, "data_start_row": 2, "is_enabled": true, "remove_summary_rows": true, "summary_keywords": ["合计", "小计"]}, "changes": [], "created_time": "2025-08-31T22:16:09.883368", "created_by": "user", "description": "功能完成里程碑", "tags": ["milestone", "feature-complete", "milestone"], "parent_version_id": "755b64de-9e6d-4761-bf89-c73cfc9bacba", "is_current": false}, {"version_id": "ce8c936d-095b-4bda-9312-0ad9d6472ca2", "sheet_name": "测试工资表", "version_number": 4, "version_type": "backup", "config_data": {"header_row": 1, "data_start_row": 2, "is_enabled": true, "remove_summary_rows": true, "summary_keywords": ["合计", "小计"]}, "changes": [], "created_time": "2025-08-31T22:16:09.886561", "created_by": "system", "description": "恢复前备份 (恢复到 v1)", "tags": [], "parent_version_id": "94ee1c0c-4f6a-4a4f-afc4-0b0ebc590f2c", "is_current": false}, {"version_id": "b6687a90-15e3-4ac6-be2d-6e0870ce1c10", "sheet_name": "测试工资表", "version_number": 5, "version_type": "manual", "config_data": {"header_row": 1, "data_start_row": 2, "is_enabled": true, "remove_summary_rows": false}, "changes": [{"field_name": "remove_summary_rows", "change_type": "update", "old_value": "True", "new_value": "False"}, {"field_name": "summary_keywords", "change_type": "delete", "old_value": "['合计', '小计']", "new_value": null}], "created_time": "2025-08-31T22:16:09.888986", "created_by": "system", "description": "恢复到版本 1", "tags": ["restore"], "parent_version_id": "ce8c936d-095b-4bda-9312-0ad9d6472ca2", "is_current": true}]}, "集成测试表": {"sheet_name": "集成测试表", "current_version_id": "bf6cc4d1-4ed6-4b00-9f9e-710cf64b67fa", "versions": [{"version_id": "7fa6d45a-8970-4e7e-bb2c-b6dec617b726", "sheet_name": "集成测试表", "version_number": 1, "version_type": "auto", "config_data": {"sheet_name": "集成测试表", "header_row": 1, "data_start_row": 2, "data_end_row": null, "skip_empty_rows": true, "has_header": true, "auto_detect_header": true, "remove_summary_rows": false, "summary_keywords": ["合计", "小计", "总计", "汇总"], "trim_whitespace": true, "normalize_numbers": true, "handle_merged_cells": true, "fill_empty_values": false, "field_mappings": {}, "field_types": {}, "required_fields": [], "validation_rules": {}, "created_time": "2025-08-31T22:16:09.957693", "modified_time": "2025-08-31T22:16:09.957693", "is_enabled": true, "notes": ""}, "changes": [{"field_name": "sheet_name", "change_type": "create", "old_value": null, "new_value": "集成测试表"}, {"field_name": "header_row", "change_type": "create", "old_value": null, "new_value": "1"}, {"field_name": "data_start_row", "change_type": "create", "old_value": null, "new_value": "2"}, {"field_name": "data_end_row", "change_type": "create", "old_value": null, "new_value": null}, {"field_name": "skip_empty_rows", "change_type": "create", "old_value": null, "new_value": "True"}, {"field_name": "has_header", "change_type": "create", "old_value": null, "new_value": "True"}, {"field_name": "auto_detect_header", "change_type": "create", "old_value": null, "new_value": "True"}, {"field_name": "remove_summary_rows", "change_type": "create", "old_value": null, "new_value": "False"}, {"field_name": "summary_keywords", "change_type": "create", "old_value": null, "new_value": "['合计', '小计', '总计', '汇总']"}, {"field_name": "trim_whitespace", "change_type": "create", "old_value": null, "new_value": "True"}, {"field_name": "normalize_numbers", "change_type": "create", "old_value": null, "new_value": "True"}, {"field_name": "handle_merged_cells", "change_type": "create", "old_value": null, "new_value": "True"}, {"field_name": "fill_empty_values", "change_type": "create", "old_value": null, "new_value": "False"}, {"field_name": "field_mappings", "change_type": "create", "old_value": null, "new_value": "{}"}, {"field_name": "field_types", "change_type": "create", "old_value": null, "new_value": "{}"}, {"field_name": "required_fields", "change_type": "create", "old_value": null, "new_value": "[]"}, {"field_name": "validation_rules", "change_type": "create", "old_value": null, "new_value": "{}"}, {"field_name": "created_time", "change_type": "create", "old_value": null, "new_value": "2025-08-31T22:16:09.957693"}, {"field_name": "modified_time", "change_type": "create", "old_value": null, "new_value": "2025-08-31T22:16:09.957693"}, {"field_name": "is_enabled", "change_type": "create", "old_value": null, "new_value": "True"}, {"field_name": "notes", "change_type": "create", "old_value": null, "new_value": ""}], "created_time": "2025-08-31T22:16:09.957693", "created_by": "system", "description": "自动版本 - 更新字段: remove_summary_rows, summary_keywords", "tags": [], "parent_version_id": null, "is_current": false}, {"version_id": "bf6cc4d1-4ed6-4b00-9f9e-710cf64b67fa", "sheet_name": "集成测试表", "version_number": 2, "version_type": "milestone", "config_data": {"sheet_name": "集成测试表", "header_row": 1, "data_start_row": 2, "data_end_row": null, "skip_empty_rows": true, "has_header": true, "auto_detect_header": true, "remove_summary_rows": false, "summary_keywords": ["合计", "小计", "总计", "汇总"], "trim_whitespace": true, "normalize_numbers": true, "handle_merged_cells": true, "fill_empty_values": false, "field_mappings": {}, "field_types": {}, "required_fields": [], "validation_rules": {}, "created_time": "2025-08-31T22:16:09.957693", "modified_time": "2025-08-31T22:16:09.957693", "is_enabled": true, "notes": ""}, "changes": [], "created_time": "2025-08-31T22:16:09.957693", "created_by": "user", "description": "配置优化完成", "tags": ["optimization", "stable", "milestone"], "parent_version_id": "7fa6d45a-8970-4e7e-bb2c-b6dec617b726", "is_current": true}]}, "清理测试表": {"sheet_name": "清理测试表", "current_version_id": "edef51ec-6517-4989-9a5c-52298db04544", "versions": [{"version_id": "5031f6b3-972c-4219-9e02-2382a71c3e1d", "sheet_name": "清理测试表", "version_number": 8, "version_type": "auto", "config_data": {"header_row": 1, "data_start_row": 2, "test_field": "value_7", "version_number": 8}, "changes": [{"field_name": "version_number", "change_type": "update", "old_value": "7", "new_value": "8"}, {"field_name": "test_field", "change_type": "update", "old_value": "value_6", "new_value": "value_7"}], "created_time": "2025-08-31T22:16:10.158814", "created_by": "system", "description": "测试版本 8", "tags": [], "parent_version_id": "0d5503fd-d96f-4b31-8847-7ba758f9c351", "is_current": false}, {"version_id": "767058a9-331d-4f92-94fe-7b3b742ec244", "sheet_name": "清理测试表", "version_number": 9, "version_type": "milestone", "config_data": {"header_row": 1, "data_start_row": 2, "test_field": "value_7", "version_number": 8}, "changes": [], "created_time": "2025-08-31T22:16:10.165065", "created_by": "user", "description": "重要里程碑", "tags": ["important", "milestone"], "parent_version_id": "5031f6b3-972c-4219-9e02-2382a71c3e1d", "is_current": false}, {"version_id": "a66ccece-8881-4d45-9295-5cfb15c58f3c", "sheet_name": "清理测试表", "version_number": 10, "version_type": "auto", "config_data": {"header_row": 1, "data_start_row": 2, "test_field": "after_milestone_0", "version_number": 10}, "changes": [{"field_name": "version_number", "change_type": "update", "old_value": "8", "new_value": "10"}, {"field_name": "test_field", "change_type": "update", "old_value": "value_7", "new_value": "after_milestone_0"}], "created_time": "2025-08-31T22:16:10.225435", "created_by": "system", "description": "里程碑后版本 1", "tags": [], "parent_version_id": "767058a9-331d-4f92-94fe-7b3b742ec244", "is_current": false}, {"version_id": "f0f616a1-4eac-48bb-a411-722c66bf66b7", "sheet_name": "清理测试表", "version_number": 11, "version_type": "auto", "config_data": {"header_row": 1, "data_start_row": 2, "test_field": "after_milestone_1", "version_number": 11}, "changes": [{"field_name": "version_number", "change_type": "update", "old_value": "10", "new_value": "11"}, {"field_name": "test_field", "change_type": "update", "old_value": "after_milestone_0", "new_value": "after_milestone_1"}], "created_time": "2025-08-31T22:16:10.226000", "created_by": "system", "description": "里程碑后版本 2", "tags": [], "parent_version_id": "a66ccece-8881-4d45-9295-5cfb15c58f3c", "is_current": false}, {"version_id": "edef51ec-6517-4989-9a5c-52298db04544", "sheet_name": "清理测试表", "version_number": 12, "version_type": "auto", "config_data": {"header_row": 1, "data_start_row": 2, "test_field": "after_milestone_2", "version_number": 12}, "changes": [{"field_name": "version_number", "change_type": "update", "old_value": "11", "new_value": "12"}, {"field_name": "test_field", "change_type": "update", "old_value": "after_milestone_1", "new_value": "after_milestone_2"}], "created_time": "2025-08-31T22:16:10.226000", "created_by": "system", "description": "里程碑后版本 3", "tags": [], "parent_version_id": "f0f616a1-4eac-48bb-a411-722c66bf66b7", "is_current": true}]}}, "saved_time": "2025-08-31T22:16:10.235722", "version": "1.0"}