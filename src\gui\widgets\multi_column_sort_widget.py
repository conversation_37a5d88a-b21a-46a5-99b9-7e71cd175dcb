"""
多列排序UI组件 - P3级优化
提供可视化的多列排序配置界面
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, 
    QListWidget, QListWidgetItem, QPushButton,
    QLabel, QComboBox, QCheckBox, QGroupBox,
    QToolButton, QMenu, QAction
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QIcon, QFont
from typing import List, Dict, Any, Optional
from loguru import logger

from src.core.multi_column_sort_manager import get_multi_sort_manager, SortDirection


class SortColumnItem(QWidget):
    """排序列项组件"""
    
    # 信号
    direction_changed = pyqtSignal(str, str)  # column_name, direction
    priority_changed = pyqtSignal(str, int)  # column_name, priority
    remove_requested = pyqtSignal(str)  # column_name
    
    def __init__(self, column_name: str, direction: str = "asc", 
                 priority: int = 0, parent=None):
        super().__init__(parent)
        self.column_name = column_name
        self.direction = direction
        self.priority = priority
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # 优先级标签
        self.priority_label = QLabel(f"{self.priority + 1}.")
        self.priority_label.setFixedWidth(20)
        layout.addWidget(self.priority_label)
        
        # 列名标签
        self.column_label = QLabel(self.column_name)
        self.column_label.setMinimumWidth(100)
        layout.addWidget(self.column_label)
        
        # 方向按钮
        self.direction_btn = QPushButton()
        self.direction_btn.setFixedSize(30, 22)
        self._update_direction_icon()
        self.direction_btn.clicked.connect(self._toggle_direction)
        layout.addWidget(self.direction_btn)
        
        # 上移按钮
        self.up_btn = QToolButton()
        self.up_btn.setText("↑")
        self.up_btn.setFixedSize(22, 22)
        self.up_btn.setToolTip("提高优先级")
        self.up_btn.clicked.connect(lambda: self.priority_changed.emit(self.column_name, self.priority - 1))
        layout.addWidget(self.up_btn)
        
        # 下移按钮
        self.down_btn = QToolButton()
        self.down_btn.setText("↓")
        self.down_btn.setFixedSize(22, 22)
        self.down_btn.setToolTip("降低优先级")
        self.down_btn.clicked.connect(lambda: self.priority_changed.emit(self.column_name, self.priority + 1))
        layout.addWidget(self.down_btn)
        
        # 删除按钮
        self.remove_btn = QToolButton()
        self.remove_btn.setText("×")
        self.remove_btn.setFixedSize(22, 22)
        self.remove_btn.setToolTip("移除排序")
        self.remove_btn.clicked.connect(lambda: self.remove_requested.emit(self.column_name))
        layout.addWidget(self.remove_btn)
        
        layout.addStretch()
    
    def _toggle_direction(self):
        """切换排序方向"""
        self.direction = "desc" if self.direction == "asc" else "asc"
        self._update_direction_icon()
        self.direction_changed.emit(self.column_name, self.direction)
    
    def _update_direction_icon(self):
        """更新方向图标"""
        if self.direction == "asc":
            self.direction_btn.setText("↑")
            self.direction_btn.setToolTip("升序")
        else:
            self.direction_btn.setText("↓")
            self.direction_btn.setToolTip("降序")
    
    def update_priority(self, priority: int):
        """更新优先级显示"""
        self.priority = priority
        self.priority_label.setText(f"{priority + 1}.")
    
    def set_button_states(self, can_move_up: bool, can_move_down: bool):
        """设置按钮状态"""
        self.up_btn.setEnabled(can_move_up)
        self.down_btn.setEnabled(can_move_down)


class MultiColumnSortWidget(QWidget):
    """
    多列排序配置组件
    
    提供可视化的多列排序配置界面
    """
    
    # 信号
    sort_changed = pyqtSignal(list)  # 排序配置变化
    sort_applied = pyqtSignal()  # 应用排序
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logger
        self.sort_manager = get_multi_sort_manager()
        self.sort_items: List[SortColumnItem] = []
        self.available_columns: List[str] = []
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题栏
        title_layout = QHBoxLayout()
        
        title_label = QLabel("多列排序设置")
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 折叠/展开按钮
        self.toggle_btn = QPushButton("隐藏")
        self.toggle_btn.setFixedSize(50, 22)
        self.toggle_btn.clicked.connect(self._toggle_visibility)
        title_layout.addWidget(self.toggle_btn)
        
        layout.addLayout(title_layout)
        
        # 主内容区
        self.content_widget = QWidget()
        content_layout = QVBoxLayout(self.content_widget)
        
        # 添加排序列
        add_layout = QHBoxLayout()
        
        add_layout.addWidget(QLabel("添加列:"))
        
        self.column_combo = QComboBox()
        self.column_combo.setMinimumWidth(150)
        add_layout.addWidget(self.column_combo)
        
        self.add_btn = QPushButton("添加")
        self.add_btn.clicked.connect(self._add_sort_column)
        add_layout.addWidget(self.add_btn)
        
        add_layout.addStretch()
        
        # 快速操作按钮
        self.clear_btn = QPushButton("清除全部")
        self.clear_btn.clicked.connect(self._clear_all)
        add_layout.addWidget(self.clear_btn)
        
        self.apply_btn = QPushButton("应用排序")
        self.apply_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        self.apply_btn.clicked.connect(self._apply_sort)
        add_layout.addWidget(self.apply_btn)
        
        content_layout.addLayout(add_layout)
        
        # 排序列列表
        self.sort_list_widget = QWidget()
        self.sort_list_layout = QVBoxLayout(self.sort_list_widget)
        self.sort_list_layout.setContentsMargins(0, 0, 0, 0)
        self.sort_list_layout.setSpacing(2)
        
        # 滚动区域
        from PyQt5.QtWidgets import QScrollArea
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.sort_list_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(200)
        content_layout.addWidget(scroll_area)
        
        # 排序摘要
        self.summary_label = QLabel("当前排序: 无")
        self.summary_label.setWordWrap(True)
        content_layout.addWidget(self.summary_label)
        
        # 高级选项
        advanced_group = QGroupBox("高级选项")
        advanced_layout = QVBoxLayout(advanced_group)
        
        self.auto_detect_type = QCheckBox("自动检测数据类型")
        self.auto_detect_type.setChecked(True)
        advanced_layout.addWidget(self.auto_detect_type)
        
        self.null_position_combo = QComboBox()
        self.null_position_combo.addItems(["空值排在最后", "空值排在最前"])
        advanced_layout.addWidget(self.null_position_combo)
        
        content_layout.addWidget(advanced_group)
        
        layout.addWidget(self.content_widget)
        
        # 设置样式
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def _connect_signals(self):
        """连接信号"""
        pass
    
    def set_available_columns(self, columns: List[str]):
        """
        设置可用列
        
        Args:
            columns: 列名列表
        """
        self.available_columns = columns
        
        # 更新下拉框
        self.column_combo.clear()
        
        # 过滤已添加的列
        existing_columns = [item.column_name for item in self.sort_items]
        available = [col for col in columns if col not in existing_columns]
        
        self.column_combo.addItems(available)
        self.add_btn.setEnabled(len(available) > 0)
    
    def _add_sort_column(self):
        """添加排序列"""
        column_name = self.column_combo.currentText()
        if not column_name:
            return
        
        # 添加到管理器
        if self.sort_manager.add_sort_column(column_name):
            # 创建UI项
            item = SortColumnItem(
                column_name=column_name,
                direction="asc",
                priority=len(self.sort_items)
            )
            
            # 连接信号
            item.direction_changed.connect(self._on_direction_changed)
            item.priority_changed.connect(self._on_priority_changed)
            item.remove_requested.connect(self._remove_sort_column)
            
            # 添加到列表
            self.sort_items.append(item)
            self.sort_list_layout.addWidget(item)
            
            # 更新可用列
            self.set_available_columns(self.available_columns)
            
            # 更新UI
            self._update_ui()
            
            self.logger.info(f"[P3优化] 添加排序列: {column_name}")
    
    def _remove_sort_column(self, column_name: str):
        """移除排序列"""
        # 从管理器移除
        if self.sort_manager.remove_sort_column(column_name):
            # 找到并移除UI项
            for item in self.sort_items:
                if item.column_name == column_name:
                    self.sort_list_layout.removeWidget(item)
                    item.deleteLater()
                    self.sort_items.remove(item)
                    break
            
            # 更新优先级
            self._update_priorities()
            
            # 更新可用列
            self.set_available_columns(self.available_columns)
            
            # 更新UI
            self._update_ui()
            
            self.logger.info(f"[P3优化] 移除排序列: {column_name}")
    
    def _on_direction_changed(self, column_name: str, direction: str):
        """处理方向变化"""
        self.sort_manager.toggle_sort_direction(column_name)
        self._update_ui()
        self.logger.info(f"[P3优化] 切换排序方向: {column_name} -> {direction}")
    
    def _on_priority_changed(self, column_name: str, new_priority: int):
        """处理优先级变化"""
        # 找到对应的项
        item_index = -1
        for i, item in enumerate(self.sort_items):
            if item.column_name == column_name:
                item_index = i
                break
        
        if item_index < 0:
            return
        
        # 计算新位置
        new_index = max(0, min(new_priority, len(self.sort_items) - 1))
        
        if new_index != item_index:
            # 移动项
            item = self.sort_items.pop(item_index)
            self.sort_items.insert(new_index, item)
            
            # 重新排列UI
            self._rearrange_items()
            
            # 更新管理器
            for i, item in enumerate(self.sort_items):
                self.sort_manager.update_priority(item.column_name, i)
            
            self._update_ui()
            self.logger.info(f"[P3优化] 更新优先级: {column_name} -> {new_index}")
    
    def _rearrange_items(self):
        """重新排列项"""
        # 清除布局
        while self.sort_list_layout.count():
            item = self.sort_list_layout.takeAt(0)
            if item.widget():
                item.widget().setParent(None)
        
        # 重新添加
        for item in self.sort_items:
            self.sort_list_layout.addWidget(item)
        
        self._update_priorities()
    
    def _update_priorities(self):
        """更新优先级显示"""
        for i, item in enumerate(self.sort_items):
            item.update_priority(i)
            item.set_button_states(i > 0, i < len(self.sort_items) - 1)
    
    def _clear_all(self):
        """清除所有排序"""
        # 清除管理器
        self.sort_manager.clear_all()
        
        # 清除UI
        for item in self.sort_items:
            self.sort_list_layout.removeWidget(item)
            item.deleteLater()
        
        self.sort_items.clear()
        
        # 更新可用列
        self.set_available_columns(self.available_columns)
        
        # 更新UI
        self._update_ui()
        
        self.logger.info("[P3优化] 清除所有排序")
    
    def _apply_sort(self):
        """应用排序"""
        # 发出信号
        config = self.sort_manager.get_sort_config()
        self.sort_changed.emit(config)
        self.sort_applied.emit()
        
        self.logger.info(f"[P3优化] 应用排序: {len(config)}列")
    
    def _update_ui(self):
        """更新UI显示"""
        # 更新摘要
        summary = self.sort_manager.get_sort_summary()
        self.summary_label.setText(f"当前排序: {summary}")
        
        # 更新按钮状态
        self.apply_btn.setEnabled(len(self.sort_items) > 0)
        self.clear_btn.setEnabled(len(self.sort_items) > 0)
        
        # 发送变化信号
        config = self.sort_manager.get_sort_config()
        self.sort_changed.emit(config)
    
    def _toggle_visibility(self):
        """切换可见性"""
        visible = self.content_widget.isVisible()
        self.content_widget.setVisible(not visible)
        self.toggle_btn.setText("显示" if not visible else "隐藏")
    
    def load_sort_config(self, config: List[Dict[str, Any]]):
        """
        加载排序配置
        
        Args:
            config: 排序配置
        """
        # 清除现有配置
        self._clear_all()
        
        # 设置新配置
        self.sort_manager.set_sort_config(config)
        
        # 创建UI项
        for item_config in config:
            column_name = item_config.get('column_name', item_config.get('column', ''))
            direction = item_config.get('sort_order', item_config.get('order', 'asc'))
            priority = item_config.get('priority', len(self.sort_items))
            
            item = SortColumnItem(
                column_name=column_name,
                direction=direction,
                priority=priority
            )
            
            # 连接信号
            item.direction_changed.connect(self._on_direction_changed)
            item.priority_changed.connect(self._on_priority_changed)
            item.remove_requested.connect(self._remove_sort_column)
            
            self.sort_items.append(item)
            self.sort_list_layout.addWidget(item)
        
        # 更新UI
        self._update_priorities()
        self._update_ui()
        
        self.logger.info(f"[P3优化] 加载排序配置: {len(config)}列")