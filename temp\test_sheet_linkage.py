#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sheet选择联动功能测试脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import Qt
from src.gui.unified_data_import_window import UnifiedDataImportWindow

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Sheet选择联动功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 说明标签
        info_label = QLabel("""
测试说明：
1. 点击下面的按钮打开数据导入窗口
2. 选择一个Excel文件
3. 在左侧Sheet列表中点击不同的Sheet
4. 观察右侧"字段映射"和"预览验证"选项卡是否正确更新

预期行为：
- 点击左侧Sheet项时，右侧选项卡应该显示对应Sheet的内容
- 字段映射选项卡应该显示该Sheet的字段列表
- 预览验证选项卡应该显示该Sheet的数据预览
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc;")
        layout.addWidget(info_label)
        
        # 测试按钮
        test_button = QPushButton("打开数据导入窗口")
        test_button.clicked.connect(self.open_import_window)
        test_button.setStyleSheet("padding: 10px; font-size: 14px; font-weight: bold;")
        layout.addWidget(test_button)
        
        # 状态标签
        self.status_label = QLabel("状态: 准备就绪")
        self.status_label.setStyleSheet("padding: 5px; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
    
    def open_import_window(self):
        """打开数据导入窗口"""
        try:
            self.status_label.setText("状态: 正在打开数据导入窗口...")
            
            # 创建数据导入窗口
            self.import_window = UnifiedDataImportWindow(self)
            
            # 显示窗口
            self.import_window.show()
            
            self.status_label.setText("状态: 数据导入窗口已打开，请测试Sheet选择联动功能")
            
        except Exception as e:
            self.status_label.setText(f"状态: 打开窗口失败 - {e}")
            print(f"错误详情: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
