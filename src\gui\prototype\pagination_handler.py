"""
分页处理器 - 统一的分页逻辑处理
"""
import time
import uuid
import threading
from typing import Optional, List, Dict, Any
from loguru import logger
import pandas as pd
from PyQt5.QtCore import QObject


class PaginationHandler(QObject):
    """统一的分页处理器"""
    
    def __init__(self, main_window, table_data_service):
        super().__init__()
        self.main_window = main_window
        self.table_data_service = table_data_service
        self.logger = logger
        self._pagination_lock = threading.RLock()
        self._processing = False
        
        # 🔧 [P2优化] 分页缓存
        self._page_cache = {}
        self._cache_ttl = 300  # 缓存5分钟
        self._max_cache_size = 20  # 最多缓存20页
        
        # 🔧 [P3优化] 字段映射管理器
        self._field_mapping_manager = None
        
        # 🔧 [P0修复] 统一状态管理器
        from src.core.unified_state_management import get_unified_state_manager
        self._unified_state_manager = get_unified_state_manager()
        self._sort_state_manager = self._unified_state_manager  # 兼容旧接口
        
    def handle_page_change(self, page: int) -> bool:
        """
        处理分页变化
        
        Args:
            page: 目标页码
            
        Returns:
            bool: 是否成功处理
        """
        try:
            # 生成请求ID
            rid = self._generate_request_id()
            table_name = self._get_current_table_name()
            
            if not table_name:
                self.logger.warning("无法获取当前表名，跳过分页")
                return False
                
            self.logger.info(f"📍[page-change] 处理分页 | table={table_name} | page={page} | rid={rid}")
            
            # 检查是否正在处理
            if not self._acquire_lock():
                self.logger.info(f"分页处理正在进行中，跳过: 第{page}页")
                return False
                
            try:
                # 设置分页上下文
                self._set_pagination_context(page)
                
                # 获取分页参数
                page_size = self._get_page_size()
                sort_columns = self._get_sort_columns()
                
                # 检查去重
                if not self._check_deduplication(table_name, page, sort_columns):
                    return False
                    
                # 加载数据
                response = self._load_page_data(
                    table_name, page, page_size, sort_columns, rid
                )
                
                if not response or not response.success:
                    self.logger.error(f"分页数据加载失败: 第{page}页")
                    return False
                    
                # 处理响应数据
                self._process_page_response(response, page, page_size, table_name)
                
                # 🔧 [P1修复] 更新统一状态管理器
                if self._unified_state_manager:
                    # 修复：使用正确的方法名 save_pagination_state
                    self._unified_state_manager.save_pagination_state(table_name, page, page_size)
                    self.logger.debug(f"[P1] 已更新统一状态: {table_name} 第{page}页")
                
                return True
                
            finally:
                self._release_lock()
                
        except Exception as e:
            self.logger.error(f"分页处理失败: {e}", exc_info=True)
            return False
            
    def _generate_request_id(self) -> str:
        """生成请求ID"""
        return f"PG-{int(time.time()*1000)}-{uuid.uuid4().hex[:8]}"
        
    def _get_current_table_name(self) -> Optional[str]:
        """获取当前表名"""
        return getattr(self.main_window, 'current_table_name', None) or \
               getattr(self.main_window.main_workspace, 'current_table_name', None)
               
    def _acquire_lock(self) -> bool:
        """获取处理锁"""
        with self._pagination_lock:
            if self._processing:
                return False
            self._processing = True
            return True
            
    def _release_lock(self):
        """释放处理锁"""
        with self._pagination_lock:
            self._processing = False
            
    def _set_pagination_context(self, page: int):
        """设置分页上下文"""
        self.main_window._current_operation_context = {
            'operation_type': 'page_change',
            'target_page': page,
            'timestamp': time.time(),
            'persistent': True
        }
        
        # 设置分页模式标志
        if hasattr(self.main_window.main_workspace, 'expandable_table'):
            self.main_window.main_workspace.expandable_table._pagination_mode = True
            
    def _get_page_size(self) -> int:
        """获取页大小"""
        if hasattr(self.main_window.main_workspace, 'pagination_widget'):
            return getattr(self.main_window.main_workspace.pagination_widget, 'page_size', 50)
        return 50
        
    def _get_sort_columns(self) -> List[Dict]:
        """获取排序列 - P0修复：从持久化状态获取"""
        table_name = self._get_current_table_name()
        
        # 优先从持久化状态获取
        if table_name and self._sort_state_manager:
            saved_sort = self._sort_state_manager.get_sort_state(table_name)
            if saved_sort:
                self.logger.debug(f"使用持久化排序状态: {table_name}, 列数={len(saved_sort)}")
                return saved_sort
        
        # 其次从主窗口获取
        if hasattr(self.main_window, 'current_sort_columns'):
            return self.main_window.current_sort_columns
            
        return []
        
    def _check_deduplication(self, table_name: str, page: int, sort_columns: List) -> bool:
        """检查去重"""
        from src.core.request_deduplication_manager import RequestDeduplicationManager
        dedup_manager = RequestDeduplicationManager()
        
        if not dedup_manager.should_allow_pagination_request(table_name, page, sort_columns):
            self.logger.info(f"分页请求被去重: 第{page}页")
            return False
            
        return True
        
    def _load_page_data(self, table_name: str, page: int, page_size: int, 
                       sort_columns: List, rid: str) -> Any:
        """加载分页数据"""
        # 🔧 [P2优化] 检查缓存
        cache_key = self._get_cache_key(table_name, page, page_size, sort_columns)
        cached_data = self._get_cached_page(cache_key)
        
        if cached_data is not None:
            self.logger.info(f"🔧 [P2优化] 使用缓存数据: {cache_key}")
            return cached_data
            
        # 判断是否需要强制重载
        is_sort_operation = self.main_window._current_operation_context.get('operation_type') == 'sort_change'
        force_reload = is_sort_operation
        
        response = self.table_data_service.load_table_data(
            table_name=table_name,
            page=page,
            page_size=page_size,
            sort_columns=sort_columns,
            force_reload=force_reload,
            request_id=rid
        )
        
        # 🔧 [P2优化] 缓存成功的响应
        if response and response.success:
            self._cache_page(cache_key, response)
            
        return response
        
    def _get_cache_key(self, table_name: str, page: int, page_size: int, sort_columns: List) -> str:
        """生成缓存键"""
        sort_key = "_".join([f"{col.get('column_name', '')}{col.get('order', '')}" 
                            for col in sort_columns]) if sort_columns else "nosort"
        return f"{table_name}_p{page}_s{page_size}_{sort_key}"
        
    def _get_cached_page(self, cache_key: str) -> Optional[Any]:
        """获取缓存的页面数据"""
        if cache_key in self._page_cache:
            cached_item = self._page_cache[cache_key]
            # 检查是否过期
            if time.time() - cached_item['timestamp'] < self._cache_ttl:
                return cached_item['data']
            else:
                # 过期，删除缓存
                del self._page_cache[cache_key]
        return None
        
    def _cache_page(self, cache_key: str, response: Any):
        """缓存页面数据"""
        # 检查缓存大小
        if len(self._page_cache) >= self._max_cache_size:
            # 删除最旧的缓存项
            oldest_key = min(self._page_cache.keys(), 
                           key=lambda k: self._page_cache[k]['timestamp'])
            del self._page_cache[oldest_key]
            
        self._page_cache[cache_key] = {
            'data': response,
            'timestamp': time.time()
        }
        
    def clear_cache(self, table_name: Optional[str] = None):
        """清除缓存"""
        if table_name:
            # 清除特定表的缓存
            keys_to_remove = [k for k in self._page_cache.keys() if k.startswith(table_name)]
            for key in keys_to_remove:
                del self._page_cache[key]
            self.logger.info(f"🔧 [P2优化] 清除表 {table_name} 的缓存: {len(keys_to_remove)} 项")
        else:
            # 清除所有缓存
            self._page_cache.clear()
            self.logger.info("🔧 [P2优化] 清除所有分页缓存")
        
    def _process_page_response(self, response, page: int, page_size: int, table_name: str):
        """处理分页响应"""
        try:
            # 更新分页组件状态
            self._update_pagination_widget(response, page, page_size)
            
            # 处理数据
            if response.data is not None:
                self._update_ui_data(response.data, table_name, page, page_size)
                
        finally:
            # 清除分页模式标志
            if hasattr(self.main_window.main_workspace, 'expandable_table'):
                self.main_window.main_workspace.expandable_table._pagination_mode = False
                
    def _update_pagination_widget(self, response, page: int, page_size: int):
        """更新分页组件"""
        if hasattr(self.main_window.main_workspace, 'pagination_widget'):
            widget = self.main_window.main_workspace.pagination_widget
            rid = getattr(response, 'request_id', None) or self._generate_request_id()
            
            widget.batch_update(
                total_records=getattr(response, 'total_records', 0),
                current_page=page,
                page_size=page_size,
                source="pagination_handler",
                request_id=rid
            )
            
    def _update_ui_data(self, data, table_name: str, page: int, page_size: int):
        """更新UI数据"""
        # 🔧 [P3优化] 确保字段映射完整性
        self._ensure_field_mapping_complete(table_name, data)
        
        # 🔧 [P3优化] 验证映射
        if hasattr(data, 'columns'):
            validation_result = self._validate_field_mapping(table_name, list(data.columns))
            if not validation_result.get('is_valid', True):
                self.logger.warning(f"🔧 [P3优化] 字段映射验证警告: {validation_result}")
        
        # 应用字段映射
        mapped_data = self.main_window._apply_field_mapping_to_dataframe(data, table_name)
        
        # 过滤系统字段
        filtered_data = self.main_window._apply_system_field_filtering(mapped_data, table_name)
        
        # 设置数据到UI
        self.main_window.main_workspace.set_data(
            df=filtered_data,
            preserve_headers=False,
            table_name=table_name,
            current_table_name=table_name
        )
        
        # 恢复UI状态
        self.main_window._restore_table_ui_state(table_name)
        
        # 强制刷新表头
        self._force_refresh_headers(filtered_data, page, page_size)
        
    def _force_refresh_headers(self, data, page: int, page_size: int):
        """强制刷新表头"""
        if hasattr(self.main_window.main_workspace, 'expandable_table'):
            table = self.main_window.main_workspace.expandable_table
            if hasattr(table, 'header_update_manager'):
                headers = list(data.columns)
                start_record = (page - 1) * page_size + 1
                count = len(data)
                
                table.header_update_manager.force_update_all(
                    headers=headers,
                    start_record=start_record,
                    count=count
                )
                
    def _get_field_mapping_manager(self):
        """获取字段映射管理器（懒加载）"""
        if self._field_mapping_manager is None:
            from src.core.field_mapping_manager import FieldMappingManager
            self._field_mapping_manager = FieldMappingManager()
        return self._field_mapping_manager
        
    def _ensure_field_mapping_complete(self, table_name: str, data):
        """确保字段映射完整"""
        try:
            manager = self._get_field_mapping_manager()
            
            # 🔧 [P3优化] 分页时强制重新加载映射
            if hasattr(self, '_last_mapped_table') and self._last_mapped_table != table_name:
                manager.reload_mappings()
                self.logger.info(f"🔧 [P3优化] 切换表时重新加载映射: {table_name}")
            
            self._last_mapped_table = table_name
            
            # 确保映射完整
            manager.ensure_table_mapping_complete(table_name)
            
        except Exception as e:
            self.logger.error(f"🔧 [P3优化] 确保字段映射完整失败: {e}")
            
    def _validate_field_mapping(self, table_name: str, columns: List[str]) -> Dict:
        """验证字段映射"""
        try:
            manager = self._get_field_mapping_manager()
            return manager.validate_mapping(table_name, columns)
        except Exception as e:
            self.logger.error(f"🔧 [P3优化] 验证字段映射失败: {e}")
            return {"is_valid": False, "error": str(e)}