"""
字段类型管理器
提供自定义字段类型的创建、编辑和管理功能
"""

import json
import os
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
from loguru import logger


class FieldTypeManager:
    """字段类型管理器"""
    
    def __init__(self, storage_dir: str = None):
        """
        初始化字段类型管理器
        
        Args:
            storage_dir: 存储目录，默认为 state/field_types
        """
        if storage_dir is None:
            storage_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                'state', 'field_types'
            )
        
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 文件路径
        self.custom_types_file = self.storage_dir / 'custom_field_types.json'
        self.user_rules_file = self.storage_dir / 'user_formatting_rules.json'
        
        # 加载数据
        self.custom_field_types = self._load_custom_field_types()
        self.user_formatting_rules = self._load_user_formatting_rules()
        
        logger.info(f"字段类型管理器初始化完成，存储目录: {self.storage_dir}")
    
    def _load_custom_field_types(self) -> Dict[str, Any]:
        """加载自定义字段类型"""
        if self.custom_types_file.exists():
            try:
                with open(self.custom_types_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载自定义字段类型失败: {e}")
                return {}
        return {}
    
    def _load_user_formatting_rules(self) -> Dict[str, Any]:
        """加载用户格式化规则"""
        if self.user_rules_file.exists():
            try:
                with open(self.user_rules_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载用户格式化规则失败: {e}")
                return {}
        return {}
    
    def _save_custom_field_types(self):
        """保存自定义字段类型"""
        try:
            with open(self.custom_types_file, 'w', encoding='utf-8') as f:
                json.dump(self.custom_field_types, f, ensure_ascii=False, indent=2)
            logger.info("自定义字段类型已保存")
        except Exception as e:
            logger.error(f"保存自定义字段类型失败: {e}")
    
    def _save_user_formatting_rules(self):
        """保存用户格式化规则"""
        try:
            with open(self.user_rules_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_formatting_rules, f, ensure_ascii=False, indent=2)
            logger.info("用户格式化规则已保存")
        except Exception as e:
            logger.error(f"保存用户格式化规则失败: {e}")
    
    def create_field_type(self, type_id: str, type_name: str, description: str,
                         base_rule_type: str, default_config: Dict[str, Any] = None,
                         validation_rules: Dict[str, Any] = None) -> bool:
        """
        创建自定义字段类型
        
        Args:
            type_id: 类型标识符（唯一）
            type_name: 类型名称
            description: 类型描述
            base_rule_type: 基础规则类型（number, string, date, code, custom）
            default_config: 默认配置
            validation_rules: 验证规则
            
        Returns:
            是否创建成功
        """
        if type_id in self.custom_field_types:
            logger.warning(f"字段类型 {type_id} 已存在")
            return False
        
        field_type_info = {
            "id": type_id,
            "name": type_name,
            "description": description,
            "rule_type": base_rule_type,
            "default_config": default_config or {},
            "validation_rules": validation_rules or {},
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "is_custom": True
        }
        
        self.custom_field_types[type_id] = field_type_info
        self._save_custom_field_types()
        
        logger.info(f"创建自定义字段类型: {type_id} - {type_name}")
        return True
    
    def update_field_type(self, type_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新字段类型
        
        Args:
            type_id: 类型标识符
            updates: 更新内容
            
        Returns:
            是否更新成功
        """
        if type_id not in self.custom_field_types:
            logger.warning(f"字段类型 {type_id} 不存在")
            return False
        
        # 更新字段
        field_type = self.custom_field_types[type_id]
        for key, value in updates.items():
            if key != "id":  # 不允许修改ID
                field_type[key] = value
        
        field_type["updated_at"] = datetime.now().isoformat()
        
        self._save_custom_field_types()
        logger.info(f"更新字段类型: {type_id}")
        return True
    
    def delete_field_type(self, type_id: str) -> bool:
        """
        删除字段类型
        
        Args:
            type_id: 类型标识符
            
        Returns:
            是否删除成功
        """
        if type_id not in self.custom_field_types:
            logger.warning(f"字段类型 {type_id} 不存在")
            return False
        
        del self.custom_field_types[type_id]
        self._save_custom_field_types()
        
        logger.info(f"删除字段类型: {type_id}")
        return True
    
    def get_field_type(self, type_id: str) -> Optional[Dict[str, Any]]:
        """
        获取字段类型信息
        
        Args:
            type_id: 类型标识符
            
        Returns:
            字段类型信息
        """
        return self.custom_field_types.get(type_id)
    
    def list_custom_field_types(self) -> List[Dict[str, Any]]:
        """
        列出所有自定义字段类型
        
        Returns:
            字段类型列表
        """
        return list(self.custom_field_types.values())
    
    def create_formatting_rule(self, rule_id: str, rule_name: str, rule_type: str,
                              rule_config: Dict[str, Any], 
                              applicable_types: List[str] = None) -> bool:
        """
        创建格式化规则
        
        Args:
            rule_id: 规则ID
            rule_name: 规则名称
            rule_type: 规则类型
            rule_config: 规则配置
            applicable_types: 适用的字段类型列表
            
        Returns:
            是否创建成功
        """
        if rule_id in self.user_formatting_rules:
            logger.warning(f"格式化规则 {rule_id} 已存在")
            return False
        
        rule_info = {
            "id": rule_id,
            "name": rule_name,
            "type": rule_type,
            "config": rule_config,
            "applicable_types": applicable_types or [],
            "created_at": datetime.now().isoformat(),
            "is_active": True
        }
        
        self.user_formatting_rules[rule_id] = rule_info
        self._save_user_formatting_rules()
        
        logger.info(f"创建格式化规则: {rule_id} - {rule_name}")
        return True
    
    def get_formatting_rule(self, rule_id: str) -> Optional[Dict[str, Any]]:
        """获取格式化规则"""
        return self.user_formatting_rules.get(rule_id)
    
    def list_formatting_rules(self, field_type: str = None) -> List[Dict[str, Any]]:
        """
        列出格式化规则
        
        Args:
            field_type: 筛选特定字段类型的规则
            
        Returns:
            规则列表
        """
        rules = list(self.user_formatting_rules.values())
        
        if field_type:
            # 筛选适用于特定字段类型的规则
            rules = [
                rule for rule in rules
                if not rule.get("applicable_types") or field_type in rule.get("applicable_types", [])
            ]
        
        return rules
    
    def export_field_types(self, export_path: str) -> bool:
        """
        导出字段类型配置
        
        Args:
            export_path: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            export_data = {
                "custom_field_types": self.custom_field_types,
                "formatting_rules": self.user_formatting_rules,
                "export_time": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"字段类型配置导出到: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出字段类型配置失败: {e}")
            return False
    
    def import_field_types(self, import_path: str, overwrite: bool = False) -> bool:
        """
        导入字段类型配置
        
        Args:
            import_path: 导入文件路径
            overwrite: 是否覆盖现有配置
            
        Returns:
            是否导入成功
        """
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            # 导入自定义字段类型
            if "custom_field_types" in import_data:
                if overwrite:
                    self.custom_field_types = import_data["custom_field_types"]
                else:
                    self.custom_field_types.update(import_data["custom_field_types"])
                self._save_custom_field_types()
            
            # 导入格式化规则
            if "formatting_rules" in import_data:
                if overwrite:
                    self.user_formatting_rules = import_data["formatting_rules"]
                else:
                    self.user_formatting_rules.update(import_data["formatting_rules"])
                self._save_user_formatting_rules()
            
            logger.info(f"从 {import_path} 导入字段类型配置成功")
            return True
            
        except Exception as e:
            logger.error(f"导入字段类型配置失败: {e}")
            return False
    
    def generate_field_type_code(self, type_id: str) -> Optional[str]:
        """
        生成字段类型的Python代码
        
        Args:
            type_id: 类型标识符
            
        Returns:
            生成的Python代码
        """
        field_type = self.custom_field_types.get(type_id)
        if not field_type:
            return None
        
        # 生成代码模板
        code = f"""
# 自定义字段类型: {field_type['name']}
# {field_type['description']}

def format_function(value, config):
    \"\"\"格式化函数\"\"\"
    import pandas as pd
    
    if pd.isna(value):
        return ""
    
    # 在这里实现您的格式化逻辑
    result = str(value)
    
    # 示例：根据配置处理
    if config.get('uppercase'):
        result = result.upper()
    
    return result

def validate_function(value, config):
    \"\"\"验证函数\"\"\"
    import pandas as pd
    
    if pd.isna(value):
        return True
    
    # 在这里实现您的验证逻辑
    # 返回 True 表示验证通过，False 表示验证失败
    
    return True
"""
        return code
    
    def get_builtin_field_types(self) -> Dict[str, Dict[str, Any]]:
        """
        获取内置字段类型（从格式化引擎获取）
        
        Returns:
            内置字段类型字典
        """
        try:
            from src.modules.data_import.formatting_engine import get_formatting_engine
            engine = get_formatting_engine()
            return engine.get_field_types()
        except Exception as e:
            logger.error(f"获取内置字段类型失败: {e}")
            return {}
    
    def get_all_field_types(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有字段类型（内置 + 自定义）
        
        Returns:
            所有字段类型字典
        """
        all_types = self.get_builtin_field_types()
        all_types.update(self.custom_field_types)
        return all_types