"""
配置管理对话窗口
提供图形化界面来管理系统的各种配置文件和用户偏好设置
"""

import os
import json
import copy
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QTreeWidget, QTreeWidgetItem, QScrollArea, QFormLayout,
    QLineEdit, QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox,
    QPushButton, QGroupBox, QLabel, QTextEdit, QFileDialog,
    QMessageBox, QSplitter, QHeaderView, QFrame, QGridLayout
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from src.utils.log_config import setup_logger


class ConfigManagerDialog(QDialog):
    """配置管理对话窗口"""
    
    config_changed = pyqtSignal(str, dict)  # 配置改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 配置文件路径
        self.config_paths = {
            'main_config': 'config.json',
            'user_preferences': 'user_preferences.json',
            'field_mappings': 'state/data/field_mappings.json'
        }
        
        # 存储原始配置和当前配置
        self.original_configs = {}
        self.current_configs = {}
        
        # 配置项中英文映射字典
        self.config_translations = {
            # 通用配置项
            'version': '版本',
            'debug_mode': '调试模式',
            'log_level': '日志级别',
            'language': '语言设置',
            
            # 数据库配置
            'database': '数据库',
            'db_name': '数据库名称',
            'db_path': '数据库路径',
            'backup_enabled': '启用备份',
            'backup_count': '备份数量',
            'connection_timeout': '连接超时时间',
            
            # 导入设置
            'import_settings': '导入设置',
            'supported_formats': '支持的格式',
            'max_file_size_mb': '最大文件大小(MB)',
            'chunk_size': '分块大小',
            'encoding_priority': '编码优先级',
            'default_import_encoding': '默认导入编码',
            'auto_detect_encoding': '自动检测编码',
            'skip_empty_rows': '跳过空行',
            'trim_whitespace': '去除空白字符',
            
            # 字段映射
            'field_mapping': '字段映射',
            'auto_generate_mappings': '自动生成映射',
            'force_mapping_for_unknown_sheets': '强制映射未知表格',
            'default_excel_header_as_display_name': '使用Excel表头作为显示名称',
            'preserve_chinese_headers': '保留中文表头',
            'fuzzy_match_threshold': '模糊匹配阈值',
            'save_auto_generated_mappings': '保存自动生成的映射',
            'enable_smart_suggestions': '启用智能建议',
            'save_edit_history': '保存编辑历史',
            
            # 处理设置
            'processing': '处理设置',
            'match_threshold': '匹配阈值',
            'calculation_precision': '计算精度',
            'async_processing': '异步处理',
            'max_workers': '最大工作线程数',
            'progress_update_interval': '进度更新间隔',
            'validation_level': '验证级别',
            
            # 界面设置
            'ui': '界面设置',
            'theme': '主题',
            'window_width': '窗口宽度',
            'window_height': '窗口高度',
            'auto_save_interval': '自动保存间隔',
            'show_progress': '显示进度',
            'font_family': '字体',
            'font_size': '字体大小',
            'window_maximized': '窗口最大化',
            'window_position': '窗口位置',
            'window_size': '窗口大小',
            'show_toolbar': '显示工具栏',
            'show_statusbar': '显示状态栏',
            'show_sidebar': '显示侧边栏',
            'show_menubar': '显示菜单栏',
            'sidebar_width': '侧边栏宽度',
            'auto_save_layout': '自动保存布局',
            'x': 'X坐标',
            'y': 'Y坐标',
            'width': '宽度',
            'height': '高度',
            
            # 报告设置
            'reports': '报告设置',
            'default_template_dir': '默认模板目录',
            'output_dir': '输出目录',
            'auto_open_report': '自动打开报告',
            'export_formats': '导出格式',
            'default_output_format': '默认输出格式',
            'auto_open_reports': '自动打开报告',
            'save_report_history': '保存报告历史',
            'default_output_directory': '默认输出目录',
            'include_timestamp_in_filename': '文件名包含时间戳',
            'compress_large_reports': '压缩大型报告',
            'report_quality': '报告质量',
            
            # 行为设置
            'behavior': '行为设置',
            'auto_save_enabled': '启用自动保存',
            'confirm_before_exit': '退出前确认',
            'confirm_before_delete': '删除前确认',
            'remember_last_files': '记住最近文件',
            'max_recent_files': '最近文件数量',
            'auto_backup': '自动备份',
            'show_welcome_dialog': '显示欢迎对话框',
            
            # 数据处理偏好
            'data_processing': '数据处理',
            
            # 高级设置
            'advanced': '高级设置',
            'enable_debug_mode': '启用调试模式',
            'memory_limit_mb': '内存限制(MB)',
            'max_parallel_processes': '最大并行进程数',
            'cache_enabled': '启用缓存',
            'cache_size_mb': '缓存大小(MB)',
            'performance_monitoring': '性能监控',
            
            # 快捷键设置
            'shortcuts': '快捷键',
            'new_file': '新建文件',
            'open_file': '打开文件',
            'save_file': '保存文件',
            'save_as': '另存为',
            'exit': '退出',
            'undo': '撤销',
            'redo': '重做',
            'copy': '复制',
            'paste': '粘贴',
            'find': '查找',
            'replace': '替换',
            
            # 字段映射相关
            'global_settings': '全局设置',
            'table_mappings': '表映射',
            'field_templates': '字段模板',
            'user_preferences': '用户偏好',
            'default_field_patterns': '默认字段模式',
            'recent_edits': '最近编辑',
            'favorite_mappings': '收藏的映射'
        }
        
        # 设置对话框
        self.setWindowTitle("系统配置管理")
        self.setModal(True)
        self.resize(1000, 700)
        
        self.setup_ui()
        self.load_all_configs()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：配置文件树
        self.setup_config_tree(splitter)
        
        # 右侧：配置编辑区域
        self.setup_config_editor(splitter)
        
        # 设置分割器比例
        splitter.setSizes([300, 700])
        
        # 底部按钮
        self.setup_buttons(layout)
        
    def setup_config_tree(self, parent):
        """设置左侧配置树"""
        tree_frame = QFrame()
        tree_layout = QVBoxLayout(tree_frame)
        
        # 标题
        title_label = QLabel("配置文件")
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        tree_layout.addWidget(title_label)
        
        # 配置树
        self.config_tree = QTreeWidget()
        self.config_tree.setHeaderLabel("配置项目")
        self.config_tree.itemClicked.connect(self.on_tree_item_clicked)
        tree_layout.addWidget(self.config_tree)
        
        # 快速操作按钮
        quick_buttons_layout = QVBoxLayout()
        
        self.btn_reset_all = QPushButton("重置所有配置")
        self.btn_reset_all.clicked.connect(self.reset_all_configs)
        quick_buttons_layout.addWidget(self.btn_reset_all)
        
        self.btn_backup_configs = QPushButton("备份当前配置")
        self.btn_backup_configs.clicked.connect(self.backup_configs)
        quick_buttons_layout.addWidget(self.btn_backup_configs)
        
        self.btn_restore_configs = QPushButton("恢复配置备份")
        self.btn_restore_configs.clicked.connect(self.restore_configs)
        quick_buttons_layout.addWidget(self.btn_restore_configs)
        
        tree_layout.addLayout(quick_buttons_layout)
        parent.addWidget(tree_frame)
        
    def setup_config_editor(self, parent):
        """设置右侧配置编辑区域"""
        editor_frame = QFrame()
        editor_layout = QVBoxLayout(editor_frame)
        
        # 当前配置文件标题
        self.config_title = QLabel("请选择配置项目")
        self.config_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        editor_layout.addWidget(self.config_title)
        
        # 配置描述
        self.config_description = QLabel("")
        self.config_description.setWordWrap(True)
        self.config_description.setStyleSheet("color: #666; margin-bottom: 10px;")
        editor_layout.addWidget(self.config_description)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 配置表单容器
        self.config_widget = QWidget()
        self.config_layout = QVBoxLayout(self.config_widget)
        scroll_area.setWidget(self.config_widget)
        
        editor_layout.addWidget(scroll_area)
        
        # 当前配置操作按钮
        config_buttons_layout = QHBoxLayout()
        
        self.btn_reset_current = QPushButton("重置当前配置")
        self.btn_reset_current.clicked.connect(self.reset_current_config)
        config_buttons_layout.addWidget(self.btn_reset_current)
        
        self.btn_validate_config = QPushButton("验证配置")
        self.btn_validate_config.clicked.connect(self.validate_current_config)
        config_buttons_layout.addWidget(self.btn_validate_config)
        
        config_buttons_layout.addStretch()
        editor_layout.addLayout(config_buttons_layout)
        
        parent.addWidget(editor_frame)
        
    def setup_buttons(self, layout):
        """设置底部按钮"""
        buttons_layout = QHBoxLayout()
        
        self.btn_save = QPushButton("保存配置")
        self.btn_save.clicked.connect(self.save_configs)
        self.btn_save.setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px 16px; }")
        
        self.btn_cancel = QPushButton("取消")
        self.btn_cancel.clicked.connect(self.reject)
        
        self.btn_apply = QPushButton("应用")
        self.btn_apply.clicked.connect(self.apply_configs)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.btn_save)
        buttons_layout.addWidget(self.btn_apply)
        buttons_layout.addWidget(self.btn_cancel)
        
        layout.addLayout(buttons_layout)
        
    def load_all_configs(self):
        """加载所有配置文件"""
        self.original_configs = {}
        self.current_configs = {}
        
        for config_name, config_path in self.config_paths.items():
            try:
                full_path = os.path.join(os.getcwd(), config_path)
                if os.path.exists(full_path):
                    with open(full_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    self.original_configs[config_name] = config_data
                    self.current_configs[config_name] = copy.deepcopy(config_data)
                else:
                    # 如果文件不存在，使用默认配置
                    default_config = self.get_default_config(config_name)
                    self.original_configs[config_name] = default_config
                    self.current_configs[config_name] = copy.deepcopy(default_config)
            except Exception as e:
                self.logger.error(f"加载配置文件 {config_path} 失败: {e}")
                
        self.populate_config_tree()
        
    def get_default_config(self, config_name: str) -> Dict[str, Any]:
        """获取默认配置"""
        defaults = {
            'main_config': {
                "version": "1.0.0",
                "debug_mode": False,
                "log_level": "INFO",
                "database": {
                    "db_name": "salary_system.db",
                    "db_path": "data/db",
                    "backup_enabled": True,
                    "backup_count": 5,
                    "connection_timeout": 30
                },
                "import_settings": {
                    "supported_formats": [".xlsx", ".xls", ".csv"],
                    "max_file_size_mb": 100,
                    "chunk_size": 1000,
                    "encoding_priority": ["utf-8", "gbk", "gb2312"]
                },
                "field_mapping": {
                    "auto_generate_mappings": True,
                    "force_mapping_for_unknown_sheets": True,
                    "default_excel_header_as_display_name": True,
                    "preserve_chinese_headers": True,
                    "fuzzy_match_threshold": 0.7,
                    "save_auto_generated_mappings": True
                },
                "processing": {
                    "match_threshold": 0.95,
                    "calculation_precision": 2,
                    "async_processing": True,
                    "max_workers": 4
                },
                "ui": {
                    "theme": "default",
                    "window_width": 1200,
                    "window_height": 800,
                    "auto_save_interval": 300,
                    "show_progress": True
                },
                "reports": {
                    "default_template_dir": "template",
                    "output_dir": "output",
                    "auto_open_report": True,
                    "export_formats": ["docx", "pdf", "xlsx"]
                }
            },
            'user_preferences': {
                "ui": {
                    "theme": "default",
                    "font_family": "Microsoft YaHei",
                    "font_size": 10,
                    "window_maximized": False,
                    "window_position": {"x": 100, "y": 100},
                    "window_size": {"width": 1200, "height": 800},
                    "show_toolbar": True,
                    "show_statusbar": True,
                    "show_sidebar": True,
                    "show_menubar": True,
                    "sidebar_width": 200,
                    "auto_save_layout": True
                },
                "behavior": {
                    "auto_save_enabled": True,
                    "auto_save_interval": 300,
                    "confirm_before_exit": True,
                    "confirm_before_delete": True,
                    "remember_last_files": True,
                    "max_recent_files": 10,
                    "auto_backup": True,
                    "show_welcome_dialog": True
                },
                "data_processing": {
                    "default_import_encoding": "utf-8",
                    "chunk_size": 1000,
                    "progress_update_interval": 100,
                    "validation_level": "strict",
                    "auto_detect_encoding": True,
                    "skip_empty_rows": True,
                    "trim_whitespace": True
                },
                "reports": {
                    "default_output_format": "docx",
                    "auto_open_reports": True,
                    "save_report_history": True,
                    "default_output_directory": "output",
                    "include_timestamp_in_filename": True,
                    "compress_large_reports": False,
                    "report_quality": "high"
                },
                "advanced": {
                    "enable_debug_mode": False,
                    "log_level": "INFO",
                    "memory_limit_mb": 1000,
                    "max_parallel_processes": 4,
                    "cache_enabled": True,
                    "cache_size_mb": 100,
                    "performance_monitoring": False
                },
                "shortcuts": {
                    "new_file": "Ctrl+N",
                    "open_file": "Ctrl+O",
                    "save_file": "Ctrl+S",
                    "save_as": "Ctrl+Shift+S",
                    "exit": "Ctrl+Q",
                    "undo": "Ctrl+Z",
                    "redo": "Ctrl+Y",
                    "copy": "Ctrl+C",
                    "paste": "Ctrl+V",
                    "find": "Ctrl+F",
                    "replace": "Ctrl+H"
                },
                "version": "1.0.0",
                "language": "zh-CN"
            },
            'field_mappings': {
                "version": "2.0",
                "global_settings": {
                    "auto_generate_mappings": True,
                    "enable_smart_suggestions": True,
                    "save_edit_history": True,
                    "preserve_chinese_headers": True
                },
                "table_mappings": {},
                "field_templates": {},
                "user_preferences": {
                    "default_field_patterns": {},
                    "recent_edits": [],
                    "favorite_mappings": []
                }
            }
        }
        
        return defaults.get(config_name, {})
        
    def populate_config_tree(self):
        """填充配置树"""
        self.config_tree.clear()
        
        # 配置项目的中文描述
        config_descriptions = {
            'main_config': {
                'name': '主要配置',
                'description': '系统核心配置，包括数据库、导入设置、处理参数等',
                'sections': {
                    'database': '数据库配置',
                    'import_settings': '数据导入设置',
                    'field_mapping': '字段映射配置',
                    'processing': '数据处理配置',
                    'ui': '界面配置',
                    'reports': '报告生成配置'
                }
            },
            'user_preferences': {
                'name': '用户偏好',
                'description': '个人偏好设置，包括界面布局、操作习惯、快捷键等',
                'sections': {
                    'ui': '界面偏好',
                    'behavior': '操作行为',
                    'data_processing': '数据处理偏好',
                    'reports': '报告偏好',
                    'advanced': '高级设置',
                    'shortcuts': '快捷键设置'
                }
            },
            'field_mappings': {
                'name': '字段映射',
                'description': 'Excel字段与数据库字段的映射关系配置',
                'sections': {
                    'global_settings': '全局设置',
                    'table_mappings': '表映射',
                    'field_templates': '字段模板',
                    'user_preferences': '用户偏好'
                }
            }
        }
        
        for config_name, config_data in self.current_configs.items():
            if config_name in config_descriptions:
                desc = config_descriptions[config_name]
                
                # 创建顶级项目
                top_item = QTreeWidgetItem(self.config_tree, [desc['name']])
                top_item.setData(0, Qt.UserRole, {'type': 'config', 'config_name': config_name})
                
                # 创建子项目
                for section_key, section_data in config_data.items():
                    if isinstance(section_data, dict) and section_key in desc['sections']:
                        section_item = QTreeWidgetItem(top_item, [desc['sections'][section_key]])
                        section_item.setData(0, Qt.UserRole, {
                            'type': 'section',
                            'config_name': config_name,
                            'section': section_key
                        })
                
                top_item.setExpanded(True)
        
    def on_tree_item_clicked(self, item, column):
        """处理树项目点击事件"""
        data = item.data(0, Qt.UserRole)
        if not data:
            return
            
        if data['type'] == 'config':
            self.show_config_overview(data['config_name'])
        elif data['type'] == 'section':
            self.show_config_section(data['config_name'], data['section'])
            
    def show_config_overview(self, config_name: str):
        """显示配置文件概览"""
        # 清空当前布局
        self.clear_config_layout()
        
        config_descriptions = {
            'main_config': {
                'title': '主要配置文件 (config.json)',
                'description': '包含系统核心配置，如数据库连接、数据导入设置、字段映射规则等。'
            },
            'user_preferences': {
                'title': '用户偏好文件 (user_preferences.json)',
                'description': '保存用户的个人偏好设置，包括界面布局、操作习惯、快捷键配置等。'
            },
            'field_mappings': {
                'title': '字段映射文件 (field_mappings.json)',
                'description': 'Excel表格字段与数据库字段的映射关系，用于数据导入时的字段转换。'
            }
        }
        
        if config_name in config_descriptions:
            desc = config_descriptions[config_name]
            self.config_title.setText(desc['title'])
            self.config_description.setText(desc['description'])
            
            # 显示配置文件统计信息
            config_data = self.current_configs[config_name]
            stats_group = QGroupBox("配置统计")
            stats_layout = QGridLayout(stats_group)
            
            stats_layout.addWidget(QLabel("配置项数量:"), 0, 0)
            stats_layout.addWidget(QLabel(str(len(config_data))), 0, 1)
            
            if 'version' in config_data:
                stats_layout.addWidget(QLabel("配置版本:"), 1, 0)
                stats_layout.addWidget(QLabel(str(config_data['version'])), 1, 1)
                
            self.config_layout.addWidget(stats_group)
            
    def show_config_section(self, config_name: str, section: str):
        """显示配置段落"""
        # 清空当前布局
        self.clear_config_layout()
        
        section_descriptions = {
            'database': '数据库连接和存储配置',
            'import_settings': '数据导入相关设置',
            'field_mapping': '字段映射自动化配置',
            'processing': '数据处理和计算配置',
            'ui': '用户界面显示配置',
            'reports': '报告生成和导出配置',
            'behavior': '用户操作行为配置',
            'data_processing': '数据处理偏好配置',
            'advanced': '高级功能配置',
            'shortcuts': '键盘快捷键配置',
            'global_settings': '全局字段映射设置',
            'table_mappings': '具体表的字段映射',
            'field_templates': '字段映射模板',
            'user_preferences': '用户映射偏好'
        }
        
        self.config_title.setText(f"{section} 配置")
        self.config_description.setText(section_descriptions.get(section, ""))
        
        # 获取配置数据
        config_data = self.current_configs[config_name]
        if section not in config_data:
            self.config_layout.addWidget(QLabel("该配置段落不存在"))
            return
            
        section_data = config_data[section]
        
        # 创建表单
        form_group = QGroupBox("配置项")
        form_layout = QFormLayout(form_group)
        
        self.create_config_form(form_layout, section_data, config_name, section)
        
        self.config_layout.addWidget(form_group)
        self.config_layout.addStretch()
        
    def create_config_form(self, layout, data, config_name, section, prefix=""):
        """递归创建配置表单"""
        for key, value in data.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            # 获取中文名称，如果没有映射则使用原始名称
            display_name = self.config_translations.get(key, key)
            
            if isinstance(value, dict):
                # 创建子组
                sub_group = QGroupBox(display_name)
                sub_layout = QFormLayout(sub_group)
                self.create_config_form(sub_layout, value, config_name, section, full_key)
                layout.addRow(sub_group)
            else:
                # 创建编辑控件
                widget = self.create_value_widget(value, config_name, section, full_key)
                layout.addRow(display_name + ":", widget)
                
    def create_value_widget(self, value, config_name, section, key):
        """根据值类型创建相应的编辑控件"""
        if isinstance(value, bool):
            widget = QCheckBox()
            widget.setChecked(value)
            widget.stateChanged.connect(
                lambda state, cn=config_name, s=section, k=key: 
                self.update_config_value(cn, s, k, state == Qt.Checked)
            )
        elif isinstance(value, int):
            widget = QSpinBox()
            widget.setRange(-999999, 999999)
            widget.setValue(value)
            widget.valueChanged.connect(
                lambda val, cn=config_name, s=section, k=key:
                self.update_config_value(cn, s, k, val)
            )
        elif isinstance(value, float):
            widget = QDoubleSpinBox()
            widget.setRange(-999999.0, 999999.0)
            widget.setDecimals(3)
            widget.setValue(value)
            widget.valueChanged.connect(
                lambda val, cn=config_name, s=section, k=key:
                self.update_config_value(cn, s, k, val)
            )
        elif isinstance(value, list):
            widget = QTextEdit()
            widget.setMaximumHeight(100)
            widget.setPlainText('\n'.join(map(str, value)))
            widget.textChanged.connect(
                lambda cn=config_name, s=section, k=key, w=widget:
                self.update_config_value(cn, s, k, w.toPlainText().split('\n'))
            )
        else:
            widget = QLineEdit()
            widget.setText(str(value))
            widget.textChanged.connect(
                lambda text, cn=config_name, s=section, k=key:
                self.update_config_value(cn, s, k, text)
            )
            
        return widget
        
    def update_config_value(self, config_name, section, key, value):
        """更新配置值"""
        keys = key.split('.')
        data = self.current_configs[config_name][section]
        
        # 导航到正确的位置
        for k in keys[:-1]:
            data = data[k]
            
        # 设置值
        data[keys[-1]] = value
        
    def clear_config_layout(self):
        """清空配置布局"""
        while self.config_layout.count():
            child = self.config_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
    def reset_current_config(self):
        """重置当前选择的配置"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置当前配置到默认值吗？\n这将丢失当前的修改。",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 重新显示当前配置
            current_item = self.config_tree.currentItem()
            if current_item:
                self.on_tree_item_clicked(current_item, 0)
                
    def reset_all_configs(self):
        """重置所有配置"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有配置到默认值吗？\n这将丢失所有的自定义配置。",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 重置为默认配置
            for config_name in self.config_paths.keys():
                self.current_configs[config_name] = self.get_default_config(config_name)
            
            self.populate_config_tree()
            QMessageBox.information(self, "重置完成", "所有配置已重置为默认值")
            
    def validate_current_config(self):
        """验证当前配置"""
        # 简单的配置验证
        try:
            json.dumps(self.current_configs)
            QMessageBox.information(self, "验证结果", "配置格式正确")
        except Exception as e:
            QMessageBox.warning(self, "验证失败", f"配置格式错误: {e}")
            
    def backup_configs(self):
        """备份当前配置"""
        backup_dir = QFileDialog.getExistingDirectory(self, "选择备份目录")
        if backup_dir:
            try:
                import datetime
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_subdir = os.path.join(backup_dir, f"config_backup_{timestamp}")
                os.makedirs(backup_subdir, exist_ok=True)
                
                for config_name, config_data in self.current_configs.items():
                    backup_file = os.path.join(backup_subdir, f"{config_name}.json")
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        json.dump(config_data, f, indent=2, ensure_ascii=False)
                        
                QMessageBox.information(self, "备份完成", f"配置已备份到: {backup_subdir}")
            except Exception as e:
                QMessageBox.warning(self, "备份失败", f"备份过程中发生错误: {e}")
                
    def restore_configs(self):
        """恢复配置备份"""
        backup_dir = QFileDialog.getExistingDirectory(self, "选择备份目录")
        if backup_dir:
            try:
                for config_name in self.config_paths.keys():
                    backup_file = os.path.join(backup_dir, f"{config_name}.json")
                    if os.path.exists(backup_file):
                        with open(backup_file, 'r', encoding='utf-8') as f:
                            self.current_configs[config_name] = json.load(f)
                            
                self.populate_config_tree()
                QMessageBox.information(self, "恢复完成", "配置已从备份恢复")
            except Exception as e:
                QMessageBox.warning(self, "恢复失败", f"恢复过程中发生错误: {e}")
                
    def save_configs(self):
        """保存配置到文件"""
        try:
            for config_name, config_path in self.config_paths.items():
                if config_name in self.current_configs:
                    full_path = os.path.join(os.getcwd(), config_path)
                    
                    # 确保目录存在
                    os.makedirs(os.path.dirname(full_path), exist_ok=True)
                    
                    with open(full_path, 'w', encoding='utf-8') as f:
                        json.dump(self.current_configs[config_name], f, indent=2, ensure_ascii=False)
                        
            QMessageBox.information(self, "保存成功", "所有配置已保存")
            self.accept()
        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存配置时发生错误: {e}")
            
    def apply_configs(self):
        """应用配置（不关闭对话框）"""
        try:
            for config_name, config_path in self.config_paths.items():
                if config_name in self.current_configs:
                    full_path = os.path.join(os.getcwd(), config_path)
                    
                    # 确保目录存在
                    os.makedirs(os.path.dirname(full_path), exist_ok=True)
                    
                    with open(full_path, 'w', encoding='utf-8') as f:
                        json.dump(self.current_configs[config_name], f, indent=2, ensure_ascii=False)
                        
                    # 发射配置改变信号
                    self.config_changed.emit(config_name, self.current_configs[config_name])
                    
            QMessageBox.information(self, "应用成功", "配置已应用")
        except Exception as e:
            QMessageBox.warning(self, "应用失败", f"应用配置时发生错误: {e}")


if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    dialog = ConfigManagerDialog()
    dialog.show()
    sys.exit(app.exec_())