#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入预览和模拟引擎

功能说明:
- 在实际导入前模拟导入过程
- 预览导入结果和数据变化
- 检测潜在的导入问题
- 提供导入风险评估和建议
"""

import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import copy

from src.utils.log_config import setup_logger


class PreviewIssueLevel(Enum):
    """预览问题级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class PreviewIssueCategory(Enum):
    """预览问题分类"""
    DATA_FORMAT = "data_format"        # 数据格式问题
    DATA_QUALITY = "data_quality"      # 数据质量问题
    FIELD_MAPPING = "field_mapping"    # 字段映射问题
    BUSINESS_LOGIC = "business_logic"  # 业务逻辑问题
    PERFORMANCE = "performance"        # 性能问题


@dataclass
class PreviewIssue:
    """预览问题"""
    id: str
    level: PreviewIssueLevel
    category: PreviewIssueCategory
    title: str
    description: str
    sheet_name: str
    row_number: Optional[int] = None
    column_name: Optional[str] = None
    affected_value: Any = None
    suggested_action: Optional[str] = None
    auto_fixable: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'level': self.level.value,
            'category': self.category.value,
            'title': self.title,
            'description': self.description,
            'sheet_name': self.sheet_name,
            'row_number': self.row_number,
            'column_name': self.column_name,
            'affected_value': str(self.affected_value) if self.affected_value is not None else None,
            'suggested_action': self.suggested_action,
            'auto_fixable': self.auto_fixable
        }


@dataclass
class PreviewStatistics:
    """预览统计信息"""
    total_rows: int = 0
    valid_rows: int = 0
    invalid_rows: int = 0
    empty_rows: int = 0
    summary_rows: int = 0
    duplicate_rows: int = 0
    
    # 字段统计
    total_fields: int = 0
    mapped_fields: int = 0
    unmapped_fields: int = 0
    
    # 数据类型统计
    numeric_fields: int = 0
    text_fields: int = 0
    date_fields: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'total_rows': self.total_rows,
            'valid_rows': self.valid_rows,
            'invalid_rows': self.invalid_rows,
            'empty_rows': self.empty_rows,
            'summary_rows': self.summary_rows,
            'duplicate_rows': self.duplicate_rows,
            'total_fields': self.total_fields,
            'mapped_fields': self.mapped_fields,
            'unmapped_fields': self.unmapped_fields,
            'numeric_fields': self.numeric_fields,
            'text_fields': self.text_fields,
            'date_fields': self.date_fields,
            'data_quality_score': self.get_data_quality_score()
        }
    
    def get_data_quality_score(self) -> float:
        """计算数据质量评分 (0-100)"""
        if self.total_rows == 0:
            return 0.0
        
        # 基础分数：有效行比例
        base_score = (self.valid_rows / self.total_rows) * 70
        
        # 字段映射完整性加分
        if self.total_fields > 0:
            mapping_score = (self.mapped_fields / self.total_fields) * 20
        else:
            mapping_score = 0
        
        # 重复数据扣分
        duplicate_penalty = min((self.duplicate_rows / self.total_rows) * 10, 10) if self.total_rows > 0 else 0
        
        # 空行扣分
        empty_penalty = min((self.empty_rows / self.total_rows) * 5, 5) if self.total_rows > 0 else 0
        
        final_score = base_score + mapping_score - duplicate_penalty - empty_penalty
        return max(0, min(100, final_score))


@dataclass
class PreviewResult:
    """预览结果"""
    sheet_name: str
    is_successful: bool
    statistics: PreviewStatistics
    issues: List[PreviewIssue] = field(default_factory=list)
    preview_data: Optional[pd.DataFrame] = None
    processed_data: Optional[pd.DataFrame] = None
    preview_time: datetime = field(default_factory=datetime.now)
    processing_time_ms: float = 0.0
    
    def add_issue(self, issue: PreviewIssue):
        """添加预览问题"""
        self.issues.append(issue)
    
    def get_issues_by_level(self, level: PreviewIssueLevel) -> List[PreviewIssue]:
        """按级别获取问题"""
        return [issue for issue in self.issues if issue.level == level]
    
    def get_issues_by_category(self, category: PreviewIssueCategory) -> List[PreviewIssue]:
        """按分类获取问题"""
        return [issue for issue in self.issues if issue.category == category]
    
    def get_risk_level(self) -> str:
        """获取风险级别"""
        critical_count = len(self.get_issues_by_level(PreviewIssueLevel.CRITICAL))
        error_count = len(self.get_issues_by_level(PreviewIssueLevel.ERROR))
        warning_count = len(self.get_issues_by_level(PreviewIssueLevel.WARNING))
        
        if critical_count > 0:
            return "critical"
        elif error_count > 0:
            return "high"
        elif warning_count > 0:
            return "medium"
        else:
            return "low"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'sheet_name': self.sheet_name,
            'is_successful': self.is_successful,
            'statistics': self.statistics.to_dict(),
            'issues_count': len(self.issues),
            'critical_issues': len(self.get_issues_by_level(PreviewIssueLevel.CRITICAL)),
            'error_issues': len(self.get_issues_by_level(PreviewIssueLevel.ERROR)),
            'warning_issues': len(self.get_issues_by_level(PreviewIssueLevel.WARNING)),
            'info_issues': len(self.get_issues_by_level(PreviewIssueLevel.INFO)),
            'risk_level': self.get_risk_level(),
            'preview_time': self.preview_time.isoformat(),
            'processing_time_ms': self.processing_time_ms,
            'issues': [issue.to_dict() for issue in self.issues]
        }


class ImportPreviewEngine:
    """导入预览引擎"""
    
    def __init__(self):
        """初始化预览引擎"""
        self.logger = setup_logger(__name__)
        
        # 预览历史
        self.preview_history: List[PreviewResult] = []
        
        self.logger.info("导入预览引擎初始化完成")
    
    def preview_import(self, file_path: str, sheet_name: str, sheet_config, 
                      field_mappings: Optional[Dict[str, str]] = None,
                      max_preview_rows: int = 1000) -> PreviewResult:
        """
        预览导入过程
        
        Args:
            file_path: Excel文件路径
            sheet_name: Sheet名称
            sheet_config: Sheet配置
            field_mappings: 字段映射
            max_preview_rows: 最大预览行数
            
        Returns:
            预览结果
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始预览导入: {sheet_name}")
            
            result = PreviewResult(
                sheet_name=sheet_name,
                is_successful=True,
                statistics=PreviewStatistics()
            )
            
            # 1. 读取原始数据
            raw_data = self._read_raw_data(file_path, sheet_name, sheet_config, max_preview_rows)
            if raw_data is None or raw_data.empty:
                result.is_successful = False
                result.add_issue(PreviewIssue(
                    id=f"no_data_{sheet_name}",
                    level=PreviewIssueLevel.CRITICAL,
                    category=PreviewIssueCategory.DATA_FORMAT,
                    title="无法读取数据",
                    description="Sheet中没有可读取的数据",
                    sheet_name=sheet_name,
                    suggested_action="检查Sheet是否存在数据或配置是否正确"
                ))
                return result
            
            result.preview_data = raw_data.copy()
            
            # 2. 数据预处理和清洗
            processed_data = self._preprocess_data(raw_data, sheet_config, result)
            
            # 3. 字段映射处理
            if field_mappings:
                processed_data = self._apply_field_mappings(processed_data, field_mappings, result)
            
            # 4. 数据验证
            self._validate_data(processed_data, sheet_config, result)
            
            # 5. 统计信息计算
            self._calculate_statistics(raw_data, processed_data, result)
            
            # 6. 业务逻辑验证
            self._validate_business_logic(processed_data, result)
            
            result.processed_data = processed_data
            
            # 计算处理时间
            end_time = datetime.now()
            result.processing_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # 记录预览历史
            self.preview_history.append(result)
            
            self.logger.info(f"预览导入完成: {sheet_name}, 成功: {result.is_successful}, 问题数: {len(result.issues)}")
            return result
            
        except Exception as e:
            self.logger.error(f"预览导入失败: {e}")
            result = PreviewResult(
                sheet_name=sheet_name,
                is_successful=False,
                statistics=PreviewStatistics()
            )
            result.add_issue(PreviewIssue(
                id=f"preview_error_{sheet_name}",
                level=PreviewIssueLevel.CRITICAL,
                category=PreviewIssueCategory.DATA_FORMAT,
                title="预览过程出错",
                description=f"预览导入过程中发生错误: {e}",
                sheet_name=sheet_name,
                suggested_action="检查文件格式和配置是否正确"
            ))
            return result

    def _read_raw_data(self, file_path: str, sheet_name: str, sheet_config, max_rows: int) -> Optional[pd.DataFrame]:
        """读取原始数据"""
        try:
            # 使用Excel导入器读取数据
            from src.modules.data_import.excel_importer import ExcelImporter
            importer = ExcelImporter()

            # 读取数据
            df = importer.import_data(file_path, sheet_name, max_rows=max_rows)

            if df is not None and not df.empty:
                self.logger.info(f"读取原始数据成功: {len(df)} 行, {len(df.columns)} 列")
                return df
            else:
                self.logger.warning(f"读取的数据为空: {sheet_name}")
                return None

        except Exception as e:
            self.logger.error(f"读取原始数据失败: {e}")
            return None

    def _preprocess_data(self, df: pd.DataFrame, sheet_config, result: PreviewResult) -> pd.DataFrame:
        """数据预处理和清洗"""
        try:
            processed_df = df.copy()

            # 1. 处理表头
            if getattr(sheet_config, 'has_header', True):
                header_row = getattr(sheet_config, 'header_row', 1) - 1  # 转换为0基索引
                if header_row < len(processed_df):
                    # 设置表头
                    new_columns = processed_df.iloc[header_row].astype(str).tolist()
                    processed_df.columns = new_columns
                    # 删除表头行
                    processed_df = processed_df.drop(processed_df.index[header_row]).reset_index(drop=True)

            # 2. 处理数据范围
            data_start_row = getattr(sheet_config, 'data_start_row', 2) - 1  # 转换为0基索引
            if getattr(sheet_config, 'has_header', True):
                data_start_row -= 1  # 如果已经删除了表头行，需要调整起始行

            if data_start_row > 0 and data_start_row < len(processed_df):
                processed_df = processed_df.iloc[data_start_row:].reset_index(drop=True)

            data_end_row = getattr(sheet_config, 'data_end_row', None)
            if data_end_row and data_end_row > 0:
                end_index = min(data_end_row - data_start_row - 1, len(processed_df))
                processed_df = processed_df.iloc[:end_index]

            # 3. 跳过空行
            if getattr(sheet_config, 'skip_empty_rows', True):
                before_count = len(processed_df)
                processed_df = processed_df.dropna(how='all')
                empty_rows_removed = before_count - len(processed_df)
                if empty_rows_removed > 0:
                    result.add_issue(PreviewIssue(
                        id=f"empty_rows_{result.sheet_name}",
                        level=PreviewIssueLevel.INFO,
                        category=PreviewIssueCategory.DATA_QUALITY,
                        title=f"移除了{empty_rows_removed}行空行",
                        description=f"根据配置移除了{empty_rows_removed}行完全为空的行",
                        sheet_name=result.sheet_name,
                        suggested_action="这是正常的数据清洗过程"
                    ))

            # 4. 移除汇总行
            if getattr(sheet_config, 'remove_summary_rows', False):
                summary_keywords = getattr(sheet_config, 'summary_keywords', ['合计', '小计', '总计', '汇总'])
                if summary_keywords:
                    before_count = len(processed_df)
                    # 检查第一列是否包含汇总关键词
                    if len(processed_df.columns) > 0:
                        first_col = processed_df.iloc[:, 0].astype(str)
                        summary_mask = first_col.str.contains('|'.join(summary_keywords), case=False, na=False)
                        processed_df = processed_df[~summary_mask]
                        summary_rows_removed = before_count - len(processed_df)
                        if summary_rows_removed > 0:
                            result.add_issue(PreviewIssue(
                                id=f"summary_rows_{result.sheet_name}",
                                level=PreviewIssueLevel.INFO,
                                category=PreviewIssueCategory.DATA_QUALITY,
                                title=f"移除了{summary_rows_removed}行汇总行",
                                description=f"根据配置移除了包含汇总关键词的{summary_rows_removed}行",
                                sheet_name=result.sheet_name,
                                suggested_action="这是正常的数据清洗过程"
                            ))

            # 5. 去除前后空格
            if getattr(sheet_config, 'trim_whitespace', True):
                for col in processed_df.select_dtypes(include=['object']).columns:
                    processed_df[col] = processed_df[col].astype(str).str.strip()

            # 6. 数字格式统一
            if getattr(sheet_config, 'normalize_numbers', True):
                self._normalize_numeric_columns(processed_df, result)

            return processed_df

        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            result.add_issue(PreviewIssue(
                id=f"preprocess_error_{result.sheet_name}",
                level=PreviewIssueLevel.ERROR,
                category=PreviewIssueCategory.DATA_FORMAT,
                title="数据预处理失败",
                description=f"数据预处理过程中发生错误: {e}",
                sheet_name=result.sheet_name,
                suggested_action="检查数据格式和配置是否正确"
            ))
            return df

    def _normalize_numeric_columns(self, df: pd.DataFrame, result: PreviewResult):
        """统一数字格式"""
        try:
            for col in df.columns:
                # 尝试将看起来像数字的列转换为数字
                col_data = df[col].astype(str).str.replace(',', '').str.replace('，', '')

                # 检查是否大部分值都是数字
                numeric_count = 0
                total_count = 0
                for value in col_data:
                    if value and value != 'nan':
                        total_count += 1
                        try:
                            float(value)
                            numeric_count += 1
                        except:
                            pass

                # 如果超过80%的值是数字，则转换整列
                if total_count > 0 and numeric_count / total_count > 0.8:
                    try:
                        df[col] = pd.to_numeric(col_data, errors='coerce')
                        result.add_issue(PreviewIssue(
                            id=f"numeric_conversion_{result.sheet_name}_{col}",
                            level=PreviewIssueLevel.INFO,
                            category=PreviewIssueCategory.DATA_FORMAT,
                            title=f"列'{col}'已转换为数字格式",
                            description=f"检测到列'{col}'主要包含数字，已自动转换格式",
                            sheet_name=result.sheet_name,
                            column_name=col,
                            suggested_action="这是正常的格式统一过程"
                        ))
                    except Exception as e:
                        result.add_issue(PreviewIssue(
                            id=f"numeric_conversion_error_{result.sheet_name}_{col}",
                            level=PreviewIssueLevel.WARNING,
                            category=PreviewIssueCategory.DATA_FORMAT,
                            title=f"列'{col}'数字转换失败",
                            description=f"尝试转换列'{col}'为数字格式时失败: {e}",
                            sheet_name=result.sheet_name,
                            column_name=col,
                            suggested_action="检查该列的数据格式"
                        ))

        except Exception as e:
            self.logger.error(f"数字格式统一失败: {e}")

    def _apply_field_mappings(self, df: pd.DataFrame, field_mappings: Dict[str, str],
                             result: PreviewResult) -> pd.DataFrame:
        """应用字段映射"""
        try:
            mapped_df = df.copy()

            # 重命名列
            rename_dict = {}
            for excel_field, target_field in field_mappings.items():
                if excel_field in mapped_df.columns and target_field:
                    rename_dict[excel_field] = target_field

            if rename_dict:
                mapped_df = mapped_df.rename(columns=rename_dict)
                result.add_issue(PreviewIssue(
                    id=f"field_mapping_{result.sheet_name}",
                    level=PreviewIssueLevel.INFO,
                    category=PreviewIssueCategory.FIELD_MAPPING,
                    title=f"应用了{len(rename_dict)}个字段映射",
                    description=f"字段映射: {', '.join([f'{k}->{v}' for k, v in rename_dict.items()])}",
                    sheet_name=result.sheet_name,
                    suggested_action="字段映射已成功应用"
                ))

            # 检查未映射的字段
            unmapped_fields = [col for col in df.columns if col not in field_mappings]
            if unmapped_fields:
                result.add_issue(PreviewIssue(
                    id=f"unmapped_fields_{result.sheet_name}",
                    level=PreviewIssueLevel.WARNING,
                    category=PreviewIssueCategory.FIELD_MAPPING,
                    title=f"发现{len(unmapped_fields)}个未映射字段",
                    description=f"未映射字段: {', '.join(unmapped_fields)}",
                    sheet_name=result.sheet_name,
                    suggested_action="考虑为这些字段配置映射或确认是否需要导入"
                ))

            return mapped_df

        except Exception as e:
            self.logger.error(f"字段映射应用失败: {e}")
            result.add_issue(PreviewIssue(
                id=f"field_mapping_error_{result.sheet_name}",
                level=PreviewIssueLevel.ERROR,
                category=PreviewIssueCategory.FIELD_MAPPING,
                title="字段映射应用失败",
                description=f"应用字段映射时发生错误: {e}",
                sheet_name=result.sheet_name,
                suggested_action="检查字段映射配置是否正确"
            ))
            return df

    def _validate_data(self, df: pd.DataFrame, sheet_config, result: PreviewResult):
        """数据验证"""
        try:
            # 1. 检查重复数据
            duplicate_count = df.duplicated().sum()
            if duplicate_count > 0:
                result.add_issue(PreviewIssue(
                    id=f"duplicates_{result.sheet_name}",
                    level=PreviewIssueLevel.WARNING,
                    category=PreviewIssueCategory.DATA_QUALITY,
                    title=f"发现{duplicate_count}行重复数据",
                    description=f"数据中存在{duplicate_count}行完全重复的记录",
                    sheet_name=result.sheet_name,
                    suggested_action="考虑在导入前去除重复数据"
                ))

            # 2. 检查空值比例
            for col in df.columns:
                null_count = df[col].isnull().sum()
                null_ratio = null_count / len(df) if len(df) > 0 else 0

                if null_ratio > 0.5:  # 超过50%为空
                    result.add_issue(PreviewIssue(
                        id=f"high_null_{result.sheet_name}_{col}",
                        level=PreviewIssueLevel.WARNING,
                        category=PreviewIssueCategory.DATA_QUALITY,
                        title=f"列'{col}'空值比例过高",
                        description=f"列'{col}'有{null_ratio:.1%}的值为空",
                        sheet_name=result.sheet_name,
                        column_name=col,
                        suggested_action="检查数据完整性或考虑填充空值"
                    ))
                elif null_ratio > 0.2:  # 超过20%为空
                    result.add_issue(PreviewIssue(
                        id=f"medium_null_{result.sheet_name}_{col}",
                        level=PreviewIssueLevel.INFO,
                        category=PreviewIssueCategory.DATA_QUALITY,
                        title=f"列'{col}'存在较多空值",
                        description=f"列'{col}'有{null_ratio:.1%}的值为空",
                        sheet_name=result.sheet_name,
                        column_name=col,
                        suggested_action="注意空值处理"
                    ))

            # 3. 检查数据类型一致性
            for col in df.columns:
                if df[col].dtype == 'object':  # 文本列
                    # 检查是否混合了数字和文本
                    non_null_values = df[col].dropna().astype(str)
                    if len(non_null_values) > 0:
                        numeric_count = 0
                        for value in non_null_values:
                            try:
                                float(value.replace(',', '').replace('，', ''))
                                numeric_count += 1
                            except:
                                pass

                        numeric_ratio = numeric_count / len(non_null_values)
                        if 0.3 < numeric_ratio < 0.8:  # 部分是数字，部分不是
                            result.add_issue(PreviewIssue(
                                id=f"mixed_type_{result.sheet_name}_{col}",
                                level=PreviewIssueLevel.WARNING,
                                category=PreviewIssueCategory.DATA_FORMAT,
                                title=f"列'{col}'数据类型不一致",
                                description=f"列'{col}'中{numeric_ratio:.1%}为数字，其余为文本",
                                sheet_name=result.sheet_name,
                                column_name=col,
                                suggested_action="检查数据格式是否正确"
                            ))

        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")

    def _calculate_statistics(self, raw_data: pd.DataFrame, processed_data: pd.DataFrame, result: PreviewResult):
        """计算统计信息"""
        try:
            stats = result.statistics

            # 基本统计
            stats.total_rows = len(raw_data)
            stats.valid_rows = len(processed_data)
            stats.invalid_rows = stats.total_rows - stats.valid_rows

            # 空行统计
            stats.empty_rows = raw_data.isnull().all(axis=1).sum()

            # 重复行统计
            stats.duplicate_rows = processed_data.duplicated().sum()

            # 字段统计
            stats.total_fields = len(processed_data.columns)
            stats.mapped_fields = stats.total_fields  # 简化处理
            stats.unmapped_fields = 0

            # 数据类型统计
            for col in processed_data.columns:
                if processed_data[col].dtype in ['int64', 'float64']:
                    stats.numeric_fields += 1
                elif processed_data[col].dtype == 'object':
                    stats.text_fields += 1
                elif 'datetime' in str(processed_data[col].dtype):
                    stats.date_fields += 1

        except Exception as e:
            self.logger.error(f"统计信息计算失败: {e}")

    def _validate_business_logic(self, df: pd.DataFrame, result: PreviewResult):
        """业务逻辑验证"""
        try:
            # 这里可以添加具体的业务逻辑验证
            # 例如：工资数据的合理性检查、日期范围检查等

            # 示例：检查数字列的合理性
            for col in df.select_dtypes(include=['int64', 'float64']).columns:
                if '工资' in col or '薪资' in col or 'salary' in col.lower():
                    # 检查工资数据的合理性
                    negative_count = (df[col] < 0).sum()
                    if negative_count > 0:
                        result.add_issue(PreviewIssue(
                            id=f"negative_salary_{result.sheet_name}_{col}",
                            level=PreviewIssueLevel.WARNING,
                            category=PreviewIssueCategory.BUSINESS_LOGIC,
                            title=f"发现{negative_count}个负数工资",
                            description=f"列'{col}'中有{negative_count}个负数值，请确认是否正确",
                            sheet_name=result.sheet_name,
                            column_name=col,
                            suggested_action="检查工资数据的正确性"
                        ))

                    # 检查异常高的工资
                    high_salary_threshold = df[col].quantile(0.95) * 2  # 95分位数的2倍
                    high_salary_count = (df[col] > high_salary_threshold).sum()
                    if high_salary_count > 0:
                        result.add_issue(PreviewIssue(
                            id=f"high_salary_{result.sheet_name}_{col}",
                            level=PreviewIssueLevel.INFO,
                            category=PreviewIssueCategory.BUSINESS_LOGIC,
                            title=f"发现{high_salary_count}个异常高的工资值",
                            description=f"列'{col}'中有{high_salary_count}个值显著高于平均水平",
                            sheet_name=result.sheet_name,
                            column_name=col,
                            suggested_action="确认这些高值是否正确"
                        ))

        except Exception as e:
            self.logger.error(f"业务逻辑验证失败: {e}")

    def get_preview_summary(self, results: List[PreviewResult]) -> Dict[str, Any]:
        """获取预览摘要"""
        if not results:
            return {}

        total_sheets = len(results)
        successful_sheets = len([r for r in results if r.is_successful])
        total_issues = sum(len(r.issues) for r in results)
        total_rows = sum(r.statistics.total_rows for r in results)
        total_valid_rows = sum(r.statistics.valid_rows for r in results)

        # 风险评估
        risk_levels = [r.get_risk_level() for r in results]
        critical_sheets = len([r for r in risk_levels if r == 'critical'])
        high_risk_sheets = len([r for r in risk_levels if r == 'high'])

        return {
            'total_sheets': total_sheets,
            'successful_sheets': successful_sheets,
            'failed_sheets': total_sheets - successful_sheets,
            'total_issues': total_issues,
            'total_rows': total_rows,
            'total_valid_rows': total_valid_rows,
            'data_quality_score': (total_valid_rows / total_rows * 100) if total_rows > 0 else 0,
            'critical_sheets': critical_sheets,
            'high_risk_sheets': high_risk_sheets,
            'overall_risk': 'critical' if critical_sheets > 0 else 'high' if high_risk_sheets > 0 else 'low',
            'preview_time': datetime.now().isoformat()
        }

    def get_preview_history(self, limit: int = 10) -> List[PreviewResult]:
        """获取预览历史"""
        return self.preview_history[-limit:] if limit > 0 else self.preview_history
