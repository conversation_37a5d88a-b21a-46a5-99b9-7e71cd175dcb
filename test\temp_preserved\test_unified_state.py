"""
测试统一状态管理器
验证P1修复是否成功
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.enhanced_unified_state_manager import get_unified_state_manager, StateType
import time

def test_unified_state():
    """测试统一状态管理"""
    print("=" * 60)
    print("测试统一状态管理器")
    print("=" * 60)
    
    manager = get_unified_state_manager()
    
    # 1. 清空所有状态
    print("\n1. 清空所有状态...")
    manager.clear_all_states()
    
    # 2. 测试表状态创建
    print("\n2. 测试表状态创建...")
    test_table = "salary_2025_01_active"
    state = manager.get_table_state(test_table)
    print(f"   创建表状态: {test_table}")
    print(f"   默认页码: {state.current_page}")
    print(f"   默认页大小: {state.page_size}")
    
    # 3. 测试排序状态更新
    print("\n3. 测试排序状态更新...")
    sort_columns = [
        {'column_name': 'employee_name', 'sort_order': 'asc'},
        {'column_name': 'total_salary', 'sort_order': 'desc'}
    ]
    
    success = manager.update_sort(test_table, sort_columns, "test")
    print(f"   更新排序: {'成功' if success else '失败'}")
    
    # 验证排序状态
    sort_state = manager.get_state_value(test_table, StateType.SORT)
    if sort_state == sort_columns:
        print("   [PASS] 排序状态正确")
    else:
        print("   [FAIL] 排序状态不正确")
    
    # 4. 测试分页状态更新
    print("\n4. 测试分页状态更新...")
    success = manager.update_pagination(test_table, 3, 100, "test")
    print(f"   更新分页: {'成功' if success else '失败'}")
    
    # 验证分页状态
    page_state = manager.get_state_value(test_table, StateType.PAGINATION)
    if page_state['page'] == 3 and page_state['page_size'] == 100:
        print("   [PASS] 分页状态正确")
    else:
        print("   [FAIL] 分页状态不正确")
    
    # 5. 测试过滤状态更新
    print("\n5. 测试过滤状态更新...")
    filters = {
        'department': '研发部',
        'salary_range': (5000, 10000)
    }
    
    success = manager.update_filter(test_table, filters, "test")
    print(f"   更新过滤: {'成功' if success else '失败'}")
    
    # 6. 测试状态监听器
    print("\n6. 测试状态监听器...")
    
    callback_triggered = [False]
    
    def test_callback(table_name, old_value, new_value):
        callback_triggered[0] = True
        print(f"   监听器触发: {table_name}")
    
    manager.add_listener(StateType.SELECTION, test_callback)
    manager.update_selection(test_table, [1, 2, 3], "test")
    
    if callback_triggered[0]:
        print("   [PASS] 监听器正常工作")
    else:
        print("   [FAIL] 监听器未触发")
    
    # 7. 测试变更历史
    print("\n7. 测试变更历史...")
    history = manager.get_change_history(test_table)
    print(f"   历史记录数: {len(history)}")
    
    if len(history) >= 4:  # 应该有排序、分页、过滤、选择四个变更
        print("   [PASS] 变更历史记录正确")
        for event in history[-3:]:
            print(f"   - {event.state_type.value}: {event.source}")
    else:
        print("   [FAIL] 变更历史记录不完整")
    
    # 8. 测试状态持久化
    print("\n8. 测试状态持久化...")
    success = manager.save_states()
    print(f"   保存状态: {'成功' if success else '失败'}")
    
    # 创建新实例（模拟重启）
    del manager
    manager2 = get_unified_state_manager()
    
    # 验证状态恢复
    state2 = manager2.get_table_state(test_table)
    if state2.current_page == 3 and state2.page_size == 100:
        print("   [PASS] 状态持久化成功")
    else:
        print("   [FAIL] 状态持久化失败")
    
    # 9. 测试多表状态管理
    print("\n9. 测试多表状态管理...")
    
    tables = [
        "salary_2025_01_active",
        "salary_2025_01_retired", 
        "change_data_2025_01"
    ]
    
    for i, table in enumerate(tables):
        manager2.update_pagination(table, i+1, 50, "test")
    
    # 验证各表状态独立
    independent = True
    for i, table in enumerate(tables):
        page_state = manager2.get_state_value(table, StateType.PAGINATION)
        if page_state['page'] != i+1:
            independent = False
            break
    
    if independent:
        print("   [PASS] 多表状态独立管理")
    else:
        print("   [FAIL] 多表状态混淆")
    
    # 10. 获取统计信息
    print("\n10. 统计信息...")
    stats = manager2.get_statistics()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_unified_state()