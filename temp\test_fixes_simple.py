#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试修复效果
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("=" * 60)
print("修复效果简单测试")
print("=" * 60)

print("\n测试1: 配置向导语法错误")
print("-" * 40)
try:
    # 只测试导入，不创建实例
    from src.gui.config_wizard_dialog import ConfigWizardDialog
    print("[OK] 配置向导模块导入成功，语法错误已修复")
except SyntaxError as e:
    print(f"[FAIL] 语法错误: {e}")
except Exception as e:
    print(f"[ERROR] 其他错误: {e}")

print("\n测试2: change_data_config_dialog 导入")
print("-" * 40)
try:
    from src.gui.change_data_config_dialog import ChangeDataConfigDialog
    print("[OK] ChangeDataConfigDialog 导入成功")
    
    # 检查关键方法是否存在
    if hasattr(ChangeDataConfigDialog, 'get_formatting_rules'):
        print("[OK] get_formatting_rules 方法存在")
    
    if hasattr(ChangeDataConfigDialog, 'apply_formatting_rules_to_table'):
        print("[OK] apply_formatting_rules_to_table 方法存在")
    
    if hasattr(ChangeDataConfigDialog, 'refresh_preview'):
        print("[OK] refresh_preview 方法存在")
        
    if hasattr(ChangeDataConfigDialog, 'get_current_configuration'):
        print("[OK] get_current_configuration 方法存在")
        
except Exception as e:
    print(f"[FAIL] 导入失败: {e}")

print("\n测试3: 编译检查")
print("-" * 40)
import py_compile
import tempfile

files_to_check = [
    "src/gui/config_wizard_dialog.py",
    "src/gui/change_data_config_dialog.py"
]

for file_path in files_to_check:
    try:
        with tempfile.NamedTemporaryFile(suffix='.pyc', delete=True) as tmp:
            py_compile.compile(file_path, cfile=tmp.name, doraise=True)
            print(f"[OK] {file_path} 编译成功")
    except py_compile.PyCompileError as e:
        print(f"[FAIL] {file_path} 编译失败: {e}")

print("\n" + "=" * 60)
print("测试完成！")
print("=" * 60)