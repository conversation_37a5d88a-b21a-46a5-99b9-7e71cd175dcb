#!/usr/bin/env python3
"""
完整测试修复后的另存配置功能

模拟真实使用场景：
1. 用户打开多工作表Excel文件
2. 配置第一个工作表的字段类型
3. 直接点击"另存配置"按钮（不切换工作表）
4. 应该能成功保存，而不是提示错误
"""

import sys
import os
import pandas as pd
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager

def create_test_excel():
    """创建测试用的Excel文件"""
    test_file = Path("temp/test_multi_sheet.xlsx")
    
    # 创建多个工作表的数据
    sheet_data = {
        'A岗职工': pd.DataFrame({
            '序号': [1, 2, 3],
            '工号': ['001', '002', '003'], 
            '姓名': ['张三', '李四', '王五'],
            '基本工资': [5000.0, 5500.0, 6000.0],
            '津贴': [1000.0, 1200.0, 1500.0],
            '应发工资': [6000.0, 6700.0, 7500.0]
        }),
        '退休人员工资表': pd.DataFrame({
            '序号': [1, 2],
            '姓名': ['退休张', '退休李'],
            '基本退休费': [3000.0, 3200.0],
            '津贴': [500.0, 600.0],
            '应发工资': [3500.0, 3800.0]
        }),
        '离休人员表': pd.DataFrame({
            '人员代码': ['LX001', 'LX002'],
            '姓名': ['离休王', '离休赵'],
            '基本离休费': [4000.0, 4200.0],
            '护理费': [800.0, 900.0]
        })
    }
    
    # 保存到Excel文件
    with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
        for sheet_name, df in sheet_data.items():
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    logger.info(f"已创建测试Excel文件: {test_file}")
    return test_file

def simulate_user_workflow():
    """模拟用户工作流程"""
    logger.info("=== 模拟真实用户使用场景 ===")
    
    # 1. 创建测试Excel文件
    test_file = create_test_excel()
    
    # 2. 模拟对话框的初始化过程
    class MockChangeDataConfigDialog:
        def __init__(self, excel_file):
            self.config_manager = ChangeDataConfigManager()
            self.all_sheets_configs = {}
            self.current_sheet_name = 'unknown'
            
            # 加载Excel数据
            self.all_sheets_data = {}
            xl_file = pd.ExcelFile(excel_file)
            for sheet_name in xl_file.sheet_names:
                self.all_sheets_data[sheet_name] = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            # 模拟加载第一个工作表
            if self.all_sheets_data:
                first_sheet = list(self.all_sheets_data.keys())[0]
                self.excel_data = self.all_sheets_data[first_sheet]
                self.current_sheet_name = first_sheet  # 设置当前工作表名称
                logger.info(f"已加载工作表: {first_sheet}, 包含 {len(self.excel_data.columns)} 列")
        
        def get_current_configuration(self):
            """模拟用户配置字段类型"""
            if self.current_sheet_name == 'A岗职工':
                return {
                    'field_mapping': {
                        '序号': '序号',
                        '工号': '工号',
                        '姓名': '姓名', 
                        '基本工资': '基本工资',
                        '津贴': '津贴',
                        '应发工资': '应发工资'
                    },
                    'field_types': {
                        '序号': 'integer',
                        '工号': 'employee_id_string',
                        '姓名': 'name_string',
                        '基本工资': 'salary_float',
                        '津贴': 'salary_float',
                        '应发工资': 'salary_float'
                    },
                    'formatting_rules': {}
                }
            elif self.current_sheet_name == '退休人员工资表':
                return {
                    'field_mapping': {
                        '序号': '序号',
                        '姓名': '姓名',
                        '基本退休费': '基本退休费',
                        '津贴': '津贴',
                        '应发工资': '应发工资'
                    },
                    'field_types': {
                        '序号': 'integer',
                        '姓名': 'name_string',
                        '基本退休费': 'salary_float',
                        '津贴': 'salary_float',
                        '应发工资': 'salary_float'
                    },
                    'formatting_rules': {}
                }
            return None
        
        def save_configuration(self, config_name="测试配置"):
            """修复后的另存配置逻辑"""
            logger.info(f"开始保存配置：{config_name}")
            logger.info(f"当前工作表：{self.current_sheet_name}")
            logger.info(f"all_sheets_configs 初始状态：{list(self.all_sheets_configs.keys())}")
            
            # 修复后的逻辑：先保存当前工作表的配置
            if hasattr(self, 'current_sheet_name') and self.current_sheet_name != 'unknown':
                try:
                    current_config = self.get_current_configuration()
                    if current_config and current_config.get('field_mapping'):
                        self.all_sheets_configs[self.current_sheet_name] = current_config
                        logger.info(f"已保存当前工作表 '{self.current_sheet_name}' 配置")
                        logger.info(f"配置包含 {len(current_config.get('field_mapping', {}))} 个字段")
                except Exception as e:
                    logger.warning(f"保存当前工作表配置时出错: {e}")
            
            # 检查是否有配置需要保存
            if not self.all_sheets_configs:
                logger.error("没有找到任何已配置的工作表")
                return False
            
            # 模拟保存过程
            user_config_dir = self.config_manager.config_dir / 'user_configs'
            user_config_dir.mkdir(exist_ok=True)
            
            # 统计总字段数
            total_fields = sum(len(config.get('field_mapping', {})) for config in self.all_sheets_configs.values())
            
            # 创建多表配置结构
            multi_sheet_config = {
                'name': config_name,
                'description': f"多工作表配置 - {len(self.all_sheets_configs)} 个工作表，共 {total_fields} 个字段",
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'version': '2.0',
                'type': 'multi_sheet_user_config',
                'sheet_count': len(self.all_sheets_configs),
                'sheets': {}
            }
            
            # 保存每个工作表的配置
            for sheet_name, sheet_config in self.all_sheets_configs.items():
                multi_sheet_config['sheets'][sheet_name] = {
                    'sheet_name': sheet_name,
                    'field_count': len(sheet_config.get('field_mapping', {})),
                    'config': sheet_config
                }
            
            # 保存到文件
            config_file_path = user_config_dir / f"{config_name}.json"
            with open(config_file_path, 'w', encoding='utf-8') as f:
                json.dump(multi_sheet_config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"配置已保存到：{config_file_path}")
            logger.info(f"包含工作表：{list(multi_sheet_config['sheets'].keys())}")
            logger.info(f"总字段数：{total_fields}")
            
            return True
        
        def simulate_switch_and_save(self):
            """模拟切换工作表并保存多表配置"""
            logger.info("\n--- 模拟切换到第二个工作表并配置 ---")
            
            # 模拟切换到第二个工作表
            second_sheet = '退休人员工资表'
            if second_sheet in self.all_sheets_data:
                # 先保存当前工作表配置
                if hasattr(self, 'current_sheet_name') and self.current_sheet_name != 'unknown':
                    current_config = self.get_current_configuration()
                    if current_config and current_config.get('field_mapping'):
                        self.all_sheets_configs[self.current_sheet_name] = current_config
                        logger.info(f"切换前保存工作表 '{self.current_sheet_name}' 配置")
                
                # 切换到新工作表
                self.current_sheet_name = second_sheet
                self.excel_data = self.all_sheets_data[second_sheet]
                logger.info(f"已切换到工作表：{second_sheet}")
                
                # 保存多工作表配置
                return self.save_configuration("多表配置测试")
            
            return False
    
    # 3. 测试场景1：用户只配置一个工作表就保存
    logger.info("\n--- 场景1：单工作表直接保存 ---")
    dialog1 = MockChangeDataConfigDialog(test_file)
    result1 = dialog1.save_configuration("单表配置测试")
    
    # 4. 测试场景2：用户配置多个工作表后保存
    logger.info("\n--- 场景2：多工作表配置后保存 ---")
    dialog2 = MockChangeDataConfigDialog(test_file)
    result2 = dialog2.simulate_switch_and_save()
    
    # 5. 验证保存的配置文件
    logger.info("\n--- 验证保存的配置文件 ---")
    config_manager = ChangeDataConfigManager()
    user_configs = config_manager.list_user_configs()
    
    saved_configs = []
    for config in user_configs:
        if config.get('type') == 'multi_sheet_user_config':
            saved_configs.append(config)
            logger.info(f"找到多工作表配置：{config['name']}")
            logger.info(f"  - 工作表数量：{config.get('sheet_count', 0)}")
            logger.info(f"  - 包含工作表：{config.get('sheets', [])}")
    
    # 清理测试文件
    if test_file.exists():
        test_file.unlink()
        logger.info(f"已清理测试文件：{test_file}")
    
    return result1, result2, len(saved_configs)

def main():
    """主函数"""
    try:
        result1, result2, saved_count = simulate_user_workflow()
        
        logger.info("\n=== 最终测试结果 ===")
        logger.info(f"单表配置保存：{'成功' if result1 else '失败'}")
        logger.info(f"多表配置保存：{'成功' if result2 else '失败'}")
        logger.info(f"保存的配置文件数量：{saved_count}")
        
        success = result1 and result2 and saved_count >= 2
        
        print(f"\n>>> 综合测试结果：{'通过' if success else '失败'}")
        if success:
            print(">>> ✅ 修复生效：另存配置功能现在工作正常")
            print(">>> ✅ 用户现在可以配置字段后直接点击另存配置")
            print(">>> ✅ 不再需要先切换工作表才能保存配置")
        else:
            print(">>> ❌ 仍存在问题，需要进一步调试")
            
        return 0 if success else 1
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())