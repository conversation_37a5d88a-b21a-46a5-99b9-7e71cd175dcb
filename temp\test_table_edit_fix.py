#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试表格编辑修复效果的最小化示例
专门测试QTableWidget的编辑功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QTableWidget, QTableWidgetItem, QLabel, QPushButton
)
from PyQt5.QtCore import Qt

class TestTableEditWindow(QMainWindow):
    """测试表格编辑的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("表格编辑修复测试")
        self.setGeometry(100, 100, 800, 500)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("测试表格编辑功能：")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")
        layout.addWidget(info_label)
        
        # 创建测试表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["Excel列名", "数据库字段", "显示名称"])
        
        # 设置表格属性（与修复后的代码相同）
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.verticalHeader().setVisible(False)
        
        # 设置编辑触发器
        self.table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.SelectedClicked | QTableWidget.EditKeyPressed)
        
        # 设置行高
        self.table.verticalHeader().setDefaultSectionSize(35)
        
        # 应用修复的样式表
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #cfe2ff;
                selection-color: #0d6efd;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 12px;
            }
            
            QTableWidget::item {
                padding: 4px 8px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
                background-color: transparent;
            }
            
            QTableWidget::item:selected {
                background-color: #cfe2ff;
                color: #0d6efd;
            }
            
            QTableWidget::item:hover {
                background-color: #e7f1ff;
            }
            
            /* 确保编辑器正常显示 */
            QTableWidget::item:edit {
                background-color: #ffffff;
                border: 2px solid #86b7fe;
                border-radius: 4px;
                padding: 2px 6px;
            }
            
            QLineEdit {
                background-color: #ffffff;
                border: 2px solid #86b7fe;
                border-radius: 4px;
                padding: 2px 6px;
                font-size: 12px;
                color: #495057;
            }
            
            QLineEdit:focus {
                border-color: #0d6efd;
                outline: none;
            }
        """)
        
        layout.addWidget(self.table)
        
        # 添加测试数据
        self._add_test_data()
        
        # 添加测试按钮
        test_btn = QPushButton("添加更多测试行")
        test_btn.clicked.connect(self._add_test_row)
        layout.addWidget(test_btn)
        
        # 添加说明
        instruction_label = QLabel("""
测试步骤：
1. 双击"数据库字段"列的任意单元格
2. 检查是否显示原有内容（不应该是空白）
3. 尝试修改内容
4. 按回车确认或点击其他地方
5. 检查修改是否保存成功

如果编辑时显示空白或看不到输入内容，说明问题仍然存在。
        """)
        instruction_label.setStyleSheet("color: #666; font-size: 12px; margin: 10px;")
        instruction_label.setWordWrap(True)
        layout.addWidget(instruction_label)
        
    def _clean_field_name(self, field_name: str) -> str:
        """清理字段名，移除特殊字符"""
        import re
        
        if not field_name:
            return "field_name"
        
        # 移除所有空白字符（换行符、制表符、空格等）
        cleaned = re.sub(r'\s+', '', field_name.strip())
        
        # 移除特殊字符，只保留字母、数字、下划线和中文
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', cleaned)
        
        # 如果清理后为空，使用默认名称
        if not cleaned:
            cleaned = "field_name"
        
        # 确保不以数字开头（数据库字段名规范）
        if cleaned and cleaned[0].isdigit():
            cleaned = f"field_{cleaned}"
        
        return cleaned
        
    def _add_test_data(self):
        """添加测试数据"""
        test_data = [
            "姓 名",  # 包含空格
            "身份证号\n码",  # 包含换行符
            "基本工资",
            "岗位津贴",
            "绩效工资"
        ]
        
        self.table.setRowCount(len(test_data))
        
        for row, excel_field in enumerate(test_data):
            # Excel列名（只读）
            excel_item = QTableWidgetItem(excel_field)
            excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 0, excel_item)
            
            # 数据库字段（可编辑）- 应用修复
            cleaned_field_name = self._clean_field_name(excel_field)
            db_field_item = QTableWidgetItem(cleaned_field_name)
            db_field_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable | Qt.ItemIsSelectable)
            db_field_item.setToolTip(f"原字段名: {excel_field}\n清理后: {cleaned_field_name}")
            self.table.setItem(row, 1, db_field_item)
            
            # 显示名称（可编辑）
            display_item = QTableWidgetItem(excel_field)
            self.table.setItem(row, 2, display_item)
            
        print("测试数据已加载，请测试编辑功能")
        
    def _add_test_row(self):
        """添加测试行"""
        current_rows = self.table.rowCount()
        self.table.setRowCount(current_rows + 1)
        
        # 添加新行
        excel_item = QTableWidgetItem(f"测试字段{current_rows + 1}")
        excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
        self.table.setItem(current_rows, 0, excel_item)
        
        db_field_item = QTableWidgetItem(f"test_field_{current_rows + 1}")
        db_field_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable | Qt.ItemIsSelectable)
        self.table.setItem(current_rows, 1, db_field_item)
        
        display_item = QTableWidgetItem(f"测试字段{current_rows + 1}")
        self.table.setItem(current_rows, 2, display_item)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestTableEditWindow()
    window.show()
    
    print("表格编辑修复测试启动")
    print("请双击'数据库字段'列进行编辑测试")
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
