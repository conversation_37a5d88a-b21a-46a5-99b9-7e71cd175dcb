"""
异动表智能分析器
用于自动分析异动表结构，推断字段类型，生成格式化规则
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
import re
from datetime import datetime
from loguru import logger


class ChangeDataAnalyzer:
    """异动表智能分析器"""
    
    def __init__(self):
        """初始化分析器"""
        # 字段类型定义
        self.FIELD_TYPES = {
            'employee_id': '工号',
            'name': '姓名', 
            'department': '部门',
            'position': '职位',
            'category': '类别',
            'salary': '工资',
            'allowance': '津贴',
            'date': '日期',
            'numeric': '数值',
            'text': '文本',
            'unknown': '未知'
        }
        
        # 字段类型识别规则
        self.field_patterns = {
            'employee_id': [
                r'工号', r'员工号', r'职工号', r'编号', r'代码',
                r'employee.*id', r'emp.*no', r'staff.*code'
            ],
            'name': [
                r'姓名', r'名字', r'员工姓名', r'职工姓名',
                r'name', r'employee.*name', r'staff.*name'
            ],
            'department': [
                r'部门', r'科室', r'单位', r'组织',
                r'dept', r'department', r'division'
            ],
            'position': [
                r'职位', r'岗位', r'职务', r'职称',
                r'position', r'title', r'job'
            ],
            'category': [
                r'类别', r'类型', r'分类', r'级别',
                r'category', r'type', r'class', r'level'
            ],
            'salary': [
                r'工资', r'薪资', r'薪酬', r'月薪', r'年薪',
                r'salary', r'wage', r'pay', r'compensation'
            ],
            'allowance': [
                r'津贴', r'补贴', r'补助', r'奖金', r'绩效',
                r'allowance', r'subsidy', r'bonus', r'benefit'
            ],
            'date': [
                r'日期', r'时间', r'年', r'月', r'日',
                r'date', r'time', r'year', r'month', r'day'
            ]
        }
        
        # 数据质量指标权重
        self.quality_weights = {
            'completeness': 0.3,  # 完整性
            'consistency': 0.3,   # 一致性
            'accuracy': 0.2,      # 准确性
            'uniqueness': 0.2     # 唯一性
        }
        
        logger.info("异动表智能分析器初始化完成")
    
    def analyze_excel_structure(self, df: pd.DataFrame, 
                               table_name: Optional[str] = None) -> Dict[str, Any]:
        """
        分析Excel结构，返回字段类型和格式化规则
        
        Args:
            df: 要分析的DataFrame
            table_name: 表名（可选）
            
        Returns:
            包含分析结果的字典
        """
        logger.info(f"开始分析Excel结构: {table_name if table_name else '未命名表'}")
        logger.info(f"数据维度: {df.shape[0]} 行 x {df.shape[1]} 列")
        
        try:
            analysis_result = {
                'table_name': table_name,
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'field_analysis': {},
                'formatting_rules': {},
                'data_quality': {},
                'recommendations': [],
                'timestamp': datetime.now().isoformat()
            }
            
            # 分析每个字段
            for column in df.columns:
                logger.debug(f"分析字段: {column}")
                
                # 推断字段类型
                field_type, confidence = self._infer_field_type(
                    df[column], column
                )
                
                # 计算数据质量
                quality_metrics = self._calculate_data_quality(
                    df[column], field_type
                )
                
                # 生成格式化规则
                formatting_rule = self._generate_formatting_rule(
                    field_type, df[column]
                )
                
                # 记录分析结果
                analysis_result['field_analysis'][column] = {
                    'field_type': field_type,
                    'confidence': confidence,
                    'data_type': str(df[column].dtype),
                    'quality_metrics': quality_metrics,
                    'formatting_rule': formatting_rule,
                    'sample_values': self._get_sample_values(df[column]),
                    'statistics': self._get_column_statistics(df[column])
                }
                
                analysis_result['formatting_rules'][column] = formatting_rule
            
            # 计算整体数据质量
            analysis_result['data_quality'] = self._calculate_overall_quality(
                analysis_result['field_analysis']
            )
            
            # 生成建议
            analysis_result['recommendations'] = self._generate_recommendations(
                analysis_result
            )
            
            logger.info("Excel结构分析完成")
            logger.info(f"识别字段类型: {len(analysis_result['field_analysis'])} 个")
            logger.info(f"整体数据质量得分: {analysis_result['data_quality'].get('overall_score', 0):.2f}")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析Excel结构时发生错误: {str(e)}")
            return {
                'error': str(e),
                'field_analysis': {},
                'formatting_rules': {}
            }
    
    def _infer_field_type(self, series: pd.Series, column_name: str) -> Tuple[str, float]:
        """
        推断字段类型，返回类型和置信度
        
        Args:
            series: 数据列
            column_name: 列名
            
        Returns:
            (字段类型, 置信度)
        """
        column_name_lower = column_name.lower()
        
        # 基于列名的模式匹配
        for field_type, patterns in self.field_patterns.items():
            for pattern in patterns:
                if re.search(pattern, column_name_lower):
                    logger.debug(f"字段 '{column_name}' 通过模式匹配识别为: {field_type}")
                    return field_type, 0.9
        
        # 基于数据内容推断
        non_null = series.dropna()
        if len(non_null) == 0:
            return 'unknown', 0.0
        
        # 检查是否为数值类型
        try:
            numeric_values = pd.to_numeric(non_null, errors='coerce')
            numeric_ratio = numeric_values.notna().sum() / len(non_null)
            
            if numeric_ratio > 0.8:
                # 检查是否可能是工资/津贴（通常有小数）
                if non_null.astype(str).str.contains(r'\.\d+').any():
                    # 检查数值范围
                    if numeric_values.max() > 1000:
                        return 'salary', 0.7
                    else:
                        return 'allowance', 0.6
                else:
                    return 'numeric', 0.7
        except:
            pass
        
        # 检查是否为日期
        if self._is_date_column(non_null):
            return 'date', 0.7
        
        # 检查是否为工号（通常是字符串但有特定格式）
        if self._is_employee_id(non_null):
            return 'employee_id', 0.6
        
        # 默认为文本
        return 'text', 0.5
    
    def _calculate_data_quality(self, series: pd.Series, field_type: str) -> Dict[str, float]:
        """
        计算数据质量指标
        
        Args:
            series: 数据列
            field_type: 字段类型
            
        Returns:
            质量指标字典
        """
        metrics = {}
        
        # 完整性：非空值比例
        total_count = len(series)
        non_null_count = series.notna().sum()
        metrics['completeness'] = non_null_count / total_count if total_count > 0 else 0
        
        # 一致性：数据格式一致性
        if field_type in ['employee_id', 'date']:
            # 检查格式一致性
            non_null = series.dropna()
            if len(non_null) > 0:
                # 获取最常见的格式模式
                patterns = non_null.astype(str).apply(self._get_format_pattern)
                most_common_pattern = patterns.mode()[0] if not patterns.empty else None
                if most_common_pattern:
                    consistency_ratio = (patterns == most_common_pattern).sum() / len(patterns)
                    metrics['consistency'] = consistency_ratio
                else:
                    metrics['consistency'] = 0.5
            else:
                metrics['consistency'] = 0
        else:
            metrics['consistency'] = 1.0  # 其他类型默认一致
        
        # 准确性：基于字段类型的特定检查
        if field_type == 'salary' or field_type == 'allowance':
            # 检查是否有负值或异常大的值
            numeric_values = pd.to_numeric(series, errors='coerce')
            if numeric_values.notna().any():
                has_negative = (numeric_values < 0).any()
                has_extreme = (numeric_values > numeric_values.quantile(0.99) * 10).any()
                metrics['accuracy'] = 0.5 if (has_negative or has_extreme) else 1.0
            else:
                metrics['accuracy'] = 0
        else:
            metrics['accuracy'] = 0.8  # 默认准确性
        
        # 唯一性：对于ID类字段检查重复
        if field_type == 'employee_id':
            non_null = series.dropna()
            if len(non_null) > 0:
                metrics['uniqueness'] = len(non_null.unique()) / len(non_null)
            else:
                metrics['uniqueness'] = 0
        else:
            metrics['uniqueness'] = 1.0  # 非ID字段不检查唯一性
        
        # 计算综合得分
        overall_score = sum(
            metrics.get(key, 0) * weight 
            for key, weight in self.quality_weights.items()
        )
        metrics['overall_score'] = overall_score
        
        return metrics
    
    def _generate_formatting_rule(self, field_type: str, series: pd.Series) -> Dict[str, Any]:
        """
        根据字段类型生成格式化规则
        
        Args:
            field_type: 字段类型
            series: 数据列
            
        Returns:
            格式化规则字典
        """
        rule = {
            'field_type': field_type,
            'format_type': 'text',
            'alignment': 'left',
            'width': 100
        }
        
        if field_type == 'employee_id':
            rule.update({
                'format_type': 'text',
                'alignment': 'center',
                'width': 100,
                'preserve_leading_zeros': True
            })
        
        elif field_type == 'name':
            rule.update({
                'format_type': 'text',
                'alignment': 'left',
                'width': 80
            })
        
        elif field_type == 'department':
            rule.update({
                'format_type': 'text',
                'alignment': 'left',
                'width': 150
            })
        
        elif field_type in ['salary', 'allowance']:
            rule.update({
                'format_type': 'currency',
                'alignment': 'right',
                'width': 100,
                'decimal_places': 2,
                'thousands_separator': True
            })
        
        elif field_type == 'date':
            # 检测日期格式
            date_format = self._detect_date_format(series)
            rule.update({
                'format_type': 'date',
                'alignment': 'center',
                'width': 100,
                'date_format': date_format
            })
        
        elif field_type == 'numeric':
            # 检查是否需要小数位
            numeric_values = pd.to_numeric(series, errors='coerce')
            has_decimal = False
            if numeric_values.notna().any():
                has_decimal = (numeric_values % 1 != 0).any()
            
            rule.update({
                'format_type': 'number',
                'alignment': 'right',
                'width': 80,
                'decimal_places': 2 if has_decimal else 0
            })
        
        else:  # text, category, unknown
            # 基于内容长度调整宽度
            max_length = series.astype(str).str.len().max()
            width = min(max(max_length * 8, 50), 300)  # 8像素每字符，最小50，最大300
            rule.update({
                'format_type': 'text',
                'alignment': 'left',
                'width': width
            })
        
        return rule
    
    def _is_date_column(self, series: pd.Series) -> bool:
        """检查是否为日期列"""
        try:
            # 尝试转换为日期
            pd.to_datetime(series, errors='coerce')
            return True
        except:
            return False
    
    def _is_employee_id(self, series: pd.Series) -> bool:
        """检查是否为工号"""
        # 工号通常有固定长度或特定格式
        str_series = series.astype(str)
        
        # 检查长度一致性
        lengths = str_series.str.len()
        if lengths.nunique() == 1:
            # 所有值长度相同，可能是工号
            return True
        
        # 检查是否符合常见工号格式（字母+数字组合）
        pattern = r'^[A-Z]{0,3}\d{4,10}$'
        matches = str_series.str.match(pattern)
        if matches.sum() / len(matches) > 0.8:
            return True
        
        return False
    
    def _get_format_pattern(self, value: str) -> str:
        """获取值的格式模式"""
        # 将字母替换为A，数字替换为0，其他保持不变
        pattern = ''
        for char in str(value):
            if char.isalpha():
                pattern += 'A'
            elif char.isdigit():
                pattern += '0'
            else:
                pattern += char
        return pattern
    
    def _detect_date_format(self, series: pd.Series) -> str:
        """检测日期格式"""
        # 尝试常见的日期格式
        formats = [
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%Y年%m月%d日',
            '%Y.%m.%d',
            '%d/%m/%Y',
            '%m/%d/%Y'
        ]
        
        non_null = series.dropna()
        if len(non_null) == 0:
            return '%Y-%m-%d'
        
        for fmt in formats:
            try:
                pd.to_datetime(non_null, format=fmt)
                return fmt
            except:
                continue
        
        return '%Y-%m-%d'  # 默认格式
    
    def _get_sample_values(self, series: pd.Series, n: int = 5) -> List[Any]:
        """获取样本值"""
        non_null = series.dropna()
        if len(non_null) == 0:
            return []
        
        # 获取唯一值
        unique_values = non_null.unique()
        
        # 如果唯一值少于n个，返回所有
        if len(unique_values) <= n:
            return unique_values.tolist()
        
        # 否则返回前n个
        return unique_values[:n].tolist()
    
    def _get_column_statistics(self, series: pd.Series) -> Dict[str, Any]:
        """获取列统计信息"""
        stats = {
            'count': len(series),
            'non_null_count': series.notna().sum(),
            'null_count': series.isna().sum(),
            'unique_count': series.nunique(),
            'dtype': str(series.dtype)
        }
        
        # 对于数值类型，添加额外统计
        try:
            numeric_series = pd.to_numeric(series, errors='coerce')
            if numeric_series.notna().any():
                stats.update({
                    'mean': float(numeric_series.mean()),
                    'std': float(numeric_series.std()),
                    'min': float(numeric_series.min()),
                    'max': float(numeric_series.max()),
                    'median': float(numeric_series.median())
                })
        except:
            pass
        
        return stats
    
    def _calculate_overall_quality(self, field_analysis: Dict[str, Any]) -> Dict[str, float]:
        """计算整体数据质量"""
        if not field_analysis:
            return {'overall_score': 0}
        
        quality_scores = []
        for field, analysis in field_analysis.items():
            if 'quality_metrics' in analysis:
                quality_scores.append(
                    analysis['quality_metrics'].get('overall_score', 0)
                )
        
        if quality_scores:
            return {
                'overall_score': sum(quality_scores) / len(quality_scores),
                'min_score': min(quality_scores),
                'max_score': max(quality_scores),
                'field_count': len(quality_scores)
            }
        
        return {'overall_score': 0}
    
    def _generate_recommendations(self, analysis_result: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 检查数据质量
        overall_quality = analysis_result.get('data_quality', {}).get('overall_score', 0)
        if overall_quality < 0.6:
            recommendations.append("数据质量较低，建议检查数据源的完整性和准确性")
        
        # 检查字段识别置信度
        low_confidence_fields = []
        for field, analysis in analysis_result.get('field_analysis', {}).items():
            if analysis.get('confidence', 0) < 0.6:
                low_confidence_fields.append(field)
        
        if low_confidence_fields:
            recommendations.append(
                f"以下字段类型识别置信度较低，建议手动确认: {', '.join(low_confidence_fields[:5])}"
            )
        
        # 检查空值
        high_null_fields = []
        for field, analysis in analysis_result.get('field_analysis', {}).items():
            quality = analysis.get('quality_metrics', {})
            if quality.get('completeness', 1) < 0.5:
                high_null_fields.append(field)
        
        if high_null_fields:
            recommendations.append(
                f"以下字段存在大量空值: {', '.join(high_null_fields[:5])}"
            )
        
        # 检查唯一性（对于工号字段）
        for field, analysis in analysis_result.get('field_analysis', {}).items():
            if analysis.get('field_type') == 'employee_id':
                uniqueness = analysis.get('quality_metrics', {}).get('uniqueness', 1)
                if uniqueness < 1.0:
                    recommendations.append(f"字段 '{field}' 存在重复值，可能影响数据准确性")
        
        if not recommendations:
            recommendations.append("数据质量良好，可以正常处理")
        
        return recommendations


def create_analyzer() -> ChangeDataAnalyzer:
    """创建分析器实例的工厂函数"""
    return ChangeDataAnalyzer()