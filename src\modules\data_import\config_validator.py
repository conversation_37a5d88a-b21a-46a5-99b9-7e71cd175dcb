#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证和冲突检测引擎

功能说明:
- 验证Sheet配置的合理性和一致性
- 检测配置冲突和潜在问题
- 提供配置修复建议和自动修复
- 支持跨Sheet配置验证
"""

from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from src.utils.log_config import setup_logger


class ValidationLevel(Enum):
    """验证级别"""
    INFO = "info"          # 信息提示
    WARNING = "warning"    # 警告
    ERROR = "error"        # 错误
    CRITICAL = "critical"  # 严重错误


class ValidationCategory(Enum):
    """验证分类"""
    RANGE_VALIDATION = "range"           # 数据范围验证
    LOGIC_VALIDATION = "logic"           # 逻辑验证
    CONSISTENCY_VALIDATION = "consistency"  # 一致性验证
    PERFORMANCE_VALIDATION = "performance"  # 性能验证
    DATA_QUALITY_VALIDATION = "quality"     # 数据质量验证


@dataclass
class ValidationIssue:
    """验证问题"""
    id: str
    level: ValidationLevel
    category: ValidationCategory
    title: str
    description: str
    sheet_name: str
    field_name: Optional[str] = None
    current_value: Any = None
    suggested_value: Any = None
    auto_fixable: bool = False
    fix_action: Optional[str] = None
    created_time: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'level': self.level.value,
            'category': self.category.value,
            'title': self.title,
            'description': self.description,
            'sheet_name': self.sheet_name,
            'field_name': self.field_name,
            'current_value': self.current_value,
            'suggested_value': self.suggested_value,
            'auto_fixable': self.auto_fixable,
            'fix_action': self.fix_action,
            'created_time': self.created_time.isoformat()
        }


@dataclass
class ValidationResult:
    """验证结果"""
    sheet_name: str
    is_valid: bool
    issues: List[ValidationIssue] = field(default_factory=list)
    warnings_count: int = 0
    errors_count: int = 0
    critical_count: int = 0
    auto_fixable_count: int = 0
    validation_time: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """计算统计信息"""
        self.warnings_count = len([i for i in self.issues if i.level == ValidationLevel.WARNING])
        self.errors_count = len([i for i in self.issues if i.level == ValidationLevel.ERROR])
        self.critical_count = len([i for i in self.issues if i.level == ValidationLevel.CRITICAL])
        self.auto_fixable_count = len([i for i in self.issues if i.auto_fixable])
        
        # 如果有错误或严重错误，则验证失败
        self.is_valid = self.errors_count == 0 and self.critical_count == 0
    
    def add_issue(self, issue: ValidationIssue):
        """添加验证问题"""
        self.issues.append(issue)
        self.__post_init__()  # 重新计算统计信息
    
    def get_issues_by_level(self, level: ValidationLevel) -> List[ValidationIssue]:
        """按级别获取问题"""
        return [issue for issue in self.issues if issue.level == level]
    
    def get_issues_by_category(self, category: ValidationCategory) -> List[ValidationIssue]:
        """按分类获取问题"""
        return [issue for issue in self.issues if issue.category == category]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'sheet_name': self.sheet_name,
            'is_valid': self.is_valid,
            'warnings_count': self.warnings_count,
            'errors_count': self.errors_count,
            'critical_count': self.critical_count,
            'auto_fixable_count': self.auto_fixable_count,
            'issues': [issue.to_dict() for issue in self.issues],
            'validation_time': self.validation_time.isoformat()
        }


class ConfigValidator:
    """配置验证引擎"""
    
    def __init__(self):
        """初始化验证引擎"""
        self.logger = setup_logger(__name__)
        
        # 验证规则
        self._init_validation_rules()
        
        # 验证历史
        self.validation_history: List[ValidationResult] = []
        
        self.logger.info("配置验证引擎初始化完成")
    
    def _init_validation_rules(self):
        """初始化验证规则"""
        
        # 数据范围验证规则
        self.range_rules = {
            'header_row': {
                'min': 1,
                'max': 100,
                'description': '表头行号应在1-100之间'
            },
            'data_start_row': {
                'min': 1,
                'max': 1000,
                'description': '数据起始行号应在1-1000之间'
            },
            'data_end_row': {
                'min': 1,
                'max': 1000000,
                'description': '数据结束行号应在1-1000000之间'
            }
        }
        
        # 逻辑验证规则
        self.logic_rules = [
            {
                'name': 'header_before_data',
                'description': '表头行应在数据起始行之前',
                'check': lambda config: config.header_row < config.data_start_row
            },
            {
                'name': 'data_range_valid',
                'description': '数据结束行应在数据起始行之后',
                'check': lambda config: not config.data_end_row or config.data_end_row >= config.data_start_row
            },
            {
                'name': 'enabled_sheet_has_config',
                'description': '启用的Sheet应有完整的配置',
                'check': lambda config: not config.is_enabled or (config.header_row and config.data_start_row)
            }
        ]
        
        # 性能验证规则
        self.performance_rules = [
            {
                'name': 'large_data_range',
                'description': '数据范围过大可能影响性能',
                'check': lambda config: not config.data_end_row or (config.data_end_row - config.data_start_row) < 50000,
                'level': ValidationLevel.WARNING
            },
            {
                'name': 'too_many_summary_keywords',
                'description': '汇总关键词过多可能影响性能',
                'check': lambda config: not hasattr(config, 'summary_keywords') or len(config.summary_keywords or []) < 20,
                'level': ValidationLevel.WARNING
            }
        ]
    
    def validate_single_config(self, sheet_name: str, config) -> ValidationResult:
        """验证单个Sheet配置"""
        try:
            self.logger.info(f"开始验证Sheet配置: {sheet_name}")
            
            result = ValidationResult(sheet_name=sheet_name, is_valid=True)
            
            # 1. 数据范围验证
            self._validate_ranges(config, result)
            
            # 2. 逻辑验证
            self._validate_logic(config, result)
            
            # 3. 性能验证
            self._validate_performance(config, result)
            
            # 4. 数据质量验证
            self._validate_data_quality(config, result)
            
            # 记录验证历史
            self.validation_history.append(result)
            
            self.logger.info(f"配置验证完成: {sheet_name}, 有效性: {result.is_valid}, 问题数: {len(result.issues)}")
            return result
            
        except Exception as e:
            self.logger.error(f"验证配置失败: {e}")
            # 返回包含错误的验证结果
            result = ValidationResult(sheet_name=sheet_name, is_valid=False)
            result.add_issue(ValidationIssue(
                id=f"validation_error_{sheet_name}",
                level=ValidationLevel.CRITICAL,
                category=ValidationCategory.LOGIC_VALIDATION,
                title="验证过程出错",
                description=f"配置验证过程中发生错误: {e}",
                sheet_name=sheet_name,
                auto_fixable=False
            ))
            return result

    def _validate_ranges(self, config, result: ValidationResult):
        """验证数据范围"""
        for field_name, rule in self.range_rules.items():
            if hasattr(config, field_name):
                value = getattr(config, field_name)
                if value is not None:
                    if value < rule['min'] or value > rule['max']:
                        result.add_issue(ValidationIssue(
                            id=f"range_{result.sheet_name}_{field_name}",
                            level=ValidationLevel.ERROR,
                            category=ValidationCategory.RANGE_VALIDATION,
                            title=f"{field_name}值超出范围",
                            description=f"{rule['description']}，当前值: {value}",
                            sheet_name=result.sheet_name,
                            field_name=field_name,
                            current_value=value,
                            suggested_value=max(rule['min'], min(rule['max'], value)),
                            auto_fixable=True,
                            fix_action=f"将{field_name}调整到合理范围内"
                        ))

    def _validate_logic(self, config, result: ValidationResult):
        """验证逻辑规则"""
        for rule in self.logic_rules:
            try:
                if not rule['check'](config):
                    result.add_issue(ValidationIssue(
                        id=f"logic_{result.sheet_name}_{rule['name']}",
                        level=ValidationLevel.ERROR,
                        category=ValidationCategory.LOGIC_VALIDATION,
                        title=f"逻辑验证失败: {rule['name']}",
                        description=rule['description'],
                        sheet_name=result.sheet_name,
                        auto_fixable=rule['name'] in ['header_before_data'],
                        fix_action=self._get_logic_fix_action(rule['name'], config)
                    ))
            except Exception as e:
                self.logger.warning(f"逻辑规则检查失败 {rule['name']}: {e}")

    def _validate_performance(self, config, result: ValidationResult):
        """验证性能相关配置"""
        for rule in self.performance_rules:
            try:
                if not rule['check'](config):
                    level = rule.get('level', ValidationLevel.WARNING)
                    result.add_issue(ValidationIssue(
                        id=f"performance_{result.sheet_name}_{rule['name']}",
                        level=level,
                        category=ValidationCategory.PERFORMANCE_VALIDATION,
                        title=f"性能警告: {rule['name']}",
                        description=rule['description'],
                        sheet_name=result.sheet_name,
                        auto_fixable=False,
                        fix_action="建议优化配置以提升性能"
                    ))
            except Exception as e:
                self.logger.warning(f"性能规则检查失败 {rule['name']}: {e}")

    def _validate_data_quality(self, config, result: ValidationResult):
        """验证数据质量相关配置"""

        # 检查是否启用了数据清洗
        if not getattr(config, 'trim_whitespace', True):
            result.add_issue(ValidationIssue(
                id=f"quality_{result.sheet_name}_no_trim",
                level=ValidationLevel.WARNING,
                category=ValidationCategory.DATA_QUALITY_VALIDATION,
                title="未启用空格清理",
                description="建议启用去除前后空格功能以提高数据质量",
                sheet_name=result.sheet_name,
                field_name='trim_whitespace',
                current_value=False,
                suggested_value=True,
                auto_fixable=True,
                fix_action="启用去除前后空格功能"
            ))

        # 检查是否启用了数字格式统一
        if not getattr(config, 'normalize_numbers', True):
            result.add_issue(ValidationIssue(
                id=f"quality_{result.sheet_name}_no_normalize",
                level=ValidationLevel.INFO,
                category=ValidationCategory.DATA_QUALITY_VALIDATION,
                title="未启用数字格式统一",
                description="建议启用数字格式统一功能以提高数据一致性",
                sheet_name=result.sheet_name,
                field_name='normalize_numbers',
                current_value=False,
                suggested_value=True,
                auto_fixable=True,
                fix_action="启用数字格式统一功能"
            ))

        # 检查汇总关键词配置
        if getattr(config, 'remove_summary_rows', False):
            keywords = getattr(config, 'summary_keywords', [])
            if not keywords:
                result.add_issue(ValidationIssue(
                    id=f"quality_{result.sheet_name}_no_keywords",
                    level=ValidationLevel.WARNING,
                    category=ValidationCategory.DATA_QUALITY_VALIDATION,
                    title="启用了汇总行移除但未配置关键词",
                    description="已启用移除汇总行功能，但未配置汇总关键词，可能无法正确识别汇总行",
                    sheet_name=result.sheet_name,
                    field_name='summary_keywords',
                    current_value=keywords,
                    suggested_value=['合计', '小计', '总计', '汇总'],
                    auto_fixable=True,
                    fix_action="添加默认汇总关键词"
                ))

    def _get_logic_fix_action(self, rule_name: str, config) -> str:
        """获取逻辑错误的修复建议"""
        if rule_name == 'header_before_data':
            return f"将表头行调整为{config.data_start_row - 1}或将数据起始行调整为{config.header_row + 1}"
        elif rule_name == 'data_range_valid':
            return f"将数据结束行调整为不小于{config.data_start_row}"
        elif rule_name == 'enabled_sheet_has_config':
            return "完善Sheet配置或禁用此Sheet"
        else:
            return "请检查并修复配置逻辑错误"

    def validate_multiple_configs(self, configs: Dict[str, Any]) -> Dict[str, ValidationResult]:
        """验证多个Sheet配置"""
        try:
            self.logger.info(f"开始验证多个Sheet配置: {len(configs)} 个")

            results = {}

            # 验证每个Sheet配置
            for sheet_name, config in configs.items():
                results[sheet_name] = self.validate_single_config(sheet_name, config)

            # 跨Sheet一致性验证
            self._validate_cross_sheet_consistency(configs, results)

            self.logger.info(f"多Sheet配置验证完成")
            return results

        except Exception as e:
            self.logger.error(f"多Sheet配置验证失败: {e}")
            return {}

    def _validate_cross_sheet_consistency(self, configs: Dict[str, Any], results: Dict[str, ValidationResult]):
        """验证跨Sheet一致性"""

        # 检查启用的Sheet是否有相似的配置模式
        enabled_configs = {name: config for name, config in configs.items() if config.is_enabled}

        if len(enabled_configs) > 1:
            # 检查表头行一致性
            header_rows = [config.header_row for config in enabled_configs.values()]
            if len(set(header_rows)) > 3:  # 如果表头行差异过大
                for sheet_name in enabled_configs.keys():
                    if sheet_name in results:
                        results[sheet_name].add_issue(ValidationIssue(
                            id=f"consistency_{sheet_name}_header_inconsistent",
                            level=ValidationLevel.INFO,
                            category=ValidationCategory.CONSISTENCY_VALIDATION,
                            title="表头行配置不一致",
                            description="多个Sheet的表头行配置差异较大，建议检查是否正确",
                            sheet_name=sheet_name,
                            auto_fixable=False,
                            fix_action="检查并统一表头行配置"
                        ))

    def auto_fix_issues(self, sheet_name: str, config, issues: List[ValidationIssue]) -> Tuple[Any, List[str]]:
        """自动修复可修复的问题"""
        try:
            fixed_actions = []

            for issue in issues:
                if issue.auto_fixable and issue.sheet_name == sheet_name:
                    if issue.field_name and hasattr(config, issue.field_name):
                        # 应用建议值
                        old_value = getattr(config, issue.field_name)
                        setattr(config, issue.field_name, issue.suggested_value)
                        fixed_actions.append(f"已修复 {issue.field_name}: {old_value} -> {issue.suggested_value}")
                        self.logger.info(f"自动修复: {issue.title}")

            return config, fixed_actions

        except Exception as e:
            self.logger.error(f"自动修复失败: {e}")
            return config, []

    def get_validation_summary(self, results: Dict[str, ValidationResult]) -> Dict[str, Any]:
        """获取验证摘要"""
        total_sheets = len(results)
        valid_sheets = len([r for r in results.values() if r.is_valid])
        total_issues = sum(len(r.issues) for r in results.values())
        total_warnings = sum(r.warnings_count for r in results.values())
        total_errors = sum(r.errors_count for r in results.values())
        total_critical = sum(r.critical_count for r in results.values())
        total_auto_fixable = sum(r.auto_fixable_count for r in results.values())

        return {
            'total_sheets': total_sheets,
            'valid_sheets': valid_sheets,
            'invalid_sheets': total_sheets - valid_sheets,
            'total_issues': total_issues,
            'warnings_count': total_warnings,
            'errors_count': total_errors,
            'critical_count': total_critical,
            'auto_fixable_count': total_auto_fixable,
            'validation_time': datetime.now().isoformat(),
            'overall_health': 'good' if total_errors == 0 and total_critical == 0 else 'warning' if total_critical == 0 else 'critical'
        }

    def get_validation_history(self, limit: int = 10) -> List[ValidationResult]:
        """获取验证历史"""
        return self.validation_history[-limit:] if limit > 0 else self.validation_history
