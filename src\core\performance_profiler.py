"""
性能分析器 - P2级优化
用于分析和监控应用性能
"""

import time
import functools
import psutil
import os
from typing import Dict, Any, Callable, Optional
from dataclasses import dataclass, field
from collections import defaultdict
from loguru import logger


@dataclass
class PerformanceMetrics:
    """性能指标"""
    name: str
    start_time: float
    end_time: float = 0
    duration: float = 0
    memory_before: float = 0
    memory_after: float = 0
    memory_delta: float = 0
    cpu_percent: float = 0
    
    def complete(self):
        """完成计时"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.memory_after = get_memory_usage()
        self.memory_delta = self.memory_after - self.memory_before


class PerformanceProfiler:
    """
    性能分析器
    
    功能：
    - 函数执行时间测量
    - 内存使用监控
    - CPU使用率监控
    - 启动时间分析
    - 热点分析
    """
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.logger = logger
        
        # 性能数据
        self._metrics: Dict[str, list] = defaultdict(list)
        self._current_metrics: Dict[str, PerformanceMetrics] = {}
        
        # 启动时间记录
        self._startup_metrics = []
        self._app_start_time = time.time()
        
        # 进程信息
        self._process = psutil.Process(os.getpid())
        
        self.logger.info("PerformanceProfiler 初始化完成")
    
    def start_timing(self, name: str) -> PerformanceMetrics:
        """
        开始计时
        
        Args:
            name: 计时名称
        
        Returns:
            性能指标对象
        """
        metric = PerformanceMetrics(
            name=name,
            start_time=time.time(),
            memory_before=get_memory_usage(),
            cpu_percent=self._process.cpu_percent()
        )
        
        self._current_metrics[name] = metric
        return metric
    
    def end_timing(self, name: str) -> Optional[PerformanceMetrics]:
        """
        结束计时
        
        Args:
            name: 计时名称
        
        Returns:
            完成的性能指标
        """
        if name not in self._current_metrics:
            return None
        
        metric = self._current_metrics.pop(name)
        metric.complete()
        
        self._metrics[name].append(metric)
        
        if metric.duration > 0.1:  # 记录超过100ms的操作
            self.logger.warning(f"性能警告: {name} 耗时 {metric.duration:.3f}秒")
        
        return metric
    
    def time_function(self, func: Callable) -> Callable:
        """
        装饰器：测量函数执行时间
        
        Args:
            func: 要测量的函数
        
        Returns:
            包装后的函数
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            name = f"{func.__module__}.{func.__name__}"
            self.start_timing(name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                self.end_timing(name)
        
        return wrapper
    
    def record_startup_metric(self, phase: str):
        """
        记录启动阶段指标
        
        Args:
            phase: 启动阶段名称
        """
        elapsed = time.time() - self._app_start_time
        memory = get_memory_usage()
        
        self._startup_metrics.append({
            'phase': phase,
            'elapsed': elapsed,
            'memory': memory
        })
        
        self.logger.info(f"启动阶段: {phase} - {elapsed:.3f}秒, {memory:.1f}MB")
    
    def get_statistics(self, name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取统计信息
        
        Args:
            name: 指定名称，None表示所有
        
        Returns:
            统计信息字典
        """
        if name and name in self._metrics:
            metrics = self._metrics[name]
            if not metrics:
                return {}
            
            durations = [m.duration for m in metrics]
            memory_deltas = [m.memory_delta for m in metrics]
            
            return {
                'name': name,
                'count': len(metrics),
                'total_time': sum(durations),
                'avg_time': sum(durations) / len(durations),
                'max_time': max(durations),
                'min_time': min(durations),
                'avg_memory_delta': sum(memory_deltas) / len(memory_deltas) if memory_deltas else 0
            }
        
        # 返回所有统计
        all_stats = {}
        for name, metrics in self._metrics.items():
            if metrics:
                all_stats[name] = self.get_statistics(name)
        
        return all_stats
    
    def get_hotspots(self, top_n: int = 10) -> list:
        """
        获取性能热点
        
        Args:
            top_n: 返回前N个热点
        
        Returns:
            热点列表
        """
        hotspots = []
        
        for name, stats in self.get_statistics().items():
            if stats:
                hotspots.append({
                    'name': name,
                    'total_time': stats['total_time'],
                    'count': stats['count'],
                    'avg_time': stats['avg_time']
                })
        
        # 按总时间排序
        hotspots.sort(key=lambda x: x['total_time'], reverse=True)
        
        return hotspots[:top_n]
    
    def get_startup_report(self) -> Dict[str, Any]:
        """
        获取启动报告
        
        Returns:
            启动报告字典
        """
        if not self._startup_metrics:
            return {}
        
        total_time = self._startup_metrics[-1]['elapsed'] if self._startup_metrics else 0
        
        phases = []
        prev_elapsed = 0
        for metric in self._startup_metrics:
            phase_time = metric['elapsed'] - prev_elapsed
            phases.append({
                'phase': metric['phase'],
                'time': phase_time,
                'cumulative': metric['elapsed'],
                'memory': metric['memory']
            })
            prev_elapsed = metric['elapsed']
        
        return {
            'total_time': total_time,
            'phases': phases,
            'final_memory': self._startup_metrics[-1]['memory'] if self._startup_metrics else 0
        }
    
    def print_report(self):
        """打印性能报告"""
        print("\n" + "=" * 60)
        print("性能分析报告")
        print("=" * 60)
        
        # 启动报告
        startup = self.get_startup_report()
        if startup:
            print(f"\n启动性能:")
            print(f"  总时间: {startup['total_time']:.3f}秒")
            print(f"  最终内存: {startup['final_memory']:.1f}MB")
            print(f"\n  各阶段耗时:")
            for phase in startup['phases']:
                print(f"    {phase['phase']}: {phase['time']:.3f}秒")
        
        # 热点分析
        hotspots = self.get_hotspots()
        if hotspots:
            print(f"\n性能热点 (Top 10):")
            for i, hotspot in enumerate(hotspots, 1):
                print(f"  {i}. {hotspot['name']}")
                print(f"     总时间: {hotspot['total_time']:.3f}秒")
                print(f"     调用次数: {hotspot['count']}")
                print(f"     平均时间: {hotspot['avg_time']:.3f}秒")
        
        # 内存信息
        print(f"\n当前内存使用: {get_memory_usage():.1f}MB")
        print(f"CPU使用率: {self._process.cpu_percent()}%")
        
        print("=" * 60)
    
    def reset(self):
        """重置所有指标"""
        self._metrics.clear()
        self._current_metrics.clear()
        self._startup_metrics.clear()
        self._app_start_time = time.time()


def get_memory_usage() -> float:
    """
    获取当前进程内存使用量(MB)
    
    Returns:
        内存使用量(MB)
    """
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024


def profile_function(func: Callable) -> Callable:
    """
    性能分析装饰器
    
    Args:
        func: 要分析的函数
    
    Returns:
        包装后的函数
    """
    profiler = get_performance_profiler()
    return profiler.time_function(func)


def profile_startup(phase: str):
    """
    记录启动阶段
    
    Args:
        phase: 阶段名称
    """
    profiler = get_performance_profiler()
    profiler.record_startup_metric(phase)


# 全局实例
_performance_profiler = None

def get_performance_profiler() -> PerformanceProfiler:
    """获取性能分析器的全局实例"""
    global _performance_profiler
    if _performance_profiler is None:
        _performance_profiler = PerformanceProfiler()
    return _performance_profiler