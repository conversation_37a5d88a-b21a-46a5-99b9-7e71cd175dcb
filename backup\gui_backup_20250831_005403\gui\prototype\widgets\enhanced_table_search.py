"""
增强表格搜索组件 - Phase 3 主工作区域增强

扩展Phase 2的SmartSearchDebounce算法到表格搜索功能，
实现表格内容的实时搜索和高亮显示。

主要特性:
- 智能防抖搜索：基于SmartSearchDebounce算法
- 多列搜索：支持跨列搜索和列特定搜索
- 实时高亮：搜索结果实时高亮显示
- 复杂查询：支持AND/OR逻辑和通配符
- 搜索历史：自动保存和自动补全

技术架构:
- EnhancedTableSearch: 主搜索组件
- TableSearchEngine: 搜索引擎核心
- SearchHighlighter: 高亮显示管理器
- SearchHistoryManager: 搜索历史管理

作者: PyQt5重构项目组
创建时间: 2025-06-19 16:45
"""

import sys
import re
import time
from typing import List, Dict, Any, Optional, Set, Tuple
from enum import Enum
from dataclasses import dataclass

from PyQt5.QtWidgets import (
    QWidget, QLineEdit, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QComboBox, QCheckBox, QCompleter, QFrame,
    QApplication, QMainWindow, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import (
    Qt, QTimer, pyqtSignal, QStringListModel, QModelIndex
)
from PyQt5.QtGui import (
    QColor, QFont, QTextCharFormat, QIcon, QPalette
)

from src.utils.log_config import setup_logger
from .smart_search_debounce import SmartSearchDebounce


class SearchMode(Enum):
    """搜索模式枚举"""
    SIMPLE = "simple"        # 简单搜索
    ADVANCED = "advanced"    # 高级搜索
    REGEX = "regex"         # 正则表达式搜索


class SearchScope(Enum):
    """搜索范围枚举"""
    ALL_COLUMNS = "all"      # 全部列
    SPECIFIC_COLUMN = "specific"  # 指定列
    VISIBLE_COLUMNS = "visible"   # 可见列


@dataclass
class SearchResult:
    """搜索结果数据结构"""
    row: int                 # 行索引
    column: int             # 列索引
    match_text: str         # 匹配文本
    match_start: int        # 匹配开始位置
    match_end: int          # 匹配结束位置
    relevance_score: float  # 相关性分数


class SearchHistoryManager:
    """
    搜索历史管理器
    
    管理搜索历史记录，提供自动补全功能。
    """
    
    def __init__(self, max_history: int = 50):
        self.logger = setup_logger(__name__)
        self.max_history = max_history
        self.search_history: List[str] = []
        self.search_frequency: Dict[str, int] = {}
    
    def add_search(self, query: str):
        """添加搜索记录"""
        if not query.strip():
            return
        
        query = query.strip()
        
        # 更新频次
        self.search_frequency[query] = self.search_frequency.get(query, 0) + 1
        
        # 移除旧记录并添加到前面
        if query in self.search_history:
            self.search_history.remove(query)
        self.search_history.insert(0, query)
        
        # 保持历史记录数量限制
        if len(self.search_history) > self.max_history:
            removed = self.search_history.pop()
            if removed in self.search_frequency:
                del self.search_frequency[removed]
    
    def get_suggestions(self, partial_query: str, limit: int = 10) -> List[str]:
        """获取搜索建议"""
        if not partial_query:
            return self.search_history[:limit]
        
        partial_lower = partial_query.lower()
        suggestions = []
        
        # 按频次和匹配度排序
        for query in self.search_history:
            if partial_lower in query.lower():
                frequency = self.search_frequency.get(query, 1)
                suggestions.append((query, frequency))
        
        # 排序：频次高的优先，然后按历史顺序
        suggestions.sort(key=lambda x: (-x[1], self.search_history.index(x[0])))
        
        return [item[0] for item in suggestions[:limit]]
    
    def clear_history(self):
        """清空搜索历史"""
        self.search_history.clear()
        self.search_frequency.clear()


class TableSearchEngine:
    """
    表格搜索引擎
    
    实现高效的表格内容搜索算法。
    """
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.case_sensitive = False
        self.whole_words_only = False
        self.regex_enabled = False
    
    def search(self, table: QTableWidget, query: str, 
              scope: SearchScope = SearchScope.ALL_COLUMNS,
              target_column: int = -1) -> List[SearchResult]:
        """
        执行表格搜索
        
        Args:
            table: 目标表格
            query: 搜索查询
            scope: 搜索范围
            target_column: 目标列（当scope为SPECIFIC_COLUMN时）
            
        Returns:
            搜索结果列表
        """
        if not query.strip():
            return []
        
        start_time = time.time()
        results = []
        
        try:
            # 确定搜索列范围
            search_columns = self._get_search_columns(table, scope, target_column)
            
            # 执行搜索
            for row in range(table.rowCount()):
                for col in search_columns:
                    item = table.item(row, col)
                    if item:
                        cell_text = item.text()
                        matches = self._find_matches(cell_text, query)
                        
                        for match in matches:
                            result = SearchResult(
                                row=row,
                                column=col,
                                match_text=match['text'],
                                match_start=match['start'],
                                match_end=match['end'],
                                relevance_score=match['score']
                            )
                            results.append(result)
            
            # 按相关性排序
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            
            elapsed_time = (time.time() - start_time) * 1000
            self.logger.debug(f"搜索完成: 查询='{query}', 结果={len(results)}个, "
                           f"耗时={elapsed_time:.2f}ms")
            
            return results
            
        except Exception as e:
            self.logger.error(f"搜索执行失败: {e}")
            return []
    
    def _get_search_columns(self, table: QTableWidget, scope: SearchScope, 
                          target_column: int) -> List[int]:
        """获取搜索列范围"""
        if scope == SearchScope.ALL_COLUMNS:
            return list(range(table.columnCount()))
        elif scope == SearchScope.SPECIFIC_COLUMN:
            if 0 <= target_column < table.columnCount():
                return [target_column]
            else:
                return []
        elif scope == SearchScope.VISIBLE_COLUMNS:
            # 获取可见列
            visible_columns = []
            for col in range(table.columnCount()):
                if not table.isColumnHidden(col):
                    visible_columns.append(col)
            return visible_columns
        
        return []
    
    def _find_matches(self, text: str, query: str) -> List[Dict[str, Any]]:
        """在文本中查找匹配项"""
        matches = []
        
        if not text or not query:
            return matches
        
        try:
            if self.regex_enabled:
                # 正则表达式搜索
                flags = 0 if self.case_sensitive else re.IGNORECASE
                pattern = re.compile(query, flags)
                
                for match in pattern.finditer(text):
                    matches.append({
                        'text': match.group(),
                        'start': match.start(),
                        'end': match.end(),
                        'score': self._calculate_relevance_score(
                            text, query, match.start(), match.end()
                        )
                    })
            else:
                # 普通文本搜索
                search_text = text if self.case_sensitive else text.lower()
                search_query = query if self.case_sensitive else query.lower()
                
                if self.whole_words_only:
                    # 整词搜索
                    pattern = r'\b' + re.escape(search_query) + r'\b'
                    flags = 0 if self.case_sensitive else re.IGNORECASE
                    
                    for match in re.finditer(pattern, search_text):
                        matches.append({
                            'text': text[match.start():match.end()],
                            'start': match.start(),
                            'end': match.end(),
                            'score': self._calculate_relevance_score(
                                text, query, match.start(), match.end()
                            )
                        })
                else:
                    # 部分匹配搜索
                    start = 0
                    while True:
                        pos = search_text.find(search_query, start)
                        if pos == -1:
                            break
                        
                        matches.append({
                            'text': text[pos:pos + len(query)],
                            'start': pos,
                            'end': pos + len(query),
                            'score': self._calculate_relevance_score(
                                text, query, pos, pos + len(query)
                            )
                        })
                        
                        start = pos + 1
                        
        except Exception as e:
            self.logger.error(f"文本匹配失败: {e}")
        
        return matches
    
    def _calculate_relevance_score(self, text: str, query: str, 
                                 start: int, end: int) -> float:
        """计算相关性分数"""
        score = 1.0
        
        # 完全匹配得分更高
        if text.strip() == query.strip():
            score += 2.0
        
        # 开头匹配得分更高
        if start == 0:
            score += 1.0
        
        # 查询长度占比
        query_ratio = len(query) / len(text) if text else 0
        score += query_ratio
        
        # 位置权重（靠前的匹配得分更高）
        position_weight = 1.0 - (start / len(text)) if text else 0
        score += position_weight * 0.5
        
        return score


class SearchHighlighter:
    """
    搜索高亮管理器
    
    管理表格中搜索结果的高亮显示。
    """
    
    def __init__(self, table: QTableWidget):
        self.table = table
        self.logger = setup_logger(__name__)
        
        # 高亮样式配置
        self.highlight_color = QColor(255, 255, 0, 128)  # 黄色高亮
        self.selected_highlight_color = QColor(255, 165, 0, 128)  # 橙色（选中）
        
        # 存储原始样式
        self.original_styles: Dict[Tuple[int, int], str] = {}
        self.highlighted_cells: Set[Tuple[int, int]] = set()
    
    def highlight_results(self, results: List[SearchResult]):
        """高亮显示搜索结果"""
        try:
            # 清除之前的高亮
            self.clear_highlights()
            
            # 应用新的高亮
            for result in results:
                self._highlight_cell(result.row, result.column)
            
            self.logger.debug(f"高亮显示 {len(results)} 个搜索结果")
            
        except Exception as e:
            self.logger.error(f"高亮显示失败: {e}")
    
    def _highlight_cell(self, row: int, column: int):
        """高亮单个单元格"""
        try:
            item = self.table.item(row, column)
            if not item:
                return
            
            cell_key = (row, column)
            
            # 保存原始样式
            if cell_key not in self.original_styles:
                self.original_styles[cell_key] = item.background().color().name()
            
            # 应用高亮样式
            item.setBackground(self.highlight_color)
            self.highlighted_cells.add(cell_key)
            
        except Exception as e:
            self.logger.error(f"高亮单元格失败: 行{row}, 列{column}, 错误: {e}")
    
    def clear_highlights(self):
        """清除所有高亮"""
        try:
            for row, column in self.highlighted_cells:
                item = self.table.item(row, column)
                if item:
                    # 恢复原始样式
                    original_color = self.original_styles.get((row, column), "white")
                    item.setBackground(QColor(original_color))
            
            self.highlighted_cells.clear()
            self.original_styles.clear()
            
        except Exception as e:
            self.logger.error(f"清除高亮失败: {e}")
    
    def scroll_to_first_result(self, results: List[SearchResult]):
        """滚动到第一个搜索结果"""
        if results:
            first_result = results[0]
            self.table.scrollToItem(
                self.table.item(first_result.row, first_result.column),
                self.table.PositionAtCenter
            )


class EnhancedTableSearch(QWidget):
    """
    增强表格搜索主组件
    
    基于SmartSearchDebounce算法的表格搜索界面组件。
    """
    
    # 信号定义
    search_performed = pyqtSignal(str, list)    # 搜索执行完成
    search_cleared = pyqtSignal()               # 搜索清除
    
    def __init__(self, table: QTableWidget, parent=None):
        super().__init__(parent)
        self.table = table
        self.logger = setup_logger(__name__)
        
        # 初始化组件
        self.search_debounce = SmartSearchDebounce(self)
        self.search_engine = TableSearchEngine()
        self.search_highlighter = SearchHighlighter(table)
        self.history_manager = SearchHistoryManager()
        
        # 搜索状态
        self.current_results: List[SearchResult] = []
        self.current_result_index = 0
        
        # 初始化UI
        self.setup_ui()
        self.setup_connections()
        
        self.logger.info("增强表格搜索组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # 主搜索行
        main_search_layout = QHBoxLayout()
        
        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索表格内容...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #E0E0E0;
                border-radius: 20px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2196F3;
                background-color: #F8F9FA;
            }
        """)
        
        # 搜索历史自动补全
        self.completer = QCompleter(self)
        self.completer_model = QStringListModel(self)
        self.completer.setModel(self.completer_model)
        self.search_input.setCompleter(self.completer)
        
        # 搜索按钮
        self.search_button = QPushButton("搜索")
        self.search_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        
        # 清除按钮
        self.clear_button = QPushButton("清除")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E64A19;
            }
        """)
        
        main_search_layout.addWidget(self.search_input)
        main_search_layout.addWidget(self.search_button)
        main_search_layout.addWidget(self.clear_button)
        
        layout.addLayout(main_search_layout)
        
        # 高级搜索选项
        options_layout = QHBoxLayout()
        
        # 搜索范围
        self.scope_combo = QComboBox()
        self.scope_combo.addItems(["全部列", "当前列", "可见列"])
        self.scope_combo.setStyleSheet("""
            QComboBox {
                padding: 4px 8px;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                background-color: white;
            }
        """)
        
        # 搜索选项
        self.case_sensitive_cb = QCheckBox("区分大小写")
        self.whole_words_cb = QCheckBox("整词匹配")
        self.regex_cb = QCheckBox("正则表达式")
        
        for cb in [self.case_sensitive_cb, self.whole_words_cb, self.regex_cb]:
            cb.setStyleSheet("""
                QCheckBox {
                    font-size: 12px;
                    color: #666;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                }
            """)
        
        options_layout.addWidget(QLabel("搜索范围:"))
        options_layout.addWidget(self.scope_combo)
        options_layout.addStretch()
        options_layout.addWidget(self.case_sensitive_cb)
        options_layout.addWidget(self.whole_words_cb)
        options_layout.addWidget(self.regex_cb)
        
        layout.addLayout(options_layout)
        
        # 结果状态栏
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                padding: 4px;
                background-color: #F5F5F5;
                border-radius: 4px;
            }
        """)
        
        layout.addWidget(self.status_label)
    
    def setup_connections(self):
        """设置信号连接"""
        # 搜索输入连接到防抖系统
        self.search_input.textChanged.connect(self._on_search_text_changed)
        self.search_debounce.search_triggered.connect(self._on_debounce_search_triggered)
        
        # 按钮连接
        self.search_button.clicked.connect(self._manual_search)
        self.clear_button.clicked.connect(self.clear_search)
        
        # 选项变化连接
        self.case_sensitive_cb.toggled.connect(self._update_search_options)
        self.whole_words_cb.toggled.connect(self._update_search_options)
        self.regex_cb.toggled.connect(self._update_search_options)
        self.scope_combo.currentTextChanged.connect(self._update_search_options)
    
    def _on_search_text_changed(self, text: str):
        """搜索文本变化处理"""
        if text.strip():
            # 触发防抖搜索
            self.search_debounce.search(text)
            self.status_label.setText("搜索中...")
        else:
            # 清空搜索
            self.clear_search()
    
    def _on_debounce_search_triggered(self, query: str, context: dict):
        """防抖搜索触发处理"""
        self._perform_search(query)
    
    def _manual_search(self):
        """手动搜索"""
        query = self.search_input.text().strip()
        if query:
            self._perform_search(query)
    
    def _perform_search(self, query: str):
        """执行搜索"""
        start_time = time.time()
        
        try:
            # 更新搜索引擎配置
            self._update_search_engine_config()
            
            # 确定搜索范围
            scope = self._get_current_scope()
            target_column = self._get_target_column()
            
            # 执行搜索
            results = self.search_engine.search(
                self.table, query, scope, target_column
            )
            
            # 高亮显示结果
            self.search_highlighter.highlight_results(results)
            
            # 滚动到第一个结果
            if results:
                self.search_highlighter.scroll_to_first_result(results)
            
            # 更新状态
            self.current_results = results
            self.current_result_index = 0
            
            # 添加到搜索历史
            self.history_manager.add_search(query)
            self._update_completer()
            
            # 更新状态显示
            elapsed_time = (time.time() - start_time) * 1000
            self.status_label.setText(
                f"找到 {len(results)} 个结果 (耗时: {elapsed_time:.1f}ms)"
            )
            
            # 发送信号
            self.search_performed.emit(query, results)
            
            self.logger.debug(f"搜索完成: '{query}' - {len(results)} 个结果")
            
        except Exception as e:
            self.status_label.setText(f"搜索失败: {str(e)}")
            self.logger.error(f"搜索执行失败: {e}")
    
    def _update_search_engine_config(self):
        """更新搜索引擎配置"""
        self.search_engine.case_sensitive = self.case_sensitive_cb.isChecked()
        self.search_engine.whole_words_only = self.whole_words_cb.isChecked()
        self.search_engine.regex_enabled = self.regex_cb.isChecked()
    
    def _get_current_scope(self) -> SearchScope:
        """获取当前搜索范围"""
        scope_text = self.scope_combo.currentText()
        if scope_text == "全部列":
            return SearchScope.ALL_COLUMNS
        elif scope_text == "当前列":
            return SearchScope.SPECIFIC_COLUMN
        elif scope_text == "可见列":
            return SearchScope.VISIBLE_COLUMNS
        return SearchScope.ALL_COLUMNS
    
    def _get_target_column(self) -> int:
        """获取目标列索引"""
        if self.scope_combo.currentText() == "当前列":
            return self.table.currentColumn()
        return -1
    
    def _update_search_options(self):
        """更新搜索选项时重新搜索"""
        query = self.search_input.text().strip()
        if query:
            self._perform_search(query)
    
    def _update_completer(self):
        """更新自动补全"""
        history = self.history_manager.search_history[:20]  # 最近20条
        self.completer_model.setStringList(history)
    
    def clear_search(self):
        """清除搜索"""
        self.search_input.clear()
        self.search_highlighter.clear_highlights()
        self.current_results.clear()
        self.current_result_index = 0
        self.status_label.setText("就绪")
        self.search_cleared.emit()
    
    def get_search_results(self) -> List[SearchResult]:
        """获取当前搜索结果"""
        return self.current_results.copy()
    
    def get_search_stats(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        return {
            'total_results': len(self.current_results),
            'current_index': self.current_result_index,
            'search_history_count': len(self.history_manager.search_history),
            'debounce_stats': self.search_debounce.get_statistics()
        }


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建测试表格
    table = QTableWidget(20, 5)
    table.setHorizontalHeaderLabels(["姓名", "工号", "部门", "职位", "工资"])
    
    # 填充测试数据
    test_data = [
        ["张三", "001", "技术部", "工程师", "8000"],
        ["李四", "002", "财务部", "会计", "6000"],
        ["王五", "003", "技术部", "高级工程师", "12000"],
        ["赵六", "004", "人事部", "专员", "5000"],
        ["钱七", "005", "技术部", "架构师", "15000"],
    ]
    
    for i, row_data in enumerate(test_data * 4):  # 重复数据
        for j, cell_data in enumerate(row_data):
            table.setItem(i, j, QTableWidgetItem(f"{cell_data}_{i}"))
    
    # 创建搜索组件
    search_widget = EnhancedTableSearch(table)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("增强表格搜索测试")
    main_window.resize(800, 600)
    
    # 布局
    central_widget = QWidget()
    layout = QVBoxLayout(central_widget)
    layout.addWidget(search_widget)
    layout.addWidget(table)
    
    main_window.setCentralWidget(central_widget)
    main_window.show()
    
    sys.exit(app.exec_()) 