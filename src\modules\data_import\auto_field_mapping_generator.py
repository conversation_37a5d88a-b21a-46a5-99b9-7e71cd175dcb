"""
自动字段映射生成器

功能说明:
- 在数据导入时自动为每个表生成完整的字段映射配置
- 智能映射算法 + 用户自定义规则
- 多策略映射生成：精确匹配、模糊匹配、保持原始名称
- 映射质量评估和建议生成

功能函数:
- generate_mapping(): 生成字段映射配置
- assess_mapping_quality(): 评估映射质量
- update_mapping_rules(): 更新映射规则
- export_mapping_template(): 导出映射模板

创建时间: 2025-06-23
作者: 开发团队
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
from difflib import SequenceMatcher

from src.utils.log_config import setup_logger


class AutoFieldMappingGenerator:
    """自动字段映射生成器
    
    提供智能的字段映射自动生成功能
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化自动映射生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.logger = setup_logger(__name__)
        
        # 初始化配置
        self.config_path = config_path or "state/data/field_mappings.json"
        self.mapping_rules_path = "state/data/auto_mapping_rules.json"
        
        # 加载映射规则
        self.standard_mappings = self._load_standard_mappings()
        self.user_mapping_rules = self._load_user_mapping_rules()
        
        # 映射质量统计
        self.mapping_stats = {
            "total_generated": 0,
            "exact_matches": 0,
            "fuzzy_matches": 0,
            "original_kept": 0
        }
        
        self.logger.info("自动字段映射生成器初始化完成")
    
    def generate_mapping(self, excel_columns: List[str], table_name: str) -> Dict[str, str]:
        """
        多策略字段映射生成算法

        修改：以数据库字段名为键，而非Excel列名

        策略优先级:
        1. 精确匹配已知字段映射规则
        2. 模糊匹配常用字段模式
        3. 保持原始列名作为显示名
        4. 用户自定义映射规则

        Args:
            excel_columns: Excel列名列表
            table_name: 表名

        Returns:
            Dict[str, str]: 生成的字段映射配置 (数据库字段名 -> 显示名)
        """
        mapping_result = {}
        generation_log = []

        self.logger.info(f"开始为表 '{table_name}' 生成字段映射，共 {len(excel_columns)} 个字段")

        for excel_col in excel_columns:
            # 关键修改：获取对应的数据库字段名
            db_field_name = self._excel_column_to_db_field(excel_col)

            # 清理字段名
            clean_col = self._clean_field_name(excel_col)

            # 策略1: 精确匹配
            exact_match = self._exact_match_mapping(clean_col)
            if exact_match:
                mapping_result[db_field_name] = exact_match
                generation_log.append(f"精确匹配: {excel_col} -> {db_field_name} -> {exact_match}")
                self.mapping_stats["exact_matches"] += 1
                continue

            # 策略2: 模糊匹配
            fuzzy_match = self._fuzzy_match_mapping(clean_col)
            if fuzzy_match:
                mapping_result[db_field_name] = fuzzy_match
                generation_log.append(f"模糊匹配: {excel_col} -> {db_field_name} -> {fuzzy_match}")
                self.mapping_stats["fuzzy_matches"] += 1
                continue

            # 策略3: 用户自定义规则匹配
            user_match = self._user_rule_mapping(clean_col, table_name)
            if user_match:
                mapping_result[db_field_name] = user_match
                generation_log.append(f"用户规则匹配: {excel_col} -> {db_field_name} -> {user_match}")
                continue

            # 策略4: 中文表头特殊处理
            if self._contains_chinese(excel_col):
                # 如果是中文表头，直接使用原始表头作为显示名
                mapping_result[db_field_name] = excel_col
                generation_log.append(f"中文表头保持: {excel_col} -> {db_field_name} -> {excel_col}")
                self.mapping_stats["original_kept"] += 1
            else:
                # 策略5: 保持原始名称
                mapping_result[db_field_name] = clean_col
                generation_log.append(f"保持原名: {excel_col} -> {db_field_name} -> {clean_col}")
                self.mapping_stats["original_kept"] += 1

        self.mapping_stats["total_generated"] += len(excel_columns)

        # 记录生成日志
        self.logger.info(f"字段映射生成完成，详细信息:")
        for log_entry in generation_log:
            self.logger.debug(log_entry)

        return mapping_result

    def validate_mapping_keys(self, field_mapping: Dict[str, str], actual_db_fields: List[str]) -> Dict[str, str]:
        """验证映射键与实际数据库字段的一致性

        Args:
            field_mapping: 字段映射配置 (数据库字段名 -> 显示名)
            actual_db_fields: 实际的数据库字段列表

        Returns:
            Dict[str, str]: 验证后的映射配置
        """
        validated_mapping = {}
        validation_log = []

        # 1. 验证映射中的键是否都是有效的数据库字段
        for db_field, display_name in field_mapping.items():
            if db_field in actual_db_fields:
                validated_mapping[db_field] = display_name
                validation_log.append(f"✓ 验证通过: {db_field} -> {display_name}")
            else:
                validation_log.append(f"⚠ 字段不存在: {db_field} (已跳过)")

        # 2. 为缺失的数据库字段生成默认显示名
        missing_fields = set(actual_db_fields) - set(validated_mapping.keys())
        for db_field in missing_fields:
            default_display_name = self._generate_default_display_name(db_field)
            validated_mapping[db_field] = default_display_name
            validation_log.append(f"+ 补充字段: {db_field} -> {default_display_name}")

        # 记录验证结果
        self.logger.info(f"字段映射验证完成:")
        for log_entry in validation_log:
            self.logger.debug(log_entry)

        self.logger.info(f"验证结果: 原始 {len(field_mapping)} 个字段，验证后 {len(validated_mapping)} 个字段")

        return validated_mapping

    def _generate_default_display_name(self, db_field: str) -> str:
        """为数据库字段生成默认显示名称

        Args:
            db_field: 数据库字段名

        Returns:
            str: 友好的显示名称
        """
        # 如果已经是中文，直接返回
        if any('\u4e00' <= char <= '\u9fff' for char in db_field):
            return db_field

        # 常见字段映射
        field_display_map = {
            'employee_id': '工号',
            'employee_name': '姓名',
            'department': '部门',
            'position': '职位',
            'basic_salary': '基本工资',
            'position_salary': '岗位工资',
            'grade_salary': '薪级工资',
            'performance': '绩效工资',
            'allowance': '津贴补贴',
            'total_salary': '应发合计',
            'social_insurance': '五险一金个人',
            'data_source': '数据来源',
            'import_time': '导入时间',
            'created_at': '创建时间',
            'updated_at': '更新时间'
        }

        # 检查直接映射
        if db_field in field_display_map:
            return field_display_map[db_field]

        # 转换下划线命名为更友好的显示
        display_name = db_field.replace('_', ' ').title()
        return display_name

    def _clean_field_name(self, field_name: str) -> str:
        """清理字段名称
        
        Args:
            field_name: 原始字段名
            
        Returns:
            str: 清理后的字段名
        """
        if not field_name:
            return ""
        
        # 去除首尾空白
        cleaned = str(field_name).strip()
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', '', cleaned)
        
        # 移除特殊字符
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', cleaned)
        
        return cleaned

    def _excel_column_to_db_field(self, excel_column: str) -> str:
        """Excel列名转换为数据库字段名

        Args:
            excel_column: Excel列名

        Returns:
            str: 对应的数据库字段名
        """
        # 清理输入
        clean_column = self._clean_field_name(excel_column)

        # 🔧 [P1-重要修复] Excel列名到数据库字段名的增强映射规则
        field_mapping = {
            # 基础信息字段
            "序号": "sequence_number",
            "工号": "employee_id",
            "人员代码": "employee_id",
            "员工号": "employee_id",
            "职工编号": "employee_id",
            "编号": "employee_id",

            "姓名": "employee_name",
            "员工姓名": "employee_name",
            "职工姓名": "employee_name",
            "名字": "employee_name",

            "部门": "department",
            "部门名称": "department",
            "所属部门": "department",
            "单位": "department",
            "科室": "department",

            "职位": "position",
            "职务": "position",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",

            "身份证号": "id_card",
            "身份证": "id_card",

            # 工资字段
            "基本工资": "basic_salary",
            "基础工资": "basic_salary",
            "基本薪资": "basic_salary",

            "岗位工资": "position_salary",
            "职位工资": "position_salary",
            "岗位薪资": "position_salary",
            "2025年岗位工资": "position_salary_2025",

            "薪级工资": "grade_salary",
            "等级工资": "grade_salary",
            "薪级薪资": "grade_salary",
            "2025年薪级工资": "grade_salary_2025",

            "校龄工资": "seniority_salary",
            "2025年校龄工资": "seniority_salary_2025",

            "绩效": "performance_bonus",
            "奖金": "performance_bonus",
            "绩效工资": "performance_bonus",
            "绩效奖金": "performance_bonus",
            "奖励性绩效": "performance_bonus",
            "基础性绩效": "basic_performance",
            "2025年基础性绩效": "basic_performance_2025",
            "2025年奖励性绩效预发": "performance_bonus_2025",

            "津贴": "allowance",
            "补贴": "allowance",
            "津贴补贴": "allowance",
            "结余津贴": "balance_allowance",
            "生活补贴": "living_allowance",
            "2025年生活补贴": "living_allowance_2025",
            "住房补贴": "housing_allowance",
            "物业补贴": "property_allowance",
            "交通补贴": "transport_allowance",
            "通讯补贴": "communication_allowance",
            "车补": "car_allowance",
            "卫生费": "health_fee",

            # 退休相关字段
            "基本退休费": "basic_retirement_salary",
            "基本离休费": "basic_retirement_salary",
            "离退休生活补贴": "retirement_living_allowance",
            "离休补贴": "retirement_allowance",
            "护理费": "nursing_fee",
            "增发一次性生活补贴": "one_time_living_allowance",
            "增资预付": "salary_advance",

            # 调整字段
            "2016待遇调整": "adjustment_2016",
            "2017待遇调整": "adjustment_2017",
            "2018待遇调整": "adjustment_2018",
            "2019待遇调整": "adjustment_2019",
            "2020待遇调整": "adjustment_2020",
            "2021待遇调整": "adjustment_2021",
            "2022待遇调整": "adjustment_2022",
            "2023待遇调整": "adjustment_2023",

            "应发工资": "total_salary",
            "应发合计": "total_salary",
            "工资合计": "total_salary",
            "总工资": "total_salary",
            "合计": "total",

            "五险一金": "social_insurance",
            "社保": "social_insurance",
            "公积金": "provident_fund",
            "2025公积金": "provident_fund_2025",
            "公积": "provident_fund",
            "代扣代存养老保险": "pension_insurance",
            "保险扣款": "insurance_deduction",
            "五险一金个人": "social_insurance",

            # 其他字段
            "补发": "supplement",
            "借支": "advance",
            "备注": "remarks",

            # 时间字段
            "数据来源": "data_source",
            "导入时间": "import_time",
            "创建时间": "created_at",
            "更新时间": "updated_at"
        }

        # 1. 直接匹配
        if clean_column in field_mapping:
            return field_mapping[clean_column]

        # 2. 模糊匹配（处理带年份的字段，优先匹配更长的模式）
        # 按模式长度排序，优先匹配更具体的模式
        sorted_patterns = sorted(field_mapping.items(), key=lambda x: len(x[0]), reverse=True)
        for excel_pattern, db_field in sorted_patterns:
            # 处理如 "2025年岗位工资" -> "岗位工资" -> "position_salary"
            if excel_pattern in clean_column:
                return db_field

        # 3. 如果没有匹配，使用标准化的字段名
        return self._normalize_field_name(clean_column)

    def _normalize_field_name(self, field_name: str) -> str:
        """标准化字段名为数据库字段格式

        Args:
            field_name: 原始字段名

        Returns:
            str: 标准化的数据库字段名
        """
        if not field_name:
            return "unknown_field"

        # 移除特殊字符，转换为下划线命名
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '_', str(field_name))

        # 如果包含中文，尝试转换为英文
        if any('\u4e00' <= char <= '\u9fff' for char in normalized):
            # 简单的中文到英文转换
            chinese_to_english = {
                '工号': 'employee_id',
                '姓名': 'employee_name',
                '部门': 'department',
                '职位': 'position',
                '工资': 'salary',
                '奖金': 'bonus',
                '津贴': 'allowance',
                '绩效': 'performance'
            }

            for chinese, english in chinese_to_english.items():
                if chinese in normalized:
                    normalized = normalized.replace(chinese, english)

        # 转换为小写并清理
        normalized = normalized.lower().strip('_')

        # 确保不为空
        return normalized if normalized else "unknown_field"

    def _exact_match_mapping(self, field_name: str) -> Optional[str]:
        """精确匹配映射
        
        Args:
            field_name: 字段名
            
        Returns:
            Optional[str]: 匹配的映射名，如果没有匹配返回None
        """
        # 检查标准映射中的精确匹配
        for pattern_info in self.standard_mappings.values():
            if isinstance(pattern_info, dict):
                # 检查aliases中的精确匹配
                aliases = pattern_info.get("aliases", [])
                if field_name in aliases or field_name.lower() in [alias.lower() for alias in aliases]:
                    return pattern_info.get("display_name", field_name)
                
                # 检查正则表达式精确匹配
                regex_pattern = pattern_info.get("regex", "")
                if regex_pattern and re.fullmatch(regex_pattern, field_name, re.IGNORECASE):
                    return pattern_info.get("display_name", field_name)
        
        return None
    
    def _fuzzy_match_mapping(self, field_name: str) -> Optional[str]:
        """模糊匹配映射
        
        Args:
            field_name: 字段名
            
        Returns:
            Optional[str]: 匹配的映射名，如果没有匹配返回None
        """
        best_match = None
        best_score = 0.0
        min_similarity = 0.7  # 最小相似度阈值
        
        for pattern_info in self.standard_mappings.values():
            if isinstance(pattern_info, dict):
                # 与display_name的相似度匹配
                display_name = pattern_info.get("display_name", "")
                similarity = self._calculate_similarity(field_name, display_name)
                
                if similarity > best_score and similarity >= min_similarity:
                    best_score = similarity
                    best_match = display_name
                
                # 与aliases的相似度匹配
                aliases = pattern_info.get("aliases", [])
                for alias in aliases:
                    similarity = self._calculate_similarity(field_name, alias)
                    if similarity > best_score and similarity >= min_similarity:
                        best_score = similarity
                        best_match = display_name
        
        return best_match
    
    def _user_rule_mapping(self, field_name: str, table_name: str) -> Optional[str]:
        """用户自定义规则映射
        
        Args:
            field_name: 字段名
            table_name: 表名
            
        Returns:
            Optional[str]: 匹配的映射名，如果没有匹配返回None
        """
        # 检查全局用户规则
        global_patterns = self.user_mapping_rules.get("global_patterns", {})
        if field_name in global_patterns:
            return global_patterns[field_name]
        
        # 检查表特定规则
        table_patterns = self.user_mapping_rules.get("table_specific_patterns", {})
        for pattern, mappings in table_patterns.items():
            # 支持通配符匹配
            if self._match_table_pattern(table_name, pattern):
                if field_name in mappings:
                    return mappings[field_name]
        
        return None
    
    def _match_table_pattern(self, table_name: str, pattern: str) -> bool:
        """匹配表名模式（支持通配符）
        
        Args:
            table_name: 表名
            pattern: 模式（支持*通配符）
            
        Returns:
            bool: 是否匹配
        """
        # 转换通配符为正则表达式
        regex_pattern = pattern.replace("*", ".*")
        return bool(re.match(regex_pattern, table_name, re.IGNORECASE))
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """计算两个字符串的相似度
        
        Args:
            str1: 字符串1
            str2: 字符串2
            
        Returns:
            float: 相似度分数 (0.0-1.0)
        """
        if not str1 or not str2:
            return 0.0
        
        return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()
    
    def _load_standard_mappings(self) -> Dict[str, Any]:
        """加载标准映射规则库
        
        Returns:
            Dict[str, Any]: 标准映射规则
        """
        return {
            # 基础信息字段
            "employee_id": {
                "aliases": ["工号", "员工号", "职工编号", "编号"],
                "display_name": "工号"
            },
            "employee_name": {
                "aliases": ["姓名", "员工姓名", "职工姓名", "名字"],
                "display_name": "姓名"
            },
            "department": {
                "aliases": ["部门", "所属部门", "单位", "科室", "部门名称"],
                "display_name": "部门"
            },
            "position": {
                "aliases": ["职位", "岗位", "职务"],
                "display_name": "职位"
            },
            
            # 工资字段
            "basic_salary": {
                "aliases": ["基本工资", "基础工资", "基本薪资"],
                "display_name": "基本工资"
            },
            "position_salary": {
                "aliases": ["岗位工资", "职位工资", "岗位薪资"],
                "display_name": "岗位工资"
            },
            "grade_salary": {
                "aliases": ["薪级工资", "等级工资", "薪级薪资"],
                "display_name": "薪级工资"
            },
            "performance": {
                "aliases": ["绩效", "奖金", "绩效工资", "绩效奖金"],
                "display_name": "绩效工资"
            },
            "allowance": {
                "aliases": ["津贴", "补贴", "津贴补贴"],
                "display_name": "津贴补贴"
            },
            "total_salary": {
                "aliases": ["应发工资", "应发合计", "工资合计", "总工资"],
                "display_name": "应发合计"
            },
            "social_insurance": {
                "aliases": ["五险一金", "社保", "公积金"],
                "display_name": "五险一金个人"
            }
        }
    
    def _load_user_mapping_rules(self) -> Dict[str, Any]:
        """加载用户自定义映射规则
        
        Returns:
            Dict[str, Any]: 用户映射规则
        """
        try:
            if Path(self.mapping_rules_path).exists():
                with open(self.mapping_rules_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载用户映射规则失败: {e}")
        
        # 返回默认用户规则
        return {
            "global_patterns": {
                "薪级工资": "薪级工资",
                "岗位工资": "岗位工资",
                "应发工资": "应发合计"
            },
            "table_specific_patterns": {
                "salary_data_*": {
                    "2025年岗位工资": "岗位工资",
                    "2025年薪级工资": "薪级工资",
                    "2025年奖励性绩效预发": "绩效工资"
                }
            }
        }
    
    def assess_mapping_quality(self, mapping: Dict[str, str]) -> Dict[str, Any]:
        """评估生成的映射质量
        
        Args:
            mapping: 字段映射配置
            
        Returns:
            Dict[str, Any]: 质量评估结果
        """
        total_fields = len(mapping)
        if total_fields == 0:
            return {"coverage_rate": 0, "overall_score": 0}
        
        # 计算覆盖率
        chinese_mapped = sum(1 for v in mapping.values() if self._contains_chinese(v))
        coverage_rate = chinese_mapped / total_fields
        
        return {
            "coverage_rate": round(coverage_rate, 3),
            "total_fields": total_fields,
            "chinese_mapped": chinese_mapped,
            "overall_score": round(coverage_rate, 3)
        }
    
    def _contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符
        
        Args:
            text: 文本
            
        Returns:
            bool: 是否包含中文
        """
        return bool(re.search(r'[\u4e00-\u9fff]', text))
    
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """获取映射生成统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.mapping_stats.copy()

    def create_initial_field_mapping(self, excel_headers: List[str], db_fields: List[str]) -> Dict[str, str]:
        """创建智能字段映射：数据库字段名 → Excel列名（按照解决方案设计）

        Args:
            excel_headers: Excel原始表头列表
            db_fields: 数据库字段名列表

        Returns:
            Dict[str, str]: 字段映射配置（数据库字段名 → 显示名）
        """
        mapping = {}

        # 预定义的智能匹配规则 - 增加人员类别精确匹配
        field_patterns = {
            'employee_id': ['工号', '职工编号', '员工号', '编号'],
            'employee_name': ['姓名', '职工姓名', '员工姓名', '名字'],
            'department': ['部门', '部门名称', '所属部门'],
            'employee_type': ['人员类别'],  # 精确匹配，避免与代码混淆
            'employee_type_code': ['人员类别代码'],  # 精确匹配
            'position': ['职位', '岗位', '职务'],
            'basic_salary': ['基本工资', '基础工资', '岗位工资'],
            'performance_bonus': ['绩效工资', '绩效奖金'],
            'total_salary': ['应发工资', '工资合计', '应发合计'],
            'allowance': ['津贴', '津贴补贴', '补贴'],
            'deduction': ['扣款', '代扣', '扣除'],
            'net_salary': ['实发工资', '实发合计', '到手工资'],
            'social_insurance': ['五险一金', '社保', '公积金'],
            'tax': ['个税', '个人所得税', '税费'],
        }

        # 1. 智能匹配
        for db_field in db_fields:
            if db_field in field_patterns:
                patterns = field_patterns[db_field]
                for excel_header in excel_headers:
                    if excel_header in patterns:
                        mapping[db_field] = excel_header
                        break

        # 2. 安全位置匹配（备用策略）- 避免人员类别字段的错误匹配
        critical_fields = {'employee_type', 'employee_type_code'}
        for i, db_field in enumerate(db_fields):
            if db_field not in mapping and i < len(excel_headers):
                excel_header = excel_headers[i]
                
                # 对关键字段进行安全性检查
                if db_field in critical_fields:
                    # 人员类别字段必须精确匹配，不允许位置匹配
                    if (db_field == 'employee_type' and excel_header == '人员类别') or \
                       (db_field == 'employee_type_code' and excel_header == '人员类别代码'):
                        mapping[db_field] = excel_header
                        self.logger.info(f"关键字段安全匹配: {db_field} -> {excel_header}")
                    else:
                        self.logger.warning(f"跳过关键字段的不安全位置匹配: {db_field} <-> {excel_header}")
                        continue
                else:
                    mapping[db_field] = excel_header

        # 3. 默认映射（最后兜底）
        for db_field in db_fields:
            if db_field not in mapping:
                # 将下划线字段名转为友好显示名
                display_name = db_field.replace('_', ' ').title()
                mapping[db_field] = display_name

        self.logger.info(f"智能字段映射生成完成: {len(mapping)} 个字段")
        self.logger.debug(f"映射详情: {mapping}")

        return mapping