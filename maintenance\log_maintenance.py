#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志维护脚本 - 定期清理和归档日志
可以通过Windows任务计划程序定期运行
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
import zipfile
import shutil

def archive_old_logs(log_dir="logs", days_to_keep=7):
    """归档旧日志文件"""
    
    log_path = Path(log_dir)
    if not log_path.exists():
        return
    
    archive_dir = log_path / "archive"
    archive_dir.mkdir(exist_ok=True)
    
    # 当前时间
    now = datetime.now()
    cutoff_time = now - timedelta(days=days_to_keep)
    
    # 遍历日志文件
    for log_file in log_path.glob("*.log*"):
        # 跳过当前活动日志
        if log_file.name == "salary_system.log":
            continue
        
        # 检查文件修改时间
        mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
        if mtime < cutoff_time:
            # 创建压缩文件名
            archive_name = f"{log_file.stem}_{mtime.strftime('%Y%m%d')}.zip"
            archive_path = archive_dir / archive_name
            
            # 压缩并归档
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                zf.write(log_file, log_file.name)
            
            # 删除原文件
            log_file.unlink()
            print(f"已归档: {log_file.name} -> {archive_name}")

def clean_large_logs(log_dir="logs", max_size_mb=10):
    """清理过大的日志文件"""
    
    log_path = Path(log_dir)
    if not log_path.exists():
        return
    
    for log_file in log_path.glob("*.log"):
        size_mb = log_file.stat().st_size / (1024 * 1024)
        if size_mb > max_size_mb:
            # 备份当前日志
            backup_name = f"{log_file.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            backup_path = log_path / "archive" / backup_name
            backup_path.parent.mkdir(exist_ok=True)
            
            shutil.copy2(log_file, backup_path)
            
            # 清空当前日志
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"Log rotated at {datetime.now()}\n")
            
            print(f"已轮转: {log_file.name} ({size_mb:.1f}MB)")

if __name__ == "__main__":
    print("开始日志维护...")
    archive_old_logs()
    clean_large_logs()
    print("日志维护完成")
