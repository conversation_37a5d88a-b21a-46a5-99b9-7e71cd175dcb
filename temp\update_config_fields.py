"""
更新配置文件中的字段规则，添加离休人员字段
"""
import json

# 要添加的离休人员字段
retired_fields = [
    "基本离休费", "结余津贴", "生活补贴", "住房补贴",
    "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴",
    "补发", "借支", "合计"
]

# 处理配置文件
config_file = "config/format_config.json"

# 读取配置
with open(config_file, 'r', encoding='utf-8') as f:
    config = json.load(f)

# 获取currency_fields
if 'field_type_rules' not in config:
    config['field_type_rules'] = {}

if 'currency_fields' not in config['field_type_rules']:
    config['field_type_rules']['currency_fields'] = []

currency_fields = config['field_type_rules']['currency_fields']

# 添加离休人员字段（避免重复）
for field in retired_fields:
    if field not in currency_fields:
        currency_fields.append(field)

print(f"添加了 {len(retired_fields)} 个离休人员字段到currency_fields")

# 保存配置
with open(config_file, 'w', encoding='utf-8') as f:
    json.dump(config, f, ensure_ascii=False, indent=2)

print(f"配置文件已更新: {config_file}")

# 验证
print("\n验证离休字段:")
for field in ["基本离休费", "离休补贴", "护理费", "增发一次性生活补贴"]:
    if field in currency_fields:
        print(f"  [v] {field}")
    else:
        print(f"  [x] {field}")