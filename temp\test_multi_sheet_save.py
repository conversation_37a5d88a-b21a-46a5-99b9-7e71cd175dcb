#!/usr/bin/env python3
"""
测试多工作表一次性保存功能

验证修改后的"另存配置"功能：
1. 能够一次性保存所有已配置的工作表
2. 生成正确的多工作表配置文件格式
3. 配置管理器能正确识别和列出多工作表配置
"""

import sys
import os
import pandas as pd
from pathlib import Path
from datetime import datetime
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager


def simulate_multi_sheet_save():
    """模拟多工作表配置保存"""
    logger.info("=== 测试多工作表一次性保存功能 ===")
    
    # 1. 模拟多个工作表的配置
    all_sheets_configs = {
        'A岗职工': {
            'field_types': {
                '序号': 'integer',
                '工号': 'employee_id_string',
                '姓名': 'name_string',
                '基本工资': 'salary_float',
                '津贴': 'salary_float',
                '应发工资': 'salary_float'
            },
            'field_mapping': {
                '序号': '序号',
                '工号': '工号',
                '姓名': '姓名',
                '基本工资': '基本工资',
                '津贴': '津贴',
                '应发工资': '应发工资'
            },
            'formatting_rules': {}
        },
        '退休人员工资表': {
            'field_types': {
                '序号': 'integer',
                '姓名': 'name_string',
                '基本退休费': 'salary_float',
                '津贴': 'salary_float',
                '应发工资': 'salary_float'
            },
            'field_mapping': {
                '序号': '序号',
                '姓名': '姓名',
                '基本退休费': '基本退休费',
                '津贴': '津贴',
                '应发工资': '应发工资'
            },
            'formatting_rules': {}
        },
        '离休人员表': {
            'field_types': {
                '人员代码': 'employee_id_string',
                '姓名': 'name_string',
                '基本离休费': 'salary_float',
                '护理费': 'salary_float'
            },
            'field_mapping': {
                '人员代码': '人员代码',
                '姓名': '姓名',
                '基本离休费': '基本离休费',
                '护理费': '护理费'
            },
            'formatting_rules': {}
        }
    }
    
    # 2. 模拟保存过程
    config_manager = ChangeDataConfigManager()
    user_config_dir = config_manager.config_dir / 'user_configs'
    
    # 统计总字段数
    total_fields = sum(len(config.get('field_mapping', {})) for config in all_sheets_configs.values())
    
    # 创建多表配置结构
    config_name = "测试多工作表配置"
    clean_name = config_name.replace(' ', '_').replace(':', '_')
    
    multi_sheet_config = {
        'name': clean_name,
        'description': f"多工作表配置 - {len(all_sheets_configs)} 个工作表，共 {total_fields} 个字段",
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat(),
        'version': '2.0',
        'type': 'multi_sheet_user_config',
        'sheet_count': len(all_sheets_configs),
        'sheets': {}
    }
    
    # 保存每个工作表的配置
    for sheet_name, sheet_config in all_sheets_configs.items():
        multi_sheet_config['sheets'][sheet_name] = {
            'sheet_name': sheet_name,
            'field_count': len(sheet_config.get('field_mapping', {})),
            'config': sheet_config
        }
    
    # 保存到文件
    config_file_path = user_config_dir / f"{clean_name}.json"
    with open(config_file_path, 'w', encoding='utf-8') as f:
        json.dump(multi_sheet_config, f, ensure_ascii=False, indent=2)
    
    logger.info(f"✅ 多工作表配置已保存到: {config_file_path}")
    
    # 3. 验证配置管理器能正确识别
    logger.info("\n--- 验证配置管理器识别 ---")
    
    user_configs = config_manager.list_user_configs()
    logger.info(f"找到 {len(user_configs)} 个用户配置:")
    
    multi_sheet_found = False
    for config in user_configs:
        logger.info(f"配置名称: {config['name']}")
        logger.info(f"类型: {config['type']}")
        logger.info(f"描述: {config['description']}")
        
        if config.get('type') == 'multi_sheet_user_config':
            multi_sheet_found = True
            logger.info(f"✅ 多工作表配置识别成功!")
            logger.info(f"  - 工作表数量: {config.get('sheet_count', 0)}")
            logger.info(f"  - 包含工作表: {config.get('sheets', [])}")
            logger.info(f"  - 总字段数: {config.get('total_field_count', 0)}")
        logger.info("")
    
    if not multi_sheet_found:
        logger.error("❌ 配置管理器未能正确识别多工作表配置")
        return False
    
    # 4. 验证文件内容
    logger.info("--- 验证保存的文件内容 ---")
    
    with open(config_file_path, 'r', encoding='utf-8') as f:
        saved_config = json.load(f)
    
    # 验证基本信息
    if saved_config.get('type') != 'multi_sheet_user_config':
        logger.error("❌ 配置类型不正确")
        return False
    
    if saved_config.get('sheet_count') != 3:
        logger.error("❌ 工作表数量不正确")
        return False
    
    # 验证每个工作表的配置
    saved_sheets = saved_config.get('sheets', {})
    for sheet_name in all_sheets_configs.keys():
        if sheet_name not in saved_sheets:
            logger.error(f"❌ 工作表 '{sheet_name}' 未保存")
            return False
        
        saved_sheet_config = saved_sheets[sheet_name]['config']
        original_field_mapping = all_sheets_configs[sheet_name]['field_mapping']
        saved_field_mapping = saved_sheet_config.get('field_mapping', {})
        
        if original_field_mapping != saved_field_mapping:
            logger.error(f"❌ 工作表 '{sheet_name}' 字段映射不匹配")
            return False
        
        logger.info(f"✅ 工作表 '{sheet_name}' 配置验证通过")
    
    # 5. 显示保存的文件统计
    logger.info("\n--- 保存文件统计 ---")
    file_size = config_file_path.stat().st_size
    logger.info(f"文件路径: {config_file_path}")
    logger.info(f"文件大小: {file_size} 字节")
    logger.info(f"包含工作表: {list(saved_sheets.keys())}")
    logger.info(f"总字段数: {sum(sheet['field_count'] for sheet in saved_sheets.values())}")
    
    logger.info("\n🎉 多工作表一次性保存功能测试成功！")
    return True


def show_before_after_comparison():
    """显示修改前后的对比"""
    logger.info("\n=== 修改前后功能对比 ===")
    
    print("\n📋 修改前（旧版本）:")
    print("❌ '另存配置'只保存当前选中的单个工作表")
    print("❌ 需要逐个工作表分别保存")
    print("❌ 用户体验差，操作繁琐")
    
    print("\n✅ 修改后（新版本）:")
    print("✅ '另存配置'一次性保存所有已配置的工作表")
    print("✅ 用户只需点击一次按钮")
    print("✅ 生成多工作表配置文件，便于整体管理")
    print("✅ 保持向后兼容，支持旧的单工作表配置")
    
    print("\n🎯 用户使用流程:")
    print("1. 配置第1个工作表（如'A岗职工'）")
    print("2. 切换到第2个工作表（如'退休人员工资表'）")
    print("3. 配置第2个工作表")
    print("4. 切换到第3个工作表（如'离休人员表'）")  
    print("5. 配置第3个工作表")
    print("6. 点击'另存配置'按钮 → 所有3个工作表的配置一次性保存！")


def main():
    """主函数"""
    try:
        # 运行测试
        success = simulate_multi_sheet_save()
        
        # 显示对比
        show_before_after_comparison()
        
        if success:
            print(f"\n>>> 测试结果：✅ 多工作表一次性保存功能正常")
            print(f">>> 用户不再需要逐个工作表分别保存！")
            print(f">>> 一次点击，保存全部配置！")
            return 0
        else:
            print(f"\n>>> 测试结果：❌ 多工作表保存功能存在问题")
            return 1
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())