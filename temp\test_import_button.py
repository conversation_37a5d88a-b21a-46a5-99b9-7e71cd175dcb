#!/usr/bin/env python3
"""
测试导入数据按钮功能
验证重构后的导入功能是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_import_functionality():
    """测试导入功能"""
    print("🔧 测试导入数据按钮功能...")
    
    try:
        # 1. 测试核心组件导入
        print("1. 测试核心组件导入...")
        from src.gui.unified_data_import_window import UnifiedDataImportWindow
        print("   ✅ UnifiedDataImportWindow 导入成功")
        
        from src.modules.system_config.config_manager import ConfigManager
        print("   ✅ ConfigManager 导入成功")
        
        # 2. 测试PyQt5
        print("2. 测试PyQt5...")
        from PyQt5.QtWidgets import QApplication
        print("   ✅ PyQt5 导入成功")
        
        # 3. 创建应用程序实例
        print("3. 创建应用程序实例...")
        app = QApplication([])
        print("   ✅ 应用程序实例创建成功")
        
        # 4. 测试窗口创建
        print("4. 测试窗口创建...")
        try:
            window = UnifiedDataImportWindow()
            print("   ✅ UnifiedDataImportWindow 创建成功")
            
            # 检查窗口的基本属性
            if hasattr(window, 'config_manager'):
                print("   ✅ 配置管理器已初始化")
            else:
                print("   ❌ 配置管理器未初始化")
                
            if hasattr(window, 'import_manager'):
                print("   ✅ 导入管理器已初始化")
            else:
                print("   ❌ 导入管理器未初始化")
                
        except Exception as e:
            print(f"   ❌ 窗口创建失败: {e}")
            return False
        
        # 5. 测试主窗口导入功能
        print("5. 测试主窗口导入功能...")
        try:
            from src.gui.prototype.prototype_main_window import PrototypeMainWindow
            print("   ✅ PrototypeMainWindow 导入成功")
            
            # 检查主窗口是否有导入方法
            main_window = PrototypeMainWindow()
            if hasattr(main_window, '_show_unified_import_dialog'):
                print("   ✅ _show_unified_import_dialog 方法存在")
            else:
                print("   ❌ _show_unified_import_dialog 方法不存在")
                
        except Exception as e:
            print(f"   ❌ 主窗口测试失败: {e}")
            return False
        
        print("\n🎉 所有测试通过！导入数据按钮功能应该正常工作。")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_dialog_creation():
    """测试导入对话框创建"""
    print("\n🔧 测试导入对话框创建...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.unified_data_import_window import UnifiedDataImportWindow
        
        app = QApplication([])
        
        # 创建对话框
        dialog = UnifiedDataImportWindow()
        
        # 检查对话框的基本功能
        print("   ✅ 对话框创建成功")
        print(f"   📏 窗口大小: {dialog.size().width()} x {dialog.size().height()}")
        print(f"   📝 窗口标题: {dialog.windowTitle()}")
        
        # 检查核心组件
        components = [
            ('config_manager', '配置管理器'),
            ('import_manager', '导入管理器'),
            ('user_preferences', '用户偏好'),
        ]
        
        for attr, name in components:
            if hasattr(dialog, attr):
                print(f"   ✅ {name} 已初始化")
            else:
                print(f"   ❌ {name} 未初始化")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 对话框创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 导入数据按钮功能测试")
    print("=" * 60)
    
    success1 = test_import_functionality()
    success2 = test_import_dialog_creation()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过！重构成功，导入数据按钮应该正常工作。")
        exit_code = 0
    else:
        print("❌ 部分测试失败，需要进一步修复。")
        exit_code = 1
    
    print("=" * 60)
    sys.exit(exit_code)
