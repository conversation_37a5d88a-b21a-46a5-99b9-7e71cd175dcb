"""
月度工资异动处理系统 - 数据导入集成组件

本模块实现Phase 3的数据导入集成功能，将现代化表格编辑器与数据导入模块深度集成。

主要功能:
1. 与data_import模块深度集成
2. 实时数据刷新
3. 导入状态显示
4. 导入进度监控
5. 错误处理和提示
6. 数据预览和验证
"""

import os
import sys
from typing import List, Dict, Any, Optional, Callable
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QProgressBar, QTextEdit, QSplitter, QGroupBox, QFrame,
    QFileDialog, QMessageBox, QTabWidget, QTableWidget,
    QComboBox, QSpinBox, QCheckBox, QLineEdit, QApplication
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QColor, QPalette, QMovie

from src.utils.log_config import setup_logger
from src.core.application_service import ApplicationService, ServiceResult
from src.modules.data_import import ExcelImporter, DataValidator
from src.gui.modern_table_editor import ModernTableEditor
from src.gui.toast_system import ToastManager


class DataImportWorker(QThread):
    """数据导入工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)          # 进度更新信号
    status_updated = pyqtSignal(str)            # 状态更新信号
    data_imported = pyqtSignal(list, list)      # 数据导入完成信号 (data, headers)
    error_occurred = pyqtSignal(str)            # 错误发生信号
    import_completed = pyqtSignal(bool)         # 导入完成信号
    
    def __init__(self, file_path: str, sheet_name: str = None, 
                 start_row: int = 0, max_rows: int = None):
        """初始化导入工作线程"""
        super().__init__()
        self.file_path = file_path
        self.sheet_name = sheet_name
        self.start_row = start_row
        self.max_rows = max_rows
        base_logger = setup_logger(__name__)
        # 绑定组件上下文（界面组件名）
        from src.utils.logging_utils import bind_context, log_sample, redact
        self.logger = bind_context(base_logger, component="DataImportIntegration")
        
        # 创建导入器
        self.excel_importer = ExcelImporter()
        self.data_validator = DataValidator()
        
    def run(self):
        """执行数据导入"""
        try:
            self.status_updated.emit("正在验证文件...")
            self.progress_updated.emit(10)
            
            # 验证文件
            file_info = self.excel_importer.validate_file(self.file_path)
            if not file_info['is_valid']:
                self.error_occurred.emit(f"文件验证失败: {file_info['error_message']}")
                return
            
            self.status_updated.emit("正在读取数据...")
            self.progress_updated.emit(30)
            
            # 导入数据
            df = self.excel_importer.import_data(
                file_path=self.file_path,
                sheet_name=self.sheet_name,
                start_row=self.start_row,
                max_rows=self.max_rows,
                progress_callback=self._update_progress
            )
            
            self.status_updated.emit("正在验证数据...")
            self.progress_updated.emit(80)
            
            # 数据验证
            validation_result = self.data_validator.validate_dataframe(df)
            if validation_result['total_errors'] > 0:
                self.status_updated.emit(f"数据验证警告: {validation_result['total_errors']}个错误")
            else:
                self.status_updated.emit("数据验证通过")
            
            # 转换为列表格式
            headers = df.columns.tolist()
            data = df.to_dict('records')
            
            self.status_updated.emit("导入完成")
            self.progress_updated.emit(100)
            
            # 发送导入完成信号
            self.data_imported.emit(data, headers)
            self.import_completed.emit(True)
            
        except Exception as e:
            self.logger.error(f"数据导入失败: {e}")
            self.error_occurred.emit(str(e))
            self.import_completed.emit(False)
    
    def _update_progress(self, progress: int):
        """更新进度回调"""
        # 调整进度范围 (30-80)
        adjusted_progress = 30 + int(progress * 0.5)
        self.progress_updated.emit(adjusted_progress)


class ImportStatusWidget(QWidget):
    """导入状态显示组件"""
    
    def __init__(self, parent=None):
        """初始化状态显示组件"""
        super().__init__(parent)
        self._init_ui()
        self._init_animations()
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 状态标题
        self.status_label = QLabel("导入状态")
        status_font = QFont()
        status_font.setPointSize(12)
        status_font.setBold(True)
        self.status_label.setFont(status_font)
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: #f8f9fa;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                           stop:0 #0078d4, stop:1 #106ebe);
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 状态描述
        self.status_description = QLabel("就绪")
        self.status_description.setStyleSheet("color: #6c757d; font-size: 13px;")
        layout.addWidget(self.status_description)
        
        # 文件信息
        file_info_group = QGroupBox("文件信息")
        file_info_layout = QVBoxLayout(file_info_group)
        
        self.file_name_label = QLabel("文件名: 未选择")
        self.file_size_label = QLabel("文件大小: -")
        self.sheet_count_label = QLabel("工作表数: -")
        self.data_rows_label = QLabel("数据行数: -")
        
        for label in [self.file_name_label, self.file_size_label, 
                     self.sheet_count_label, self.data_rows_label]:
            label.setStyleSheet("font-size: 12px; color: #495057;")
            file_info_layout.addWidget(label)
        
        layout.addWidget(file_info_group)
        
        # 导入统计
        stats_group = QGroupBox("导入统计")
        stats_layout = QVBoxLayout(stats_group)
        
        self.imported_rows_label = QLabel("已导入行数: 0")
        self.validation_errors_label = QLabel("验证错误: 0")
        self.import_time_label = QLabel("导入时间: -")
        
        for label in [self.imported_rows_label, self.validation_errors_label, 
                     self.import_time_label]:
            label.setStyleSheet("font-size: 12px; color: #495057;")
            stats_layout.addWidget(label)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
    
    def _init_animations(self):
        """初始化动画效果"""
        self.progress_animation = QPropertyAnimation(self.progress_bar, b"value")
        self.progress_animation.setDuration(300)
        self.progress_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def update_progress(self, value: int):
        """更新进度"""
        self.progress_animation.setStartValue(self.progress_bar.value())
        self.progress_animation.setEndValue(value)
        self.progress_animation.start()
    
    def update_status(self, status: str):
        """更新状态描述"""
        self.status_description.setText(status)
    
    def update_file_info(self, file_info: Dict[str, Any]):
        """更新文件信息"""
        self.file_name_label.setText(f"文件名: {file_info.get('name', '未知')}")
        self.file_size_label.setText(f"文件大小: {file_info.get('size_mb', 0):.2f} MB")
        self.sheet_count_label.setText(f"工作表数: {file_info.get('sheet_count', 0)}")
    
    def update_import_stats(self, stats: Dict[str, Any]):
        """更新导入统计"""
        self.imported_rows_label.setText(f"已导入行数: {stats.get('rows', 0)}")
        self.validation_errors_label.setText(f"验证错误: {stats.get('errors', 0)}")
        self.import_time_label.setText(f"导入时间: {stats.get('time', '-')}")
    
    def reset(self):
        """重置状态"""
        self.progress_bar.setValue(0)
        self.status_description.setText("就绪")
        self.file_name_label.setText("文件名: 未选择")
        self.file_size_label.setText("文件大小: -")
        self.sheet_count_label.setText("工作表数: -")
        self.data_rows_label.setText("数据行数: -")
        self.imported_rows_label.setText("已导入行数: 0")
        self.validation_errors_label.setText("验证错误: 0")
        self.import_time_label.setText("导入时间: -")


class DataImportIntegration(QWidget):
    """
    数据导入集成主组件
    
    整合现代化表格编辑器与数据导入模块，提供完整的数据导入体验。
    """
    
    # 信号定义
    data_imported = pyqtSignal(list, list)      # 数据导入完成信号
    import_started = pyqtSignal(str)            # 导入开始信号
    import_completed = pyqtSignal(bool)         # 导入完成信号
    
    def __init__(self, parent=None):
        """初始化数据导入集成组件"""
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 核心组件
        self.table_editor = None
        self.status_widget = None
        self.toast_system = ToastManager()
        self.toast_system.set_parent(self)
        
        # 服务组件
        self.app_service = ApplicationService()
        self.excel_importer = ExcelImporter()
        
        # 工作线程
        self.import_worker = None
        
        # 状态管理
        self.current_file_path = ""
        self.current_sheet_names = []
        self.import_in_progress = False
        
        # 初始化服务
        self._init_services()
        
        # 初始化UI
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
        
        self.logger.info("数据导入集成组件初始化完成")
    
    def _init_services(self):
        """初始化服务"""
        try:
            result = self.app_service.initialize()
            if not result.success:
                self.logger.error(f"应用服务初始化失败: {result.message}")
                self.toast_system.show_error(f"服务初始化失败: {result.message}")
        except Exception as e:
            self.logger.error(f"服务初始化异常: {e}")
            self.toast_system.show_error(f"服务初始化异常: {e}")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：表格编辑器
        self._create_table_area(splitter)
        
        # 右侧：导入控制面板
        self._create_import_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([800, 300])
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 0)
        
        layout.addWidget(splitter)
    
    def _create_table_area(self, parent):
        """创建表格区域"""
        table_frame = QFrame()
        table_frame.setFrameStyle(QFrame.StyledPanel)
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(8, 8, 8, 8)
        
        # 表格标题
        table_title = QLabel("数据表格")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        table_title.setFont(title_font)
        table_layout.addWidget(table_title)
        
        # 现代化表格编辑器
        self.table_editor = ModernTableEditor()
        table_layout.addWidget(self.table_editor)
        
        parent.addWidget(table_frame)
    
    def _create_import_panel(self, parent):
        """创建导入控制面板"""
        panel_frame = QFrame()
        panel_frame.setFrameStyle(QFrame.StyledPanel)
        panel_frame.setMaximumWidth(320)
        panel_layout = QVBoxLayout(panel_frame)
        panel_layout.setContentsMargins(8, 8, 8, 8)
        panel_layout.setSpacing(12)
        
        # 面板标题
        panel_title = QLabel("数据导入")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        panel_title.setFont(title_font)
        panel_layout.addWidget(panel_title)
        
        # 文件选择区域
        self._create_file_selection_area(panel_layout)
        
        # 导入选项区域
        self._create_import_options_area(panel_layout)
        
        # 导入控制按钮
        self._create_import_controls(panel_layout)
        
        # 状态显示组件
        self.status_widget = ImportStatusWidget()
        panel_layout.addWidget(self.status_widget)
        
        parent.addWidget(panel_frame)
    
    def _create_file_selection_area(self, parent_layout):
        """创建文件选择区域"""
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
        
        # 文件路径显示
        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet("""
            QLabel {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                background-color: #f8f9fa;
                color: #6c757d;
            }
        """)
        file_layout.addWidget(self.file_path_label)
        
        # 选择文件按钮
        self.select_file_btn = QPushButton("选择Excel文件")
        self.select_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        file_layout.addWidget(self.select_file_btn)
        
        parent_layout.addWidget(file_group)
    
    def _create_import_options_area(self, parent_layout):
        """创建导入选项区域"""
        options_group = QGroupBox("导入选项")
        options_layout = QVBoxLayout(options_group)
        
        # 工作表选择
        sheet_layout = QHBoxLayout()
        sheet_layout.addWidget(QLabel("工作表:"))
        self.sheet_combo = QComboBox()
        self.sheet_combo.setEnabled(False)
        sheet_layout.addWidget(self.sheet_combo)
        options_layout.addLayout(sheet_layout)
        
        # 起始行设置
        row_layout = QHBoxLayout()
        row_layout.addWidget(QLabel("起始行:"))
        self.start_row_spin = QSpinBox()
        self.start_row_spin.setMinimum(0)
        self.start_row_spin.setMaximum(9999)
        self.start_row_spin.setValue(0)
        row_layout.addWidget(self.start_row_spin)
        options_layout.addLayout(row_layout)
        
        # 最大行数设置
        max_row_layout = QHBoxLayout()
        max_row_layout.addWidget(QLabel("最大行数:"))
        self.max_rows_spin = QSpinBox()
        self.max_rows_spin.setMinimum(0)
        self.max_rows_spin.setMaximum(999999)
        self.max_rows_spin.setValue(0)  # 0表示不限制
        self.max_rows_spin.setSpecialValueText("不限制")
        max_row_layout.addWidget(self.max_rows_spin)
        options_layout.addLayout(max_row_layout)
        
        # 数据验证选项
        self.validate_data_check = QCheckBox("导入时验证数据")
        self.validate_data_check.setChecked(True)
        options_layout.addWidget(self.validate_data_check)
        
        parent_layout.addWidget(options_group)
    
    def _create_import_controls(self, parent_layout):
        """创建导入控制按钮"""
        controls_layout = QHBoxLayout()
        
        # 预览按钮
        self.preview_btn = QPushButton("预览数据")
        self.preview_btn.setEnabled(False)
        self.preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
            }
            QPushButton:enabled:hover {
                background-color: #5a6268;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)
        controls_layout.addWidget(self.preview_btn)
        
        # 导入按钮
        self.import_btn = QPushButton("开始导入")
        self.import_btn.setEnabled(False)
        self.import_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:enabled:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
        """)
        controls_layout.addWidget(self.import_btn)
        
        parent_layout.addLayout(controls_layout)
    
    def _connect_signals(self):
        """连接信号"""
        # 文件选择
        self.select_file_btn.clicked.connect(self._select_file)
        
        # 工作表变化
        self.sheet_combo.currentTextChanged.connect(self._on_sheet_changed)
        
        # 控制按钮
        self.preview_btn.clicked.connect(self._preview_data)
        self.import_btn.clicked.connect(self._start_import)
        
        # 表格编辑器信号
        if self.table_editor:
            self.table_editor.import_requested.connect(self._select_file)
            self.table_editor.data_changed.connect(self._on_table_data_changed)
    
    def _select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件",
            "", "Excel文件 (*.xlsx *.xls)"
        )
        
        if file_path:
            self._load_file_info(file_path)
    
    def _load_file_info(self, file_path: str):
        """加载文件信息"""
        try:
            # 验证文件
            file_info = self.excel_importer.validate_file(file_path)
            
            # 获取工作表名称
            sheet_names = self.excel_importer.get_sheet_names(file_path)
            
            # 更新UI
            self.current_file_path = file_path
            self.current_sheet_names = sheet_names
            
            self.file_path_label.setText(os.path.basename(file_path))
            self.file_path_label.setToolTip(file_path)
            
            # 更新工作表下拉框
            self.sheet_combo.clear()
            self.sheet_combo.addItems(sheet_names)
            self.sheet_combo.setEnabled(True)
            
            # 启用按钮
            self.preview_btn.setEnabled(True)
            self.import_btn.setEnabled(True)
            
            # 更新状态组件
            file_info['sheet_count'] = len(sheet_names)
            self.status_widget.update_file_info(file_info)
            
            self.toast_system.show_success(f"文件加载成功: {os.path.basename(file_path)}")
            self.logger.info(f"文件信息加载成功: {file_path}")
            
        except Exception as e:
            self.logger.error(f"加载文件信息失败: {e}")
            self.toast_system.show_error(f"文件加载失败: {e}")
    
    def _on_sheet_changed(self, sheet_name: str):
        """工作表变化处理"""
        if sheet_name and self.current_file_path:
            self.logger.debug(f"工作表切换到: {sheet_name}")
    
    def _preview_data(self):
        """预览数据"""
        if not self.current_file_path:
            self.toast_system.show_warning("请先选择文件")
            return
        
        try:
            sheet_name = self.sheet_combo.currentText()
            start_row = self.start_row_spin.value()  # 获取起始行设置
            
            # 预览前10行数据，考虑起始行参数
            preview_data = self.excel_importer.preview_data(
                self.current_file_path,
                sheet_name=sheet_name,
                max_rows=10
            )
            
            # 如果设置了起始行，需要相应调整预览数据
            headers = preview_data['columns']
            data = preview_data['sample_data']
            
            # 如果起始行大于0，则模拟跳过前面的行
            if start_row > 0 and len(data) > 0:
                # 重新读取考虑起始行的数据进行预览
                import pandas as pd
                try:
                    df_preview = pd.read_excel(
                        self.current_file_path,
                        sheet_name=sheet_name,
                        skiprows=start_row,
                        nrows=10
                    )
                    headers = list(df_preview.columns)
                    data = df_preview.to_dict('records')
                except Exception as read_error:
                    self.logger.warning(f"重新读取预览数据失败，使用原始预览: {read_error}")
            
            self.table_editor.set_data(data, headers)
            self.toast_system.show_info(f"预览数据: {len(data)} 行 (起始行: {start_row + 1})")
            
        except Exception as e:
            self.logger.error(f"预览数据失败: {e}")
            self.toast_system.show_error(f"预览失败: {e}")
    
    def _start_import(self):
        """开始导入"""
        if not self.current_file_path:
            self.toast_system.show_warning("请先选择文件")
            return
        
        if self.import_in_progress:
            self.toast_system.show_warning("导入正在进行中")
            return
        
        try:
            # 获取导入参数
            sheet_name = self.sheet_combo.currentText()
            start_row = self.start_row_spin.value()
            max_rows = self.max_rows_spin.value() if self.max_rows_spin.value() > 0 else None
            
            # 重置状态
            self.status_widget.reset()
            self.import_in_progress = True
            
            # 禁用控制按钮
            self.import_btn.setEnabled(False)
            self.preview_btn.setEnabled(False)
            
            # 创建工作线程
            self.import_worker = DataImportWorker(
                self.current_file_path, sheet_name, start_row, max_rows
            )
            
            # 连接工作线程信号
            self.import_worker.progress_updated.connect(self.status_widget.update_progress)
            self.import_worker.status_updated.connect(self.status_widget.update_status)
            self.import_worker.data_imported.connect(self._on_data_imported)
            self.import_worker.error_occurred.connect(self._on_import_error)
            self.import_worker.import_completed.connect(self._on_import_completed)
            
            # 启动导入
            self.import_worker.start()
            self.import_started.emit(self.current_file_path)
            
            self.toast_system.show_info("开始导入数据...")
            self.logger.info(f"开始导入数据: {self.current_file_path}")
            
        except Exception as e:
            self.logger.error(f"启动导入失败: {e}")
            self.toast_system.show_error(f"导入启动失败: {e}")
            self._reset_import_state()
    
    def _on_data_imported(self, data: List[Dict[str, Any]], headers: List[str]):
        """数据导入完成处理"""
        try:
            # 更新表格编辑器
            self.table_editor.set_data(data, headers)
            
            # 更新统计信息
            stats = {
                'rows': len(data),
                'errors': 0,  # TODO: 从验证结果获取
                'time': '完成'
            }
            self.status_widget.update_import_stats(stats)
            # 高频完成提示采样输出
            try:
                from src.utils.logging_utils import log_sample
                if log_sample("import-finish", 5):
                    self.logger.debug(f"导入完成(采样): 行数={len(data)}")
            except Exception:
                pass
            
            # 发送信号
            self.data_imported.emit(data, headers)
            
            self.toast_system.show_success(f"数据导入成功: {len(data)} 行")
            self.logger.info(f"数据导入完成: {len(data)} 行")
            
        except Exception as e:
            self.logger.error(f"处理导入数据失败: {e}")
            self.toast_system.show_error(f"数据处理失败: {e}")
    
    def _on_import_error(self, error_message: str):
        """导入错误处理"""
        self.toast_system.show_error(f"导入失败: {error_message}")
        self.logger.error(f"导入错误: {error_message}")
    
    def _on_import_completed(self, success: bool):
        """导入完成处理"""
        self._reset_import_state()
        self.import_completed.emit(success)
        
        if success:
            self.status_widget.update_status("导入完成")
        else:
            self.status_widget.update_status("导入失败")
    
    def _on_table_data_changed(self, change_info: Dict[str, Any]):
        """表格数据变化处理"""
        self.logger.debug(f"表格数据变化: {change_info}")
        # TODO: 实现数据变化的实时同步
    
    def _reset_import_state(self):
        """重置导入状态"""
        self.import_in_progress = False
        self.import_btn.setEnabled(True)
        self.preview_btn.setEnabled(True)
        
        if self.import_worker:
            self.import_worker.quit()
            self.import_worker.wait()
            self.import_worker = None
    
    def get_current_data(self) -> List[Dict[str, Any]]:
        """获取当前表格数据"""
        if self.table_editor:
            return self.table_editor.get_data()
        return []
    
    def refresh_data(self):
        """刷新数据"""
        if self.current_file_path and not self.import_in_progress:
            self._start_import()
    
    def clear_data(self):
        """清空数据"""
        if self.table_editor:
            self.table_editor.set_data([], [])
        self.status_widget.reset()


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建数据导入集成组件
    integration = DataImportIntegration()
    integration.setWindowTitle("数据导入集成测试")
    integration.resize(1200, 800)
    integration.show()
    
    sys.exit(app.exec_()) 