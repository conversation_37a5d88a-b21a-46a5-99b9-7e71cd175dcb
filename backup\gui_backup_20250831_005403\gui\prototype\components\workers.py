"""
工作线程组件 - 从主窗口文件中分离
"""

import pandas as pd
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QRunnable, QSize
from loguru import logger
from src.utils.log_config import setup_logger


class WorkerSignals(QObject):
    """定义Worker线程可以发出的信号"""
    finished = pyqtSignal()
    error = pyqtSignal(tuple)
    result = pyqtSignal(object)
    window_resized = pyqtSignal(QSize)


class Worker(QRunnable):
    """通用工作线程，用于执行耗时操作而不阻塞UI线程"""
    def __init__(self, fn, *args, **kwargs):
        super(Worker, self).__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = WorkerSignals()

    @pyqtSlot()
    def run(self):
        """运行工作线程中的方法"""
        try:
            result = self.fn(*self.args, **self.kwargs)
            self.signals.result.emit(result)
        except Exception as e:
            logger.error(f"Worker线程执行失败: {e}", exc_info=True)
            self.signals.error.emit((type(e), e, e.__traceback__))
        finally:
            self.signals.finished.emit()


class PaginationWorker(QRunnable):
    """分页数据加载工作线程"""
    def __init__(self, table_manager, table_name, page=1, page_size=50, 
                 apply_mapping_func=None, sort_state_manager=None):
        super(PaginationWorker, self).__init__()
        self.table_manager = table_manager
        self.table_name = table_name
        self.page = page
        self.page_size = page_size
        self.apply_mapping_func = apply_mapping_func
        self.sort_state_manager = sort_state_manager
        self.signals = WorkerSignals()
        self.logger = setup_logger(f"PaginationWorker.{table_name}")

    @pyqtSlot()
    def run(self):
        """加载分页数据 - 增强版本，保证数据一致性"""
        try:
            self.logger.info(f"开始加载表 {self.table_name} 第{self.page}页数据，每页{self.page_size}条")

            # 验证表管理器可用性
            if not self.table_manager:
                raise Exception("表管理器不可用")

            # 验证表是否存在
            if not hasattr(self.table_manager, 'table_exists') or not self.table_manager.table_exists(self.table_name):
                raise Exception(f"表 {self.table_name} 不存在")

            # 获取排序状态并使用支持排序的查询方法
            sort_columns = []
            if self.sort_state_manager:
                try:
                    sort_state = self.sort_state_manager.restore_sort_state(self.table_name)
                    if sort_state and hasattr(sort_state, 'sort_columns') and sort_state.sort_columns:
                        sort_columns = sort_state.sort_columns
                        self.logger.info(f"应用排序状态: {len(sort_columns)} 列")
                except Exception as e:
                    self.logger.warning(f"获取排序状态失败: {e}")

            # 使用支持排序的分页查询方法，添加安全的返回值处理
            df = None
            total_records = 0  # 预先定义默认值，避免变量未定义错误

            try:
                if hasattr(self.table_manager, 'get_dataframe_paginated_with_sort'):
                    result = self.table_manager.get_dataframe_paginated_with_sort(
                        self.table_name, self.page, self.page_size, sort_columns
                    )
                    # 安全的返回值解包
                    if isinstance(result, tuple) and len(result) == 2:
                        df, total_records = result
                    elif hasattr(result, '__iter__') and not isinstance(result, str):
                        # 处理其他可迭代类型
                        result_list = list(result)
                        if len(result_list) >= 2:
                            df, total_records = result_list[0], result_list[1]
                        elif len(result_list) == 1:
                            df = result_list[0]
                            total_records = len(df) if hasattr(df, '__len__') else 0
                    else:
                        # 单个返回值，假设是DataFrame
                        df = result
                        total_records = len(df) if hasattr(df, '__len__') else 0
                    self.logger.info(f"使用排序查询: {len(sort_columns)} 个排序列, 总记录数: {total_records}")
                else:
                    # 普通分页查询，同样添加安全处理
                    result = self.table_manager.get_dataframe_paginated(
                        self.table_name, self.page, self.page_size
                    )
                    # 安全的返回值解包
                    if isinstance(result, tuple) and len(result) == 2:
                        df, total_records = result
                    elif hasattr(result, '__iter__') and not isinstance(result, str):
                        # 处理其他可迭代类型
                        result_list = list(result)
                        if len(result_list) >= 2:
                            df, total_records = result_list[0], result_list[1]
                        elif len(result_list) == 1:
                            df = result_list[0]
                            total_records = len(df) if hasattr(df, '__len__') else 0
                    else:
                        # 单个返回值，假设是DataFrame
                        df = result
                        total_records = len(df) if hasattr(df, '__len__') else 0
                    self.logger.info(f"使用普通分页查询（不支持排序）, 总记录数: {total_records}")
            except Exception as query_error:
                self.logger.error(f"分页查询失败: {query_error}")
                # 查询失败时的降级处理
                df = pd.DataFrame()  # 空DataFrame
                total_records = 0
                raise Exception(f"分页查询失败: {query_error}")

            # 数据验证
            if df is None:
                self.logger.error(f"表 {self.table_name} 返回的DataFrame为None")
                raise Exception("数据加载失败：返回的数据为空")

            if df.empty:
                self.logger.warning(f"表 {self.table_name} 第{self.page}页数据为空")
                result = {
                    'success': True,  # 数据为空也是正常状态
                    'data': df,
                    'total_records': total_records,
                    'current_page': self.page,
                    'page_size': self.page_size,
                    'table_name': self.table_name,
                    'loaded_fields': len(df.columns),
                    'is_empty': True
                }
                self.signals.result.emit(result)
                return

            # 记录原始数据信息
            original_columns = list(df.columns)
            original_count = len(df)
            self.logger.info(f"原始数据: {original_count}行, {len(original_columns)}列")
            self.logger.debug(f"原始列名: {original_columns[:10]}...")  # 只显示前10个

            # 应用字段映射（如果有）
            mapped_df = df
            if self.apply_mapping_func:
                try:
                    self.logger.info("开始应用字段映射")
                    mapped_df = self.apply_mapping_func(df.copy(), self.table_name)

                    # 验证映射后的数据完整性
                    if mapped_df is None or mapped_df.empty:
                        self.logger.debug("字段映射后数据为空，使用原始数据")
                        self.logger.error("PaginationWorker - 字段映射后数据为空，使用原始数据")
                        mapped_df = df
                    else:
                        self.logger.debug(f"字段映射成功: {len(df.columns)} -> {len(mapped_df.columns)}列")
                        self.logger.info(f"PaginationWorker - 字段映射成功: {len(df.columns)} -> {len(mapped_df.columns)}列")

                    # 验证映射结果
                    if mapped_df is None:
                        self.logger.warning("字段映射返回空结果，使用原始数据")
                        mapped_df = df
                    elif mapped_df.empty and not df.empty:
                        self.logger.warning("字段映射导致数据为空，使用原始数据")
                        mapped_df = df
                    elif len(mapped_df) != len(df):
                        self.logger.warning(f"字段映射导致数据行数变化: {len(df)} -> {len(mapped_df)}，使用原始数据")
                        mapped_df = df
                    else:
                        mapped_columns = list(mapped_df.columns)
                        self.logger.info(f"字段映射成功: {len(mapped_columns)}列")
                        self.logger.debug(f"映射后列名: {mapped_columns[:10]}...")  # 只显示前10个

                except Exception as e:
                    self.logger.error(f"字段映射失败: {e}，使用原始数据")
                    mapped_df = df

            # 数据完整性检查
            empty_rows = mapped_df.isnull().all(axis=1).sum()
            if empty_rows > 0:
                self.logger.warning(f"发现 {empty_rows} 行完全为空的数据")

            # 检查关键字段是否有数据
            key_fields = ['employee_name', 'employee_id', '姓名', '人员代码', '工号']
            has_key_data = False
            for field in key_fields:
                if field in mapped_df.columns and not mapped_df[field].isnull().all():
                    has_key_data = True
                    break

            if not has_key_data:
                self.logger.warning(f"页面数据缺少关键字段信息，可能导致显示问题")

            # 构建结果数据
            result = {
                'success': True,
                'data': mapped_df,
                'total_records': total_records,
                'current_page': self.page,
                'page_size': self.page_size,
                'table_name': self.table_name,
                'loaded_fields': len(mapped_df.columns),
                'is_empty': False,
                'original_columns': original_columns,
                'mapped_columns': list(mapped_df.columns),
                'has_sort': len(sort_columns) > 0,
                'sort_columns': sort_columns
            }

            self.logger.info(f"成功加载表 {self.table_name} 第{self.page}页，共{len(mapped_df)}条记录")
            self.signals.result.emit(result)

        except Exception as e:
            self.logger.error(f"分页数据加载失败: {e}", exc_info=True)
            error_result = {
                'success': False,
                'error': str(e),
                'table_name': self.table_name,
                'current_page': self.page,
                'page_size': self.page_size
            }
            self.signals.error.emit((type(e), e, e.__traceback__))
            self.signals.result.emit(error_result)