"""
清理format_config.py中的离休人员专用配置
"""

def clean_format_config():
    """清理format_config.py文件"""
    
    file_path = "src/modules/format_management/format_config.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 定义要删除的行范围
    # 1. 删除retired_staff_format_config配置段 (232-276行)
    # 2. 删除get_retired_staff_format_config方法 (756-786行)
    # 3. 删除is_retired_staff_table方法 (866-887行)
    # 4. 删除优先级处理逻辑 (642-646行)
    
    # 注意：由于行号是从1开始的，而列表索引是从0开始的，所以需要-1
    delete_ranges = [
        (231, 276),  # retired_staff_format_config配置段
        (755, 786),  # get_retired_staff_format_config方法
        (865, 887),  # is_retired_staff_table方法
        (641, 646),  # 优先级处理逻辑
    ]
    
    # 标记要删除的行
    lines_to_delete = set()
    for start, end in delete_ranges:
        for i in range(start, end + 1):
            lines_to_delete.add(i)
    
    # 创建新的内容（不包含要删除的行）
    new_lines = []
    for i, line in enumerate(lines, 1):
        if i not in lines_to_delete:
            new_lines.append(line)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print(f"已清理 {file_path}")
    print(f"删除了 {len(lines_to_delete)} 行")
    
    # 统计删除的内容
    deleted_sections = [
        "retired_staff_format_config配置段",
        "get_retired_staff_format_config方法",
        "is_retired_staff_table方法",
        "优先级处理逻辑"
    ]
    
    print("\n已删除的内容：")
    for section in deleted_sections:
        print(f"  - {section}")

if __name__ == "__main__":
    clean_format_config()
    print("\n清理完成！")