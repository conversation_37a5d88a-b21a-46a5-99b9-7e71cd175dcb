#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
P3级问题修复 - 代码清理和优化
清理旧架构遗留代码，优化代码结构
"""

import sys
import re
from pathlib import Path
from typing import List, Tuple

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def find_old_architecture_code():
    """查找旧架构代码"""
    
    print("="*60)
    print("查找旧架构代码")
    print("="*60)
    
    src_dir = Path("src")
    
    # 要搜索的模式
    patterns = [
        r"旧架构",
        r"DEPRECATED",
        r"LEGACY", 
        r"TODO.*移除",
        r"兼容性代码",
        r"降级逻辑",
        r"_legacy_"
    ]
    
    found_items = []
    
    for py_file in src_dir.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            for line_num, line in enumerate(lines, 1):
                for pattern in patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        found_items.append({
                            'file': py_file.relative_to(src_dir),
                            'line': line_num,
                            'content': line.strip(),
                            'pattern': pattern
                        })
                        break
        except Exception as e:
            print(f"[WARN] 无法读取文件 {py_file}: {e}")
    
    # 按文件分组显示
    files_with_old_code = {}
    for item in found_items:
        file_path = item['file']
        if file_path not in files_with_old_code:
            files_with_old_code[file_path] = []
        files_with_old_code[file_path].append(item)
    
    print(f"\n发现 {len(files_with_old_code)} 个文件包含旧架构代码：")
    for file_path, items in files_with_old_code.items():
        print(f"\n文件: {file_path}")
        for item in items[:5]:  # 每个文件只显示前5个
            content = item['content'][:80].encode('gbk', errors='ignore').decode('gbk')
            print(f"  L{item['line']}: {content}")
    
    return found_items

def clean_comments():
    """清理注释中的旧架构标记"""
    
    print("\n" + "="*60)
    print("清理注释标记")
    print("="*60)
    
    # 生成清理建议
    suggestions = [
        {
            "pattern": r"#\s*🔧\s*\[P1-移除旧架构\].*",
            "replacement": "",
            "description": "移除P1修复标记"
        },
        {
            "pattern": r"#\s*🔧\s*\[P1-完成\].*",
            "replacement": "",
            "description": "移除P1完成标记"
        },
        {
            "pattern": r"#.*旧架构.*已.*移除.*",
            "replacement": "",
            "description": "移除旧架构移除说明"
        },
        {
            "pattern": r"#.*降级逻辑.*",
            "replacement": "",
            "description": "移除降级逻辑注释"
        }
    ]
    
    print("建议清理以下注释模式：")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"{i}. {suggestion['description']}")
        pattern_str = suggestion['pattern'].encode('gbk', errors='ignore').decode('gbk')
        print(f"   Pattern: {pattern_str}")
    
    return suggestions

def optimize_imports():
    """优化导入语句"""
    
    print("\n" + "="*60)
    print("优化导入语句")
    print("="*60)
    
    # 检查未使用的导入
    print("检查未使用的导入（需要使用 flake8 或 pylint）")
    print("建议运行: flake8 src/ --select=F401")
    
    # 生成导入优化脚本
    optimize_script = '''#!/bin/bash
# 导入优化脚本

echo "检查未使用的导入..."
flake8 src/ --select=F401 > unused_imports.txt

echo "检查导入顺序..."
isort src/ --check-only --diff

echo "自动修复导入顺序..."
read -p "是否自动修复导入顺序? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    isort src/
    echo "导入顺序已修复"
fi
'''
    
    script_file = Path("temp/optimize_imports.sh")
    with open(script_file, 'w') as f:
        f.write(optimize_script)
    
    print(f"导入优化脚本已生成: {script_file}")
    
    return script_file

def create_code_quality_report():
    """创建代码质量报告"""
    
    print("\n" + "="*60)
    print("代码质量分析")
    print("="*60)
    
    src_dir = Path("src")
    
    # 统计代码行数
    total_lines = 0
    total_files = 0
    large_files = []
    
    for py_file in src_dir.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = len(f.readlines())
                total_lines += lines
                total_files += 1
                
                if lines > 500:
                    large_files.append((py_file.relative_to(src_dir), lines))
        except:
            pass
    
    print(f"代码统计：")
    print(f"  总文件数: {total_files}")
    print(f"  总代码行数: {total_lines:,}")
    print(f"  平均每文件: {total_lines // total_files if total_files > 0 else 0} 行")
    
    if large_files:
        print(f"\n大文件（>500行）：")
        for file_path, lines in sorted(large_files, key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {file_path}: {lines} 行")
    
    # 检查函数复杂度
    print("\n建议使用以下工具进行深度分析：")
    print("  1. radon cc src/ -s  # 圈复杂度分析")
    print("  2. radon mi src/ -s  # 可维护性指数")
    print("  3. pylint src/       # 代码规范检查")
    
    return {
        'total_files': total_files,
        'total_lines': total_lines,
        'large_files': large_files
    }

def generate_cleanup_script():
    """生成清理脚本"""
    
    print("\n" + "="*60)
    print("生成清理脚本")
    print("="*60)
    
    cleanup_script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动代码清理脚本
清理旧架构标记和优化代码
"""

import re
from pathlib import Path

def clean_file(file_path):
    """清理单个文件"""
    
    # 要清理的模式
    patterns_to_remove = [
        r"^\s*#\s*🔧\s*\[P1-移除旧架构\].*$",
        r"^\s*#\s*🔧\s*\[P1-完成\].*$",
        r"^\s*#.*旧架构.*已.*移除.*$",
        r"^\s*#.*降级逻辑.*$",
    ]
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    cleaned_lines = []
    removed_count = 0
    
    for line in lines:
        should_remove = False
        for pattern in patterns_to_remove:
            if re.match(pattern, line):
                should_remove = True
                removed_count += 1
                break
        
        if not should_remove:
            cleaned_lines.append(line)
    
    if removed_count > 0:
        # 备份原文件
        backup_path = file_path.with_suffix('.py.bak')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        # 写入清理后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(cleaned_lines)
        
        print(f"清理 {file_path.name}: 移除 {removed_count} 行")
        return removed_count
    
    return 0

def main():
    """主函数"""
    
    # 需要清理的文件
    files_to_clean = [
        Path("src/gui/prototype/prototype_main_window.py"),
        Path("src/gui/prototype/widgets/virtualized_expandable_table.py")
    ]
    
    total_removed = 0
    
    for file_path in files_to_clean:
        if file_path.exists():
            removed = clean_file(file_path)
            total_removed += removed
    
    print(f"\\n清理完成，共移除 {total_removed} 行旧架构标记")

if __name__ == "__main__":
    main()
'''
    
    script_file = Path("temp/auto_cleanup.py")
    with open(script_file, 'w', encoding='utf-8') as f:
        f.write(cleanup_script)
    
    print(f"自动清理脚本已生成: {script_file}")
    print("运行方式: python temp/auto_cleanup.py")
    
    return script_file

def main():
    """主函数"""
    print("\n" + "="*70)
    print("P3级问题修复 - 代码清理")
    print("="*70)
    
    # 1. 查找旧架构代码
    old_code_items = find_old_architecture_code()
    
    # 2. 清理建议
    cleanup_suggestions = clean_comments()
    
    # 3. 优化导入
    import_script = optimize_imports()
    
    # 4. 代码质量报告
    quality_report = create_code_quality_report()
    
    # 5. 生成清理脚本
    cleanup_script = generate_cleanup_script()
    
    print("\n" + "="*70)
    print("代码清理分析完成")
    print("="*70)
    print("\n总结：")
    print(f"1. 发现 {len(old_code_items)} 处旧架构代码")
    print(f"2. 建议清理 {len(cleanup_suggestions)} 种注释模式")
    print(f"3. 总代码量: {quality_report['total_lines']:,} 行")
    print(f"4. 自动清理脚本: {cleanup_script}")
    print("\n建议：")
    print("1. 运行自动清理脚本清理注释")
    print("2. 手动审查并移除不需要的代码")
    print("3. 使用代码质量工具持续监控")

if __name__ == "__main__":
    main()