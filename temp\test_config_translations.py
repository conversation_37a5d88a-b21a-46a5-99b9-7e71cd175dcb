"""
测试配置项翻译字典是否正确设置
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.gui.dialogs.config_manager_dialog import ConfigManagerDialog

# 创建一个对话框实例但不显示
dialog = ConfigManagerDialog()

# 测试一些常用配置项的翻译
test_items = [
    'database', 'import_settings', 'field_mapping', 'processing', 'ui', 'reports',
    'db_name', 'db_path', 'backup_enabled', 'auto_save_interval', 'theme',
    'window_width', 'window_height', 'shortcuts', 'behavior', 'advanced'
]

print("配置项中英文映射测试:")
print("-" * 50)

for item in test_items:
    chinese = dialog.config_translations.get(item, f"[未翻译: {item}]")
    print(f"{item:30} -> {chinese}")
    
print("-" * 50)
print(f"总共翻译了 {len(dialog.config_translations)} 个配置项")
print("测试完成！")