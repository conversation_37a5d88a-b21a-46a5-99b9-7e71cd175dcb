#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义字段映射工作表选择修复

测试目标：
1. 验证智能工作表选择逻辑
2. 确保"离休人员工资表"优先被选择
3. 验证ChangeDataConfigDialog获取正确的工作表数据

创建时间: 2025-08-28
修复问题: 自定义字段映射弹窗示例数据不匹配问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import unittest
from unittest.mock import Mock, MagicMock, patch
from PyQt5.QtWidgets import QApplication
from src.gui.main_dialogs import DataImportDialog


class TestFieldMappingSheetSelectionFix(unittest.TestCase):
    """测试字段映射工作表选择修复"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        # 确保QApplication存在
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置每个测试"""
        self.dialog = DataImportDialog()
        # 模拟必要的属性
        self.dialog.logger = Mock()
        self.dialog.sheet_combo = Mock()
        self.dialog.sheet_matcher = Mock()
        self.dialog.target_selection_widget = Mock()
    
    def test_select_default_sheet_with_retired_employees(self):
        """测试优先选择离休人员工资表"""
        # 测试数据：包含离休人员工资表
        sheet_names = [
            "全部在职人员工资表",
            "退休人员工资表", 
            "离休人员工资表",
            "A岗职工"
        ]
        
        # 执行测试
        selected_sheet = self.dialog._select_default_sheet(sheet_names)
        
        # 验证结果
        self.assertEqual(selected_sheet, "离休人员工资表")
        self.dialog.logger.info.assert_called_with("🎯 [智能选择] 找到优先工作表: 离休人员工资表")
    
    def test_select_default_sheet_without_retired_employees(self):
        """测试不包含离休人员工资表时的选择逻辑"""
        # 测试数据：不包含离休人员工资表
        sheet_names = [
            "全部在职人员工资表",
            "A岗职工",
            "其他数据表"
        ]
        
        # 执行测试
        selected_sheet = self.dialog._select_default_sheet(sheet_names)
        
        # 验证结果：应该选择第一个优先级工作表
        self.assertEqual(selected_sheet, "全部在职人员工资表")
        self.dialog.logger.info.assert_called_with("🎯 [智能选择] 找到优先工作表: 全部在职人员工资表")
    
    def test_select_default_sheet_with_partial_match(self):
        """测试部分匹配的工作表名称"""
        # 测试数据：包含部分匹配的名称
        sheet_names = [
            "2025年5月份离休人员工资表",
            "在职人员明细",
            "其他数据"
        ]
        
        # 执行测试
        selected_sheet = self.dialog._select_default_sheet(sheet_names)
        
        # 验证结果：应该选择包含"离休人员工资表"的工作表
        self.assertEqual(selected_sheet, "2025年5月份离休人员工资表")
        self.dialog.logger.info.assert_called_with("🎯 [智能选择] 找到优先工作表: 2025年5月份离休人员工资表")
    
    def test_select_default_sheet_smart_matcher_fallback(self):
        """测试智能匹配器回退逻辑"""
        # 测试数据：没有优先工作表
        sheet_names = [
            "数据表1",
            "数据表2",
            "数据表3"
        ]
        
        # 模拟智能匹配器
        match_result = Mock()
        match_result.sheet_name = "数据表2"
        match_result.score = 0.8
        self.dialog.sheet_matcher.find_best_match.return_value = match_result
        
        # 模拟目标选择组件
        target = {'category': '离休人员'}
        self.dialog.target_selection_widget.get_current_target.return_value = target
        
        # 执行测试
        selected_sheet = self.dialog._select_default_sheet(sheet_names)
        
        # 验证结果
        self.assertEqual(selected_sheet, "数据表2")
        self.dialog.logger.info.assert_called_with("🔍 [智能匹配] 匹配到工作表: 数据表2 (得分: 0.80)")
    
    def test_select_default_sheet_final_fallback(self):
        """测试最终回退到第一个工作表"""
        # 测试数据
        sheet_names = [
            "未知表1",
            "未知表2"
        ]
        
        # 模拟智能匹配器返回低分结果
        match_result = Mock()
        match_result.score = 0.3  # 低于0.5阈值
        self.dialog.sheet_matcher.find_best_match.return_value = match_result
        
        # 执行测试
        selected_sheet = self.dialog._select_default_sheet(sheet_names)
        
        # 验证结果：应该回退到第一个工作表
        self.assertEqual(selected_sheet, "未知表1")
        self.dialog.logger.info.assert_called_with("⚠️ [回退选择] 使用第一个工作表: 未知表1")
    
    def test_select_default_sheet_empty_list(self):
        """测试空工作表列表"""
        sheet_names = []
        
        # 执行测试
        selected_sheet = self.dialog._select_default_sheet(sheet_names)
        
        # 验证结果
        self.assertEqual(selected_sheet, "")
    
    @patch('src.gui.main_dialogs.ChangeDataConfigDialog')
    def test_open_change_data_config_dialog_with_correct_sheet(self, mock_dialog_class):
        """测试打开字段配置对话框时使用正确的工作表"""
        # 模拟必要的属性
        self.dialog.current_file_path = "test.xlsx"
        self.dialog.sheet_combo.currentText.return_value = "离休人员工资表"
        self.dialog.importer = Mock()
        mock_excel_data = Mock()
        self.dialog.importer.import_data.return_value = mock_excel_data
        
        # 模拟配置数据
        self.dialog.change_data_configs = {
            "离休人员工资表": {"field_mapping": {"姓名": "employee_name"}}
        }
        
        # 创建模拟对话框实例
        mock_dialog_instance = Mock()
        mock_dialog_class.return_value = mock_dialog_instance
        
        # 执行测试
        self.dialog._open_change_data_config_dialog()
        
        # 验证调用
        self.dialog.importer.import_data.assert_called_with("test.xlsx", "离休人员工资表")
        mock_dialog_class.assert_called_with(
            excel_data=mock_excel_data,
            excel_file_path="test.xlsx",
            parent=self.dialog
        )


def run_test():
    """运行测试"""
    print("🧪 开始测试自定义字段映射工作表选择修复...")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestFieldMappingSheetSelectionFix)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！字段映射工作表选择修复成功。")
        print("\n📋 修复效果总结：")
        print("1. ✅ 智能工作表选择逻辑正常工作")
        print("2. ✅ 优先选择'离休人员工资表'")
        print("3. ✅ 部分匹配工作表名称正常")
        print("4. ✅ 智能匹配器回退逻辑正常")
        print("5. ✅ 最终回退逻辑正常")
        print("6. ✅ 自定义字段映射对话框获取正确数据")
        return True
    else:
        print(f"\n❌ 测试失败！失败数: {len(result.failures)}, 错误数: {len(result.errors)}")
        for test, error in result.failures + result.errors:
            print(f"   - {test}: {error}")
        return False


if __name__ == "__main__":
    success = run_test()
    sys.exit(0 if success else 1)
