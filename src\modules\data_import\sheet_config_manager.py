"""
Sheet级别配置管理器

提供Excel导入中每个Sheet的个性化配置管理功能，包括：
- Sheet级别的导入参数配置
- 数据范围和处理规则配置
- 字段映射和验证规则配置
- 配置的持久化和缓存管理
"""

import json
import os
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, field, asdict
from loguru import logger

from src.utils.log_config import setup_logger
from src.modules.data_import.config_types import ConfigRecommendation


@dataclass
class SheetImportConfig:
    """Sheet级别的导入配置"""
    sheet_name: str
    
    # 数据范围配置
    header_row: int = 1                    # 表头行号
    data_start_row: int = 2                # 数据起始行
    data_end_row: Optional[int] = None     # 数据结束行（None表示到文件末尾）
    skip_empty_rows: bool = True           # 跳过空行
    
    # 数据处理配置
    has_header: bool = True                # 是否有表头
    auto_detect_header: bool = True        # 自动检测表头
    remove_summary_rows: bool = False      # 移除汇总行
    summary_keywords: List[str] = field(default_factory=lambda: ["合计", "小计", "总计", "汇总"])
    
    # 数据清洗配置
    trim_whitespace: bool = True           # 去除前后空格
    normalize_numbers: bool = True         # 统一数字格式
    handle_merged_cells: bool = True       # 处理合并单元格
    fill_empty_values: bool = False        # 填充空值
    
    # 字段映射配置
    field_mappings: Dict[str, str] = field(default_factory=dict)      # Excel字段 -> 数据库字段
    field_types: Dict[str, str] = field(default_factory=dict)         # 字段类型定义
    required_fields: List[str] = field(default_factory=list)          # 必填字段列表
    
    # 验证规则
    validation_rules: Dict[str, Any] = field(default_factory=dict)    # 字段验证规则
    
    # 元数据
    created_time: datetime = field(default_factory=datetime.now)
    modified_time: datetime = field(default_factory=datetime.now)
    is_enabled: bool = True                # 是否启用此Sheet的导入
    notes: str = ""                        # 备注信息
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 处理datetime序列化
        data['created_time'] = self.created_time.isoformat()
        data['modified_time'] = self.modified_time.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SheetImportConfig':
        """从字典创建配置对象"""
        # 处理datetime反序列化
        if 'created_time' in data and isinstance(data['created_time'], str):
            data['created_time'] = datetime.fromisoformat(data['created_time'])
        if 'modified_time' in data and isinstance(data['modified_time'], str):
            data['modified_time'] = datetime.fromisoformat(data['modified_time'])
        
        return cls(**data)
    
    def update_modified_time(self):
        """更新修改时间"""
        self.modified_time = datetime.now()


class SheetConfigManager:
    """Sheet配置管理器"""
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """
        初始化Sheet配置管理器
        
        Args:
            config_dir: 配置文件存储目录，默认为 state/sheet_configs
        """
        self.logger = setup_logger(__name__)
        
        # 配置存储目录
        if config_dir is None:
            config_dir = Path("state/sheet_configs")
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 内存缓存
        self.sheet_configs: Dict[str, SheetImportConfig] = {}
        self.current_sheet: Optional[str] = None
        self.current_file_path: Optional[str] = None
        
        # 配置文件路径
        self.current_config_file: Optional[Path] = None

        # 智能推荐引擎（延迟初始化）
        self._smart_recommender = None

        # 配置模板管理器（延迟初始化）
        self._template_manager = None

        # 批量配置管理器（延迟初始化）
        self._batch_manager = None

        # 配置验证器（延迟初始化）
        self._config_validator = None

        # 导入预览引擎（延迟初始化）
        self._preview_engine = None

        # 配置版本管理器（延迟初始化）
        self._version_manager = None

        self.logger.info(f"Sheet配置管理器初始化完成，配置目录: {self.config_dir}")
    
    def set_excel_file(self, file_path: str) -> None:
        """
        设置当前Excel文件路径
        
        Args:
            file_path: Excel文件路径
        """
        self.current_file_path = file_path
        
        # 生成配置文件名（基于文件名）
        file_name = Path(file_path).stem
        safe_name = "".join(c for c in file_name if c.isalnum() or c in ('-', '_'))
        self.current_config_file = self.config_dir / f"{safe_name}_sheet_configs.json"
        
        # 加载现有配置
        self._load_configs_from_file()
        
        self.logger.info(f"设置Excel文件: {file_path}")
    
    def get_or_create_config(self, sheet_name: str) -> SheetImportConfig:
        """
        获取或创建Sheet配置
        
        Args:
            sheet_name: Sheet名称
            
        Returns:
            Sheet配置对象
        """
        if sheet_name not in self.sheet_configs:
            self.sheet_configs[sheet_name] = self._create_default_config(sheet_name)
            self.logger.info(f"为Sheet '{sheet_name}' 创建默认配置")
        
        return self.sheet_configs[sheet_name]
    
    def switch_sheet(self, sheet_name: str) -> SheetImportConfig:
        """
        切换到指定Sheet
        
        Args:
            sheet_name: Sheet名称
            
        Returns:
            Sheet配置对象
        """
        self.current_sheet = sheet_name
        config = self.get_or_create_config(sheet_name)
        self.logger.debug(f"切换到Sheet: {sheet_name}")
        return config
    
    def update_config(self, sheet_name: str, **kwargs) -> bool:
        """
        更新Sheet配置
        
        Args:
            sheet_name: Sheet名称
            **kwargs: 要更新的配置项
            
        Returns:
            是否更新成功
        """
        try:
            config = self.get_or_create_config(sheet_name)

            # 检查是否有实际变更
            has_changes = False
            for key, value in kwargs.items():
                if hasattr(config, key):
                    current_value = getattr(config, key)
                    if current_value != value:
                        has_changes = True
                        break

            # 如果有变更，创建版本
            if has_changes:
                # 创建当前配置的版本（在修改前）
                current_config_dict = config.to_dict()
                self.version_manager.create_version(
                    sheet_name=sheet_name,
                    config_data=current_config_dict,
                    description=f"自动版本 - 更新字段: {', '.join(kwargs.keys())}"
                )

            # 更新配置项
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)
                else:
                    self.logger.warning(f"未知的配置项: {key}")

            # 更新修改时间
            config.update_modified_time()

            # 自动保存
            self._save_configs_to_file()

            self.logger.debug(f"更新Sheet '{sheet_name}' 配置: {list(kwargs.keys())}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新Sheet配置失败: {e}")
            return False
    
    def get_current_config(self) -> Optional[SheetImportConfig]:
        """
        获取当前Sheet的配置
        
        Returns:
            当前Sheet配置，如果没有当前Sheet则返回None
        """
        if self.current_sheet:
            return self.get_or_create_config(self.current_sheet)
        return None
    
    def get_all_configs(self) -> Dict[str, SheetImportConfig]:
        """
        获取所有Sheet配置
        
        Returns:
            所有Sheet配置的字典
        """
        return self.sheet_configs.copy()
    
    def remove_config(self, sheet_name: str) -> bool:
        """
        删除Sheet配置
        
        Args:
            sheet_name: Sheet名称
            
        Returns:
            是否删除成功
        """
        try:
            if sheet_name in self.sheet_configs:
                del self.sheet_configs[sheet_name]
                self._save_configs_to_file()
                self.logger.info(f"删除Sheet '{sheet_name}' 配置")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"删除Sheet配置失败: {e}")
            return False
    
    def _create_default_config(self, sheet_name: str) -> SheetImportConfig:
        """
        创建默认配置
        
        Args:
            sheet_name: Sheet名称
            
        Returns:
            默认配置对象
        """
        config = SheetImportConfig(sheet_name=sheet_name)
        
        # 根据Sheet名称智能推断配置
        sheet_name_lower = sheet_name.lower()
        
        # 汇总表检测
        if any(keyword in sheet_name_lower for keyword in ["汇总", "统计", "总计", "合计"]):
            config.remove_summary_rows = True
            config.is_enabled = False  # 默认不导入汇总表
            config.notes = "检测为汇总表，默认不导入"
        
        # 说明表检测
        elif any(keyword in sheet_name_lower for keyword in ["说明", "备注", "注释", "readme"]):
            config.is_enabled = False
            config.notes = "检测为说明表，默认不导入"
        
        # 模板表检测
        elif any(keyword in sheet_name_lower for keyword in ["模板", "template", "示例"]):
            config.is_enabled = False
            config.notes = "检测为模板表，默认不导入"
        
        # 数据表智能配置
        else:
            # 根据常见的表头模式调整起始行
            if "月" in sheet_name or "年" in sheet_name:
                config.header_row = 1
                config.data_start_row = 2
                config.notes = "检测为数据表，使用标准配置"
        
        return config

    @property
    def smart_recommender(self):
        """延迟初始化智能推荐引擎"""
        if self._smart_recommender is None:
            from src.modules.data_import.smart_config_recommender import SmartConfigRecommender
            self._smart_recommender = SmartConfigRecommender()
        return self._smart_recommender

    @property
    def template_manager(self):
        """延迟初始化配置模板管理器"""
        if self._template_manager is None:
            from src.modules.data_import.config_template_manager import ConfigTemplateManager
            self._template_manager = ConfigTemplateManager()
        return self._template_manager

    @property
    def batch_manager(self):
        """延迟初始化批量配置管理器"""
        if self._batch_manager is None:
            from src.modules.data_import.batch_config_manager import BatchConfigManager
            self._batch_manager = BatchConfigManager(self)
        return self._batch_manager

    @property
    def config_validator(self):
        """延迟初始化配置验证器"""
        if self._config_validator is None:
            from src.modules.data_import.config_validator import ConfigValidator
            self._config_validator = ConfigValidator()
        return self._config_validator

    @property
    def preview_engine(self):
        """延迟初始化导入预览引擎"""
        if self._preview_engine is None:
            from src.modules.data_import.import_preview_engine import ImportPreviewEngine
            self._preview_engine = ImportPreviewEngine()
        return self._preview_engine

    @property
    def version_manager(self):
        """延迟初始化配置版本管理器"""
        if self._version_manager is None:
            from src.modules.data_import.config_version_manager import ConfigVersionManager
            self._version_manager = ConfigVersionManager()
        return self._version_manager

    def get_smart_recommendation(self, sheet_name: str, sample_data: Optional[Any] = None) -> ConfigRecommendation:
        """
        获取智能配置推荐

        Args:
            sheet_name: Sheet名称
            sample_data: 样本数据（可选）

        Returns:
            配置推荐结果
        """
        try:
            return self.smart_recommender.recommend_config(
                sheet_name=sheet_name,
                file_path=self.current_file_path,
                sample_data=sample_data
            )
        except Exception as e:
            self.logger.error(f"获取智能推荐失败: {e}")
            # 返回默认推荐
            return ConfigRecommendation(
                config=self._create_default_config(sheet_name),
                confidence=0.1,
                reasons=["使用默认配置"],
                warnings=[f"智能推荐失败: {e}"]
            )

    def apply_smart_recommendation(self, sheet_name: str, sample_data: Optional[Any] = None) -> bool:
        """
        应用智能配置推荐

        Args:
            sheet_name: Sheet名称
            sample_data: 样本数据（可选）

        Returns:
            是否应用成功
        """
        try:
            recommendation = self.get_smart_recommendation(sheet_name, sample_data)

            # 应用推荐的配置
            self.sheet_configs[sheet_name] = recommendation.config

            # 保存配置
            self._save_configs_to_file()

            self.logger.info(f"应用智能推荐配置: {sheet_name}, 置信度: {recommendation.confidence:.2f}")
            self.logger.debug(f"推荐理由: {', '.join(recommendation.reasons)}")

            if recommendation.warnings:
                self.logger.warning(f"推荐警告: {', '.join(recommendation.warnings)}")

            return True

        except Exception as e:
            self.logger.error(f"应用智能推荐失败: {e}")
            return False

    def get_recommendation_summary(self, sheet_name: str) -> Dict[str, Any]:
        """
        获取推荐摘要信息

        Args:
            sheet_name: Sheet名称

        Returns:
            推荐摘要字典
        """
        try:
            recommendation = self.get_smart_recommendation(sheet_name)

            return {
                'sheet_name': sheet_name,
                'confidence': recommendation.confidence,
                'is_enabled': recommendation.config.is_enabled,
                'has_header': recommendation.config.has_header,
                'header_row': recommendation.config.header_row,
                'data_start_row': recommendation.config.data_start_row,
                'remove_summary_rows': recommendation.config.remove_summary_rows,
                'reasons': recommendation.reasons,
                'warnings': recommendation.warnings,
                'suggestions': recommendation.suggestions
            }

        except Exception as e:
            self.logger.error(f"获取推荐摘要失败: {e}")
            return {
                'sheet_name': sheet_name,
                'confidence': 0.0,
                'error': str(e)
            }

    def apply_template_to_sheet(self, sheet_name: str, template_id: str) -> bool:
        """
        将模板应用到指定Sheet

        Args:
            sheet_name: Sheet名称
            template_id: 模板ID

        Returns:
            是否应用成功
        """
        try:
            # 获取或创建Sheet配置
            config = self.get_or_create_config(sheet_name)

            # 应用模板
            success = self.template_manager.apply_template(template_id, config)

            if success:
                # 保存配置
                self._save_configs_to_file()
                self.logger.info(f"模板应用成功: {template_id} -> {sheet_name}")

            return success

        except Exception as e:
            self.logger.error(f"应用模板失败: {e}")
            return False

    def create_template_from_sheet(self, sheet_name: str, template_name: str,
                                  description: str, tags: Optional[List[str]] = None) -> Optional[str]:
        """
        从Sheet配置创建模板

        Args:
            sheet_name: Sheet名称
            template_name: 模板名称
            description: 模板描述
            tags: 标签列表

        Returns:
            创建的模板ID，失败返回None
        """
        try:
            config = self.get_or_create_config(sheet_name)

            template_id = self.template_manager.create_template_from_config(
                name=template_name,
                description=description,
                sheet_config=config,
                tags=tags
            )

            self.logger.info(f"从Sheet '{sheet_name}' 创建模板: {template_name}")
            return template_id

        except Exception as e:
            self.logger.error(f"创建模板失败: {e}")
            return None

    def get_recommended_templates(self, sheet_name: str) -> List[Any]:
        """
        获取推荐的模板

        Args:
            sheet_name: Sheet名称

        Returns:
            推荐模板列表
        """
        try:
            # 基于Sheet名称搜索相关模板
            templates = []

            # 1. 搜索名称相关的模板
            name_templates = self.template_manager.search_templates(sheet_name)
            templates.extend(name_templates)

            # 2. 基于Sheet名称特征推荐模板
            sheet_name_lower = sheet_name.lower()

            if any(keyword in sheet_name_lower for keyword in ['工资', '薪资', 'salary']):
                salary_templates = self.template_manager.get_templates_by_tags(['工资表'])
                templates.extend(salary_templates)

            if any(keyword in sheet_name_lower for keyword in ['汇总', '统计', '总计']):
                summary_templates = self.template_manager.get_templates_by_tags(['汇总表'])
                templates.extend(summary_templates)

            if any(keyword in sheet_name_lower for keyword in ['说明', '备注', '文档']):
                doc_templates = self.template_manager.get_templates_by_tags(['说明文档'])
                templates.extend(doc_templates)

            # 3. 添加热门模板
            popular_templates = self.template_manager.get_popular_templates(3)
            templates.extend(popular_templates)

            # 去重并排序
            unique_templates = {}
            for template in templates:
                unique_templates[template.id] = template

            result = list(unique_templates.values())
            result.sort(key=lambda t: t.usage_count, reverse=True)

            return result[:10]  # 返回前10个

        except Exception as e:
            self.logger.error(f"获取推荐模板失败: {e}")
            return []

    def get_template_summary(self) -> Dict[str, Any]:
        """
        获取模板统计摘要

        Returns:
            模板统计摘要
        """
        try:
            return self.template_manager.get_template_summary()
        except Exception as e:
            self.logger.error(f"获取模板摘要失败: {e}")
            return {}

    def batch_copy_config(self, source_sheet: str, target_sheets: List[str],
                         exclude_fields: Optional[List[str]] = None) -> Any:
        """
        批量复制配置

        Args:
            source_sheet: 源Sheet名称
            target_sheets: 目标Sheet名称列表
            exclude_fields: 排除的字段列表

        Returns:
            批量操作结果
        """
        return self.batch_manager.copy_config_to_sheets(
            source_sheet=source_sheet,
            target_sheets=target_sheets,
            exclude_fields=exclude_fields
        )

    def batch_apply_template(self, template_id: str, target_sheets: List[str]) -> Any:
        """
        批量应用模板

        Args:
            template_id: 模板ID
            target_sheets: 目标Sheet名称列表

        Returns:
            批量操作结果
        """
        return self.batch_manager.apply_template_to_sheets(
            template_id=template_id,
            target_sheets=target_sheets
        )

    def batch_apply_smart_recommendations(self, target_sheets: List[str]) -> Any:
        """
        批量应用智能推荐

        Args:
            target_sheets: 目标Sheet名称列表

        Returns:
            批量操作结果
        """
        return self.batch_manager.apply_smart_recommendations(target_sheets)

    def batch_update_config(self, target_sheets: List[str], config_updates: Dict[str, Any]) -> Any:
        """
        批量更新配置

        Args:
            target_sheets: 目标Sheet名称列表
            config_updates: 要更新的配置项

        Returns:
            批量操作结果
        """
        return self.batch_manager.bulk_update_config(
            target_sheets=target_sheets,
            config_updates=config_updates
        )

    def get_similar_sheets(self, reference_sheet: str, similarity_threshold: float = 0.7) -> List[str]:
        """
        获取相似的Sheet列表

        Args:
            reference_sheet: 参考Sheet名称
            similarity_threshold: 相似度阈值

        Returns:
            相似Sheet名称列表
        """
        return self.batch_manager.get_similar_sheets(reference_sheet, similarity_threshold)

    def preview_batch_operation(self, operation_type: str, target_sheets: List[str], **kwargs) -> Dict[str, Any]:
        """
        预览批量操作

        Args:
            operation_type: 操作类型
            target_sheets: 目标Sheet列表
            **kwargs: 操作参数

        Returns:
            预览结果
        """
        from src.modules.data_import.batch_config_manager import BatchOperationType

        # 转换操作类型
        op_type_map = {
            'copy_config': BatchOperationType.COPY_CONFIG,
            'apply_template': BatchOperationType.APPLY_TEMPLATE,
            'smart_recommend': BatchOperationType.SMART_RECOMMEND,
            'bulk_update': BatchOperationType.BULK_UPDATE
        }

        op_type = op_type_map.get(operation_type)
        if not op_type:
            return {'error': f'未知的操作类型: {operation_type}'}

        return self.batch_manager.preview_batch_operation(op_type, target_sheets, **kwargs)

    def get_batch_operation_history(self, limit: int = 10) -> List[Any]:
        """
        获取批量操作历史

        Args:
            limit: 返回数量限制

        Returns:
            操作历史列表
        """
        return self.batch_manager.get_operation_history(limit)

    def get_batch_operation_statistics(self) -> Dict[str, Any]:
        """
        获取批量操作统计

        Returns:
            统计信息
        """
        return self.batch_manager.get_operation_statistics()

    def validate_config(self, sheet_name: str) -> Any:
        """
        验证单个Sheet配置

        Args:
            sheet_name: Sheet名称

        Returns:
            验证结果
        """
        try:
            config = self.get_or_create_config(sheet_name)
            return self.config_validator.validate_single_config(sheet_name, config)
        except Exception as e:
            self.logger.error(f"验证配置失败: {e}")
            return None

    def validate_all_configs(self) -> Dict[str, Any]:
        """
        验证所有Sheet配置

        Returns:
            验证结果字典
        """
        try:
            return self.config_validator.validate_multiple_configs(self.sheet_configs)
        except Exception as e:
            self.logger.error(f"验证所有配置失败: {e}")
            return {}

    def auto_fix_config(self, sheet_name: str) -> Tuple[bool, List[str]]:
        """
        自动修复Sheet配置问题

        Args:
            sheet_name: Sheet名称

        Returns:
            (是否修复成功, 修复操作列表)
        """
        try:
            # 先验证配置
            validation_result = self.validate_config(sheet_name)
            if not validation_result:
                return False, ["验证配置失败"]

            # 获取可修复的问题
            auto_fixable_issues = [issue for issue in validation_result.issues if issue.auto_fixable]

            if not auto_fixable_issues:
                return True, ["没有可自动修复的问题"]

            # 执行自动修复
            config = self.get_or_create_config(sheet_name)
            fixed_config, fixed_actions = self.config_validator.auto_fix_issues(
                sheet_name, config, auto_fixable_issues
            )

            # 保存修复后的配置
            self.sheet_configs[sheet_name] = fixed_config
            self._save_configs_to_file()

            self.logger.info(f"自动修复配置完成: {sheet_name}, 修复项目: {len(fixed_actions)}")
            return True, fixed_actions

        except Exception as e:
            self.logger.error(f"自动修复配置失败: {e}")
            return False, [f"修复失败: {e}"]

    def get_validation_summary(self) -> Dict[str, Any]:
        """
        获取配置验证摘要

        Returns:
            验证摘要信息
        """
        try:
            validation_results = self.validate_all_configs()
            return self.config_validator.get_validation_summary(validation_results)
        except Exception as e:
            self.logger.error(f"获取验证摘要失败: {e}")
            return {}

    def get_config_health_status(self) -> Dict[str, Any]:
        """
        获取配置健康状态

        Returns:
            健康状态信息
        """
        try:
            summary = self.get_validation_summary()

            # 计算健康评分 (0-100)
            total_sheets = summary.get('total_sheets', 0)
            if total_sheets == 0:
                health_score = 100
            else:
                valid_sheets = summary.get('valid_sheets', 0)
                errors = summary.get('errors_count', 0)
                critical = summary.get('critical_count', 0)

                # 基础分数
                base_score = (valid_sheets / total_sheets) * 80

                # 扣分项
                error_penalty = min(errors * 5, 20)
                critical_penalty = min(critical * 10, 30)

                health_score = max(0, base_score - error_penalty - critical_penalty)

            # 健康等级
            if health_score >= 90:
                health_level = "excellent"
                health_text = "优秀"
            elif health_score >= 75:
                health_level = "good"
                health_text = "良好"
            elif health_score >= 60:
                health_level = "fair"
                health_text = "一般"
            elif health_score >= 40:
                health_level = "poor"
                health_text = "较差"
            else:
                health_level = "critical"
                health_text = "严重"

            return {
                'health_score': round(health_score, 1),
                'health_level': health_level,
                'health_text': health_text,
                'summary': summary,
                'recommendations': self._get_health_recommendations(summary)
            }

        except Exception as e:
            self.logger.error(f"获取健康状态失败: {e}")
            return {
                'health_score': 0,
                'health_level': 'unknown',
                'health_text': '未知',
                'error': str(e)
            }

    def _get_health_recommendations(self, summary: Dict[str, Any]) -> List[str]:
        """获取健康改进建议"""
        recommendations = []

        errors_count = summary.get('errors_count', 0)
        critical_count = summary.get('critical_count', 0)
        auto_fixable_count = summary.get('auto_fixable_count', 0)

        if critical_count > 0:
            recommendations.append(f"发现{critical_count}个严重问题，需要立即处理")

        if errors_count > 0:
            recommendations.append(f"发现{errors_count}个错误，建议尽快修复")

        if auto_fixable_count > 0:
            recommendations.append(f"有{auto_fixable_count}个问题可以自动修复，建议使用自动修复功能")

        if summary.get('invalid_sheets', 0) > 0:
            recommendations.append("部分Sheet配置无效，请检查并修复")

        if not recommendations:
            recommendations.append("配置状态良好，继续保持")

        return recommendations

    def preview_import(self, sheet_name: str, file_path: Optional[str] = None,
                      field_mappings: Optional[Dict[str, str]] = None,
                      max_preview_rows: int = 1000) -> Any:
        """
        预览Sheet导入

        Args:
            sheet_name: Sheet名称
            file_path: Excel文件路径
            field_mappings: 字段映射
            max_preview_rows: 最大预览行数

        Returns:
            预览结果
        """
        try:
            # 使用当前文件路径（如果未提供）
            if not file_path:
                file_path = self.current_file_path

            if not file_path:
                raise ValueError("未指定Excel文件路径")

            # 获取Sheet配置
            config = self.get_or_create_config(sheet_name)

            # 执行预览
            return self.preview_engine.preview_import(
                file_path=file_path,
                sheet_name=sheet_name,
                sheet_config=config,
                field_mappings=field_mappings,
                max_preview_rows=max_preview_rows
            )

        except Exception as e:
            self.logger.error(f"预览导入失败: {e}")
            return None

    def preview_multiple_sheets(self, sheet_names: List[str], file_path: Optional[str] = None,
                               max_preview_rows: int = 500) -> Dict[str, Any]:
        """
        预览多个Sheet的导入

        Args:
            sheet_names: Sheet名称列表
            file_path: Excel文件路径
            max_preview_rows: 每个Sheet的最大预览行数

        Returns:
            预览结果字典
        """
        try:
            # 使用当前文件路径（如果未提供）
            if not file_path:
                file_path = self.current_file_path

            if not file_path:
                raise ValueError("未指定Excel文件路径")

            results = {}

            for sheet_name in sheet_names:
                try:
                    result = self.preview_import(
                        sheet_name=sheet_name,
                        file_path=file_path,
                        max_preview_rows=max_preview_rows
                    )
                    if result:
                        results[sheet_name] = result
                except Exception as e:
                    self.logger.error(f"预览Sheet '{sheet_name}' 失败: {e}")

            return results

        except Exception as e:
            self.logger.error(f"批量预览失败: {e}")
            return {}

    def get_import_preview_summary(self, sheet_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        获取导入预览摘要

        Args:
            sheet_names: Sheet名称列表，如果为None则预览所有启用的Sheet

        Returns:
            预览摘要信息
        """
        try:
            # 确定要预览的Sheet
            if sheet_names is None:
                sheet_names = [name for name, config in self.sheet_configs.items() if config.is_enabled]

            # 执行批量预览
            preview_results = self.preview_multiple_sheets(sheet_names)

            # 生成摘要
            if preview_results:
                results_list = list(preview_results.values())
                return self.preview_engine.get_preview_summary(results_list)
            else:
                return {}

        except Exception as e:
            self.logger.error(f"获取预览摘要失败: {e}")
            return {}

    def simulate_import_process(self, sheet_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        模拟完整的导入过程

        Args:
            sheet_names: Sheet名称列表

        Returns:
            模拟结果
        """
        try:
            self.logger.info("开始模拟导入过程")

            # 1. 配置验证
            validation_results = self.validate_all_configs()

            # 2. 导入预览
            preview_summary = self.get_import_preview_summary(sheet_names)

            # 3. 综合评估
            simulation_result = {
                'validation_summary': self.config_validator.get_validation_summary(validation_results),
                'preview_summary': preview_summary,
                'overall_assessment': self._assess_import_readiness(validation_results, preview_summary),
                'simulation_time': datetime.now().isoformat()
            }

            self.logger.info("导入过程模拟完成")
            return simulation_result

        except Exception as e:
            self.logger.error(f"模拟导入过程失败: {e}")
            return {'error': str(e)}

    def _assess_import_readiness(self, validation_results: Dict[str, Any],
                                preview_summary: Dict[str, Any]) -> Dict[str, Any]:
        """评估导入准备情况"""
        try:
            # 配置健康度评估
            config_health = self.get_config_health_status()
            config_score = config_health.get('health_score', 0)

            # 数据质量评估
            data_quality_score = preview_summary.get('data_quality_score', 0)

            # 风险评估
            overall_risk = preview_summary.get('overall_risk', 'unknown')
            critical_sheets = preview_summary.get('critical_sheets', 0)

            # 综合评分 (0-100)
            overall_score = (config_score * 0.4 + data_quality_score * 0.6)

            # 准备状态
            if overall_score >= 80 and overall_risk in ['low', 'medium']:
                readiness = 'ready'
                readiness_text = '准备就绪'
            elif overall_score >= 60 and critical_sheets == 0:
                readiness = 'caution'
                readiness_text = '谨慎进行'
            else:
                readiness = 'not_ready'
                readiness_text = '需要修复'

            # 建议
            recommendations = []
            if config_score < 70:
                recommendations.append("建议先修复配置问题")
            if data_quality_score < 70:
                recommendations.append("建议检查和清理数据质量")
            if critical_sheets > 0:
                recommendations.append(f"有{critical_sheets}个Sheet存在严重问题，必须先解决")
            if overall_risk == 'critical':
                recommendations.append("存在严重风险，不建议立即导入")

            if not recommendations:
                recommendations.append("系统状态良好，可以安全导入")

            return {
                'overall_score': round(overall_score, 1),
                'config_score': config_score,
                'data_quality_score': data_quality_score,
                'readiness': readiness,
                'readiness_text': readiness_text,
                'overall_risk': overall_risk,
                'critical_sheets': critical_sheets,
                'recommendations': recommendations
            }

        except Exception as e:
            self.logger.error(f"评估导入准备情况失败: {e}")
            return {
                'overall_score': 0,
                'readiness': 'unknown',
                'readiness_text': '评估失败',
                'error': str(e)
            }

    def create_config_milestone(self, sheet_name: str, description: str,
                               tags: Optional[List[str]] = None) -> Optional[str]:
        """
        创建配置里程碑版本

        Args:
            sheet_name: Sheet名称
            description: 里程碑描述
            tags: 标签

        Returns:
            里程碑版本ID
        """
        try:
            config = self.get_or_create_config(sheet_name)
            config_dict = config.to_dict()

            return self.version_manager.create_milestone(
                sheet_name=sheet_name,
                description=description,
                tags=tags
            )

        except Exception as e:
            self.logger.error(f"创建配置里程碑失败: {e}")
            return None

    def restore_config_version(self, sheet_name: str, version_id: str) -> bool:
        """
        恢复配置到指定版本

        Args:
            sheet_name: Sheet名称
            version_id: 版本ID

        Returns:
            是否恢复成功
        """
        try:
            # 恢复版本
            success, backup_id = self.version_manager.restore_version(
                sheet_name=sheet_name,
                version_id=version_id,
                create_backup=True
            )

            if success:
                # 获取恢复的配置数据
                restored_version = self.version_manager.get_current_version(sheet_name)
                if restored_version:
                    # 更新内存中的配置
                    config_data = restored_version.config_data

                    # 重新创建配置对象
                    config = SheetImportConfig.from_dict(config_data)
                    self.sheet_configs[sheet_name] = config

                    # 保存到文件
                    self._save_configs_to_file()

                    self.logger.info(f"恢复配置版本成功: {sheet_name} -> {version_id}")
                    return True

            return False

        except Exception as e:
            self.logger.error(f"恢复配置版本失败: {e}")
            return False

    def get_config_version_history(self, sheet_name: str, limit: int = 20) -> List[Any]:
        """
        获取配置版本历史

        Args:
            sheet_name: Sheet名称
            limit: 返回数量限制

        Returns:
            版本历史列表
        """
        try:
            return self.version_manager.get_version_history(sheet_name, limit)
        except Exception as e:
            self.logger.error(f"获取版本历史失败: {e}")
            return []

    def compare_config_versions(self, sheet_name: str, version_id1: str, version_id2: str) -> Dict[str, Any]:
        """
        比较两个配置版本

        Args:
            sheet_name: Sheet名称
            version_id1: 版本1 ID
            version_id2: 版本2 ID

        Returns:
            比较结果
        """
        try:
            return self.version_manager.compare_versions(sheet_name, version_id1, version_id2)
        except Exception as e:
            self.logger.error(f"比较配置版本失败: {e}")
            return {'error': str(e)}

    def get_config_milestones(self, sheet_name: str) -> List[Any]:
        """
        获取配置里程碑版本

        Args:
            sheet_name: Sheet名称

        Returns:
            里程碑版本列表
        """
        try:
            return self.version_manager.get_milestones(sheet_name)
        except Exception as e:
            self.logger.error(f"获取里程碑版本失败: {e}")
            return []

    def delete_config_version(self, sheet_name: str, version_id: str, force: bool = False) -> bool:
        """
        删除配置版本

        Args:
            sheet_name: Sheet名称
            version_id: 版本ID
            force: 是否强制删除

        Returns:
            是否删除成功
        """
        try:
            return self.version_manager.delete_version(sheet_name, version_id, force)
        except Exception as e:
            self.logger.error(f"删除配置版本失败: {e}")
            return False

    def get_version_statistics(self, sheet_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取版本统计信息

        Args:
            sheet_name: Sheet名称，如果为None则统计所有Sheet

        Returns:
            统计信息
        """
        try:
            return self.version_manager.get_version_statistics(sheet_name)
        except Exception as e:
            self.logger.error(f"获取版本统计失败: {e}")
            return {}

    def get_config_audit_trail(self, sheet_name: str) -> Dict[str, Any]:
        """
        获取配置审计跟踪

        Args:
            sheet_name: Sheet名称

        Returns:
            审计跟踪信息
        """
        try:
            # 获取版本历史
            versions = self.get_config_version_history(sheet_name, limit=0)  # 获取所有版本

            if not versions:
                return {'sheet_name': sheet_name, 'audit_trail': []}

            # 构建审计跟踪
            audit_trail = []

            for version in versions:
                audit_entry = {
                    'version_id': version.version_id,
                    'version_number': version.version_number,
                    'version_type': version.version_type.value,
                    'created_time': version.created_time.isoformat(),
                    'created_by': version.created_by,
                    'description': version.description,
                    'tags': version.tags,
                    'changes': [change.to_dict() for change in version.changes],
                    'changes_count': len(version.changes),
                    'is_current': version.is_current
                }
                audit_trail.append(audit_entry)

            # 统计信息
            total_changes = sum(len(v.changes) for v in versions)
            change_types = {}
            for version in versions:
                for change in version.changes:
                    change_type = change.change_type.value
                    change_types[change_type] = change_types.get(change_type, 0) + 1

            return {
                'sheet_name': sheet_name,
                'total_versions': len(versions),
                'total_changes': total_changes,
                'change_types': change_types,
                'first_version_time': versions[-1].created_time.isoformat() if versions else None,
                'last_version_time': versions[0].created_time.isoformat() if versions else None,
                'audit_trail': audit_trail
            }

        except Exception as e:
            self.logger.error(f"获取审计跟踪失败: {e}")
            return {'error': str(e)}

    def _load_configs_from_file(self) -> None:
        """从文件加载配置"""
        if not self.current_config_file or not self.current_config_file.exists():
            return
        
        try:
            with open(self.current_config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 解析配置数据
            self.sheet_configs = {}
            for sheet_name, config_data in data.get('sheet_configs', {}).items():
                self.sheet_configs[sheet_name] = SheetImportConfig.from_dict(config_data)
            
            self.logger.info(f"从文件加载了 {len(self.sheet_configs)} 个Sheet配置")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
    
    def _save_configs_to_file(self) -> bool:
        """保存配置到文件"""
        if not self.current_config_file:
            return False
        
        try:
            # 构建保存数据
            save_data = {
                'file_path': self.current_file_path,
                'created_time': datetime.now().isoformat(),
                'sheet_configs': {}
            }
            
            # 转换配置为字典
            for sheet_name, config in self.sheet_configs.items():
                save_data['sheet_configs'][sheet_name] = config.to_dict()
            
            # 写入文件
            with open(self.current_config_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"保存了 {len(self.sheet_configs)} 个Sheet配置到文件")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def export_config(self, output_path: Union[str, Path]) -> bool:
        """
        导出配置到指定文件
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            output_path = Path(output_path)
            
            # 构建导出数据
            export_data = {
                'export_time': datetime.now().isoformat(),
                'source_file': self.current_file_path,
                'sheet_count': len(self.sheet_configs),
                'configs': {}
            }
            
            for sheet_name, config in self.sheet_configs.items():
                export_data['configs'][sheet_name] = config.to_dict()
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
