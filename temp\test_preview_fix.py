#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PreviewValidationWidget修复的脚本

用于验证update_preview_data方法是否正确实现
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_preview_validation_widget():
    """测试PreviewValidationWidget的update_preview_data方法"""
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.unified_data_import_window import PreviewValidationWidget
        from src.utils.log_config import setup_logger
        
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        # 创建PreviewValidationWidget实例
        widget = PreviewValidationWidget()
        
        # 创建测试DataFrame
        test_data = {
            '姓名': ['张三', '李四', '王五'],
            '工资': [5000, 6000, 7000],
            '部门': ['技术部', '销售部', '财务部']
        }
        df = pd.DataFrame(test_data)
        
        print("测试DataFrame:")
        print(df)
        print(f"DataFrame类型: {type(df)}")
        print(f"DataFrame形状: {df.shape}")
        
        # 设置当前Sheet名称
        widget.current_sheet_name = "测试Sheet"
        
        # 测试update_preview_data方法
        print("\n开始测试update_preview_data方法...")
        
        # 检查方法是否存在
        if hasattr(widget, 'update_preview_data'):
            print("✅ update_preview_data方法存在")
            
            # 调用方法
            widget.update_preview_data(df)
            print("✅ update_preview_data方法调用成功")
            
            # 检查预览表格是否有数据
            if widget.preview_table.rowCount() > 0:
                print(f"✅ 预览表格有数据: {widget.preview_table.rowCount()} 行")
                print(f"✅ 预览表格列数: {widget.preview_table.columnCount()} 列")
            else:
                print("❌ 预览表格没有数据")
                
            # 检查记录数标签
            record_text = widget.record_count_label.text()
            print(f"✅ 记录数标签: {record_text}")
            
        else:
            print("❌ update_preview_data方法不存在")
            return False
            
        # 测试空DataFrame
        print("\n测试空DataFrame...")
        empty_df = pd.DataFrame()
        widget.update_preview_data(empty_df)
        print("✅ 空DataFrame处理成功")
        
        # 测试None值
        print("\n测试None值...")
        widget.update_preview_data(None)
        print("✅ None值处理成功")
        
        print("\n🎉 所有测试通过！PreviewValidationWidget修复成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dataframe_conversion():
    """测试DataFrame到字典列表的转换"""
    try:
        print("\n=== 测试DataFrame转换 ===")
        
        # 创建测试DataFrame
        test_data = {
            '姓名': ['张三', '李四'],
            '工资': [5000, 6000],
            '部门': ['技术部', '销售部']
        }
        df = pd.DataFrame(test_data)
        
        print("原始DataFrame:")
        print(df)
        
        # 转换为字典列表
        data_dicts = df.to_dict('records')
        print(f"\n转换后的字典列表:")
        for i, record in enumerate(data_dicts):
            print(f"记录 {i+1}: {record}")
            
        print(f"\n转换结果类型: {type(data_dicts)}")
        print(f"记录数量: {len(data_dicts)}")
        
        # 验证数据完整性
        if len(data_dicts) == len(df):
            print("✅ 记录数量一致")
        else:
            print("❌ 记录数量不一致")
            
        if len(data_dicts[0].keys()) == len(df.columns):
            print("✅ 字段数量一致")
        else:
            print("❌ 字段数量不一致")
            
        print("✅ DataFrame转换测试通过")
        return True
        
    except Exception as e:
        print(f"❌ DataFrame转换测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试PreviewValidationWidget修复...")
    
    # 测试DataFrame转换
    conversion_success = test_dataframe_conversion()
    
    # 测试PreviewValidationWidget
    widget_success = test_preview_validation_widget()
    
    if conversion_success and widget_success:
        print("\n🎉 所有测试通过！修复验证成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！需要进一步检查修复。")
        sys.exit(1)
