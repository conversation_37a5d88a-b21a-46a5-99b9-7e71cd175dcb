#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证和冲突检测功能测试脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_validator import ConfigValidator, ValidationLevel, ValidationCategory
from src.modules.data_import.sheet_config_manager import SheetConfigManager, SheetImportConfig

def test_config_validator():
    """测试配置验证器"""
    print("=== 测试配置验证器 ===")
    
    validator = ConfigValidator()
    
    # 1. 测试正常配置
    print("\n📋 测试正常配置:")
    normal_config = SheetImportConfig(
        sheet_name="正常工资表",
        header_row=1,
        data_start_row=2,
        data_end_row=100,
        is_enabled=True,
        remove_summary_rows=True,
        summary_keywords=['合计', '小计'],
        trim_whitespace=True,
        normalize_numbers=True
    )
    
    result = validator.validate_single_config("正常工资表", normal_config)
    print(f"  验证结果: {'✅ 有效' if result.is_valid else '❌ 无效'}")
    print(f"  问题数量: {len(result.issues)}")
    print(f"  警告: {result.warnings_count}, 错误: {result.errors_count}, 严重: {result.critical_count}")
    
    if result.issues:
        for issue in result.issues:
            level_icon = {"info": "ℹ️", "warning": "⚠️", "error": "❌", "critical": "🚨"}
            print(f"    {level_icon.get(issue.level.value, '?')} {issue.title}: {issue.description}")
    
    # 2. 测试有问题的配置
    print("\n📋 测试有问题的配置:")
    problem_config = SheetImportConfig(
        sheet_name="问题工资表",
        header_row=5,  # 表头行在数据行之后
        data_start_row=3,  # 数据起始行在表头行之前
        data_end_row=2,  # 数据结束行在起始行之前
        is_enabled=True,
        remove_summary_rows=True,
        summary_keywords=[],  # 启用了汇总行移除但没有关键词
        trim_whitespace=False,  # 未启用空格清理
        normalize_numbers=False  # 未启用数字格式统一
    )
    
    result = validator.validate_single_config("问题工资表", problem_config)
    print(f"  验证结果: {'✅ 有效' if result.is_valid else '❌ 无效'}")
    print(f"  问题数量: {len(result.issues)}")
    print(f"  警告: {result.warnings_count}, 错误: {result.errors_count}, 严重: {result.critical_count}")
    print(f"  可自动修复: {result.auto_fixable_count}")
    
    if result.issues:
        for issue in result.issues:
            level_icon = {"info": "ℹ️", "warning": "⚠️", "error": "❌", "critical": "🚨"}
            auto_fix = "🔧" if issue.auto_fixable else ""
            print(f"    {level_icon.get(issue.level.value, '?')} {auto_fix} {issue.title}")
            print(f"      {issue.description}")
            if issue.auto_fixable:
                print(f"      修复建议: {issue.fix_action}")
                if issue.suggested_value is not None:
                    print(f"      建议值: {issue.current_value} -> {issue.suggested_value}")
    
    # 3. 测试自动修复
    print("\n🔧 测试自动修复:")
    auto_fixable_issues = [issue for issue in result.issues if issue.auto_fixable]
    print(f"  可修复问题数: {len(auto_fixable_issues)}")
    
    if auto_fixable_issues:
        fixed_config, fixed_actions = validator.auto_fix_issues("问题工资表", problem_config, auto_fixable_issues)
        print(f"  修复操作: {len(fixed_actions)}")
        for action in fixed_actions:
            print(f"    ✅ {action}")
        
        # 重新验证修复后的配置
        fixed_result = validator.validate_single_config("问题工资表", fixed_config)
        print(f"  修复后验证: {'✅ 有效' if fixed_result.is_valid else '❌ 仍有问题'}")
        print(f"  剩余问题: {len(fixed_result.issues)}")
    
    return validator

def test_multiple_configs_validation():
    """测试多配置验证"""
    print("\n=== 测试多配置验证 ===")
    
    validator = ConfigValidator()
    
    # 创建多个配置
    configs = {
        "工资表1": SheetImportConfig(
            sheet_name="工资表1",
            header_row=1,
            data_start_row=2,
            is_enabled=True
        ),
        "工资表2": SheetImportConfig(
            sheet_name="工资表2", 
            header_row=2,
            data_start_row=3,
            is_enabled=True
        ),
        "汇总表": SheetImportConfig(
            sheet_name="汇总表",
            header_row=1,
            data_start_row=2,
            is_enabled=False
        ),
        "问题表": SheetImportConfig(
            sheet_name="问题表",
            header_row=10,  # 表头行过大
            data_start_row=5,  # 逻辑错误
            is_enabled=True
        )
    }
    
    # 验证所有配置
    results = validator.validate_multiple_configs(configs)
    
    print(f"📊 验证结果摘要:")
    summary = validator.get_validation_summary(results)
    print(f"  总Sheet数: {summary['total_sheets']}")
    print(f"  有效Sheet数: {summary['valid_sheets']}")
    print(f"  无效Sheet数: {summary['invalid_sheets']}")
    print(f"  总问题数: {summary['total_issues']}")
    print(f"  警告: {summary['warnings_count']}")
    print(f"  错误: {summary['errors_count']}")
    print(f"  严重: {summary['critical_count']}")
    print(f"  可自动修复: {summary['auto_fixable_count']}")
    print(f"  整体健康度: {summary['overall_health']}")
    
    # 显示每个Sheet的验证结果
    print(f"\n📋 各Sheet验证详情:")
    for sheet_name, result in results.items():
        status = "✅" if result.is_valid else "❌"
        print(f"  {status} {sheet_name}: {len(result.issues)}个问题")
        
        # 显示前3个问题
        for issue in result.issues[:3]:
            level_icon = {"info": "ℹ️", "warning": "⚠️", "error": "❌", "critical": "🚨"}
            print(f"    {level_icon.get(issue.level.value, '?')} {issue.title}")

def test_sheet_config_manager_integration():
    """测试与Sheet配置管理器的集成"""
    print("\n=== 测试Sheet配置管理器集成 ===")
    
    manager = SheetConfigManager()
    
    # 1. 创建一些测试配置
    test_sheets = [
        ("正常表", {"header_row": 1, "data_start_row": 2, "is_enabled": True}),
        ("问题表", {"header_row": 5, "data_start_row": 3, "is_enabled": True}),
        ("汇总表", {"header_row": 1, "data_start_row": 2, "is_enabled": False, "remove_summary_rows": True, "summary_keywords": []})
    ]
    
    for sheet_name, config_updates in test_sheets:
        manager.update_config(sheet_name, **config_updates)
    
    # 2. 验证单个配置
    print("\n🔍 验证单个配置:")
    for sheet_name, _ in test_sheets:
        result = manager.validate_config(sheet_name)
        if result:
            status = "✅" if result.is_valid else "❌"
            print(f"  {status} {sheet_name}: {len(result.issues)}个问题")
    
    # 3. 验证所有配置
    print("\n🔍 验证所有配置:")
    all_results = manager.validate_all_configs()
    print(f"  验证了 {len(all_results)} 个Sheet")
    
    # 4. 获取验证摘要
    print("\n📊 验证摘要:")
    summary = manager.get_validation_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 5. 获取健康状态
    print("\n💊 健康状态:")
    health = manager.get_config_health_status()
    print(f"  健康评分: {health.get('health_score', 0)}/100")
    print(f"  健康等级: {health.get('health_text', '未知')}")
    
    recommendations = health.get('recommendations', [])
    if recommendations:
        print(f"  改进建议:")
        for rec in recommendations:
            print(f"    💡 {rec}")
    
    # 6. 测试自动修复
    print("\n🔧 测试自动修复:")
    for sheet_name, _ in test_sheets:
        success, actions = manager.auto_fix_config(sheet_name)
        print(f"  {sheet_name}: {'✅ 成功' if success else '❌ 失败'}")
        for action in actions:
            print(f"    {action}")

def main():
    """主函数"""
    print("🚀 配置验证和冲突检测功能测试")
    print("=" * 50)
    
    try:
        # 测试配置验证器
        validator = test_config_validator()
        
        # 测试多配置验证
        test_multiple_configs_validation()
        
        # 测试集成功能
        test_sheet_config_manager_integration()
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
