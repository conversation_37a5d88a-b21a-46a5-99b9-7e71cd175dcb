"""
测试多列排序支持
验证P3优化效果
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from src.core.multi_column_sort_manager import get_multi_sort_manager, SortDirection

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    return pd.DataFrame({
        'department': np.random.choice(['研发部', '销售部', '人事部'], 30),
        'employee_name': [f'员工{i:02d}' for i in range(30)],
        'salary': np.random.uniform(3000, 20000, 30).round(2),
        'performance': np.random.choice(['A', 'B', 'C', 'D'], 30),
        'join_date': pd.date_range('2020-01-01', periods=30, freq='M')
    })

def test_basic_multi_column_sort():
    """测试基本多列排序"""
    print("=" * 60)
    print("测试基本多列排序")
    print("=" * 60)
    
    manager = get_multi_sort_manager()
    manager.clear_all()
    
    # 创建测试数据
    df = create_test_data()
    print(f"\n原始数据前5行:")
    print(df.head())
    
    # 1. 添加单列排序
    print("\n1. 添加单列排序（部门升序）...")
    success = manager.add_sort_column('department', 'asc')
    print(f"   添加结果: {'[PASS]' if success else '[FAIL]'}")
    
    sorted_df = manager.apply_sort(df)
    print(f"   排序后前5行:")
    print(sorted_df[['department', 'employee_name', 'salary']].head())
    
    # 2. 添加第二列排序
    print("\n2. 添加第二列排序（薪资降序）...")
    success = manager.add_sort_column('salary', 'desc')
    print(f"   添加结果: {'[PASS]' if success else '[FAIL]'}")
    
    sorted_df = manager.apply_sort(df)
    print(f"   双列排序后前5行:")
    print(sorted_df[['department', 'salary', 'employee_name']].head())
    
    # 验证排序正确性
    dept_groups = sorted_df.groupby('department')
    all_sorted = True
    for dept, group in dept_groups:
        if not group['salary'].is_monotonic_decreasing:
            all_sorted = False
            break
    
    print(f"   排序验证: {'[PASS] 部门内薪资降序正确' if all_sorted else '[FAIL] 排序错误'}")
    
    # 3. 切换排序方向
    print("\n3. 切换部门排序方向...")
    new_dir = manager.toggle_sort_direction('department')
    print(f"   新方向: {new_dir}")
    
    sorted_df = manager.apply_sort(df)
    print(f"   切换后前5行:")
    print(sorted_df[['department', 'salary', 'employee_name']].head())
    
    return all_sorted

def test_priority_management():
    """测试优先级管理"""
    print("\n" + "=" * 60)
    print("测试优先级管理")
    print("=" * 60)
    
    manager = get_multi_sort_manager()
    manager.clear_all()
    
    df = create_test_data()
    
    # 设置多列排序
    print("\n1. 设置初始排序...")
    manager.add_sort_column('performance', 'asc', priority=0)
    manager.add_sort_column('salary', 'desc', priority=1)
    manager.add_sort_column('department', 'asc', priority=2)
    
    config = manager.get_sort_config()
    print(f"   当前配置: {len(config)}列")
    for cfg in config:
        print(f"   - {cfg['column_name']}: {cfg['sort_order']}, 优先级={cfg['priority']}")
    
    # 更新优先级
    print("\n2. 调整优先级（薪资移到最前）...")
    manager.update_priority('salary', 0)
    
    config = manager.get_sort_config()
    print(f"   调整后配置:")
    for cfg in config:
        print(f"   - {cfg['column_name']}: {cfg['sort_order']}, 优先级={cfg['priority']}")
    
    # 验证排序
    sorted_df = manager.apply_sort(df)
    
    # 检查薪资是否为主要排序列
    is_salary_primary = sorted_df['salary'].is_monotonic_decreasing
    print(f"\n   优先级验证: {'[PASS] 薪资为主排序列' if is_salary_primary else '[FAIL] 优先级错误'}")
    
    return is_salary_primary

def test_sort_summary():
    """测试排序摘要"""
    print("\n" + "=" * 60)
    print("测试排序摘要生成")
    print("=" * 60)
    
    manager = get_multi_sort_manager()
    manager.clear_all()
    
    # 1. 无排序
    summary = manager.get_sort_summary()
    print(f"\n1. 无排序时: {summary}")
    
    # 2. 单列排序
    manager.add_sort_column('salary', 'desc')
    summary = manager.get_sort_summary()
    print(f"2. 单列排序: {summary}")
    
    # 3. 多列排序
    manager.add_sort_column('department', 'asc')
    manager.add_sort_column('performance', 'desc')
    summary = manager.get_sort_summary()
    print(f"3. 三列排序: {summary}")
    
    # 4. 超过3列
    manager.add_sort_column('employee_name', 'asc')
    manager.add_sort_column('join_date', 'desc')
    summary = manager.get_sort_summary()
    print(f"4. 五列排序: {summary}")
    
    return True

def test_type_detection():
    """测试数据类型检测"""
    print("\n" + "=" * 60)
    print("测试数据类型检测")
    print("=" * 60)
    
    manager = get_multi_sort_manager()
    manager.clear_all()
    
    # 创建包含不同类型的数据
    df = pd.DataFrame({
        'text_col': ['apple', 'Banana', 'cherry', 'Date'],
        'numeric_col': [10, 5, 20, 15],
        'date_col': pd.date_range('2024-01-01', periods=4),
        'mixed_col': ['10', '5', 'abc', '20']
    })
    
    print("\n测试数据:")
    print(df)
    
    # 测试文本列（大小写）
    print("\n1. 文本列排序（默认区分大小写）...")
    manager.add_sort_column('text_col', 'asc')
    sorted_df = manager.apply_sort(df, detect_types=True)
    print(f"   排序结果: {sorted_df['text_col'].tolist()}")
    
    # 测试数值列
    manager.clear_all()
    print("\n2. 数值列排序...")
    manager.add_sort_column('numeric_col', 'desc')
    sorted_df = manager.apply_sort(df, detect_types=True)
    print(f"   排序结果: {sorted_df['numeric_col'].tolist()}")
    
    # 测试日期列
    manager.clear_all()
    print("\n3. 日期列排序...")
    manager.add_sort_column('date_col', 'desc')
    sorted_df = manager.apply_sort(df, detect_types=True)
    print(f"   排序结果:")
    print(sorted_df['date_col'])
    
    return True

def test_performance_optimization():
    """测试性能优化建议"""
    print("\n" + "=" * 60)
    print("测试性能优化建议")
    print("=" * 60)
    
    manager = get_multi_sort_manager()
    manager.clear_all()
    
    # 创建高基数数据
    df = pd.DataFrame({
        'unique_id': range(1000),
        'category': np.random.choice(['A', 'B'], 1000),
        'value': np.random.uniform(0, 100, 1000)
    })
    
    # 添加多个排序列
    for col in ['unique_id', 'category', 'value']:
        manager.add_sort_column(col, 'asc')
    
    # 获取优化建议
    suggestions = manager.optimize_for_performance(df)
    
    print("\n优化建议:")
    if suggestions:
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion}")
        print("   [PASS] 生成优化建议")
    else:
        print("   无优化建议")
        print("   [PASS] 无需优化")
    
    return True

def test_config_persistence():
    """测试配置持久化"""
    print("\n" + "=" * 60)
    print("测试配置持久化")
    print("=" * 60)
    
    manager = get_multi_sort_manager()
    
    # 1. 设置配置
    print("\n1. 设置排序配置...")
    manager.clear_all()
    manager.add_sort_column('col1', 'asc', 0)
    manager.add_sort_column('col2', 'desc', 1)
    
    # 获取配置
    config = manager.get_sort_config()
    print(f"   原始配置: {config}")
    
    # 2. 清除并重新加载
    print("\n2. 清除并重新加载配置...")
    manager.clear_all()
    
    # 确认已清除
    empty_config = manager.get_sort_config()
    print(f"   清除后: {empty_config}")
    
    # 重新加载
    manager.set_sort_config(config)
    restored_config = manager.get_sort_config()
    print(f"   恢复后: {restored_config}")
    
    # 验证
    config_match = (len(restored_config) == len(config) and
                   all(r['column_name'] == o['column_name'] and
                       r['sort_order'] == o['sort_order']
                       for r, o in zip(restored_config, config)))
    
    print(f"\n   配置持久化: {'[PASS]' if config_match else '[FAIL]'}")
    
    return config_match

def main():
    """主测试函数"""
    print("\n" + "=" * 60)
    print("P3优化 - 多列排序支持测试")
    print("=" * 60)
    
    results = []
    
    # 运行测试
    tests = [
        ("基本多列排序", test_basic_multi_column_sort),
        ("优先级管理", test_priority_management),
        ("排序摘要", test_sort_summary),
        ("类型检测", test_type_detection),
        ("性能优化", test_performance_optimization),
        ("配置持久化", test_config_persistence)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n[ERROR] {test_name}失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    for test_name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{status} {test_name}")
    
    # 总体结果
    all_passed = all(result for _, result in results)
    print("\n" + "=" * 60)
    if all_passed:
        print("所有测试通过 - P3优化成功!")
    else:
        print("部分测试失败 - 需要修复")
    print("=" * 60)

if __name__ == "__main__":
    main()