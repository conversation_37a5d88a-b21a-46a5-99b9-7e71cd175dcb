#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一数据格式化管道 - P2级架构优化
提供统一的数据处理流水线，确保数据一致性
"""

from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime
import pandas as pd
from loguru import logger
import hashlib
import json
from enum import Enum
import time


class PipelineStage(Enum):
    """管道阶段枚举"""
    INPUT = "input"
    VALIDATION = "validation"
    TRANSFORMATION = "transformation"
    FORMATTING = "formatting"
    CACHING = "caching"
    OUTPUT = "output"


@dataclass
class PipelineContext:
    """管道上下文"""
    table_name: str
    table_type: str
    data: Union[pd.DataFrame, List[Dict], None] = None
    headers: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    cache_key: Optional[str] = None
    stage: PipelineStage = PipelineStage.INPUT
    
    def add_metric(self, name: str, value: float):
        """添加性能指标"""
        self.performance_metrics[name] = value
    
    def add_error(self, error: str):
        """添加错误"""
        self.errors.append(error)
        logger.error(f"🔧 [P2-Pipeline] {error}")
    
    def add_warning(self, warning: str):
        """添加警告"""
        self.warnings.append(warning)
        logger.warning(f"🔧 [P2-Pipeline] {warning}")
    
    def generate_cache_key(self) -> str:
        """生成缓存键"""
        if self.cache_key:
            return self.cache_key
        
        # 基于表名、类型和数据特征生成缓存键
        key_parts = [
            self.table_name,
            self.table_type,
            str(len(self.headers)),
            str(len(self.data) if isinstance(self.data, (list, pd.DataFrame)) else 0)
        ]
        key_string = "|".join(key_parts)
        self.cache_key = hashlib.md5(key_string.encode()).hexdigest()
        return self.cache_key


class DataPipelineProcessor:
    """数据管道处理器基类"""
    
    def process(self, context: PipelineContext) -> PipelineContext:
        """处理数据"""
        raise NotImplementedError
    
    def can_process(self, context: PipelineContext) -> bool:
        """检查是否可以处理"""
        return True


class ValidationProcessor(DataPipelineProcessor):
    """数据验证处理器"""
    
    def __init__(self):
        from src.modules.data_management.data_flow_validator import DataFlowValidator
        self.validator = DataFlowValidator()
    
    def process(self, context: PipelineContext) -> PipelineContext:
        """验证数据"""
        start_time = time.time()
        context.stage = PipelineStage.VALIDATION
        
        try:
            # 转换数据格式
            if isinstance(context.data, pd.DataFrame):
                data_list = context.data.to_dict('records')
            else:
                data_list = context.data
            
            # 执行验证
            result = self.validator.validate_data_consistency(
                data=data_list,
                headers=context.headers,
                table_type=context.table_type
            )
            
            # 更新上下文
            if result.issues:
                for issue in result.issues:
                    context.add_warning(f"验证问题: {issue}")
            
            if result.fixes_applied:
                context.metadata['validation_fixes'] = result.fixes_applied
            
            # 更新数据
            context.data = result.data
            context.headers = result.headers
            
            # 记录性能
            elapsed = (time.time() - start_time) * 1000
            context.add_metric('validation_ms', elapsed)
            
            logger.info(f"🔧 [P2-Pipeline] 数据验证完成: {len(result.issues)}个问题, 耗时{elapsed:.2f}ms")
            
        except Exception as e:
            context.add_error(f"验证失败: {e}")
        
        return context


class TransformationProcessor(DataPipelineProcessor):
    """数据转换处理器"""
    
    def process(self, context: PipelineContext) -> PipelineContext:
        """转换数据到统一格式"""
        start_time = time.time()
        context.stage = PipelineStage.TRANSFORMATION
        
        try:
            # 确保数据是DataFrame格式
            if not isinstance(context.data, pd.DataFrame):
                if isinstance(context.data, list):
                    context.data = pd.DataFrame(context.data)
                else:
                    context.add_error("无法转换数据到DataFrame")
                    return context
            
            # 统一列名
            if context.headers:
                # 确保列名与headers一致
                if len(context.data.columns) == len(context.headers):
                    context.data.columns = context.headers
            
            # 数据类型推断和转换
            for col in context.data.columns:
                # 尝试转换数值列
                if any(keyword in col for keyword in ['工资', '津贴', '补贴', '金额']):
                    try:
                        context.data[col] = pd.to_numeric(context.data[col], errors='coerce')
                    except:
                        pass
            
            # 记录转换信息
            context.metadata['transformed_columns'] = list(context.data.columns)
            context.metadata['data_shape'] = context.data.shape
            
            # 记录性能
            elapsed = (time.time() - start_time) * 1000
            context.add_metric('transformation_ms', elapsed)
            
            logger.info(f"🔧 [P2-Pipeline] 数据转换完成: {context.data.shape}, 耗时{elapsed:.2f}ms")
            
        except Exception as e:
            context.add_error(f"转换失败: {e}")
        
        return context


class FormattingProcessor(DataPipelineProcessor):
    """格式化处理器"""
    
    def __init__(self):
        from src.modules.format_management.unified_format_manager import get_unified_format_manager
        self.formatter = get_unified_format_manager()
    
    def process(self, context: PipelineContext) -> PipelineContext:
        """格式化数据"""
        start_time = time.time()
        context.stage = PipelineStage.FORMATTING
        
        try:
            if not isinstance(context.data, pd.DataFrame):
                context.add_warning("数据不是DataFrame，跳过格式化")
                return context
            
            # 执行格式化
            formatted_df = self.formatter.format_data(
                data=context.data,
                table_type=context.table_type
            )
            
            context.data = formatted_df
            context.metadata['formatted'] = True
            
            # 记录性能
            elapsed = (time.time() - start_time) * 1000
            context.add_metric('formatting_ms', elapsed)
            
            logger.info(f"🔧 [P2-Pipeline] 数据格式化完成, 耗时{elapsed:.2f}ms")
            
        except Exception as e:
            context.add_error(f"格式化失败: {e}")
        
        return context


class CachingProcessor(DataPipelineProcessor):
    """缓存处理器"""
    
    def __init__(self, cache_size: int = 50):
        self._cache: Dict[str, Any] = {}
        self._cache_hits = 0
        self._cache_misses = 0
        self._max_cache_size = cache_size
    
    def process(self, context: PipelineContext) -> PipelineContext:
        """处理缓存"""
        context.stage = PipelineStage.CACHING
        
        # 生成缓存键
        cache_key = context.generate_cache_key()
        
        # 检查缓存
        if cache_key in self._cache:
            # 缓存命中
            self._cache_hits += 1
            cached_data = self._cache[cache_key]
            context.data = cached_data['data']
            context.headers = cached_data['headers']
            context.metadata['cache_hit'] = True
            context.add_metric('cache_hit', 1)
            logger.debug(f"🔧 [P2-Pipeline] 缓存命中: {cache_key}")
        else:
            # 缓存未命中
            self._cache_misses += 1
            context.metadata['cache_hit'] = False
            context.add_metric('cache_miss', 1)
            
            # 存储到缓存
            if context.data is not None:
                self._cache[cache_key] = {
                    'data': context.data.copy() if isinstance(context.data, pd.DataFrame) else context.data,
                    'headers': context.headers.copy(),
                    'timestamp': datetime.now()
                }
                
                # 限制缓存大小
                if len(self._cache) > self._max_cache_size:
                    # 删除最旧的缓存
                    oldest_key = min(self._cache.keys(), 
                                   key=lambda k: self._cache[k]['timestamp'])
                    del self._cache[oldest_key]
        
        return context
    
    def get_statistics(self) -> Dict:
        """获取缓存统计"""
        total = self._cache_hits + self._cache_misses
        hit_rate = self._cache_hits / total if total > 0 else 0
        
        return {
            'cache_size': len(self._cache),
            'cache_hits': self._cache_hits,
            'cache_misses': self._cache_misses,
            'hit_rate': hit_rate
        }


class UnifiedDataPipeline:
    """
    统一数据处理管道 - P2级架构优化
    
    提供完整的数据处理流水线：
    1. 输入验证
    2. 数据转换
    3. 格式化
    4. 缓存
    5. 输出
    """
    
    def __init__(self):
        self.processors: List[DataPipelineProcessor] = []
        self._setup_default_processors()
        self._process_count = 0
        self._error_count = 0
        logger.info("🔧 [P2-Pipeline] 统一数据管道初始化完成")
    
    def _setup_default_processors(self):
        """设置默认处理器"""
        self.processors = [
            ValidationProcessor(),      # 验证
            TransformationProcessor(),  # 转换
            FormattingProcessor(),      # 格式化
            CachingProcessor(),        # 缓存
        ]
    
    def add_processor(self, processor: DataPipelineProcessor):
        """添加处理器"""
        self.processors.append(processor)
    
    def process(self, 
               data: Union[pd.DataFrame, List[Dict]], 
               headers: List[str],
               table_name: str,
               table_type: str,
               **kwargs) -> PipelineContext:
        """
        处理数据
        
        Args:
            data: 输入数据
            headers: 表头
            table_name: 表名
            table_type: 表类型
            **kwargs: 额外参数
            
        Returns:
            处理上下文
        """
        start_time = time.time()
        self._process_count += 1
        
        # 创建上下文
        context = PipelineContext(
            table_name=table_name,
            table_type=table_type,
            data=data,
            headers=headers,
            metadata=kwargs
        )
        
        logger.info(f"🔧 [P2-Pipeline] 开始处理: {table_name} ({table_type})")
        
        # 执行处理器链
        for processor in self.processors:
            if processor.can_process(context):
                try:
                    context = processor.process(context)
                except Exception as e:
                    context.add_error(f"处理器 {processor.__class__.__name__} 失败: {e}")
                    self._error_count += 1
        
        # 设置最终阶段
        context.stage = PipelineStage.OUTPUT
        
        # 记录总体性能
        total_time = (time.time() - start_time) * 1000
        context.add_metric('total_ms', total_time)
        
        # 记录结果
        if context.errors:
            logger.error(f"🔧 [P2-Pipeline] 处理完成但有错误: {len(context.errors)}个")
        else:
            logger.info(f"🔧 [P2-Pipeline] 处理成功: 耗时{total_time:.2f}ms")
        
        return context
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        cache_stats = {}
        for processor in self.processors:
            if isinstance(processor, CachingProcessor):
                cache_stats = processor.get_statistics()
                break
        
        return {
            'total_processed': self._process_count,
            'error_count': self._error_count,
            'error_rate': self._error_count / self._process_count if self._process_count > 0 else 0,
            'cache_stats': cache_stats
        }


# 单例实例
_pipeline_instance: Optional[UnifiedDataPipeline] = None


def get_data_pipeline() -> UnifiedDataPipeline:
    """获取数据管道单例"""
    global _pipeline_instance
    if _pipeline_instance is None:
        _pipeline_instance = UnifiedDataPipeline()
    return _pipeline_instance