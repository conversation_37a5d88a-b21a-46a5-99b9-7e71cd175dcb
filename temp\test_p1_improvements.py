#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试P1级改进效果
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

print("=" * 60)
print("P1级改进验证测试")
print("=" * 60)

# 创建测试Excel文件（多sheet）
def create_test_excel():
    """创建包含多个sheet的测试Excel文件"""
    file_path = "temp/test_multi_sheet.xlsx"
    
    # 创建多个sheet的数据
    sheets_data = {
        "2025年5月工资": pd.DataFrame({
            "工号": ["001", "002", "003"],
            "姓名": ["张三", "李四", "王五"],
            "岗位工资": [5000, 6000, 7000],
            "薪级工资": [2000, 2500, 3000],
        }),
        "2025年6月工资": pd.DataFrame({
            "工号": ["001", "002", "003", "004"],
            "姓名": ["张三", "李四", "王五", "赵六"],
            "岗位工资": [5100, 6100, 7100, 8000],
            "薪级工资": [2100, 2600, 3100, 3500],
        }),
        "汇总表": pd.DataFrame({
            "月份": ["5月", "6月"],
            "人数": [3, 4],
            "总额": [25500, 36000],
        }),
        "异动明细": pd.DataFrame({
            "工号": ["004"],
            "姓名": ["赵六"],
            "异动类型": ["新增"],
            "生效日期": ["2025-06-01"],
        })
    }
    
    # 写入Excel文件
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        for sheet_name, df in sheets_data.items():
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"[OK] 创建测试Excel文件: {file_path}")
    print(f"     包含 {len(sheets_data)} 个工作表: {', '.join(sheets_data.keys())}")
    return file_path, sheets_data

print("\n测试1: 多sheet支持")
print("-" * 40)
try:
    from src.gui.change_data_config_dialog import ChangeDataConfigDialog
    
    # 创建测试文件
    excel_file, sheets_data = create_test_excel()
    
    # 创建应用
    app = QApplication([])
    
    # 创建对话框，传入文件路径
    dialog = ChangeDataConfigDialog(
        excel_data=None,
        excel_file_path=excel_file
    )
    
    # 检查是否加载了所有sheets
    if dialog.all_sheets_data:
        print(f"[OK] 成功加载 {len(dialog.all_sheets_data)} 个工作表")
        for sheet_name in dialog.all_sheets_data:
            rows, cols = dialog.all_sheets_data[sheet_name].shape
            print(f"     - {sheet_name}: {rows}行 x {cols}列")
    else:
        print("[FAIL] 未能加载多个工作表")
    
    # 检查sheet选择器是否存在
    if hasattr(dialog, 'sheet_combo'):
        print(f"[OK] Sheet选择器已创建，包含 {dialog.sheet_combo.count()} 个选项")
    else:
        print("[INFO] 未创建Sheet选择器（可能只有单个sheet）")
        
except Exception as e:
    print(f"[FAIL] 多sheet支持测试失败: {e}")

print("\n测试2: 字段类型选择器改进")
print("-" * 40)
try:
    # 测试字段类型下拉框创建
    from src.gui.change_data_config_dialog import ChangeDataConfigDialog
    
    # 创建测试数据
    test_data = pd.DataFrame({
        "工号": ["001"],
        "姓名": ["测试"],
        "工资": [5000.00]
    })
    
    dialog = ChangeDataConfigDialog(excel_data=test_data)
    
    # 测试新的字段类型创建方法
    if hasattr(dialog, 'create_field_type_combo'):
        combo = dialog.create_field_type_combo()
        print(f"[OK] 字段类型下拉框创建成功")
        print(f"     最小宽度: {combo.minimumWidth()}px")
        print(f"     选项数量: {combo.count()}")
        
        # 检查tooltip
        has_tooltip = False
        for i in range(combo.count()):
            tooltip = combo.itemData(i, Qt.ToolTipRole)
            if tooltip:
                has_tooltip = True
                break
        
        if has_tooltip:
            print("[OK] 字段类型包含说明tooltip")
        else:
            print("[FAIL] 字段类型缺少tooltip")
            
        # 检查显示格式
        if combo.count() > 0:
            first_item = combo.itemText(0)
            if "(" in first_item and ")" in first_item:
                print(f"[OK] 字段类型显示格式正确: {first_item}")
            else:
                print(f"[WARN] 字段类型显示格式可能不正确: {first_item}")
    else:
        print("[FAIL] 未找到create_field_type_combo方法")
        
except Exception as e:
    print(f"[FAIL] 字段类型选择器测试失败: {e}")

print("\n测试3: 字段配置表格UI")
print("-" * 40)
try:
    # 检查字段配置表格
    if hasattr(dialog, 'field_table'):
        print(f"[OK] 字段配置表格存在")
        
        # 检查表格列
        col_count = dialog.field_table.columnCount()
        print(f"     列数: {col_count}")
        
        # 检查是否有数据
        row_count = dialog.field_table.rowCount()
        if row_count > 0:
            print(f"[OK] 表格已加载数据: {row_count}行")
            
            # 检查第一行的字段类型下拉框
            type_widget = dialog.field_table.cellWidget(0, 1)
            if type_widget:
                print(f"[OK] 字段类型控件已设置")
                from PyQt5.QtWidgets import QComboBox
                if isinstance(type_widget, QComboBox):
                    width = type_widget.minimumWidth()
                    print(f"     下拉框最小宽度: {width}px")
                    if width >= 150:
                        print("[OK] 下拉框宽度足够")
                    else:
                        print("[WARN] 下拉框可能太窄")
        else:
            print("[INFO] 表格暂无数据")
    else:
        print("[FAIL] 未找到字段配置表格")
        
except Exception as e:
    print(f"[FAIL] 字段配置表格测试失败: {e}")

print("\n" + "=" * 60)
print("P1级改进验证完成！")
print("=" * 60)

# 清理测试文件
import os
try:
    if os.path.exists("temp/test_multi_sheet.xlsx"):
        os.remove("temp/test_multi_sheet.xlsx")
        print("\n[INFO] 测试文件已清理")
except PermissionError:
    print("\n[INFO] 测试文件正在使用中，稍后手动清理")