#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户偏好设置管理模块

提供用户个性化设置的存储和管理功能，包括：
- 界面偏好设置
- 用户操作偏好
- 数据导入偏好
- 其他用户自定义设置

创建时间: 2025-01-20
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union
from datetime import datetime

from src.utils.log_config import setup_logger


class UserPreferences:
    """用户偏好设置管理器"""
    
    def __init__(self, user_id: Optional[str] = None):
        """
        初始化用户偏好设置管理器
        
        Args:
            user_id: 用户ID，如果不提供则使用默认用户
        """
        self.logger = setup_logger(__name__)
        self.user_id = user_id or "default"
        
        # 确定用户偏好文件路径
        self.preferences_dir = Path("state/user")
        self.preferences_file = self.preferences_dir / f"preferences_{self.user_id}.json"
        
        # 确保目录存在
        self.preferences_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载偏好设置
        self._preferences = self._load_preferences()
        
        self.logger.debug(f"用户偏好设置管理器初始化完成: {self.user_id}")
    
    def _load_preferences(self) -> Dict[str, Any]:
        """
        加载用户偏好设置
        
        Returns:
            Dict[str, Any]: 偏好设置字典
        """
        try:
            if self.preferences_file.exists():
                with open(self.preferences_file, 'r', encoding='utf-8') as f:
                    preferences = json.load(f)
                    self.logger.debug(f"加载用户偏好设置: {len(preferences)} 项")
                    return preferences
            else:
                # 返回默认偏好设置
                return self._get_default_preferences()
                
        except Exception as e:
            self.logger.error(f"加载用户偏好设置失败: {e}")
            return self._get_default_preferences()
    
    def _get_default_preferences(self) -> Dict[str, Any]:
        """
        获取默认偏好设置
        
        Returns:
            Dict[str, Any]: 默认偏好设置
        """
        return {
            "ui": {
                "use_unified_import_interface": None,  # None表示未设置，会弹出选择对话框
                "theme": "default",
                "show_tooltips": True,
                "auto_save_configs": True,
                "remember_file_paths": True
            },
            "data_import": {
                "auto_detect_field_types": True,
                "use_smart_mapping": True,
                "show_preview_by_default": True,
                "validate_before_import": True,
                "max_preview_rows": 100
            },
            "performance": {
                "enable_cache": True,
                "lazy_loading": True,
                "auto_cleanup_memory": True,
                "max_memory_usage_mb": 512
            },
            "feedback": {
                "enable_usage_analytics": True,
                "auto_submit_error_reports": False,
                "participate_in_improvements": True
            },
            "advanced": {
                "debug_mode": False,
                "show_advanced_options": False,
                "expert_mode": False
            },
            "_metadata": {
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "version": "1.0.0"
            }
        }
    
    def get_setting(self, key_path: str, default_value: Any = None) -> Any:
        """
        获取设置值
        
        Args:
            key_path: 设置键路径，使用点号分隔（如 "ui.theme"）
            default_value: 默认值
            
        Returns:
            Any: 设置值
        """
        try:
            keys = key_path.split('.')
            value = self._preferences
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default_value
            
            return value
            
        except Exception as e:
            self.logger.error(f"获取设置失败 {key_path}: {e}")
            return default_value
    
    def get_preference(self, key_path: str, default_value: Any = None) -> Any:
        """
        获取偏好设置值（get_setting的别名，为保持向后兼容性）
        
        Args:
            key_path: 设置键路径，使用点号分隔（如 "ui.theme"）
            default_value: 默认值
            
        Returns:
            Any: 设置值
        """
        return self.get_setting(key_path, default_value)
    
    def set_setting(self, key_path: str, value: Any) -> bool:
        """
        设置设置值
        
        Args:
            key_path: 设置键路径，使用点号分隔（如 "ui.theme"）
            value: 设置值
            
        Returns:
            bool: 设置是否成功
        """
        try:
            keys = key_path.split('.')
            current = self._preferences
            
            # 导航到目标位置，创建必要的嵌套字典
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # 设置最终值
            current[keys[-1]] = value
            
            # 更新元数据
            if "_metadata" not in self._preferences:
                self._preferences["_metadata"] = {}
            self._preferences["_metadata"]["last_updated"] = datetime.now().isoformat()
            
            self.logger.debug(f"设置更新: {key_path} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置设置失败 {key_path}: {e}")
            return False
    
    def save_settings(self) -> bool:
        """
        保存偏好设置到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 备份现有文件
            if self.preferences_file.exists():
                backup_file = self.preferences_file.with_suffix('.json.bak')
                import shutil
                shutil.copy2(self.preferences_file, backup_file)
            
            # 保存新的偏好设置
            with open(self.preferences_file, 'w', encoding='utf-8') as f:
                json.dump(self._preferences, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"用户偏好设置已保存: {self.preferences_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存用户偏好设置失败: {e}")
            return False
    
    def reset_settings(self, category: Optional[str] = None) -> bool:
        """
        重置偏好设置
        
        Args:
            category: 要重置的分类，如果为None则重置所有设置
            
        Returns:
            bool: 重置是否成功
        """
        try:
            if category is None:
                # 重置所有设置
                self._preferences = self._get_default_preferences()
                self.logger.info("已重置所有用户偏好设置")
            else:
                # 重置指定分类
                default_prefs = self._get_default_preferences()
                if category in default_prefs:
                    self._preferences[category] = default_prefs[category]
                    self.logger.info(f"已重置用户偏好设置分类: {category}")
                else:
                    self.logger.warning(f"未知的偏好设置分类: {category}")
                    return False
            
            # 更新元数据
            self._preferences["_metadata"]["last_updated"] = datetime.now().isoformat()
            
            return self.save_settings()
            
        except Exception as e:
            self.logger.error(f"重置偏好设置失败: {e}")
            return False
    
    def get_all_settings(self) -> Dict[str, Any]:
        """
        获取所有偏好设置
        
        Returns:
            Dict[str, Any]: 所有偏好设置
        """
        return self._preferences.copy()
    
    def has_setting(self, key_path: str) -> bool:
        """
        检查是否存在指定的设置
        
        Args:
            key_path: 设置键路径
            
        Returns:
            bool: 是否存在设置
        """
        try:
            keys = key_path.split('.')
            value = self._preferences
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def delete_setting(self, key_path: str) -> bool:
        """
        删除指定的设置
        
        Args:
            key_path: 设置键路径
            
        Returns:
            bool: 删除是否成功
        """
        try:
            keys = key_path.split('.')
            current = self._preferences
            
            # 导航到父级位置
            for key in keys[:-1]:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return False  # 路径不存在
            
            # 删除最终键
            if isinstance(current, dict) and keys[-1] in current:
                del current[keys[-1]]
                
                # 更新元数据
                if "_metadata" in self._preferences:
                    self._preferences["_metadata"]["last_updated"] = datetime.now().isoformat()
                
                self.logger.debug(f"已删除设置: {key_path}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"删除设置失败 {key_path}: {e}")
            return False
    
    def export_settings(self, export_file: Union[str, Path]) -> bool:
        """
        导出偏好设置到文件
        
        Args:
            export_file: 导出文件路径
            
        Returns:
            bool: 导出是否成功
        """
        try:
            export_path = Path(export_file)
            export_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self._preferences, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"用户偏好设置已导出: {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出偏好设置失败: {e}")
            return False
    
    def import_settings(self, import_file: Union[str, Path], merge: bool = True) -> bool:
        """
        从文件导入偏好设置
        
        Args:
            import_file: 导入文件路径
            merge: 是否与现有设置合并，False则完全替换
            
        Returns:
            bool: 导入是否成功
        """
        try:
            import_path = Path(import_file)
            
            if not import_path.exists():
                self.logger.error(f"导入文件不存在: {import_path}")
                return False
            
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_prefs = json.load(f)
            
            if merge:
                # 合并设置
                self._merge_preferences(self._preferences, imported_prefs)
            else:
                # 完全替换
                self._preferences = imported_prefs
            
            # 更新元数据
            if "_metadata" not in self._preferences:
                self._preferences["_metadata"] = {}
            self._preferences["_metadata"]["last_updated"] = datetime.now().isoformat()
            self._preferences["_metadata"]["imported_from"] = str(import_path)
            
            self.logger.info(f"用户偏好设置已导入: {import_path}")
            return self.save_settings()
            
        except Exception as e:
            self.logger.error(f"导入偏好设置失败: {e}")
            return False
    
    def _merge_preferences(self, target: Dict[str, Any], source: Dict[str, Any]):
        """
        合并偏好设置字典
        
        Args:
            target: 目标字典（会被修改）
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_preferences(target[key], value)
            else:
                target[key] = value


if __name__ == "__main__":
    """测试用户偏好设置"""
    
    print("🧪 测试用户偏好设置模块")
    print("=" * 40)
    
    # 创建用户偏好管理器
    prefs = UserPreferences("test_user")
    
    # 测试设置和获取
    print("1. 测试设置和获取偏好...")
    prefs.set_setting("ui.use_unified_import_interface", True)
    prefs.set_setting("data_import.max_preview_rows", 200)
    
    value1 = prefs.get_setting("ui.use_unified_import_interface")
    value2 = prefs.get_setting("data_import.max_preview_rows")
    
    print(f"   ui.use_unified_import_interface: {value1}")
    print(f"   data_import.max_preview_rows: {value2}")
    
    # 测试保存
    print("2. 测试保存偏好...")
    result = prefs.save_settings()
    print(f"   保存结果: {result}")
    
    # 测试重新加载
    print("3. 测试重新加载...")
    prefs2 = UserPreferences("test_user")
    value3 = prefs2.get_setting("ui.use_unified_import_interface")
    print(f"   重新加载的值: {value3}")
    
    # 清理测试文件
    import os
    try:
        os.remove("state/user/preferences_test_user.json")
        os.remove("state/user/preferences_test_user.json.bak")
        print("4. 测试文件已清理")
    except:
        pass
    
    print()
    print("🎉 用户偏好设置模块测试完成！")