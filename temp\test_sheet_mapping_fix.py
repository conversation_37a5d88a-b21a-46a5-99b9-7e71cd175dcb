#!/usr/bin/env python3
"""
测试Sheet选择和字段映射修复
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.gui.unified_data_import_window import UnifiedDataImportWindow
from src.utils.log_config import setup_logger

def test_sheet_mapping_fix():
    """测试Sheet选择和字段映射修复"""
    logger = setup_logger(__name__)
    
    app = QApplication([])
    
    try:
        # 创建窗口
        window = UnifiedDataImportWindow()
        window.show()
        
        def test_mapping_functionality():
            logger.info("开始测试Sheet选择和字段映射功能...")
            
            # 测试1: 检查是否有文件路径
            if hasattr(window, 'current_file_path') and window.current_file_path:
                logger.info(f"当前文件路径: {window.current_file_path}")
                
                # 测试2: 检查Sheet信息
                if hasattr(window.sheet_management_widget, 'sheet_info'):
                    sheet_info = window.sheet_management_widget.sheet_info
                    logger.info(f"Sheet信息: {len(sheet_info)} 个工作表")
                    
                    for sheet in sheet_info:
                        logger.info(f"  - {sheet.get('name', 'Unknown')}: {sheet.get('row_count', 0)} 行")
                
                # 测试3: 模拟选择Sheet
                if hasattr(window.sheet_management_widget, 'sheet_tree'):
                    tree = window.sheet_management_widget.sheet_tree
                    if tree.topLevelItemCount() > 0:
                        # 选择第一个Sheet
                        first_item = tree.topLevelItem(0)
                        if first_item:
                            first_item.setCheckState(0, 2)  # Qt.Checked
                            tree.setCurrentItem(first_item)
                            
                            sheet_data = first_item.data(0, 256)  # Qt.UserRole
                            if sheet_data:
                                logger.info(f"选择了Sheet: {sheet_data.get('name', 'Unknown')}")
                                
                                # 手动触发选择变化
                                window.sheet_management_widget._on_sheet_selection_changed()
                                
                                # 检查映射表格是否有数据
                                QTimer.singleShot(1000, lambda: check_mapping_table(window, logger))
                            else:
                                logger.warning("Sheet数据为空")
                    else:
                        logger.warning("没有可选择的Sheet")
            else:
                logger.warning("没有当前文件路径，请先选择Excel文件")
            
            # 5秒后关闭
            QTimer.singleShot(5000, app.quit)
        
        def check_mapping_table(window, logger):
            """检查映射表格状态"""
            mapping_table = window.mapping_tab.mapping_table
            row_count = mapping_table.rowCount()
            
            logger.info(f"映射表格行数: {row_count}")
            
            if row_count > 0:
                logger.info("✅ 映射表格已填充数据")
                
                # 检查前几行的内容
                for row in range(min(3, row_count)):
                    excel_item = mapping_table.item(row, 0)
                    if excel_item:
                        excel_header = excel_item.text()
                        logger.info(f"  行 {row}: Excel列名 = {excel_header}")
                    else:
                        logger.warning(f"  行 {row}: Excel列名为空")
                
                # 测试智能映射按钮
                logger.info("测试智能映射按钮...")
                window.mapping_tab.smart_mapping_btn.click()
                
            else:
                logger.warning("❌ 映射表格仍然为空")
        
        # 2秒后开始测试
        QTimer.singleShot(2000, test_mapping_functionality)
        
        app.exec_()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sheet_mapping_fix()
