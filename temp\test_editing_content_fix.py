#!/usr/bin/env python3
"""
测试编辑内容保持修复
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication
from src.gui.unified_data_import_window import UnifiedDataImportWindow
import logging

def test_editing_content_fix():
    """测试编辑内容保持修复"""
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    app = QApplication([])
    
    # 创建统一数据导入窗口
    window = UnifiedDataImportWindow()
    window.show()
    
    # 模拟加载Excel字段头
    test_headers = [
        "人员 代码",
        "姓 名", 
        "基本\n工资",
        "年龄(岁)",
        "2025年津贴"
    ]
    
    print("=== 编辑内容保持修复测试 ===")
    print()
    print("修复内容：")
    print("1. ✅ 修复_update_mapping_config中的cellWidget错误")
    print("2. ✅ 修复智能映射生成中的cellWidget错误")
    print("3. ✅ 修复模板加载中的cellWidget错误")
    print("4. ✅ 修复自动映射应用中的cellWidget错误")
    print()
    print("问题原因：")
    print("- 数据库字段列已从QComboBox改为QTableWidgetItem")
    print("- 但多处代码仍使用cellWidget()获取值")
    print("- 导致编辑时内容被清空")
    print()
    
    # 加载测试数据
    window.load_excel_headers(test_headers, "salary_table")
    
    print("测试步骤：")
    print("1. 查看表格中的初始内容")
    print("2. 双击'数据库字段'列的任意单元格")
    print("3. 检查编辑器中是否显示原有内容")
    print("4. 修改内容并按Enter确认")
    print("5. 检查内容是否正确保存")
    print()
    
    # 检查初始内容
    print("初始表格内容：")
    for row in range(window.mapping_table.rowCount()):
        excel_item = window.mapping_table.item(row, 0)
        db_item = window.mapping_table.item(row, 1)
        display_item = window.mapping_table.item(row, 2)
        
        excel_text = excel_item.text() if excel_item else "N/A"
        db_text = db_item.text() if db_item else "N/A"
        display_text = display_item.text() if display_item else "N/A"
        
        print(f"  行{row+1}: Excel='{excel_text}' | 数据库='{db_text}' | 显示='{display_text}'")
    
    print()
    print("预期结果：")
    print("- 双击编辑时，编辑器显示当前单元格的内容")
    print("- 编辑过程中内容不会消失")
    print("- 编辑完成后内容正确保存")
    print("- 不再出现空白编辑器的问题")
    print()
    print("请手动测试双击编辑功能...")
    
    # 连接编辑信号来监控
    def on_item_changed(item):
        row = item.row()
        col = item.column()
        text = item.text()
        col_names = ["Excel列名", "数据库字段", "显示名称", "数据类型", "是否必需", "验证状态"]
        print(f"✅ 编辑成功：{col_names[col]} (行{row+1}) -> '{text}'")
    
    window.mapping_table.itemChanged.connect(on_item_changed)
    
    app.exec_()

if __name__ == "__main__":
    test_editing_content_fix()
