"""
测试P2级别字段类型设置修复
验证字段类型能够正确设置到下拉框
"""

import sys
import os
import io
import pandas as pd
from PyQt5.QtWidgets import QApplication

# 设置输出编码
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_field_type_setting():
    """测试字段类型设置功能"""
    
    # 创建应用实例
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    try:
        from src.gui.change_data_config_dialog import ChangeDataConfigDialog
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '工号': ['001', '002', '003'],
            '姓名': ['张三', '李四', '王五'],
            '部门名称': ['财务部', '人事部', '技术部'],
            '岗位工资': [5000.0, 6000.0, 7000.0],
            '薪级工资': [2000.0, 2500.0, 3000.0],
            '津贴': [500.0, 600.0, 700.0],
            '补贴': [300.0, 400.0, 500.0],
            '奖金': [1000.0, 1500.0, 2000.0],
            '人员类别': ['管理人员', '技术人员', '行政人员']
        })
        
        print("=" * 50)
        print("P2级别字段类型设置测试")
        print("=" * 50)
        
        # 创建对话框实例
        dialog = ChangeDataConfigDialog(excel_data=test_data)
        
        # 测试1：检查初始字段类型推断
        print("\n测试1：初始字段类型推断")
        print("-" * 30)
        
        field_type_map = {}
        for i in range(dialog.field_table.rowCount()):
            field_name = dialog.field_table.item(i, 0).text()
            type_combo = dialog.field_table.cellWidget(i, 1)
            if type_combo:
                current_data = type_combo.currentData()
                current_text = type_combo.currentText()
                field_type_map[field_name] = current_data
                
                # 检查是否正确推断
                is_correct = False
                if '工号' in field_name and current_data == 'employee_id_string':
                    is_correct = True
                elif '姓名' in field_name and current_data == 'name_string':
                    is_correct = True
                elif '工资' in field_name and current_data == 'salary_float':
                    is_correct = True
                elif '津贴' in field_name and current_data == 'salary_float':
                    is_correct = True
                elif '补贴' in field_name and current_data == 'salary_float':
                    is_correct = True
                elif '奖金' in field_name and current_data == 'salary_float':
                    is_correct = True
                elif '部门' in field_name and current_data == 'text_string':
                    is_correct = True
                elif '类别' in field_name and current_data == 'text_string':
                    is_correct = True
                
                status = "✓" if is_correct else "✗"
                print(f"{status} {field_name}: {current_data} ({current_text})")
        
        # 测试2：测试手动设置字段类型
        print("\n测试2：手动设置字段类型")
        print("-" * 30)
        
        test_cases = [
            (0, 'employee_id_string'),  # 第一个字段设置为工号
            (1, 'name_string'),          # 第二个字段设置为姓名
            (3, 'salary_float'),         # 第四个字段设置为工资
        ]
        
        success_count = 0
        for row, target_type in test_cases:
            if row < dialog.field_table.rowCount():
                field_name = dialog.field_table.item(row, 0).text()
                type_combo = dialog.field_table.cellWidget(row, 1)
                if type_combo:
                    # 使用辅助方法设置
                    result = dialog._set_combo_by_data(type_combo, target_type)
                    if result and type_combo.currentData() == target_type:
                        print(f"✓ {field_name} -> {target_type}: 设置成功")
                        success_count += 1
                    else:
                        print(f"✗ {field_name} -> {target_type}: 设置失败")
        
        print(f"\n手动设置成功率: {success_count}/{len(test_cases)}")
        
        # 测试3：测试模板应用
        print("\n测试3：模板应用字段类型")
        print("-" * 30)
        
        if dialog.template_combo.count() > 1:
            # 应用标准模板
            dialog.template_combo.setCurrentIndex(1)
            
            # 检查字段类型是否正确应用
            template_fields = {
                '工号': 'employee_id_string',
                '姓名': 'name_string',
                '岗位工资': 'salary_float',
                '薪级工资': 'salary_float'
            }
            
            applied_count = 0
            for i in range(dialog.field_table.rowCount()):
                field_name = dialog.field_table.item(i, 0).text()
                if field_name in template_fields:
                    type_combo = dialog.field_table.cellWidget(i, 1)
                    if type_combo and type_combo.currentData() == template_fields[field_name]:
                        print(f"✓ {field_name}: 模板应用成功")
                        applied_count += 1
                    else:
                        current = type_combo.currentData() if type_combo else "None"
                        print(f"✗ {field_name}: 期望 {template_fields[field_name]}, 实际 {current}")
            
            print(f"\n模板应用成功率: {applied_count}/{len(template_fields)}")
        
        # 测试4：测试数据格式化（验证字段类型是否正确工作）
        print("\n测试4：数据格式化验证")
        print("-" * 30)
        
        error_count = 0
        try:
            dialog.refresh_preview()
            print("✓ 数据格式化成功，没有类型转换错误")
        except Exception as e:
            error_count += 1
            print(f"✗ 数据格式化失败: {e}")
        
        # 总结
        print("\n" + "=" * 50)
        print("测试总结")
        print("=" * 50)
        
        if error_count == 0:
            print("✅ P2级别修复验证通过！")
            print("说明：")
            print("1. 字段类型能够正确推断")
            print("2. 字段类型能够正确设置")
            print("3. 模板应用功能正常")
            print("4. 数据格式化没有错误")
            return True
        else:
            print(f"⚠️ 有{error_count}个测试失败")
            return False
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if app:
            app.quit()

def main():
    """主测试函数"""
    return test_field_type_setting()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)