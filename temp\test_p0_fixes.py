#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试P0级修复效果
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("测试1: 验证QWidget导入修复")
try:
    from src.gui.import_settings_dialog import ImportSettingsDialog
    print("[OK] import_settings_dialog.py导入成功")
except ImportError as e:
    print(f"[FAIL] 导入失败: {e}")

print("\n测试2: 验证模板功能修复")
try:
    from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager
    manager = ChangeDataConfigManager()
    
    # 列出模板
    templates = manager.list_templates()
    print(f"[OK] 找到 {len(templates)} 个模板:")
    for t in templates:
        name = t['name']
        key = t['key']
        print(f"  - {name} (key: {key})")
    
    # 测试获取模板
    standard_template = manager.get_template("standard")
    if standard_template:
        name = standard_template.get('name')
        print(f"[OK] 成功获取standard模板: {name}")
    else:
        print("[FAIL] 无法获取standard模板")
        
    comprehensive_template = manager.get_template("comprehensive")
    if comprehensive_template:
        name = comprehensive_template.get('name')
        print(f"[OK] 成功获取comprehensive模板: {name}")
    else:
        print("[FAIL] 无法获取comprehensive模板")
        
except Exception as e:
    print(f"[FAIL] 模板管理器测试失败: {e}")

print("\n测试3: 验证配置对话框功能")
try:
    from PyQt5.QtWidgets import QApplication, QComboBox
    from src.gui.change_data_config_dialog import ChangeDataConfigDialog
    
    app = QApplication([])
    dialog = ChangeDataConfigDialog()
    
    # 验证模板下拉框
    print(f"[OK] 模板下拉框项目数: {dialog.template_combo.count()}")
    
    # 验证模板数据存储
    for i in range(1, dialog.template_combo.count()):
        name = dialog.template_combo.itemText(i)
        key = dialog.template_combo.itemData(i)
        print(f"  - {name} -> {key}")
    
    print("[OK] 配置对话框创建成功")
    
except Exception as e:
    print(f"[FAIL] 配置对话框测试失败: {e}")

print("\n所有P0级修复验证完成！")
