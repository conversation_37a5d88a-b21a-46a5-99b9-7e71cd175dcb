#!/usr/bin/env python3
"""
统一数据导入窗口测试文件
用于测试第一阶段的基础架构实现
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from src.gui.unified_data_import_window import UnifiedDataImportWindow


def test_window_creation():
    """测试窗口创建"""
    app = QApplication(sys.argv)
    
    try:
        # 创建窗口
        window = UnifiedDataImportWindow()
        print("✅ 统一导入窗口创建成功")
        
        # 测试核心组件初始化
        assert window.import_manager is not None, "导入管理器未初始化"
        assert window.import_manager.mapping_engine is not None, "智能映射引擎未初始化"
        assert window.import_manager.template_manager is not None, "模板管理器未初始化"
        assert window.import_manager.validation_engine is not None, "验证引擎未初始化"
        print("✅ 核心组件初始化成功")
        
        # 测试UI组件
        assert window.top_toolbar is not None, "顶部工具栏未创建"
        assert window.main_content is not None, "主内容区域未创建"
        assert window.bottom_panel is not None, "底部面板未创建"
        print("✅ UI组件创建成功")
        
        # 测试布局
        assert window.sheet_management_widget is not None, "Sheet管理组件未创建"
        assert window.mapping_tab is not None, "映射配置选项卡未创建"
        assert window.processing_tab is not None, "数据处理选项卡未创建"
        assert window.preview_tab is not None, "预览验证选项卡未创建"
        print("✅ 选项卡布局创建成功")
        
        print("\n🎉 第一阶段基础架构测试全部通过！")
        
        # 显示窗口进行手动测试
        window.show()
        print("\n📱 窗口已显示，可进行手动界面测试")
        print("关闭窗口将结束测试")
        
        # 运行应用
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def test_core_components():
    """测试核心组件功能"""
    from src.gui.core import SmartMappingEngine, TemplateManager, ValidationEngine
    
    try:
        # 测试智能映射引擎
        mapping_engine = SmartMappingEngine()
        excel_headers = ["姓名", "工号", "基本工资", "津贴", "实发合计"]
        mappings = mapping_engine.generate_smart_mapping(excel_headers, "💰 工资表")
        assert len(mappings) == len(excel_headers), "映射结果数量不匹配"
        print("✅ 智能映射引擎测试通过")
        
        # 测试模板管理器
        template_manager = TemplateManager()
        templates = template_manager.get_all_templates()
        assert len(templates) > 0, "没有找到任何模板"
        salary_template = template_manager.get_template_for_table_type("💰 工资表")
        assert salary_template is not None, "工资表模板未找到"
        print("✅ 模板管理器测试通过")
        
        # 测试验证引擎
        validation_engine = ValidationEngine()
        mapping_config = {
            "姓名": {"target_field": "姓名", "data_type": "VARCHAR(100)", "is_required": True},
            "工号": {"target_field": "工号", "data_type": "VARCHAR(50)", "is_required": True}
        }
        report = validation_engine.validate_import_configuration(mapping_config, "💰 工资表")
        assert report is not None, "验证报告为空"
        print("✅ 验证引擎测试通过")
        
        print("\n🎉 核心组件功能测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 核心组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始统一数据导入窗口第一阶段测试\n")
    
    # 先测试核心组件
    print("=" * 50)
    print("测试核心组件功能")
    print("=" * 50)
    if not test_core_components():
        sys.exit(1)
    
    # 再测试窗口界面
    print("\n" + "=" * 50)
    print("测试窗口界面")
    print("=" * 50)
    exit_code = test_window_creation()
    sys.exit(exit_code)
