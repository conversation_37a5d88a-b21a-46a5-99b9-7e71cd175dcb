#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量配置管理器

功能说明:
- 批量应用配置到多个Sheet
- 批量应用模板到多个Sheet
- 配置复制和同步
- 批量操作的进度跟踪和结果报告
"""

from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from src.utils.log_config import setup_logger


class BatchOperationType(Enum):
    """批量操作类型"""
    COPY_CONFIG = "copy_config"  # 复制配置
    APPLY_TEMPLATE = "apply_template"  # 应用模板
    SMART_RECOMMEND = "smart_recommend"  # 智能推荐
    BULK_UPDATE = "bulk_update"  # 批量更新


@dataclass
class BatchOperationResult:
    """批量操作结果"""
    operation_type: BatchOperationType
    total_count: int
    success_count: int
    failed_count: int
    skipped_count: int = 0
    
    # 详细结果
    success_sheets: List[str] = field(default_factory=list)
    failed_sheets: List[Tuple[str, str]] = field(default_factory=list)  # (sheet_name, error)
    skipped_sheets: List[Tuple[str, str]] = field(default_factory=list)  # (sheet_name, reason)
    
    # 操作信息
    operation_time: datetime = field(default_factory=datetime.now)
    duration_seconds: float = 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_count == 0:
            return 0.0
        return self.success_count / self.total_count
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'operation_type': self.operation_type.value,
            'total_count': self.total_count,
            'success_count': self.success_count,
            'failed_count': self.failed_count,
            'skipped_count': self.skipped_count,
            'success_rate': self.success_rate,
            'success_sheets': self.success_sheets,
            'failed_sheets': self.failed_sheets,
            'skipped_sheets': self.skipped_sheets,
            'operation_time': self.operation_time.isoformat(),
            'duration_seconds': self.duration_seconds
        }


class BatchConfigManager:
    """批量配置管理器"""
    
    def __init__(self, sheet_config_manager):
        """
        初始化批量配置管理器
        
        Args:
            sheet_config_manager: Sheet配置管理器实例
        """
        self.logger = setup_logger(__name__)
        self.sheet_manager = sheet_config_manager
        
        # 操作历史
        self.operation_history: List[BatchOperationResult] = []
        
        self.logger.info("批量配置管理器初始化完成")
    
    def copy_config_to_sheets(self, source_sheet: str, target_sheets: List[str],
                             exclude_fields: Optional[List[str]] = None,
                             progress_callback: Optional[Callable[[int, int], None]] = None) -> BatchOperationResult:
        """
        将源Sheet的配置复制到目标Sheet列表
        
        Args:
            source_sheet: 源Sheet名称
            target_sheets: 目标Sheet名称列表
            exclude_fields: 排除的字段列表
            progress_callback: 进度回调函数 (current, total)
            
        Returns:
            批量操作结果
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始批量复制配置: {source_sheet} -> {len(target_sheets)} 个Sheet")
            
            # 获取源配置
            source_config = self.sheet_manager.get_or_create_config(source_sheet)
            if not source_config:
                raise ValueError(f"源Sheet '{source_sheet}' 配置不存在")
            
            # 初始化结果
            result = BatchOperationResult(
                operation_type=BatchOperationType.COPY_CONFIG,
                total_count=len(target_sheets)
            )
            
            exclude_fields = exclude_fields or []
            exclude_fields.extend(['sheet_name', 'created_time', 'modified_time'])  # 总是排除这些字段
            
            # 批量复制
            for i, target_sheet in enumerate(target_sheets):
                try:
                    if progress_callback:
                        progress_callback(i + 1, len(target_sheets))
                    
                    # 跳过源Sheet自身
                    if target_sheet == source_sheet:
                        result.skipped_sheets.append((target_sheet, "源Sheet，跳过"))
                        result.skipped_count += 1
                        continue
                    
                    # 获取目标配置
                    target_config = self.sheet_manager.get_or_create_config(target_sheet)
                    
                    # 复制配置
                    self._copy_config_fields(source_config, target_config, exclude_fields)
                    
                    # 保存配置
                    success = self.sheet_manager.update_config(target_sheet, **target_config.__dict__)
                    
                    if success:
                        result.success_sheets.append(target_sheet)
                        result.success_count += 1
                        self.logger.debug(f"配置复制成功: {target_sheet}")
                    else:
                        result.failed_sheets.append((target_sheet, "保存配置失败"))
                        result.failed_count += 1
                
                except Exception as e:
                    error_msg = str(e)
                    result.failed_sheets.append((target_sheet, error_msg))
                    result.failed_count += 1
                    self.logger.error(f"复制配置到 '{target_sheet}' 失败: {error_msg}")
            
            # 计算耗时
            end_time = datetime.now()
            result.duration_seconds = (end_time - start_time).total_seconds()
            
            # 记录操作历史
            self.operation_history.append(result)
            
            self.logger.info(f"批量复制配置完成: 成功 {result.success_count}/{result.total_count}")
            return result
            
        except Exception as e:
            self.logger.error(f"批量复制配置失败: {e}")
            raise
    
    def apply_template_to_sheets(self, template_id: str, target_sheets: List[str],
                                progress_callback: Optional[Callable[[int, int], None]] = None) -> BatchOperationResult:
        """
        将模板应用到多个Sheet
        
        Args:
            template_id: 模板ID
            target_sheets: 目标Sheet名称列表
            progress_callback: 进度回调函数
            
        Returns:
            批量操作结果
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始批量应用模板: {template_id} -> {len(target_sheets)} 个Sheet")
            
            # 验证模板存在
            template = self.sheet_manager.template_manager.get_template(template_id)
            if not template:
                raise ValueError(f"模板 '{template_id}' 不存在")
            
            # 初始化结果
            result = BatchOperationResult(
                operation_type=BatchOperationType.APPLY_TEMPLATE,
                total_count=len(target_sheets)
            )
            
            # 批量应用
            for i, target_sheet in enumerate(target_sheets):
                try:
                    if progress_callback:
                        progress_callback(i + 1, len(target_sheets))
                    
                    # 应用模板
                    success = self.sheet_manager.apply_template_to_sheet(target_sheet, template_id)
                    
                    if success:
                        result.success_sheets.append(target_sheet)
                        result.success_count += 1
                        self.logger.debug(f"模板应用成功: {target_sheet}")
                    else:
                        result.failed_sheets.append((target_sheet, "应用模板失败"))
                        result.failed_count += 1
                
                except Exception as e:
                    error_msg = str(e)
                    result.failed_sheets.append((target_sheet, error_msg))
                    result.failed_count += 1
                    self.logger.error(f"应用模板到 '{target_sheet}' 失败: {error_msg}")
            
            # 计算耗时
            end_time = datetime.now()
            result.duration_seconds = (end_time - start_time).total_seconds()
            
            # 记录操作历史
            self.operation_history.append(result)
            
            self.logger.info(f"批量应用模板完成: 成功 {result.success_count}/{result.total_count}")
            return result
            
        except Exception as e:
            self.logger.error(f"批量应用模板失败: {e}")
            raise
    
    def apply_smart_recommendations(self, target_sheets: List[str],
                                   progress_callback: Optional[Callable[[int, int], None]] = None) -> BatchOperationResult:
        """
        为多个Sheet应用智能推荐配置
        
        Args:
            target_sheets: 目标Sheet名称列表
            progress_callback: 进度回调函数
            
        Returns:
            批量操作结果
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始批量应用智能推荐: {len(target_sheets)} 个Sheet")
            
            # 初始化结果
            result = BatchOperationResult(
                operation_type=BatchOperationType.SMART_RECOMMEND,
                total_count=len(target_sheets)
            )
            
            # 批量应用智能推荐
            for i, target_sheet in enumerate(target_sheets):
                try:
                    if progress_callback:
                        progress_callback(i + 1, len(target_sheets))
                    
                    # 应用智能推荐
                    success = self.sheet_manager.apply_smart_recommendation(target_sheet)
                    
                    if success:
                        result.success_sheets.append(target_sheet)
                        result.success_count += 1
                        self.logger.debug(f"智能推荐应用成功: {target_sheet}")
                    else:
                        result.failed_sheets.append((target_sheet, "应用智能推荐失败"))
                        result.failed_count += 1
                
                except Exception as e:
                    error_msg = str(e)
                    result.failed_sheets.append((target_sheet, error_msg))
                    result.failed_count += 1
                    self.logger.error(f"应用智能推荐到 '{target_sheet}' 失败: {error_msg}")
            
            # 计算耗时
            end_time = datetime.now()
            result.duration_seconds = (end_time - start_time).total_seconds()
            
            # 记录操作历史
            self.operation_history.append(result)
            
            self.logger.info(f"批量应用智能推荐完成: 成功 {result.success_count}/{result.total_count}")
            return result
            
        except Exception as e:
            self.logger.error(f"批量应用智能推荐失败: {e}")
            raise

    def bulk_update_config(self, target_sheets: List[str], config_updates: Dict[str, Any],
                          progress_callback: Optional[Callable[[int, int], None]] = None) -> BatchOperationResult:
        """
        批量更新Sheet配置

        Args:
            target_sheets: 目标Sheet名称列表
            config_updates: 要更新的配置项字典
            progress_callback: 进度回调函数

        Returns:
            批量操作结果
        """
        start_time = datetime.now()

        try:
            self.logger.info(f"开始批量更新配置: {len(target_sheets)} 个Sheet, {len(config_updates)} 个配置项")

            # 初始化结果
            result = BatchOperationResult(
                operation_type=BatchOperationType.BULK_UPDATE,
                total_count=len(target_sheets)
            )

            # 批量更新
            for i, target_sheet in enumerate(target_sheets):
                try:
                    if progress_callback:
                        progress_callback(i + 1, len(target_sheets))

                    # 更新配置
                    success = self.sheet_manager.update_config(target_sheet, **config_updates)

                    if success:
                        result.success_sheets.append(target_sheet)
                        result.success_count += 1
                        self.logger.debug(f"配置更新成功: {target_sheet}")
                    else:
                        result.failed_sheets.append((target_sheet, "更新配置失败"))
                        result.failed_count += 1

                except Exception as e:
                    error_msg = str(e)
                    result.failed_sheets.append((target_sheet, error_msg))
                    result.failed_count += 1
                    self.logger.error(f"更新配置 '{target_sheet}' 失败: {error_msg}")

            # 计算耗时
            end_time = datetime.now()
            result.duration_seconds = (end_time - start_time).total_seconds()

            # 记录操作历史
            self.operation_history.append(result)

            self.logger.info(f"批量更新配置完成: 成功 {result.success_count}/{result.total_count}")
            return result

        except Exception as e:
            self.logger.error(f"批量更新配置失败: {e}")
            raise

    def get_similar_sheets(self, reference_sheet: str, similarity_threshold: float = 0.7) -> List[str]:
        """
        获取与参考Sheet相似的Sheet列表

        Args:
            reference_sheet: 参考Sheet名称
            similarity_threshold: 相似度阈值 (0-1)

        Returns:
            相似Sheet名称列表
        """
        try:
            all_configs = self.sheet_manager.get_all_configs()
            reference_config = all_configs.get(reference_sheet)

            if not reference_config:
                return []

            similar_sheets = []

            for sheet_name, config in all_configs.items():
                if sheet_name == reference_sheet:
                    continue

                # 计算配置相似度
                similarity = self._calculate_config_similarity(reference_config, config)

                if similarity >= similarity_threshold:
                    similar_sheets.append(sheet_name)

            return similar_sheets

        except Exception as e:
            self.logger.error(f"获取相似Sheet失败: {e}")
            return []

    def preview_batch_operation(self, operation_type: BatchOperationType,
                               target_sheets: List[str], **kwargs) -> Dict[str, Any]:
        """
        预览批量操作的影响

        Args:
            operation_type: 操作类型
            target_sheets: 目标Sheet列表
            **kwargs: 操作参数

        Returns:
            预览结果字典
        """
        try:
            preview = {
                'operation_type': operation_type.value,
                'target_count': len(target_sheets),
                'target_sheets': target_sheets,
                'estimated_duration': len(target_sheets) * 0.1,  # 估算每个Sheet 0.1秒
                'warnings': [],
                'recommendations': []
            }

            # 根据操作类型进行特定检查
            if operation_type == BatchOperationType.COPY_CONFIG:
                source_sheet = kwargs.get('source_sheet')
                if source_sheet:
                    source_config = self.sheet_manager.get_or_create_config(source_sheet)
                    preview['source_config'] = {
                        'header_row': source_config.header_row,
                        'data_start_row': source_config.data_start_row,
                        'is_enabled': source_config.is_enabled,
                        'remove_summary_rows': source_config.remove_summary_rows
                    }

                    # 检查是否有禁用的源配置
                    if not source_config.is_enabled:
                        preview['warnings'].append("源Sheet配置已禁用，将导致目标Sheet也被禁用")

            elif operation_type == BatchOperationType.APPLY_TEMPLATE:
                template_id = kwargs.get('template_id')
                if template_id:
                    template = self.sheet_manager.template_manager.get_template(template_id)
                    if template:
                        preview['template_info'] = {
                            'name': template.name,
                            'description': template.description,
                            'category': template.category,
                            'usage_count': template.usage_count
                        }

                        # 检查模板是否会禁用Sheet
                        if not template.config_data.get('is_enabled', True):
                            preview['warnings'].append("模板将禁用目标Sheet的导入功能")
                    else:
                        preview['warnings'].append("指定的模板不存在")

            # 检查目标Sheet状态
            enabled_count = 0
            disabled_count = 0

            for sheet_name in target_sheets:
                config = self.sheet_manager.get_or_create_config(sheet_name)
                if config.is_enabled:
                    enabled_count += 1
                else:
                    disabled_count += 1

            preview['current_status'] = {
                'enabled_sheets': enabled_count,
                'disabled_sheets': disabled_count
            }

            # 添加建议
            if disabled_count > 0:
                preview['recommendations'].append(f"有 {disabled_count} 个Sheet当前已禁用")

            if len(target_sheets) > 10:
                preview['recommendations'].append("目标Sheet数量较多，建议分批操作")

            return preview

        except Exception as e:
            self.logger.error(f"预览批量操作失败: {e}")
            return {'error': str(e)}

    def get_operation_history(self, limit: int = 10) -> List[BatchOperationResult]:
        """
        获取操作历史

        Args:
            limit: 返回数量限制

        Returns:
            操作历史列表
        """
        return self.operation_history[-limit:] if limit > 0 else self.operation_history

    def get_operation_statistics(self) -> Dict[str, Any]:
        """
        获取操作统计信息

        Returns:
            统计信息字典
        """
        if not self.operation_history:
            return {'total_operations': 0}

        stats = {
            'total_operations': len(self.operation_history),
            'operation_types': {},
            'total_sheets_processed': 0,
            'total_success_count': 0,
            'total_failed_count': 0,
            'average_success_rate': 0.0,
            'total_duration': 0.0
        }

        for result in self.operation_history:
            # 操作类型统计
            op_type = result.operation_type.value
            if op_type not in stats['operation_types']:
                stats['operation_types'][op_type] = 0
            stats['operation_types'][op_type] += 1

            # 累计统计
            stats['total_sheets_processed'] += result.total_count
            stats['total_success_count'] += result.success_count
            stats['total_failed_count'] += result.failed_count
            stats['total_duration'] += result.duration_seconds

        # 计算平均成功率
        if stats['total_sheets_processed'] > 0:
            stats['average_success_rate'] = stats['total_success_count'] / stats['total_sheets_processed']

        return stats

    def _copy_config_fields(self, source_config, target_config, exclude_fields: List[str]):
        """复制配置字段"""
        for field_name in dir(source_config):
            if (not field_name.startswith('_') and
                field_name not in exclude_fields and
                hasattr(target_config, field_name)):

                source_value = getattr(source_config, field_name)
                if not callable(source_value):  # 排除方法
                    setattr(target_config, field_name, source_value)

    def _calculate_config_similarity(self, config1, config2) -> float:
        """计算两个配置的相似度"""
        try:
            # 比较的关键字段
            key_fields = [
                'header_row', 'data_start_row', 'has_header', 'remove_summary_rows',
                'skip_empty_rows', 'trim_whitespace', 'normalize_numbers', 'is_enabled'
            ]

            matches = 0
            total_fields = len(key_fields)

            for field in key_fields:
                if (hasattr(config1, field) and hasattr(config2, field)):
                    value1 = getattr(config1, field)
                    value2 = getattr(config2, field)
                    if value1 == value2:
                        matches += 1

            return matches / total_fields if total_fields > 0 else 0.0

        except Exception as e:
            self.logger.error(f"计算配置相似度失败: {e}")
            return 0.0
