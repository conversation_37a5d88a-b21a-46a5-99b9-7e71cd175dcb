# 异动表智能分析器技术实现细节

**文档创建时间**: 2025-08-20  
**关联文档**: 异动表处理机制完整改进方案  
**实现目标**: 详细的智能分析器代码实现和算法说明

## 📋 核心组件设计

### 1. ChangeDataAnalyzer 类结构

```python
class ChangeDataAnalyzer:
    """异动表智能分析器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.confidence_threshold = 0.7  # 置信度阈值
        self.field_type_cache = {}  # 字段类型缓存
        
        # 预定义关键词库
        self.employee_id_keywords = ['工号', '人员代码', '员工编号', '职工编号', 'id', 'code', '编号']
        self.salary_keywords = ['工资', '薪', '津贴', '补贴', '奖金', '绩效', '公积金', '基金', '费用']
        self.date_keywords = ['年', '月', '日期', '时间', 'date', 'time']
        self.name_keywords = ['姓名', '名字', 'name', '人员姓名']
        self.department_keywords = ['部门', '科室', '单位', 'department', 'dept']
    
    def analyze_excel_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """主要分析方法"""
        
    def _infer_field_type(self, series: pd.Series, column_name: str) -> Tuple[str, float]:
        """字段类型推断"""
        
    def _is_employee_id(self, series: pd.Series, column_name: str) -> Tuple[bool, float]:
        """工号字段识别"""
        
    def _is_salary_field(self, series: pd.Series, column_name: str) -> Tuple[bool, float]:
        """工资字段识别"""
        
    def _is_date_field(self, series: pd.Series, column_name: str) -> Tuple[bool, float]:
        """日期字段识别"""
        
    def _is_name_field(self, series: pd.Series, column_name: str) -> Tuple[bool, float]:
        """姓名字段识别"""
        
    def _calculate_data_quality(self, series: pd.Series) -> Dict[str, float]:
        """数据质量评估"""
```

### 2. 字段类型推断算法

#### 2.1 工号字段识别算法

```python
def _is_employee_id(self, series: pd.Series, column_name: str) -> Tuple[bool, float]:
    """
    工号字段识别算法
    
    判断依据：
    1. 字段名包含工号相关关键词 (权重: 0.4)
    2. 数据为数字序列 (权重: 0.3)
    3. 长度相对固定 (权重: 0.2)
    4. 无重复值 (权重: 0.1)
    
    Returns:
        Tuple[bool, float]: (是否为工号字段, 置信度)
    """
    confidence_score = 0.0
    
    # 1. 字段名匹配检查 (权重: 0.4)
    name_match_score = 0.0
    for keyword in self.employee_id_keywords:
        if keyword in column_name:
            name_match_score = 0.4
            break
    confidence_score += name_match_score
    
    # 2. 数据特征分析
    non_null = series.dropna()
    if len(non_null) == 0:
        return name_match_score > 0, name_match_score
    
    try:
        # 2.1 数字序列检查 (权重: 0.3)
        numeric_values = pd.to_numeric(non_null, errors='coerce')
        numeric_ratio = (len(numeric_values) - numeric_values.isna().sum()) / len(numeric_values)
        if numeric_ratio > 0.9:  # 90%以上为数字
            confidence_score += 0.3 * numeric_ratio
        
        # 2.2 长度固定性检查 (权重: 0.2)
        str_lengths = non_null.astype(str).str.len()
        length_variance = str_lengths.var()
        if length_variance < 2:  # 长度变化小于2
            confidence_score += 0.2
        elif length_variance < 5:  # 长度变化小于5
            confidence_score += 0.1
        
        # 2.3 唯一性检查 (权重: 0.1)
        unique_ratio = len(non_null.unique()) / len(non_null)
        if unique_ratio > 0.95:  # 95%以上唯一
            confidence_score += 0.1
        
        # 3. 特殊模式检查
        # 检查是否符合常见工号格式（如：8位数字）
        if len(non_null) > 0:
            sample_value = str(non_null.iloc[0])
            if len(sample_value) in [6, 7, 8, 9, 10] and sample_value.isdigit():
                confidence_score += 0.05  # 额外加分
        
        is_employee_id = confidence_score >= self.confidence_threshold
        return is_employee_id, min(confidence_score, 1.0)
        
    except Exception as e:
        self.logger.warning(f"工号字段识别异常: {e}")
        return name_match_score > 0, name_match_score
```

#### 2.2 工资字段识别算法

```python
def _is_salary_field(self, series: pd.Series, column_name: str) -> Tuple[bool, float]:
    """
    工资字段识别算法
    
    判断依据：
    1. 字段名包含工资相关关键词 (权重: 0.4)
    2. 数据为数值型 (权重: 0.3)
    3. 数值范围合理 (权重: 0.2)
    4. 精度特征 (权重: 0.1)
    
    Returns:
        Tuple[bool, float]: (是否为工资字段, 置信度)
    """
    confidence_score = 0.0
    
    # 1. 字段名匹配检查 (权重: 0.4)
    name_match_score = 0.0
    for keyword in self.salary_keywords:
        if keyword in column_name:
            name_match_score = 0.4
            break
    confidence_score += name_match_score
    
    # 2. 数据特征分析
    non_null = series.dropna()
    if len(non_null) == 0:
        return name_match_score > 0, name_match_score
    
    try:
        # 2.1 数值型检查 (权重: 0.3)
        numeric_values = pd.to_numeric(non_null, errors='coerce')
        numeric_ratio = (len(numeric_values) - numeric_values.isna().sum()) / len(numeric_values)
        if numeric_ratio > 0.8:  # 80%以上为数字
            confidence_score += 0.3 * numeric_ratio
        
        # 2.2 数值范围检查 (权重: 0.2)
        if numeric_ratio > 0.5:
            valid_numeric = numeric_values.dropna()
            if len(valid_numeric) > 0:
                min_val, max_val = valid_numeric.min(), valid_numeric.max()
                
                # 工资合理范围：0-100000
                if 0 <= min_val <= max_val <= 100000:
                    confidence_score += 0.2
                elif 0 <= min_val <= max_val <= 200000:
                    confidence_score += 0.1  # 较宽范围
        
        # 2.3 精度特征检查 (权重: 0.1)
        if numeric_ratio > 0.5:
            valid_numeric = numeric_values.dropna()
            if len(valid_numeric) > 0:
                # 检查小数位数分布
                decimal_places = []
                for val in valid_numeric:
                    str_val = str(val)
                    if '.' in str_val:
                        decimal_places.append(len(str_val.split('.')[1]))
                    else:
                        decimal_places.append(0)
                
                # 工资通常为整数或2位小数
                common_decimals = [0, 2]
                decimal_match_ratio = sum(1 for d in decimal_places if d in common_decimals) / len(decimal_places)
                confidence_score += 0.1 * decimal_match_ratio
        
        is_salary_field = confidence_score >= self.confidence_threshold
        return is_salary_field, min(confidence_score, 1.0)
        
    except Exception as e:
        self.logger.warning(f"工资字段识别异常: {e}")
        return name_match_score > 0, name_match_score
```

#### 2.3 日期字段识别算法

```python
def _is_date_field(self, series: pd.Series, column_name: str) -> Tuple[bool, float]:
    """
    日期字段识别算法
    
    判断依据：
    1. 字段名包含日期相关关键词 (权重: 0.4)
    2. 数据符合日期格式 (权重: 0.4)
    3. 数值范围合理 (权重: 0.2)
    
    Returns:
        Tuple[bool, float]: (是否为日期字段, 置信度)
    """
    confidence_score = 0.0
    
    # 1. 字段名匹配检查 (权重: 0.4)
    name_match_score = 0.0
    for keyword in self.date_keywords:
        if keyword in column_name:
            name_match_score = 0.4
            break
    confidence_score += name_match_score
    
    # 2. 数据格式分析
    non_null = series.dropna()
    if len(non_null) == 0:
        return name_match_score > 0, name_match_score
    
    try:
        # 2.1 日期格式检查 (权重: 0.4)
        date_format_score = 0.0
        
        # 尝试解析为日期
        try:
            parsed_dates = pd.to_datetime(non_null, errors='coerce')
            valid_date_ratio = (len(parsed_dates) - parsed_dates.isna().sum()) / len(parsed_dates)
            date_format_score = 0.4 * valid_date_ratio
        except:
            pass
        
        # 检查年份格式 (如: 2025)
        if date_format_score < 0.2:
            year_pattern_count = 0
            for val in non_null:
                str_val = str(val)
                if str_val.isdigit() and 1900 <= int(str_val) <= 2100:
                    year_pattern_count += 1
            
            if year_pattern_count > 0:
                year_ratio = year_pattern_count / len(non_null)
                date_format_score = max(date_format_score, 0.3 * year_ratio)
        
        # 检查月份格式 (如: 01, 02, ..., 12)
        if date_format_score < 0.2:
            month_pattern_count = 0
            for val in non_null:
                str_val = str(val).zfill(2)
                if str_val.isdigit() and 1 <= int(str_val) <= 12:
                    month_pattern_count += 1
            
            if month_pattern_count > 0:
                month_ratio = month_pattern_count / len(non_null)
                date_format_score = max(date_format_score, 0.3 * month_ratio)
        
        confidence_score += date_format_score
        
        # 2.2 数值范围检查 (权重: 0.2)
        try:
            numeric_values = pd.to_numeric(non_null, errors='coerce')
            valid_numeric = numeric_values.dropna()
            
            if len(valid_numeric) > 0:
                min_val, max_val = valid_numeric.min(), valid_numeric.max()
                
                # 年份范围检查
                if 1900 <= min_val <= max_val <= 2100:
                    confidence_score += 0.2
                # 月份范围检查
                elif 1 <= min_val <= max_val <= 12:
                    confidence_score += 0.15
                # 日期范围检查
                elif 1 <= min_val <= max_val <= 31:
                    confidence_score += 0.1
        except:
            pass
        
        is_date_field = confidence_score >= self.confidence_threshold
        return is_date_field, min(confidence_score, 1.0)
        
    except Exception as e:
        self.logger.warning(f"日期字段识别异常: {e}")
        return name_match_score > 0, name_match_score
```

### 3. 数据质量评估

```python
def _calculate_data_quality(self, series: pd.Series) -> Dict[str, float]:
    """
    数据质量评估
    
    评估指标：
    1. 完整性：非空值比例
    2. 一致性：数据类型一致性
    3. 准确性：数据格式正确性
    4. 唯一性：重复值比例
    
    Returns:
        Dict[str, float]: 质量评估结果
    """
    quality_metrics = {}
    
    total_count = len(series)
    if total_count == 0:
        return {'completeness': 0.0, 'consistency': 0.0, 'accuracy': 0.0, 'uniqueness': 0.0}
    
    # 1. 完整性评估
    non_null_count = series.count()
    quality_metrics['completeness'] = non_null_count / total_count
    
    # 2. 一致性评估（数据类型一致性）
    non_null_series = series.dropna()
    if len(non_null_series) > 0:
        type_consistency = 0.0
        
        # 检查数值型一致性
        try:
            numeric_values = pd.to_numeric(non_null_series, errors='coerce')
            numeric_ratio = (len(numeric_values) - numeric_values.isna().sum()) / len(numeric_values)
            type_consistency = max(type_consistency, numeric_ratio)
        except:
            pass
        
        # 检查字符串型一致性
        try:
            string_values = non_null_series.astype(str)
            string_ratio = len(string_values) / len(non_null_series)
            type_consistency = max(type_consistency, string_ratio)
        except:
            pass
        
        quality_metrics['consistency'] = type_consistency
    else:
        quality_metrics['consistency'] = 0.0
    
    # 3. 准确性评估（基于数据格式）
    if len(non_null_series) > 0:
        accuracy_score = 0.0
        
        # 检查数据格式的合理性
        format_valid_count = 0
        for value in non_null_series:
            str_value = str(value)
            # 基本格式检查：不包含异常字符
            if not any(char in str_value for char in ['#', 'ERROR', 'NULL', 'undefined']):
                format_valid_count += 1
        
        accuracy_score = format_valid_count / len(non_null_series)
        quality_metrics['accuracy'] = accuracy_score
    else:
        quality_metrics['accuracy'] = 0.0
    
    # 4. 唯一性评估
    if len(non_null_series) > 0:
        unique_count = len(non_null_series.unique())
        quality_metrics['uniqueness'] = unique_count / len(non_null_series)
    else:
        quality_metrics['uniqueness'] = 0.0
    
    return quality_metrics
```

### 4. 主要分析方法实现

```python
def analyze_excel_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
    """
    分析Excel结构的主要方法
    
    Args:
        df: 待分析的DataFrame
        
    Returns:
        Dict[str, Any]: 分析结果
    """
    analysis_result = {
        'field_types': {},
        'format_rules': {},
        'display_order': [],
        'confidence_scores': {},
        'quality_metrics': {},
        'recommendations': []
    }
    
    self.logger.info(f"开始分析Excel结构，共{len(df.columns)}个字段")
    
    # 逐列分析
    for column in df.columns:
        try:
            # 字段类型推断
            field_type, confidence = self._infer_field_type(df[column], column)
            analysis_result['field_types'][column] = field_type
            analysis_result['confidence_scores'][column] = confidence
            
            # 数据质量评估
            quality = self._calculate_data_quality(df[column])
            analysis_result['quality_metrics'][column] = quality
            
            # 生成格式化规则
            format_rule = self._generate_format_rule(field_type, df[column])
            analysis_result['format_rules'][column] = format_rule
            
            self.logger.debug(f"字段分析完成: {column} -> {field_type} (置信度: {confidence:.2f})")
            
        except Exception as e:
            self.logger.error(f"字段分析失败: {column}, 错误: {e}")
            # 设置默认值
            analysis_result['field_types'][column] = 'text_string'
            analysis_result['confidence_scores'][column] = 0.0
            analysis_result['quality_metrics'][column] = {'completeness': 0.0, 'consistency': 0.0, 'accuracy': 0.0, 'uniqueness': 0.0}
            analysis_result['format_rules'][column] = 'keep_original'
    
    # 生成显示顺序建议
    analysis_result['display_order'] = self._generate_display_order(analysis_result['field_types'])
    
    # 生成优化建议
    analysis_result['recommendations'] = self._generate_recommendations(analysis_result)
    
    self.logger.info(f"Excel结构分析完成，识别出{len(analysis_result['field_types'])}个字段")
    
    return analysis_result

def _generate_format_rule(self, field_type: str, series: pd.Series) -> str:
    """根据字段类型生成格式化规则"""
    
    if field_type == 'employee_id_string':
        return 'remove_decimal_keep_string'
    elif field_type == 'salary_float':
        return 'two_decimal_places'
    elif field_type == 'date_string':
        return 'standard_date_format'
    elif field_type == 'name_string':
        return 'trim_whitespace'
    else:
        return 'keep_original'

def _generate_display_order(self, field_types: Dict[str, str]) -> List[str]:
    """生成建议的显示顺序"""
    
    # 定义字段优先级
    priority_order = {
        'employee_id_string': 1,  # 工号优先
        'name_string': 2,         # 姓名其次
        'department_string': 3,   # 部门第三
        'salary_float': 4,        # 工资字段
        'date_string': 5,         # 日期字段
        'text_string': 6          # 其他文本字段
    }
    
    # 按优先级排序
    sorted_fields = sorted(
        field_types.items(),
        key=lambda x: priority_order.get(x[1], 999)
    )
    
    return [field_name for field_name, _ in sorted_fields]

def _generate_recommendations(self, analysis_result: Dict[str, Any]) -> List[str]:
    """生成优化建议"""
    
    recommendations = []
    
    # 检查数据质量问题
    for field_name, quality in analysis_result['quality_metrics'].items():
        if quality['completeness'] < 0.8:
            recommendations.append(f"字段'{field_name}'存在较多空值，建议检查数据完整性")
        
        if quality['consistency'] < 0.9:
            recommendations.append(f"字段'{field_name}'数据类型不一致，建议进行数据清洗")
    
    # 检查置信度问题
    low_confidence_fields = [
        field for field, confidence in analysis_result['confidence_scores'].items()
        if confidence < 0.6
    ]
    
    if low_confidence_fields:
        recommendations.append(f"以下字段的类型识别置信度较低，建议手动确认: {', '.join(low_confidence_fields)}")
    
    return recommendations
```

## 🔧 使用示例

```python
# 创建分析器实例
analyzer = ChangeDataAnalyzer()

# 分析Excel数据
df = pd.read_excel("异动表.xlsx")
analysis_result = analyzer.analyze_excel_structure(df)

# 查看分析结果
print("字段类型识别结果:")
for field, field_type in analysis_result['field_types'].items():
    confidence = analysis_result['confidence_scores'][field]
    print(f"  {field}: {field_type} (置信度: {confidence:.2f})")

print("\n建议的显示顺序:")
print(analysis_result['display_order'])

print("\n优化建议:")
for recommendation in analysis_result['recommendations']:
    print(f"  - {recommendation}")
```

---

**文档版本**: v1.0  
**最后更新**: 2025-08-20  
**状态**: 技术实现细节
