"""
月度工资异动处理系统 - 系统对话框模块

本模块实现系统的各种对话框界面，包括系统设置、进度显示、关于等对话框。

主要对话框:
1. SettingsDialog - 系统设置对话框
2. ProgressDialog - 进度显示对话框
3. AboutDialog - 关于系统对话框
"""

import os
import traceback
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
    QGroupBox, QTabWidget, QWidget, QLabel, QLineEdit, QPushButton,
    QComboBox, QSpinBox, QCheckBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QTextEdit, QProgressBar, QDialogButtonBox,
    QMessageBox, QFileDialog, QScrollArea, QFrame, QSplitter,
    QApplication, QSlider, QRadioButton, QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon, QColor

from src.utils.log_config import setup_logger


class SettingsDialog(QDialog):
    """系统设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.settings_changed = False
        self._init_ui()
        self._load_settings()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("系统设置")
        self.setMinimumSize(500, 400)
        self.resize(600, 500)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建设置选项卡
        self._create_settings_tabs(main_layout)
        
        # 创建按钮区域
        self._create_button_area(main_layout)
    
    def _create_settings_tabs(self, parent_layout):
        """创建设置选项卡"""
        self.tab_widget = QTabWidget()
        
        # 常规设置
        self._create_general_tab()
        
        # 数据设置
        self._create_data_tab()
        
        # 界面设置
        self._create_ui_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def _create_general_tab(self):
        """创建常规设置选项卡"""
        general_widget = QWidget()
        general_layout = QVBoxLayout(general_widget)
        
        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_layout = QFormLayout(basic_group)
        
        # 自动保存
        self.auto_save_check = QCheckBox("启用自动保存")
        basic_layout.addRow("", self.auto_save_check)
        
        # 保存间隔
        self.save_interval_spin = QSpinBox()
        self.save_interval_spin.setRange(1, 60)
        self.save_interval_spin.setValue(5)
        self.save_interval_spin.setSuffix(" 分钟")
        basic_layout.addRow("保存间隔:", self.save_interval_spin)
        
        # 备份设置
        self.backup_check = QCheckBox("启用自动备份")
        basic_layout.addRow("", self.backup_check)
        
        general_layout.addWidget(basic_group)
        general_layout.addStretch()
        
        self.tab_widget.addTab(general_widget, "常规")
    
    def _create_data_tab(self):
        """创建数据设置选项卡"""
        data_widget = QWidget()
        data_layout = QVBoxLayout(data_widget)
        
        # 数据库设置组
        db_group = QGroupBox("数据库设置")
        db_layout = QFormLayout(db_group)
        
        # 数据库路径
        self.db_path_edit = QLineEdit()
        db_browse_btn = QPushButton("浏览...")
        db_browse_btn.clicked.connect(self._browse_db_path)
        
        db_path_layout = QHBoxLayout()
        db_path_layout.addWidget(self.db_path_edit)
        db_path_layout.addWidget(db_browse_btn)
        
        db_layout.addRow("数据库路径:", db_path_layout)
        
        # 最大记录数
        self.max_records_spin = QSpinBox()
        self.max_records_spin.setRange(1000, 100000)
        self.max_records_spin.setValue(10000)
        db_layout.addRow("最大记录数:", self.max_records_spin)
        
        data_layout.addWidget(db_group)
        
        # 导入设置组
        import_group = QGroupBox("导入设置")
        import_layout = QFormLayout(import_group)
        
        # 默认编码
        self.encoding_combo = QComboBox()
        self.encoding_combo.addItems(["utf-8", "gbk", "gb2312"])
        import_layout.addRow("默认编码:", self.encoding_combo)
        
        # 错误处理
        self.error_handling_combo = QComboBox()
        self.error_handling_combo.addItems(["跳过", "停止", "询问"])
        import_layout.addRow("错误处理:", self.error_handling_combo)
        
        data_layout.addWidget(import_group)
        data_layout.addStretch()
        
        self.tab_widget.addTab(data_widget, "数据")
    
    def _create_ui_tab(self):
        """创建界面设置选项卡"""
        ui_widget = QWidget()
        ui_layout = QVBoxLayout(ui_widget)
        
        # 界面设置组
        ui_group = QGroupBox("界面设置")
        ui_group_layout = QFormLayout(ui_group)
        
        # 主题
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["默认", "深色", "浅色"])
        ui_group_layout.addRow("主题:", self.theme_combo)
        
        # 字体大小
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(10)
        ui_group_layout.addRow("字体大小:", self.font_size_spin)
        
        # 显示工具栏
        self.toolbar_check = QCheckBox("显示工具栏")
        ui_group_layout.addRow("", self.toolbar_check)
        
        # 显示状态栏
        self.statusbar_check = QCheckBox("显示状态栏")
        ui_group_layout.addRow("", self.statusbar_check)
        
        ui_layout.addWidget(ui_group)
        ui_layout.addStretch()
        
        self.tab_widget.addTab(ui_widget, "界面")
    
    def _create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        
        button_box.accepted.connect(self._save_and_close)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self._apply_settings)
        
        parent_layout.addWidget(button_box)
    
    def _browse_db_path(self):
        """浏览数据库路径"""
        path = QFileDialog.getExistingDirectory(self, "选择数据库目录")
        if path:
            self.db_path_edit.setText(path)
    
    def _load_settings(self):
        """加载设置"""
        try:
            # 这里从配置文件加载设置
            pass
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
    
    def _apply_settings(self):
        """应用设置"""
        try:
            # 这里保存设置到配置文件
            self.settings_changed = True
            QMessageBox.information(self, "提示", "设置已应用")
        except Exception as e:
            self.logger.error(f"应用设置失败: {e}")
            QMessageBox.critical(self, "错误", f"应用设置失败: {e}")
    
    def _save_and_close(self):
        """保存并关闭"""
        self._apply_settings()
        self.accept()


class ProgressDialog(QDialog):
    """进度对话框"""
    
    def __init__(self, title: str = "处理中...", parent=None):
        super().__init__(parent)
        self.canceled = False
        self._init_ui(title)
        
    def _init_ui(self, title: str):
        """初始化界面 - 美化版本"""
        self.setWindowTitle(title)
        self.setMinimumSize(350, 150)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowTitleHint)
        
        # 设置进度对话框样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #0d6efd;
                border-radius: 10px;
            }
            QLabel {
                color: #495057;
                font-size: 13px;
                font-weight: 500;
            }
            QProgressBar {
                border: 1px solid #ced4da;
                border-radius: 6px;
                text-align: center;
                font-weight: 600;
                font-size: 12px;
                color: #495057;
                background-color: #f8f9fa;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0d6efd, stop: 1 #0b5ed7);
                border-radius: 5px;
                margin: 1px;
            }
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #dc3545, stop: 1 #c82333);
                color: white;
                border: 1px solid #bd2130;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c82333, stop: 1 #bd2130);
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题标签
        self.title_label = QLabel(f"⏳ {title}")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setStyleSheet("font-size: 15px; font-weight: 600; color: #0d6efd; margin-bottom: 5px;")
        layout.addWidget(self.title_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setMinimumHeight(25)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("🔄 准备中...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("color: #6c757d; font-size: 12px; margin-top: 5px;")
        layout.addWidget(self.status_label)
        
        # 取消按钮
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.clicked.connect(self._cancel)
        layout.addWidget(cancel_btn)
    
    def update_progress(self, value: int, status: str = ""):
        """更新进度 - 美化版本"""
        self.progress_bar.setValue(value)
        if status:
            # 根据不同阶段显示不同的图标
            if "验证" in status:
                icon = "🔍"
            elif "读取" in status or "加载" in status:
                icon = "📖"
            elif "处理" in status or "导入" in status:
                icon = "⚙️"
            elif "完成" in status:
                icon = "✅"
            elif "保存" in status:
                icon = "💾"
            else:
                icon = "🔄"
            self.status_label.setText(f"{icon} {status}")
        else:
            self.status_label.setText(f"📊 进度: {value}%")
    
    def _cancel(self):
        """取消操作"""
        self.canceled = True
        self.reject()
    
    def wasCanceled(self) -> bool:
        """是否已取消"""
        return self.canceled


class AboutDialog(QDialog):
    """关于对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("关于系统")
        self.setFixedSize(400, 300)
        
        # 主布局
        layout = QVBoxLayout(self)
        
        # 系统图标
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setPixmap(self.style().standardIcon(
            self.style().SP_ComputerIcon).pixmap(64, 64))
        layout.addWidget(icon_label)
        
        # 系统名称
        name_label = QLabel("月度工资异动处理系统")
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_font = QFont()
        name_font.setPointSize(16)
        name_font.setBold(True)
        name_label.setFont(name_font)
        layout.addWidget(name_label)
        
        # 版本信息
        version_label = QLabel("版本 1.0.0")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(version_label)
        
        # 描述信息
        desc_text = QTextEdit()
        desc_text.setReadOnly(True)
        desc_text.setMaximumHeight(100)
        desc_text.setText("""
        自动化处理月度工资异动数据，支持Excel数据导入、
        异动识别计算、报告生成等功能。
        
        开发团队：月度工资异动处理系统开发团队
        """)
        layout.addWidget(desc_text)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        layout.addWidget(ok_btn)
