#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置预览对话框

提供配置详细信息的预览功能，包括字段映射、类型信息等
"""

from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QTabWidget, QWidget,
    QTextEdit, QFrame, QScrollArea, QGroupBox, QSplitter
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QIcon

from src.utils.log_config import setup_logger


class ConfigPreviewDialog(QDialog):
    """配置预览对话框"""
    
    def __init__(self, parent=None, config_name: str = "", sheets_data: Dict[str, Any] = None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        self.config_name = config_name
        self.sheets_data = sheets_data or {}
        
        self._init_ui()
        self._load_preview_data()
        
        self.logger.info(f"配置预览对话框初始化完成: {config_name}")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"配置预览 - {self.config_name}")
        self.setModal(True)
        self.resize(800, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题区域
        self._create_header(main_layout)
        
        # 内容区域
        self._create_content_area(main_layout)
        
        # 按钮区域
        self._create_buttons(main_layout)
        
        # 应用样式
        self._apply_styles()
    
    def _create_header(self, parent_layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        
        # 主标题
        title_label = QLabel(f"配置预览：{self.config_name}")
        title_label.setObjectName("titleLabel")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # 统计信息
        stats_text = f"包含 {len(self.sheets_data)} 个工作表"
        total_fields = 0
        for sheet_data in self.sheets_data.values():
            config_data = sheet_data.get('config', {})
            field_mapping = config_data.get('field_mapping', {})
            total_fields += len(field_mapping)
        
        stats_text += f"，共 {total_fields} 个字段"
        
        stats_label = QLabel(stats_text)
        stats_label.setObjectName("statsLabel")
        stats_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(stats_label)
        
        parent_layout.addWidget(header_frame)
    
    def _create_content_area(self, parent_layout):
        """创建内容区域"""
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("previewTabs")
        
        # 为每个工作表创建标签页
        for sheet_name, sheet_data in self.sheets_data.items():
            tab_widget = self._create_sheet_tab(sheet_name, sheet_data)
            self.tab_widget.addTab(tab_widget, sheet_name)
        
        # 添加汇总标签页
        summary_tab = self._create_summary_tab()
        self.tab_widget.addTab(summary_tab, "📊 汇总信息")
        
        parent_layout.addWidget(self.tab_widget)
    
    def _create_sheet_tab(self, sheet_name: str, sheet_data: Dict[str, Any]) -> QWidget:
        """创建单个工作表的标签页"""
        tab_widget = QWidget()
        tab_layout = QVBoxLayout(tab_widget)
        tab_layout.setSpacing(10)
        
        config_data = sheet_data.get('config', {})
        field_mapping = config_data.get('field_mapping', {})
        field_types = config_data.get('field_types', {})
        
        # 工作表信息
        info_group = QGroupBox("工作表信息")
        info_layout = QVBoxLayout(info_group)
        
        info_text = f"工作表名称：{sheet_name}\n"
        info_text += f"字段数量：{len(field_mapping)}\n"
        info_text += f"描述：{sheet_data.get('description', '无')}"
        
        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        info_layout.addWidget(info_label)
        
        tab_layout.addWidget(info_group)
        
        # 字段映射表
        if field_mapping:
            mapping_group = QGroupBox("字段映射")
            mapping_layout = QVBoxLayout(mapping_group)
            
            # 创建表格
            table = QTableWidget()
            table.setColumnCount(3)
            table.setHorizontalHeaderLabels(["Excel字段", "数据库字段", "字段类型"])
            table.setRowCount(len(field_mapping))
            
            # 填充数据
            for row, (excel_field, db_field) in enumerate(field_mapping.items()):
                # Excel字段
                excel_item = QTableWidgetItem(excel_field)
                table.setItem(row, 0, excel_item)
                
                # 数据库字段
                db_item = QTableWidgetItem(db_field)
                table.setItem(row, 1, db_item)
                
                # 字段类型
                field_type = field_types.get(excel_field, "未知")
                type_item = QTableWidgetItem(field_type)
                table.setItem(row, 2, type_item)
            
            # 调整列宽
            table.resizeColumnsToContents()
            table.setAlternatingRowColors(True)
            table.setSelectionBehavior(QTableWidget.SelectRows)
            
            mapping_layout.addWidget(table)
            tab_layout.addWidget(mapping_group)
        
        return tab_widget
    
    def _create_summary_tab(self) -> QWidget:
        """创建汇总信息标签页"""
        tab_widget = QWidget()
        tab_layout = QVBoxLayout(tab_widget)
        tab_layout.setSpacing(15)
        
        # 整体统计
        stats_group = QGroupBox("整体统计")
        stats_layout = QVBoxLayout(stats_group)
        
        total_sheets = len(self.sheets_data)
        total_fields = 0
        field_type_counts = {}
        
        for sheet_data in self.sheets_data.values():
            config_data = sheet_data.get('config', {})
            field_mapping = config_data.get('field_mapping', {})
            field_types = config_data.get('field_types', {})
            
            total_fields += len(field_mapping)
            
            # 统计字段类型
            for field_type in field_types.values():
                field_type_counts[field_type] = field_type_counts.get(field_type, 0) + 1
        
        stats_text = f"工作表数量：{total_sheets}\n"
        stats_text += f"总字段数量：{total_fields}\n"
        stats_text += f"平均每表字段数：{total_fields / total_sheets:.1f}\n\n"
        
        stats_text += "字段类型分布：\n"
        for field_type, count in sorted(field_type_counts.items()):
            percentage = (count / total_fields) * 100
            stats_text += f"  • {field_type}: {count} 个 ({percentage:.1f}%)\n"
        
        stats_label = QLabel(stats_text)
        stats_label.setWordWrap(True)
        stats_label.setFont(QFont("Consolas", 10))
        stats_layout.addWidget(stats_label)
        
        tab_layout.addWidget(stats_group)
        
        # 工作表列表
        sheets_group = QGroupBox("工作表列表")
        sheets_layout = QVBoxLayout(sheets_group)
        
        for sheet_name, sheet_data in self.sheets_data.items():
            config_data = sheet_data.get('config', {})
            field_mapping = config_data.get('field_mapping', {})
            
            sheet_info = f"📋 {sheet_name} ({len(field_mapping)} 个字段)"
            if sheet_data.get('description'):
                sheet_info += f" - {sheet_data['description']}"
            
            sheet_label = QLabel(sheet_info)
            sheet_label.setStyleSheet("padding: 5px; margin: 2px 0;")
            sheets_layout.addWidget(sheet_label)
        
        tab_layout.addWidget(sheets_group)
        
        # 添加弹性空间
        tab_layout.addStretch()
        
        return tab_widget
    
    def _create_buttons(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 导出按钮
        export_btn = QPushButton("📄 导出配置")
        export_btn.setObjectName("exportButton")
        export_btn.clicked.connect(self._export_config)
        button_layout.addWidget(export_btn)
        
        button_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setObjectName("closeButton")
        close_btn.clicked.connect(self.accept)
        close_btn.setDefault(True)
        button_layout.addWidget(close_btn)
        
        parent_layout.addLayout(button_layout)
    
    def _load_preview_data(self):
        """加载预览数据"""
        self.logger.info(f"加载配置预览数据: {len(self.sheets_data)} 个工作表")
    
    def _export_config(self):
        """导出配置"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        import json
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出配置",
            f"{self.config_name}_preview.json",
            "JSON文件 (*.json);;所有文件 (*)"
        )
        
        if file_path:
            try:
                from datetime import datetime

                export_data = {
                    "config_name": self.config_name,
                    "export_time": str(datetime.now()),
                    "sheets_data": self.sheets_data
                }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "导出成功", f"配置已导出到：\n{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出配置时发生错误：\n{str(e)}")
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            }
            
            #headerFrame {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 10px;
                margin: 5px 0;
                padding: 10px;
            }
            
            #titleLabel {
                color: #2c3e50;
                background: transparent;
            }
            
            #statsLabel {
                color: #6c757d;
                background: transparent;
                margin: 5px 0;
            }
            
            #previewTabs {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
            }
            
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-bottom: none;
                border-radius: 6px 6px 0 0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            
            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom: 1px solid #ffffff;
            }
            
            QTabBar::tab:hover {
                background-color: #e9ecef;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #ffffff;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px;
                color: #495057;
                background-color: #ffffff;
            }
            
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
                gridline-color: #e9ecef;
                selection-background-color: #e3f2fd;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            
            QHeaderView::section {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
            
            QPushButton {
                background-color: #ffffff;
                border: 2px solid #6c757d;
                border-radius: 6px;
                color: #6c757d;
                font-weight: 600;
                padding: 10px 20px;
                min-height: 20px;
            }
            
            QPushButton:hover {
                background-color: #6c757d;
                color: #ffffff;
            }
            
            #exportButton {
                border-color: #17a2b8;
                color: #17a2b8;
            }
            
            #exportButton:hover {
                background-color: #17a2b8;
                color: #ffffff;
            }
            
            #closeButton[default="true"] {
                border-color: #28a745;
                color: #28a745;
            }
            
            #closeButton[default="true"]:hover {
                background-color: #28a745;
                color: #ffffff;
            }
        """)


# 导出的类
__all__ = ["ConfigPreviewDialog"]
