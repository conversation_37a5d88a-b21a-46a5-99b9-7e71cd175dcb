#!/usr/bin/env python3
"""
测试缓存更新修复效果

验证用户的真实操作场景：
1. 配置第一个工作表，点击"另存配置"
2. 切换到第二个工作表，配置字段
3. 再次点击"另存配置" - 应该保存两个工作表
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger

def test_cache_update_workflow():
    """测试缓存更新工作流"""
    logger.info("=== 测试缓存更新修复效果 ===")
    
    class TestCacheDialog:
        def __init__(self):
            # 模拟对话框初始状态
            self.current_sheet_name = 'unknown'
            self.all_sheets_configs = {}  # 空的缓存
            
            # 模拟Excel工作表数据
            self.all_sheets_data = {
                '离休人员工资表': {'人员代码': '001001', '姓名': '张三'},
                '退休人员工资表': {'人员代码': '001002', '姓名': '李四'},
                'A岗职工': {'工号': '001003', '姓名': '王五'}
            }
            
            # 模拟父窗口配置
            self.parent_configs = {
                '离休人员工资表': {
                    'field_mapping': {'人员代码': '人员代码', '姓名': '姓名', '基本离休费': '基本离休费'},
                    'field_types': {'人员代码': 'employee_id_string', '姓名': 'name_string', '基本离休费': 'salary_float'}
                },
                '退休人员工资表': {
                    'field_mapping': {'人员代码': '人员代码', '姓名': '姓名', '基本退休费': '基本退休费', '津贴': '津贴'},
                    'field_types': {'人员代码': 'employee_id_string', '姓名': 'name_string', '基本退休费': 'salary_float', '津贴': 'salary_float'}
                },
                'A岗职工': {
                    'field_mapping': {'工号': '工号', '姓名': '姓名', '基本工资': '基本工资'},
                    'field_types': {'工号': 'employee_id_string', '姓名': 'name_string', '基本工资': 'salary_float'}
                }
            }
        
        def get_current_configuration(self):
            """获取当前工作表配置"""
            if self.current_sheet_name == 'unknown' and self.all_sheets_data:
                first_sheet = list(self.all_sheets_data.keys())[0]
                return self.parent_configs.get(first_sheet, {})
            return self.parent_configs.get(self.current_sheet_name, {})
        
        def parent(self):
            """模拟父窗口"""
            class MockParent:
                def __init__(self, configs):
                    self.change_data_configs = configs
            return MockParent(self.parent_configs)
        
        def _save_current_sheet_config_to_cache(self):
            """保存当前配置到缓存"""
            try:
                if hasattr(self, 'current_sheet_name') and self.current_sheet_name and self.current_sheet_name != 'unknown':
                    current_config = self.get_current_configuration()
                    if current_config and current_config.get('field_mapping'):
                        self.all_sheets_configs[self.current_sheet_name] = current_config
                        logger.info(f"🔧 [关键修复] 当前工作表 '{self.current_sheet_name}' 配置已保存到缓存")
                        return True
            except Exception as e:
                logger.warning(f"保存当前配置到缓存失败: {e}")
            return False
        
        def _collect_all_configured_sheets(self):
            """收集所有已配置的工作表"""
            configs_to_save = {}
            
            # 先保存当前配置到缓存
            self._save_current_sheet_config_to_cache()
            
            # 修复unknown名称
            actual_sheet_name = self.current_sheet_name
            if self.current_sheet_name == 'unknown' and self.all_sheets_data:
                actual_sheet_name = list(self.all_sheets_data.keys())[0]
                logger.info(f"🔧 修复工作表名称：从 'unknown' 更正为 '{actual_sheet_name}'")
            
            # 收集当前配置
            if actual_sheet_name:
                current_config = self.get_current_configuration()
                if current_config and current_config.get('field_mapping'):
                    configs_to_save[actual_sheet_name] = current_config
            
            # 收集缓存中的配置
            for sheet_name, config in self.all_sheets_configs.items():
                if config and config.get('field_mapping'):
                    if sheet_name not in configs_to_save:
                        configs_to_save[sheet_name] = config
            
            # 收集父窗口配置
            parent = self.parent()
            if parent and hasattr(parent, 'change_data_configs'):
                for sheet_name, config in parent.change_data_configs.items():
                    if config and config.get('field_mapping'):
                        if sheet_name not in configs_to_save:
                            configs_to_save[sheet_name] = config
            
            return configs_to_save
        
        def save_configuration(self, configs_to_save):
            """模拟保存配置"""
            logger.info(f"保存配置：{len(configs_to_save)} 个工作表")
            
            # 🔧 关键修复：保存后更新缓存
            for sheet_name, sheet_config in configs_to_save.items():
                self.all_sheets_configs[sheet_name] = sheet_config
                logger.info(f"🔧 [缓存更新] 工作表 '{sheet_name}' 配置已更新到缓存")
            
            return True
        
        def on_sheet_changed(self, sheet_name):
            """切换工作表"""
            # 保存当前配置
            if self.current_sheet_name != 'unknown':
                current_config = self.get_current_configuration()
                if current_config:
                    self.all_sheets_configs[self.current_sheet_name] = current_config
                    logger.info(f"已保存工作表 '{self.current_sheet_name}' 的配置")
            
            # 切换到新工作表
            self.current_sheet_name = sheet_name
            logger.info(f"切换到工作表: {sheet_name}")
        
        def simulate_user_problem_scenario(self):
            """模拟用户问题场景"""
            logger.info("\\n=== 模拟用户问题场景 ===")
            
            # 步骤1：用户在第一个工作表配置字段
            logger.info("👤 步骤1：在'离休人员工资表'配置字段类型")
            if self.current_sheet_name == 'unknown':
                self.current_sheet_name = '离休人员工资表'
                logger.info("   自动切换到第一个工作表")
            
            # 步骤2：用户点击"另存配置"
            logger.info("\\n👤 步骤2：第一次点击'另存配置'")
            configs1 = self._collect_all_configured_sheets()
            self.save_configuration(configs1)
            logger.info(f"   第一次保存结果：{len(configs1)} 个工作表")
            logger.info(f"   缓存状态：{len(self.all_sheets_configs)} 个配置")
            
            # 步骤3：用户切换到第二个工作表
            logger.info("\\n👤 步骤3：切换到'退休人员工资表'配置字段")
            self.on_sheet_changed('退休人员工资表')
            
            # 步骤4：用户再次点击"另存配置"
            logger.info("\\n👤 步骤4：第二次点击'另存配置'")
            configs2 = self._collect_all_configured_sheets()
            logger.info(f"   第二次保存结果：{len(configs2)} 个工作表")
            logger.info(f"   保存的工作表：{list(configs2.keys())}")
            
            return configs1, configs2
    
    # 执行测试
    dialog = TestCacheDialog()
    configs1, configs2 = dialog.simulate_user_problem_scenario()
    
    logger.info("\\n=== 修复效果验证 ===")
    expected_sheets = {'离休人员工资表', '退休人员工资表'}
    first_save_sheets = set(configs1.keys())
    second_save_sheets = set(configs2.keys())
    
    logger.info(f"期望最终结果：{expected_sheets}")
    logger.info(f"第一次保存：{first_save_sheets}")
    logger.info(f"第二次保存：{second_save_sheets}")
    
    # 关键验证：第二次保存应该包含两个工作表
    fix_successful = expected_sheets.issubset(second_save_sheets)
    
    logger.info(f"修复是否成功：{fix_successful}")
    
    return fix_successful

def compare_before_after_fix():
    """对比修复前后的差异"""
    logger.info("\\n=== 修复前后对比 ===")
    
    # 修复前的问题
    def before_fix():
        all_sheets_configs = {'离休人员工资表': {'field_mapping': {'test': 'test'}}}
        # 保存配置后，缓存没有更新
        # 用户切换到第二个表时，缓存被清空或覆盖
        all_sheets_configs = {}  # 问题：缓存丢失
        return 1  # 只能保存当前表
    
    # 修复后的逻辑
    def after_fix():
        all_sheets_configs = {'离休人员工资表': {'field_mapping': {'test': 'test'}}}
        # 保存配置后，立即更新缓存
        configs_to_save = {'离休人员工资表': {'field_mapping': {'test': 'test'}}}
        for sheet_name, config in configs_to_save.items():
            all_sheets_configs[sheet_name] = config  # 关键修复
        
        # 用户切换到第二个表后，缓存保持
        # 第二次保存时，能找到之前的配置
        return 2  # 能保存两个表
    
    before_result = before_fix()
    after_result = after_fix()
    
    logger.info(f"修复前结果：{before_result} 个工作表")
    logger.info(f"修复后结果：{after_result} 个工作表")
    
    return after_result > before_result

def main():
    """主函数"""
    try:
        logger.info("🚀 开始测试缓存更新修复效果")
        
        # 主要修复测试
        main_success = test_cache_update_workflow()
        
        # 对比测试
        compare_success = compare_before_after_fix()
        
        overall_success = main_success and compare_success
        
        print(f"\\n>>> 缓存更新修复测试：{'成功' if overall_success else '失败'}")
        
        if overall_success:
            print("🎉 缓存更新修复完全成功！")
            print("✅ 保存配置后，缓存会立即更新")
            print("✅ 用户切换工作表后，之前的配置不会丢失")
            print("✅ 第二次保存能收集到所有已配置的工作表")
        else:
            print("❌ 修复仍有问题，需要进一步调试")
            
        return 0 if overall_success else 1
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())