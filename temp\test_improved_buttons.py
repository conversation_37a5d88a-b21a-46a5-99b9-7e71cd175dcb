#!/usr/bin/env python3
"""
测试改进后的按钮功能
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.gui.unified_data_import_window import UnifiedDataImportWindow
from src.utils.log_config import setup_logger

def test_improved_buttons():
    """测试改进后的按钮功能"""
    logger = setup_logger(__name__)
    
    app = QApplication([])
    
    try:
        # 创建窗口
        window = UnifiedDataImportWindow()
        window.show()
        
        def test_button_improvements():
            logger.info("开始测试改进后的按钮功能...")
            
            # 测试1: 检查工具提示
            logger.info("检查按钮工具提示...")
            
            # Sheet管理按钮工具提示
            tooltips = {
                window.sheet_management_widget.select_all_btn: "选择所有工作表进行导入",
                window.sheet_management_widget.deselect_all_btn: "取消选择所有工作表",
                window.sheet_management_widget.refresh_btn: "重新扫描Excel文件中的工作表",
                window.sheet_management_widget.preview_btn: "预览选中工作表的数据内容",
                window.sheet_management_widget.analyze_btn: "分析工作表结构和数据质量",
            }
            
            for btn, expected_tooltip in tooltips.items():
                actual_tooltip = btn.toolTip()
                if actual_tooltip == expected_tooltip:
                    logger.info(f"✅ {btn.text()} 工具提示正确: {actual_tooltip}")
                else:
                    logger.warning(f"⚠️ {btn.text()} 工具提示不匹配: 期望'{expected_tooltip}', 实际'{actual_tooltip}'")
            
            # 映射配置按钮工具提示
            mapping_tooltips = {
                window.mapping_tab.smart_mapping_btn: "基于字段名称智能推荐映射关系",
                window.mapping_tab.save_template_btn: "将当前映射配置保存为模板",
                window.mapping_tab.load_template_btn: "从已保存的模板加载映射配置",
                window.mapping_tab.reset_mapping_btn: "重置所有映射配置到初始状态",
                window.mapping_tab.validate_btn: "验证当前映射配置的正确性",
                window.mapping_tab.advanced_btn: "打开高级配置选项",
            }
            
            for btn, expected_tooltip in mapping_tooltips.items():
                actual_tooltip = btn.toolTip()
                if actual_tooltip == expected_tooltip:
                    logger.info(f"✅ {btn.text()} 工具提示正确: {actual_tooltip}")
                else:
                    logger.warning(f"⚠️ {btn.text()} 工具提示不匹配: 期望'{expected_tooltip}', 实际'{actual_tooltip}'")
            
            # 测试2: 测试按钮点击反馈
            logger.info("测试按钮点击反馈...")
            
            # 测试全选按钮（无数据时）
            window.sheet_management_widget.select_all_btn.click()
            status_text = window.sheet_management_widget.status_label.text()
            logger.info(f"全选按钮状态反馈: {status_text}")
            
            # 测试智能映射按钮（无数据时）
            window.mapping_tab.smart_mapping_btn.click()
            mapping_status = window.mapping_tab.status_label.text()
            logger.info(f"智能映射按钮状态反馈: {mapping_status}")
            
            # 测试验证配置按钮（无数据时）
            window.mapping_tab.validate_btn.click()
            validate_status = window.mapping_tab.status_label.text()
            logger.info(f"验证配置按钮状态反馈: {validate_status}")
            
            logger.info("按钮功能改进测试完成")
            
            # 3秒后关闭
            QTimer.singleShot(3000, app.quit)
        
        # 1秒后开始测试
        QTimer.singleShot(1000, test_button_improvements)
        
        app.exec_()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_buttons()
