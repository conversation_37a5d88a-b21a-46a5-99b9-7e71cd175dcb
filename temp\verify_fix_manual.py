#!/usr/bin/env python3
"""
手动验证修复效果的帮助脚本

这个脚本帮助验证修复是否有效：
1. 列出现有的配置
2. 模拟工作表切换和配置加载过程
3. 验证配置匹配逻辑
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager
from loguru import logger

def main():
    """验证配置持久化修复效果"""
    logger.info("=== 验证配置持久化修复效果 ===")
    
    # 初始化配置管理器
    config_manager = ChangeDataConfigManager()
    
    # 1. 列出所有现有配置
    configs_list = config_manager.list_configs()
    logger.info(f"发现 {len(configs_list)} 个配置:")
    
    for i, config_info in enumerate(configs_list, 1):
        config_name = config_info['name']
        created_at = config_info.get('created_at', 'N/A')
        description = config_info.get('description', 'N/A')
        logger.info(f"  {i}. {config_name}")
        logger.info(f"     创建时间: {created_at}")
        logger.info(f"     描述: {description}")
    
    # 2. 测试工作表匹配逻辑
    test_sheets = ["退休人员工资表", "在职人员工资表", "离休人员表", "其他表"]
    
    for sheet_name in test_sheets:
        logger.info(f"\n--- 测试工作表: {sheet_name} ---")
        
        # 查找匹配的配置
        matching_configs = []
        for config_info in configs_list:
            config_name = config_info['name']
            if sheet_name in config_name or 'auto_save' in config_name:
                matching_configs.append(config_info)
        
        if matching_configs:
            logger.info(f"找到 {len(matching_configs)} 个匹配的配置:")
            
            for config_info in matching_configs:
                config_name = config_info['name']
                created_at = config_info.get('created_at', 'N/A')
                logger.info(f"  - {config_name} (创建于: {created_at})")
            
            # 选择最新的配置
            latest_config_info = max(matching_configs, 
                key=lambda x: x.get('created_at', ''))
            latest_config_name = latest_config_info['name']
            
            logger.info(f"最新配置: {latest_config_name}")
            
            # 尝试加载配置
            config_data = config_manager.load_config(latest_config_name)
            if config_data:
                field_types_count = len(config_data.get('field_types', {}))
                field_mapping_count = len(config_data.get('field_mapping', {}))
                logger.info(f"配置加载成功: {field_types_count} 个字段类型, {field_mapping_count} 个字段映射")
            else:
                logger.error(f"配置加载失败: {latest_config_name}")
        else:
            logger.info("没有找到匹配的配置")
    
    # 3. 显示最新保存的配置
    if configs_list:
        logger.info("\n--- 最新保存的配置 ---")
        latest_config = configs_list[-1]
        latest_config_name = latest_config['name']
        logger.info(f"配置名称: {latest_config_name}")
        
        config_data = config_manager.load_config(latest_config_name)
        if config_data:
            field_types = config_data.get('field_types', {})
            logger.info(f"包含的字段类型配置:")
            for field_name, field_type in field_types.items():
                logger.info(f"  {field_name}: {field_type}")
    
    print("\n" + "="*60)
    print("验证完成！")
    print("请按照以下步骤进行手动测试：")
    print("1. 启动应用程序")
    print("2. 打开异动表字段配置窗口")
    print("3. 选择退休人员工资表或其他工作表")
    print("4. 观察字段类型是否自动加载了最新配置")
    print("5. 修改几个字段类型")
    print("6. 点击应用按钮")
    print("7. 重新打开配置窗口")
    print("8. 验证修改是否保存成功")
    print("="*60)

if __name__ == "__main__":
    main()