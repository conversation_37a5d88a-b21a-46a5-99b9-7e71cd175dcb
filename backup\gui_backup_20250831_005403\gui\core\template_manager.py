"""
模板管理器
管理内置模板和用户自定义模板
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

from src.modules.logging.setup_logger import setup_logger


class TemplateType(Enum):
    """模板类型"""
    SALARY = "salary"           # 工资表模板
    CHANGE = "change"          # 异动表模板
    CUSTOM = "custom"          # 自定义模板


@dataclass
class FieldTemplate:
    """字段模板"""
    field_name: str            # 字段名
    display_name: str          # 显示名称
    data_type: str            # 数据类型
    is_required: bool         # 是否必需
    default_value: Any = None  # 默认值
    validation_rules: List[str] = None  # 验证规则
    description: str = ""      # 字段描述


@dataclass
class Template:
    """模板定义"""
    id: str                    # 模板ID
    name: str                  # 模板名称
    type: TemplateType         # 模板类型
    description: str           # 模板描述
    fields: List[FieldTemplate] # 字段列表
    created_by: str = "system" # 创建者
    created_at: str = ""       # 创建时间
    version: str = "1.0"       # 版本
    tags: List[str] = None     # 标签


class TemplateManager:
    """模板管理器"""
    
    def __init__(self, config_dir: str = "config/templates"):
        self.logger = setup_logger(__name__)
        self.config_dir = config_dir
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        
        # 加载模板
        self.built_in_templates = self._load_built_in_templates()
        self.user_templates = self._load_user_templates()
        
        self.logger.info(f"模板管理器初始化完成: 内置模板 {len(self.built_in_templates)} 个, 用户模板 {len(self.user_templates)} 个")
    
    def _load_built_in_templates(self) -> Dict[str, Template]:
        """加载内置模板"""
        templates = {}
        
        # 工资表标准模板
        salary_template = Template(
            id="built_in_salary_standard",
            name="标准工资表模板",
            type=TemplateType.SALARY,
            description="通用的工资表字段模板",
            fields=[
                FieldTemplate("姓名", "员工姓名", "VARCHAR(100)", True, description="员工真实姓名"),
                FieldTemplate("工号", "员工工号", "VARCHAR(50)", True, description="唯一员工标识"),
                FieldTemplate("部门", "所属部门", "VARCHAR(100)", True, description="员工所在部门"),
                FieldTemplate("职位", "职位名称", "VARCHAR(100)", False, description="员工职位"),
                FieldTemplate("基本工资", "基本工资", "DECIMAL(10,2)", True, description="基础薪资"),
                FieldTemplate("津贴", "各项津贴", "DECIMAL(10,2)", False, description="津贴补助"),
                FieldTemplate("奖金", "绩效奖金", "DECIMAL(10,2)", False, description="奖金收入"),
                FieldTemplate("扣款", "各项扣款", "DECIMAL(10,2)", False, description="扣除项目"),
                FieldTemplate("应发合计", "应发总额", "DECIMAL(10,2)", True, description="税前总收入"),
                FieldTemplate("实发合计", "实发总额", "DECIMAL(10,2)", True, description="实际到手金额"),
            ],
            created_at=datetime.now().isoformat(),
            tags=["工资", "标准", "通用"]
        )
        templates[salary_template.id] = salary_template
        
        # 异动表标准模板
        change_template = Template(
            id="built_in_change_standard",
            name="标准异动表模板",
            type=TemplateType.CHANGE,
            description="通用的人员异动表字段模板",
            fields=[
                FieldTemplate("姓名", "员工姓名", "VARCHAR(100)", True, description="员工真实姓名"),
                FieldTemplate("工号", "员工工号", "VARCHAR(50)", True, description="唯一员工标识"),
                FieldTemplate("异动类型", "异动类型", "VARCHAR(50)", True, description="异动操作类型"),
                FieldTemplate("异动原因", "异动原因", "VARCHAR(200)", True, description="异动具体原因"),
                FieldTemplate("异动日期", "异动日期", "DATE", True, description="异动生效日期"),
                FieldTemplate("原部门", "调出部门", "VARCHAR(100)", False, description="异动前部门"),
                FieldTemplate("新部门", "调入部门", "VARCHAR(100)", False, description="异动后部门"),
                FieldTemplate("原职位", "原职位", "VARCHAR(100)", False, description="异动前职位"),
                FieldTemplate("新职位", "新职位", "VARCHAR(100)", False, description="异动后职位"),
                FieldTemplate("备注", "备注信息", "TEXT", False, description="补充说明"),
            ],
            created_at=datetime.now().isoformat(),
            tags=["异动", "标准", "通用"]
        )
        templates[change_template.id] = change_template
        
        # 简化工资表模板
        salary_simple_template = Template(
            id="built_in_salary_simple",
            name="简化工资表模板",
            type=TemplateType.SALARY,
            description="简化版工资表，适用于小型企业",
            fields=[
                FieldTemplate("姓名", "员工姓名", "VARCHAR(100)", True),
                FieldTemplate("工号", "员工工号", "VARCHAR(50)", True),
                FieldTemplate("部门", "所属部门", "VARCHAR(100)", False),
                FieldTemplate("应发工资", "应发工资", "DECIMAL(10,2)", True),
                FieldTemplate("实发工资", "实发工资", "DECIMAL(10,2)", True),
            ],
            created_at=datetime.now().isoformat(),
            tags=["工资", "简化", "小型企业"]
        )
        templates[salary_simple_template.id] = salary_simple_template
        
        return templates
    
    def _load_user_templates(self) -> Dict[str, Template]:
        """加载用户自定义模板"""
        templates = {}
        user_template_file = os.path.join(self.config_dir, "user_templates.json")
        
        if os.path.exists(user_template_file):
            try:
                with open(user_template_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for template_data in data:
                    template = self._dict_to_template(template_data)
                    templates[template.id] = template
                
                self.logger.info(f"成功加载 {len(templates)} 个用户模板")
                
            except Exception as e:
                self.logger.error(f"加载用户模板失败: {e}")
        
        return templates
    
    def _dict_to_template(self, data: Dict) -> Template:
        """字典转换为模板对象"""
        fields = []
        for field_data in data.get('fields', []):
            field = FieldTemplate(**field_data)
            fields.append(field)
        
        data['fields'] = fields
        data['type'] = TemplateType(data.get('type', 'custom'))
        return Template(**data)
    
    def _template_to_dict(self, template: Template) -> Dict:
        """模板对象转换为字典"""
        template_dict = asdict(template)
        template_dict['type'] = template.type.value
        return template_dict
    
    def get_template_for_table_type(self, table_type: str) -> Optional[Template]:
        """获取表类型对应的默认模板"""
        if table_type == "💰 工资表":
            return self.built_in_templates.get("built_in_salary_standard")
        elif table_type == "🔄 异动表":
            return self.built_in_templates.get("built_in_change_standard")
        else:
            return None
    
    def get_all_templates(self, template_type: Optional[TemplateType] = None) -> List[Template]:
        """获取所有模板"""
        all_templates = {**self.built_in_templates, **self.user_templates}
        
        if template_type:
            return [template for template in all_templates.values() 
                   if template.type == template_type]
        
        return list(all_templates.values())
    
    def get_template_by_id(self, template_id: str) -> Optional[Template]:
        """根据ID获取模板"""
        return (self.built_in_templates.get(template_id) or 
                self.user_templates.get(template_id))
    
    def save_user_template(self, template: Template) -> bool:
        """保存用户自定义模板"""
        try:
            # 设置创建时间
            if not template.created_at:
                template.created_at = datetime.now().isoformat()
            
            # 设置为自定义类型
            template.type = TemplateType.CUSTOM
            
            # 添加到用户模板
            self.user_templates[template.id] = template
            
            # 持久化保存
            self._save_user_templates()
            
            self.logger.info(f"成功保存用户模板: {template.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存用户模板失败: {e}")
            return False
    
    def _save_user_templates(self):
        """持久化保存用户模板"""
        user_template_file = os.path.join(self.config_dir, "user_templates.json")
        
        template_data = []
        for template in self.user_templates.values():
            template_data.append(self._template_to_dict(template))
        
        with open(user_template_file, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, ensure_ascii=False, indent=2)
    
    def delete_user_template(self, template_id: str) -> bool:
        """删除用户模板"""
        try:
            if template_id in self.user_templates:
                del self.user_templates[template_id]
                self._save_user_templates()
                self.logger.info(f"成功删除用户模板: {template_id}")
                return True
            else:
                self.logger.warning(f"模板不存在: {template_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除用户模板失败: {e}")
            return False
    
    def export_template(self, template_id: str, file_path: str) -> bool:
        """导出模板到文件"""
        try:
            template = self.get_template_by_id(template_id)
            if not template:
                self.logger.error(f"模板不存在: {template_id}")
                return False
            
            template_data = self._template_to_dict(template)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"成功导出模板: {template.name} -> {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出模板失败: {e}")
            return False
    
    def import_template(self, file_path: str) -> bool:
        """从文件导入模板"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
            
            template = self._dict_to_template(template_data)
            
            # 生成新的ID避免冲突
            if template.id in self.built_in_templates or template.id in self.user_templates:
                template.id = f"{template.id}_imported_{int(datetime.now().timestamp())}"
            
            # 标记为导入的模板
            template.created_by = "imported"
            template.created_at = datetime.now().isoformat()
            
            return self.save_user_template(template)
            
        except Exception as e:
            self.logger.error(f"导入模板失败: {e}")
            return False
    
    def create_template_from_mapping(self, name: str, table_type: str, 
                                   mapping_config: Dict[str, Dict]) -> Template:
        """从映射配置创建模板"""
        fields = []
        
        for source_field, config in mapping_config.items():
            field = FieldTemplate(
                field_name=config.get('target_field', source_field),
                display_name=config.get('display_name', source_field),
                data_type=config.get('data_type', 'VARCHAR(255)'),
                is_required=config.get('is_required', False),
                description=config.get('description', ''),
                validation_rules=config.get('validation_rules', [])
            )
            fields.append(field)
        
        template_type = TemplateType.SALARY if table_type == "💰 工资表" else TemplateType.CHANGE
        
        template = Template(
            id=f"user_{table_type}_{int(datetime.now().timestamp())}",
            name=name,
            type=template_type,
            description=f"从映射配置创建的{table_type}模板",
            fields=fields,
            created_by="user",
            created_at=datetime.now().isoformat(),
            tags=[table_type, "用户创建"]
        )
        
        return template
    
    def get_template_suggestions(self, excel_headers: List[str]) -> List[Template]:
        """基于Excel字段推荐合适的模板"""
        suggestions = []
        
        for template in self.get_all_templates():
            # 计算匹配度
            template_fields = {field.field_name.lower() for field in template.fields}
            excel_fields = {header.lower() for header in excel_headers}
            
            # 计算交集比例
            intersection = template_fields & excel_fields
            match_ratio = len(intersection) / len(template_fields) if template_fields else 0
            
            # 如果匹配度超过30%，则推荐
            if match_ratio > 0.3:
                suggestions.append((template, match_ratio))
        
        # 按匹配度排序
        suggestions.sort(key=lambda x: x[1], reverse=True)
        
        return [template for template, _ in suggestions[:5]]  # 返回前5个推荐
    
    def validate_template(self, template: Template) -> Dict[str, List[str]]:
        """验证模板有效性"""
        errors = []
        warnings = []
        
        # 检查基本信息
        if not template.name.strip():
            errors.append("模板名称不能为空")
        
        if not template.fields:
            errors.append("模板必须包含至少一个字段")
        
        # 检查字段
        field_names = []
        for field in template.fields:
            if not field.field_name.strip():
                errors.append("字段名不能为空")
            
            if field.field_name in field_names:
                errors.append(f"字段名重复: {field.field_name}")
            field_names.append(field.field_name)
            
            # 检查数据类型
            valid_types = ['VARCHAR', 'INT', 'DECIMAL', 'DATE', 'TEXT', 'BOOLEAN']
            if not any(field.data_type.upper().startswith(vt) for vt in valid_types):
                warnings.append(f"字段 '{field.field_name}' 的数据类型可能不正确: {field.data_type}")
        
        # 检查必需字段
        required_fields = [field for field in template.fields if field.is_required]
        if not required_fields:
            warnings.append("建议至少设置一个必需字段")
        
        return {
            'errors': errors,
            'warnings': warnings
        }
    
    def save_enhanced_template(self, template_data: Dict[str, Any]) -> bool:
        """保存增强模板"""
        try:
            # 确保模板目录存在
            template_dir = "config/templates"
            os.makedirs(template_dir, exist_ok=True)
            
            # 生成模板文件名
            safe_name = "".join(c for c in template_data['name'] if c.isalnum() or c in (' ', '-', '_')).strip()
            safe_name = safe_name.replace(' ', '_')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{safe_name}_{timestamp}.json"
            filepath = os.path.join(template_dir, filename)
            
            # 添加元数据
            template_data.update({
                'id': f"template_{timestamp}",
                'created_at': datetime.now().isoformat(),
                'last_modified': datetime.now().isoformat(),
                'file_path': filepath,
                'template_format_version': '2.0'
            })
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"增强模板保存成功: {template_data['name']} -> {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存增强模板失败: {e}")
            return False
    
    def load_enhanced_templates(self) -> List[Dict[str, Any]]:
        """加载增强模板"""
        import glob
        
        templates = []
        template_dir = "config/templates"
        
        if not os.path.exists(template_dir):
            return templates
        
        try:
            # 查找所有模板文件
            pattern = os.path.join(template_dir, "*.json")
            template_files = glob.glob(pattern)
            
            for filepath in template_files:
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        template_data = json.load(f)
                        
                    # 验证模板格式
                    if self._validate_template_format(template_data):
                        templates.append(template_data)
                    else:
                        self.logger.warning(f"模板格式无效: {filepath}")
                        
                except Exception as e:
                    self.logger.error(f"加载模板文件失败: {filepath} - {e}")
            
            self.logger.info(f"成功加载 {len(templates)} 个增强模板")
            return templates
            
        except Exception as e:
            self.logger.error(f"加载增强模板失败: {e}")
            return templates
    
    def _validate_template_format(self, template_data: Dict[str, Any]) -> bool:
        """验证模板格式"""
        required_fields = ['name', 'table_type', 'mapping_config']
        
        for field in required_fields:
            if field not in template_data:
                return False
        
        # 验证映射配置格式
        mapping_config = template_data.get('mapping_config', {})
        if not isinstance(mapping_config, dict):
            return False
        
        return True
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        enhanced_templates = self.load_enhanced_templates()
        
        # 按类型统计
        type_stats = {}
        for template in enhanced_templates:
            table_type = template.get('table_type', '未知')
            type_stats[table_type] = type_stats.get(table_type, 0) + 1
        
        # 按作用域统计
        scope_stats = {}
        for template in enhanced_templates:
            scope = template.get('scope', '个人模板')
            scope_stats[scope] = scope_stats.get(scope, 0) + 1
        
        # 最近创建的模板
        recent_templates = sorted(enhanced_templates, 
                                key=lambda x: x.get('created_at', ''), reverse=True)[:5]
        
        return {
            'total_builtin_templates': len(self.built_in_templates),
            'total_user_templates': len(self.user_templates),
            'total_enhanced_templates': len(enhanced_templates),
            'type_distribution': type_stats,
            'scope_distribution': scope_stats,
            'recent_templates': [{'name': t.get('name'), 'created_at': t.get('created_at')} 
                               for t in recent_templates]
        }
    
    def delete_template(self, template_name: str) -> bool:
        """删除用户模板"""
        try:
            enhanced_templates = self.load_enhanced_templates()
            
            # 查找并删除模板文件
            for template in enhanced_templates:
                if template.get('name') == template_name:
                    file_path = template.get('file_path')
                    if file_path and os.path.exists(file_path):
                        os.remove(file_path)
                        self.logger.info(f"模板删除成功: {template_name}")
                        return True
            
            self.logger.warning(f"未找到要删除的模板: {template_name}")
            return False
            
        except Exception as e:
            self.logger.error(f"删除模板失败: {e}")
            return False
