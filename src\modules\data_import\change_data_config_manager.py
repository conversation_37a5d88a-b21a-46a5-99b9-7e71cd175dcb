"""
异动表配置管理器
管理用户配置的保存、加载、删除和模式匹配
支持统一配置格式和向后兼容
"""

import json
import os
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime
from pathlib import Path
from difflib import SequenceMatcher
import pandas as pd
from loguru import logger

# 导入统一配置格式相关模块
try:
    from src.core.unified_config_schema import (
        ConfigType, FieldType, FieldConfig, SheetConfig,
        SingleSheetConfig, MultiSheetConfig, TemplateConfig,
        ConfigSerializer, ConfigDeserializer
    )
    from src.core.config_adapter import ConfigAdapter
    UNIFIED_CONFIG_AVAILABLE = True
except ImportError as e:
    logger.warning(f"统一配置格式模块导入失败，将使用兼容模式: {e}")
    UNIFIED_CONFIG_AVAILABLE = False

# 导入缓存管理器
try:
    from src.core.config_cache_manager import get_cache_manager
    CACHE_AVAILABLE = True
except ImportError as e:
    logger.warning(f"缓存管理器导入失败，将不使用缓存: {e}")
    CACHE_AVAILABLE = False


class ChangeDataConfigManager:
    """异动表配置管理器"""
    
    def __init__(self, config_dir: str = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件存储目录，默认为 state/change_data_configs
        """
        if config_dir is None:
            config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 
                                     'state', 'change_data_configs')
        
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.configs_file = self.config_dir / 'configurations.json'
        self.templates_file = self.config_dir / 'templates.json'
        self.metadata_file = self.config_dir / 'metadata.json'
        
        # 加载现有配置
        self.configs = self._load_configs()
        self.templates = self._load_templates()
        self.metadata = self._load_metadata()
        
        # 初始化缓存管理器
        if CACHE_AVAILABLE:
            self.cache_manager = get_cache_manager()
            self.cache_enabled = True
            logger.info("配置缓存已启用")
        else:
            self.cache_manager = None
            self.cache_enabled = False
            logger.info("配置缓存未启用")

        logger.info(f"配置管理器初始化完成，配置目录: {self.config_dir}")

        # 创建用户配置和模板目录
        self.user_config_dir = self.config_dir / 'user_configs'
        self.template_dir = self.config_dir / 'templates'
        self.user_config_dir.mkdir(exist_ok=True)
        self.template_dir.mkdir(exist_ok=True)
    
    def _load_configs(self) -> Dict[str, Any]:
        """加载用户配置"""
        if self.configs_file.exists():
            try:
                with open(self.configs_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                return {}
        return {}
    
    def _load_templates(self) -> Dict[str, Any]:
        """加载配置模板"""
        if self.templates_file.exists():
            try:
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    templates = json.load(f)
                    if templates:  # 如果文件存在且不为空
                        return templates
            except Exception as e:
                logger.error(f"加载模板文件失败: {e}")
        
        # 获取默认模板并保存到文件
        templates = self._get_default_templates()
        self._save_templates(templates)
        return templates
    
    def _load_metadata(self) -> Dict[str, Any]:
        """加载元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载元数据文件失败: {e}")
                return {"version": "1.0", "last_updated": None}
        return {"version": "1.0", "last_updated": None}
    
    def _save_templates(self, templates: Dict[str, Any]):
        """保存模板到文件"""
        try:
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates, f, ensure_ascii=False, indent=2)
            logger.info(f"模板已保存到 {self.templates_file}")
        except Exception as e:
            logger.error(f"保存模板文件失败: {e}")
    
    def _get_default_templates(self) -> Dict[str, Any]:
        """获取默认配置模板"""
        return {
            "standard": {
                "name": "标准异动表模板",
                "description": "适用于大多数标准格式的异动表",
                "field_types": {
                    "工号": "employee_id_string",
                    "姓名": "name_string",
                    "部门": "text_string",
                    "职务": "text_string",
                    "岗位工资": "salary_float",
                    "薪级工资": "salary_float",
                    "津贴": "salary_float",
                    "补贴": "salary_float",
                    "奖金": "salary_float",
                    "绩效": "salary_float"
                },
                "formatting_rules": {
                    "salary_float": {
                        "decimal_places": 2,
                        "thousands_separator": True,
                        "negative_format": "parentheses"
                    },
                    "employee_id_string": {
                        "leading_zeros": True,
                        "min_length": 6
                    }
                }
            },
            "comprehensive": {
                "name": "综合异动表模板",
                "description": "包含更多字段的综合异动表",
                "field_types": {
                    "工号": "employee_id_string",
                    "姓名": "name_string",
                    "身份证号": "id_number_string",
                    "部门名称": "text_string",
                    "部门代码": "code_string",
                    "职务": "text_string",
                    "人员类别": "text_string",
                    "人员类别代码": "code_string",
                    "入职日期": "date_string",
                    "岗位工资": "salary_float",
                    "薪级工资": "salary_float",
                    "津贴": "salary_float",
                    "补贴": "salary_float",
                    "奖金": "salary_float",
                    "绩效": "salary_float",
                    "扣款": "salary_float",
                    "实发工资": "salary_float"
                },
                "formatting_rules": {
                    "salary_float": {
                        "decimal_places": 2,
                        "thousands_separator": True,
                        "negative_format": "minus"
                    },
                    "date_string": {
                        "format": "%Y-%m-%d"
                    }
                }
            }
        }
    
    def save_config(self, config_name: str, config_data: Dict[str, Any], 
                   description: str = None) -> bool:
        """
        保存用户配置
        
        Args:
            config_name: 配置名称
            config_data: 配置数据
            description: 配置描述
            
        Returns:
            是否保存成功
        """
        try:
            # 添加元数据
            config_entry = {
                "name": config_name,
                "description": description or "",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "data": config_data
            }
            
            # 保存到配置字典
            self.configs[config_name] = config_entry
            
            # 写入文件
            with open(self.configs_file, 'w', encoding='utf-8') as f:
                json.dump(self.configs, f, ensure_ascii=False, indent=2)
            
            # 更新元数据
            self.metadata["last_updated"] = datetime.now().isoformat()
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
            
            logger.info(f"配置 '{config_name}' 保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False
    
    def load_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """
        加载指定配置

        Args:
            config_name: 配置名称

        Returns:
            配置数据，如果不存在返回 None
        """
        # 🔧 [缓存] 尝试从缓存加载
        cache_key = f"config:{config_name}"
        if self.cache_enabled and self.cache_manager:
            cached_result = self.cache_manager.get(cache_key, self.configs_file)
            if cached_result is not None:
                logger.debug(f"从缓存加载配置: {config_name}")
                return cached_result

        if config_name in self.configs:
            logger.info(f"加载配置 '{config_name}'")
            config_data = self.configs[config_name]

            # 🔧 [统一格式] 使用配置适配器处理不同格式
            if UNIFIED_CONFIG_AVAILABLE:
                try:
                    unified_config = ConfigAdapter.convert_to_unified(config_data)

                    # 返回兼容旧格式的字段映射和类型
                    if isinstance(unified_config, MultiSheetConfig):
                        # 多表配置：返回第一个工作表的配置
                        if unified_config.sheets:
                            first_sheet = next(iter(unified_config.sheets.values()))
                            result = {
                                "field_mapping": first_sheet.field_mapping,
                                "field_types": first_sheet.field_types,
                                "formatting_rules": {}
                            }

                            # 🔧 [缓存] 将结果存入缓存
                            if self.cache_enabled and self.cache_manager:
                                self.cache_manager.put(
                                    cache_key,
                                    result,
                                    file_path=self.configs_file,
                                    metadata={"config_name": config_name, "config_type": "multi_sheet"}
                                )
                                logger.debug(f"多表配置已缓存: {config_name}")

                            return result
                        else:
                            logger.warning(f"多表配置 '{config_name}' 中没有找到工作表")
                            return None
                    elif isinstance(unified_config, SingleSheetConfig):
                        # 单表配置
                        if unified_config.sheet:
                            result = {
                                "field_mapping": unified_config.sheet.field_mapping,
                                "field_types": unified_config.sheet.field_types,
                                "formatting_rules": {}
                            }

                            # 🔧 [缓存] 将结果存入缓存
                            if self.cache_enabled and self.cache_manager:
                                self.cache_manager.put(
                                    cache_key,
                                    result,
                                    file_path=self.configs_file,
                                    metadata={"config_name": config_name, "config_type": "single_sheet"}
                                )
                                logger.debug(f"单表配置已缓存: {config_name}")

                            return result
                        else:
                            return None
                    else:
                        logger.warning(f"不支持的配置类型: {type(unified_config)}")
                        return None

                except Exception as e:
                    logger.error(f"统一格式转换失败，使用兼容模式: {e}")
                    # 降级到旧的处理逻辑
                    pass

            # 兼容模式：使用旧的处理逻辑
            if config_data.get("type") == "multi_sheet_user_config":
                # 多表配置：返回第一个工作表的配置
                sheets = config_data.get("sheets", {})
                if sheets:
                    first_sheet_name = next(iter(sheets.keys()))
                    first_sheet_config = sheets[first_sheet_name].get("config", {})
                    logger.info(f"从多表配置 '{config_name}' 中加载第一个工作表 '{first_sheet_name}' 的配置")

                    # 🔧 [缓存] 将结果存入缓存
                    if first_sheet_config and self.cache_enabled and self.cache_manager:
                        self.cache_manager.put(
                            cache_key,
                            first_sheet_config,
                            file_path=self.configs_file,
                            metadata={"config_name": config_name, "config_type": "legacy_multi_sheet"}
                        )
                        logger.debug(f"兼容模式多表配置已缓存: {config_name}")

                    return first_sheet_config
                else:
                    logger.warning(f"多表配置 '{config_name}' 中没有找到工作表")
                    return None
            else:
                # 单表配置：直接返回data部分
                result = config_data.get("data")

                # 🔧 [缓存] 将结果存入缓存
                if result is not None and self.cache_enabled and self.cache_manager:
                    self.cache_manager.put(
                        cache_key,
                        result,
                        file_path=self.configs_file,
                        metadata={"config_name": config_name, "config_type": config_data.get("type", "unknown")}
                    )
                    logger.debug(f"配置已缓存: {config_name}")

                return result

        logger.warning(f"配置 '{config_name}' 不存在")
        return None

    def load_unified_config(self, config_name: str) -> Optional[Union['SingleSheetConfig', 'MultiSheetConfig']]:
        """
        加载统一格式的配置对象

        Args:
            config_name: 配置名称

        Returns:
            统一格式的配置对象，如果不存在或不支持统一格式返回 None
        """
        if not UNIFIED_CONFIG_AVAILABLE:
            logger.warning("统一配置格式不可用")
            return None

        if config_name in self.configs:
            logger.info(f"加载统一格式配置 '{config_name}'")
            config_data = self.configs[config_name]

            try:
                unified_config = ConfigAdapter.convert_to_unified(config_data)
                logger.info(f"成功转换为统一格式: {type(unified_config).__name__}")
                return unified_config
            except Exception as e:
                logger.error(f"转换为统一格式失败: {e}")
                return None
        else:
            logger.warning(f"配置 '{config_name}' 不存在")
            return None

    def save_unified_config(self, config: Union['SingleSheetConfig', 'MultiSheetConfig'],
                           save_format: str = "unified") -> bool:
        """
        保存统一格式的配置

        Args:
            config: 统一格式的配置对象
            save_format: 保存格式 ("unified", "legacy_multi_user", "legacy_system")

        Returns:
            是否保存成功
        """
        if not UNIFIED_CONFIG_AVAILABLE:
            logger.warning("统一配置格式不可用")
            return False

        try:
            if save_format == "unified":
                # 保存为统一格式
                config_data = ConfigSerializer.to_dict(config)
            else:
                # 转换为旧格式保存
                config_data = ConfigAdapter.convert_to_legacy(config, save_format)

            # 添加到配置字典
            self.configs[config.name] = config_data

            # 写入文件
            with open(self.configs_file, 'w', encoding='utf-8') as f:
                json.dump(self.configs, f, ensure_ascii=False, indent=2)

            logger.info(f"统一格式配置 '{config.name}' 保存成功，格式: {save_format}")
            return True

        except Exception as e:
            logger.error(f"保存统一格式配置失败: {e}")
            return False

    def delete_config(self, config_name: str) -> bool:
        """
        删除指定配置
        
        Args:
            config_name: 配置名称
            
        Returns:
            是否删除成功
        """
        try:
            if config_name in self.configs:
                del self.configs[config_name]
                
                # 更新文件
                with open(self.configs_file, 'w', encoding='utf-8') as f:
                    json.dump(self.configs, f, ensure_ascii=False, indent=2)
                
                logger.info(f"配置 '{config_name}' 删除成功")
                return True
            
            logger.warning(f"配置 '{config_name}' 不存在")
            return False
            
        except Exception as e:
            logger.error(f"删除配置失败: {e}")
            return False
    
    def list_configs(self) -> List[Dict[str, Any]]:
        """
        列出所有用户配置
        
        Returns:
            配置列表
        """
        configs_list = []
        for name, config in self.configs.items():
            configs_list.append({
                "name": name,
                "description": config.get("description", ""),
                "created_at": config.get("created_at", ""),
                "updated_at": config.get("updated_at", "")
            })
        return configs_list
    
    def find_similar_config(self, excel_headers: List[str], threshold: float = 0.7) -> Optional[Tuple[str, float]]:
        """
        查找与给定Excel列头最相似的配置
        
        Args:
            excel_headers: Excel文件的列头列表
            threshold: 相似度阈值 (0-1)
            
        Returns:
            最相似的配置名称和相似度分数，如果没有超过阈值的返回 None
        """
        best_match = None
        best_score = 0
        
        for config_name, config_entry in self.configs.items():
            config_data = config_entry.get("data", {})
            field_mapping = config_data.get("field_mapping", {})
            
            if not field_mapping:
                continue
            
            # 计算相似度
            config_headers = list(field_mapping.keys())
            score = self._calculate_similarity(excel_headers, config_headers)
            
            if score > best_score and score >= threshold:
                best_score = score
                best_match = config_name
        
        if best_match:
            logger.info(f"找到相似配置 '{best_match}'，相似度: {best_score:.2f}")
            return best_match, best_score
        
        return None
    
    def _calculate_similarity(self, list1: List[str], list2: List[str]) -> float:
        """
        计算两个列表的相似度
        
        Args:
            list1: 第一个列表
            list2: 第二个列表
            
        Returns:
            相似度分数 (0-1)
        """
        # 使用集合交集计算
        set1 = set(list1)
        set2 = set(list2)
        
        if not set1 or not set2:
            return 0.0
        
        intersection = set1 & set2
        union = set1 | set2
        
        # Jaccard 相似度
        jaccard = len(intersection) / len(union)
        
        # 顺序相似度（使用 SequenceMatcher）
        sequence_score = SequenceMatcher(None, list1, list2).ratio()
        
        # 综合相似度（加权平均）
        return 0.7 * jaccard + 0.3 * sequence_score
    
    def get_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        获取配置模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            模板数据
        """
        return self.templates.get(template_name)
    
    def list_templates(self) -> List[Dict[str, str]]:
        """
        列出所有可用模板
        
        Returns:
            模板列表
        """
        templates_list = []
        for key, template in self.templates.items():
            templates_list.append({
                "key": key,
                "name": template.get("name", key),
                "description": template.get("description", "")
            })
        return templates_list
    
    def create_config_from_template(self, template_name: str, 
                                   excel_headers: List[str]) -> Dict[str, Any]:
        """
        基于模板和Excel列头创建配置
        
        Args:
            template_name: 模板名称
            excel_headers: Excel列头列表
            
        Returns:
            生成的配置
        """
        template = self.get_template(template_name)
        if not template:
            logger.warning(f"模板 '{template_name}' 不存在")
            return {}
        
        # 基于模板创建配置
        config = {
            "field_mapping": {},
            "field_types": {},
            "formatting_rules": template.get("formatting_rules", {}),
            "template_based": template_name
        }
        
        # 智能匹配字段
        template_fields = template.get("field_types", {})
        
        for excel_header in excel_headers:
            # 尝试精确匹配
            if excel_header in template_fields:
                config["field_mapping"][excel_header] = excel_header
                config["field_types"][excel_header] = template_fields[excel_header]
            else:
                # 模糊匹配
                best_match = self._find_best_field_match(excel_header, template_fields.keys())
                if best_match:
                    config["field_mapping"][excel_header] = best_match
                    config["field_types"][excel_header] = template_fields[best_match]
                else:
                    # 默认类型
                    config["field_mapping"][excel_header] = excel_header
                    config["field_types"][excel_header] = "text_string"
        
        return config
    
    def _find_best_field_match(self, field: str, candidates: List[str], 
                               threshold: float = 0.6) -> Optional[str]:
        """
        查找最佳字段匹配
        
        Args:
            field: 要匹配的字段
            candidates: 候选字段列表
            threshold: 匹配阈值
            
        Returns:
            最佳匹配的字段名，如果没有超过阈值的返回 None
        """
        best_match = None
        best_score = 0
        
        for candidate in candidates:
            # 计算相似度
            score = SequenceMatcher(None, field.lower(), candidate.lower()).ratio()
            
            # 特殊规则：包含关系加分
            if field in candidate or candidate in field:
                score += 0.3
            
            if score > best_score and score >= threshold:
                best_score = score
                best_match = candidate
        
        return best_match
    

    def import_config(self, import_path: str, config_name: str = None) -> bool:
        """
        从文件导入配置
        
        Args:
            import_path: 导入文件路径
            config_name: 配置名称（可选，默认使用文件中的名称）
            
        Returns:
            是否导入成功
        """
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 使用指定名称或文件中的名称
            name = config_name or config.get("name", Path(import_path).stem)
            
            # 更新时间戳
            config["imported_at"] = datetime.now().isoformat()
            config["updated_at"] = datetime.now().isoformat()
            
            # 保存配置
            self.configs[name] = config
            
            # 写入文件
            with open(self.configs_file, 'w', encoding='utf-8') as f:
                json.dump(self.configs, f, ensure_ascii=False, indent=2)
            
            logger.info(f"配置从 {import_path} 导入为 '{name}'")
            return True
            
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False
    
    def get_config_versions(self, config_name: str) -> List[Dict[str, Any]]:
        """
        获取配置的版本历史（预留接口）
        
        Args:
            config_name: 配置名称
            
        Returns:
            版本历史列表
        """
        # TODO: 实现版本控制功能
        logger.info(f"获取配置 '{config_name}' 的版本历史（功能待实现）")
        return []
    
    def merge_configs(self, config1_name: str, config2_name: str, 
                     merged_name: str) -> bool:
        """
        合并两个配置
        
        Args:
            config1_name: 第一个配置名称
            config2_name: 第二个配置名称
            merged_name: 合并后的配置名称
            
        Returns:
            是否合并成功
        """
        try:
            config1 = self.load_config(config1_name)
            config2 = self.load_config(config2_name)
            
            if not config1 or not config2:
                logger.warning("配置不存在，无法合并")
                return False
            
            # 合并配置（config2 覆盖 config1）
            merged_config = {**config1, **config2}
            
            # 合并字段映射和类型
            if "field_mapping" in config1 and "field_mapping" in config2:
                merged_config["field_mapping"] = {
                    **config1.get("field_mapping", {}),
                    **config2.get("field_mapping", {})
                }
            
            if "field_types" in config1 and "field_types" in config2:
                merged_config["field_types"] = {
                    **config1.get("field_types", {}),
                    **config2.get("field_types", {})
                }
            
            # 保存合并后的配置
            description = f"合并自 {config1_name} 和 {config2_name}"
            return self.save_config(merged_name, merged_config, description)
            
        except Exception as e:
            logger.error(f"合并配置失败: {e}")
            return False
    
    def list_user_configs(self) -> List[Dict[str, Any]]:
        """列出用户独立配置"""
        try:
            user_configs = []
            if not self.user_config_dir.exists():
                return user_configs
            
            for config_file in self.user_config_dir.glob('*.json'):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    # 处理多工作表配置
                    if config_data.get('type') == 'multi_sheet_user_config':
                        user_configs.append({
                            'name': config_data.get('name', config_file.stem),
                            'description': config_data.get('description', ''),
                            'created_at': config_data.get('created_at', ''),
                            'updated_at': config_data.get('updated_at', ''),
                            'type': config_data.get('type', 'user_config'),
                            'file_path': str(config_file),
                            'sheet_count': config_data.get('sheet_count', 0),
                            'sheets': list(config_data.get('sheets', {}).keys()),
                            'total_field_count': sum(
                                sheet_data.get('field_count', 0) 
                                for sheet_data in config_data.get('sheets', {}).values()
                            )
                        })
                    else:
                        # 单工作表配置（向后兼容）
                        user_configs.append({
                            'name': config_data.get('name', config_file.stem),
                            'description': config_data.get('description', ''),
                            'created_at': config_data.get('created_at', ''),
                            'updated_at': config_data.get('updated_at', ''),
                            'type': config_data.get('type', 'user_config'),
                            'file_path': str(config_file),
                            'sheet_name': config_data.get('sheet_name', 'unknown'),
                            'field_count': len(config_data.get('data', {}).get('field_mapping', {}))
                        })
                except Exception as e:
                    logger.warning(f"加载用户配置文件失败 {config_file}: {e}")
                    continue
            
            # 按创建时间排序
            user_configs.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return user_configs
            
        except Exception as e:
            logger.error(f"列出用户配置失败: {e}")
            return []
    
    def load_user_config(self, config_name: str) -> Optional[Dict[str, Any]]:
        """加载用户独立配置"""
        try:
            config_file = self.user_config_dir / f"{config_name}.json"
            if not config_file.exists():
                logger.warning(f"用户配置文件不存在: {config_file}")
                return None
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            logger.info(f"成功加载用户配置: {config_name}")
            return config_data.get('data', {})
            
        except Exception as e:
            logger.error(f"加载用户配置失败 {config_name}: {e}")
            return None
    
    def delete_user_config(self, config_name: str) -> bool:
        """删除用户独立配置"""
        try:
            config_file = self.user_config_dir / f"{config_name}.json"
            if config_file.exists():
                config_file.unlink()
                logger.info(f"用户配置已删除: {config_name}")
                return True
            else:
                logger.warning(f"用户配置文件不存在: {config_name}")
                return False
                
        except Exception as e:
            logger.error(f"删除用户配置失败 {config_name}: {e}")
            return False
    
    def export_user_config(self, config_name: str, export_path: str) -> bool:
        """导出用户配置到指定位置"""
        try:
            config_file = self.user_config_dir / f"{config_name}.json"
            if not config_file.exists():
                logger.error(f"用户配置不存在: {config_name}")
                return False
            
            import shutil
            shutil.copy2(config_file, export_path)
            logger.info(f"用户配置已导出: {config_name} -> {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出用户配置失败 {config_name}: {e}")
            return False
    
    def import_user_config(self, import_path: str) -> bool:
        """从文件导入用户配置"""
        try:
            import_file = Path(import_path)
            if not import_file.exists():
                logger.error(f"导入文件不存在: {import_path}")
                return False
            
            # 验证文件格式
            with open(import_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            if not config_data.get('type') == 'user_config':
                logger.error("不是有效的用户配置文件")
                return False
            
            # 复制到用户配置目录
            config_name = config_data.get('name', import_file.stem)
            target_file = self.user_config_dir / f"{config_name}.json"
            
            import shutil
            shutil.copy2(import_file, target_file)
            logger.info(f"用户配置已导入: {import_path} -> {config_name}")
            return True
            
        except Exception as e:
            logger.error(f"导入用户配置失败 {import_path}: {e}")
            return False