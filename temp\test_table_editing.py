#!/usr/bin/env python3
"""
测试表格编辑器显示效果
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTableWidget, QTableWidgetItem
from PyQt5.QtCore import QTimer

def test_table_editing():
    """测试表格编辑器显示效果"""
    
    app = QApplication([])
    
    # 创建主窗口
    window = QMainWindow()
    window.setWindowTitle("表格编辑器测试")
    window.resize(800, 600)
    
    # 创建中心部件
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 创建表格
    table = QTableWidget()
    table.setColumnCount(6)
    table.setHorizontalHeaderLabels([
        "Excel列名", "数据库字段", "显示名称", "数据类型", "是否必需", "验证状态"
    ])
    
    # 设置表格属性
    table.setAlternatingRowColors(True)
    table.setSelectionBehavior(QTableWidget.SelectRows)
    table.setSelectionMode(QTableWidget.SingleSelection)
    table.verticalHeader().setVisible(False)
    
    # 设置行高以确保编辑器有足够空间
    table.verticalHeader().setDefaultSectionSize(35)  # 设置默认行高为35px
    
    # 设置列宽
    column_widths = [150, 150, 120, 100, 80, 80]
    for col, width in enumerate(column_widths):
        table.setColumnWidth(col, width)
    
    # 添加测试数据
    test_data = [
        ("序号", "序号", "序号", "VARCHAR(100)", False, "✅"),
        ("工号", "工号", "工号", "VARCHAR(100)", False, "✅"),
        ("姓名", "姓名", "姓名", "VARCHAR(100)", False, "✅"),
        ("部门名称", "部门名称", "部门名称", "VARCHAR(100)", False, "✅"),
        ("人员类别", "人员类别", "人员类别", "VARCHAR(100)", False, "✅"),
        ("人员类别代码", "人员类别代码", "人员类别代码", "VARCHAR(100)", False, "✅"),
        ("2025年岗位工资", "field_2025年岗位工资", "2025年岗位工资", "VARCHAR(100)", False, "✅"),
        ("2025年校龄工资", "field_2025年校龄工资", "2025年校龄工资", "VARCHAR(100)", False, "✅"),
        ("津贴", "津贴", "津贴", "VARCHAR(100)", False, "✅"),
    ]
    
    table.setRowCount(len(test_data))
    
    for row, (excel_col, db_field, display_name, data_type, required, status) in enumerate(test_data):
        # Excel列名（只读）
        excel_item = QTableWidgetItem(excel_col)
        excel_item.setFlags(excel_item.flags() & ~excel_item.ItemIsEditable)
        table.setItem(row, 0, excel_item)
        
        # 数据库字段（可编辑）
        db_item = QTableWidgetItem(db_field)
        table.setItem(row, 1, db_item)
        
        # 显示名称（可编辑）
        display_item = QTableWidgetItem(display_name)
        table.setItem(row, 2, display_item)
        
        # 数据类型（只读，实际应该是下拉框）
        type_item = QTableWidgetItem(data_type)
        table.setItem(row, 3, type_item)
        
        # 是否必需（只读，实际应该是复选框）
        required_item = QTableWidgetItem("是" if required else "否")
        table.setItem(row, 4, required_item)
        
        # 验证状态（只读）
        status_item = QTableWidgetItem(status)
        status_item.setFlags(status_item.flags() & ~status_item.ItemIsEditable)
        table.setItem(row, 5, status_item)
    
    layout.addWidget(table)
    
    # 显示窗口
    window.show()
    
    def show_instructions():
        print("=== 表格编辑器测试 ===")
        print("请尝试以下操作：")
        print("1. 双击'数据库字段'列的任意单元格进行编辑")
        print("2. 双击'显示名称'列的任意单元格进行编辑")
        print("3. 检查编辑器是否有足够的高度和宽度")
        print("4. 特别注意编辑器是否清晰可见")
        print("5. 按Ctrl+C关闭测试")
        print()
        print("当前行高设置: 35px")
        print("如果编辑器仍然太窄，可能需要进一步调整行高或样式")
    
    # 2秒后显示说明
    QTimer.singleShot(2000, show_instructions)
    
    # 10秒后自动关闭
    QTimer.singleShot(10000, app.quit)
    
    app.exec_()

if __name__ == "__main__":
    test_table_editing()
