"""
P4级离休人员配置统一 - 完整集成测试
测试实际数据导入、格式化、显示的全流程
"""

import os
import sys
import json
import pandas as pd
from datetime import datetime

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_actual_data_import():
    """测试实际离休人员数据导入"""
    print("=" * 60)
    print("测试实际数据导入")
    print("=" * 60)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '姓名': ['张三', '李四', '王五', '赵六'],
        '部门名称': ['行政部', '财务部', '技术部', '后勤部'],
        '基本离休费': [5000.0, 4500.0, 5200.0, 4800.0],
        '结余津贴': [800.0, 750.0, 850.0, 800.0],
        '生活补贴': [1000.0, 1000.0, 1000.0, 1000.0],
        '住房补贴': [1500.0, 1500.0, 1500.0, 1500.0],
        '物业补贴': [300.0, 300.0, 300.0, 300.0],
        '离休补贴': [2000.0, 1800.0, 2100.0, 1900.0],
        '护理费': [1200.0, 0, 1500.0, 1000.0],
        '增发一次性生活补贴': [500.0, 500.0, 500.0, 500.0],
        '补发': [0, 200.0, 0, 150.0],
        '借支': [100.0, 0, 50.0, 0],
        '合计': [12300.0, 10550.0, 12900.0, 11950.0]
    })
    
    print("\n原始数据:")
    print(test_data.head())
    
    # 保存为临时Excel文件
    temp_file = "temp/test_retired_employees.xlsx"
    os.makedirs("temp", exist_ok=True)
    test_data.to_excel(temp_file, index=False)
    print(f"\n数据已保存到: {temp_file}")
    
    return temp_file, test_data


def test_field_type_detection():
    """测试字段类型检测"""
    print("\n" + "=" * 60)
    print("测试字段类型检测")
    print("=" * 60)
    
    from src.modules.format_management.format_config import FormatConfig
    
    config = FormatConfig("config/format_config.json")
    field_rules = config.get_field_type_rules()
    currency_fields = field_rules.get('currency_fields', [])
    
    # 检查所有离休人员货币字段
    retired_currency_fields = [
        '基本离休费', '结余津贴', '生活补贴', '住房补贴',
        '物业补贴', '离休补贴', '护理费', '增发一次性生活补贴',
        '补发', '借支', '合计'
    ]
    
    print("\n离休人员货币字段检测:")
    all_found = True
    for field in retired_currency_fields:
        if field in currency_fields:
            print(f"  [v] {field} - 已识别为货币字段")
        else:
            print(f"  [x] {field} - 未识别")
            all_found = False
    
    return all_found


def test_format_application():
    """测试格式化规则应用"""
    print("\n" + "=" * 60)
    print("测试格式化规则应用")
    print("=" * 60)
    
    from src.modules.format_management.format_config import FormatConfig
    
    config = FormatConfig("config/format_config.json")
    
    # 测试数据
    test_values = {
        '正常值': 5000.0,
        '零值': 0,
        '空值': None,
        '小数值': 1234.56
    }
    
    print("\n1. 货币格式化测试:")
    currency_format = config.get_format_rules('currency', 'retired_employees')
    # 避免打印包含特殊字符的字典
    print(f"   货币格式规则: decimal_places={currency_format.get('decimal_places', 2)}, zero_display={currency_format.get('zero_display', '0.00')}")
    
    for desc, value in test_values.items():
        if value is None:
            formatted = currency_format.get('zero_display', '0.00')
        else:
            decimal_places = currency_format.get('decimal_places', 2)
            formatted = f"{value:.{decimal_places}f}"
        print(f"   {desc}: {value} -> {formatted}")
    
    print("\n2. 字符串格式化测试:")
    string_format = config.get_format_rules('string', 'retired_employees')
    print(f"   字符串格式规则: empty_display={string_format.get('empty_display', '')}, trim_whitespace={string_format.get('trim_whitespace', True)}")
    
    test_strings = {
        '正常字符串': '张三',
        '空字符串': '',
        '空值': None,
        '破折号': '-'
    }
    
    for desc, value in test_strings.items():
        if value is None or value == '-':
            formatted = string_format.get('empty_display', '')
        else:
            formatted = value.strip() if string_format.get('trim_whitespace', True) else value
        print(f"   {desc}: '{value}' -> '{formatted}'")
    
    return True


def test_table_type_detection():
    """测试表类型检测"""
    print("\n" + "=" * 60)
    print("测试表类型检测")
    print("=" * 60)
    
    from src.modules.system_config.specialized_table_templates import SpecializedTableTemplates
    
    templates = SpecializedTableTemplates()
    
    # 离休人员表头
    headers = [
        '姓名', '部门名称', '基本离休费', '结余津贴', 
        '生活补贴', '住房补贴', '物业补贴', '离休补贴',
        '护理费', '增发一次性生活补贴', '补发', '借支', '合计'
    ]
    
    detected_type = templates.detect_template_by_headers(headers)
    print(f"\n检测到的表类型: {detected_type}")
    
    if detected_type == "retired_employees":
        print("[PASS] 离休人员表类型识别正确")
        
        # 获取模板配置
        template = templates.get_template(detected_type)
        if template:
            print(f"\n模板配置:")
            # template可能是列表，需要检查类型
            if isinstance(template, dict):
                print(f"  显示名称: {template.get('display_name', '离休人员')}")
                print(f"  描述: {template.get('description', '离休人员工资表')}")
                print(f"  关键字段: {template.get('key_fields', [])[:5]}...")
            elif isinstance(template, list) and len(template) > 0:
                print(f"  模板字段数: {len(template)}")
                print(f"  前5个字段: {template[:5]}...")
        return True
    else:
        print("[FAIL] 离休人员表类型识别失败")
        return False


def test_full_pipeline():
    """测试完整数据处理流程"""
    print("\n" + "=" * 60)
    print("测试完整数据处理流程")
    print("=" * 60)
    
    try:
        # 1. 创建测试数据
        temp_file, original_data = test_actual_data_import()
        
        # 2. 测试字段类型检测
        field_detection_ok = test_field_type_detection()
        
        # 3. 测试格式化规则
        format_application_ok = test_format_application()
        
        # 4. 测试表类型检测
        table_detection_ok = test_table_type_detection()
        
        # 5. 测试数据处理
        print("\n" + "=" * 60)
        print("测试数据处理结果")
        print("=" * 60)
        
        from src.modules.format_management.format_config import FormatConfig
        
        config = FormatConfig("config/format_config.json")
        
        # 应用格式化
        formatted_data = original_data.copy()
        
        # 格式化货币字段
        currency_fields = [
            '基本离休费', '结余津贴', '生活补贴', '住房补贴',
            '物业补贴', '离休补贴', '护理费', '增发一次性生活补贴',
            '补发', '借支', '合计'
        ]
        
        for field in currency_fields:
            if field in formatted_data.columns:
                formatted_data[field] = formatted_data[field].apply(
                    lambda x: "0.00" if pd.isna(x) or x == 0 else f"{x:.2f}"
                )
        
        print("\n格式化后的数据:")
        print(formatted_data.head(2))
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
            print(f"\n临时文件已清理: {temp_file}")
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("完整流程测试结果")
        print("=" * 60)
        
        all_passed = all([
            field_detection_ok,
            format_application_ok,
            table_detection_ok
        ])
        
        if all_passed:
            print("\n[PASS] 所有测试通过!")
            print("\n验证要点:")
            print("  [v] 离休人员字段已纳入统一货币规则")
            print("  [v] 格式化使用默认规则而非专用规则")
            print("  [v] 表类型检测正常工作")
            print("  [v] 数据处理流程完整")
        else:
            print("\n[FAIL] 部分测试失败")
        
        return all_passed
        
    except Exception as e:
        print(f"\n[ERROR] 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_no_specialized_code():
    """验证没有专用代码残留"""
    print("\n" + "=" * 60)
    print("验证专用代码完全移除")
    print("=" * 60)
    
    # 检查FormatConfig中没有专用方法
    from src.modules.format_management.format_config import FormatConfig
    
    config = FormatConfig("config/format_config.json")
    
    print("\n1. 检查专用方法:")
    methods_to_check = [
        'get_retired_staff_format_config',
        'is_retired_staff_table',
        'get_retired_format_rules'
    ]
    
    methods_found = []
    for method_name in methods_to_check:
        if hasattr(config, method_name):
            methods_found.append(method_name)
            print(f"  [x] {method_name} - 仍然存在")
        else:
            print(f"  [v] {method_name} - 已移除")
    
    print("\n2. 检查配置文件:")
    with open("config/format_config.json", 'r', encoding='utf-8') as f:
        config_json = json.load(f)
    
    specialized_sections = [
        'retired_staff_format_config',
        'retired_employees_format_config',
        'retired_format_rules'
    ]
    
    sections_found = []
    for section in specialized_sections:
        if section in config_json:
            sections_found.append(section)
            print(f"  [x] {section} - 仍然存在")
        else:
            print(f"  [v] {section} - 已移除")
    
    if not methods_found and not sections_found:
        print("\n[PASS] 所有专用代码已完全移除")
        return True
    else:
        print("\n[FAIL] 仍有专用代码残留")
        return False


def main():
    """主测试函数"""
    print("\n" + "=" * 80)
    print("P4级离休人员配置统一 - 完整集成测试")
    print("=" * 80)
    print(f"\n测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行所有测试
    results = []
    
    tests = [
        ("专用代码移除验证", verify_no_specialized_code),
        ("完整数据处理流程", test_full_pipeline)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n[ERROR] {test_name}失败: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 最终报告
    print("\n" + "=" * 80)
    print("P4优化最终验证报告")
    print("=" * 80)
    
    for test_name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{status} {test_name}")
    
    all_passed = all(result for _, result in results)
    
    print("\n" + "=" * 80)
    if all_passed:
        print("P4级优化验证成功!")
        print("\n达成目标:")
        print("  1. 完全移除离休人员专用配置代码")
        print("  2. 字段纳入统一管理规则")
        print("  3. 使用默认格式化规则")
        print("  4. 保持功能完整性")
        print("  5. 简化系统架构")
        print("\n系统优化进度: P0 -> P1 -> P2 -> P3 -> P4 [完成]")
    else:
        print("P4级优化验证失败，请检查问题")
    
    print("=" * 80)


if __name__ == "__main__":
    main()