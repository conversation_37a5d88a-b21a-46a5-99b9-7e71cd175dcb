#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段类型配置组件测试脚本

用于测试新创建的FieldTypeConfigWidget组件是否能正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

try:
    from src.gui.widgets.field_type_config_widget import FieldTypeConfigWidget
    print("✓ FieldTypeConfigWidget 导入成功")
except ImportError as e:
    print(f"✗ FieldTypeConfigWidget 导入失败: {e}")
    sys.exit(1)


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("字段类型配置组件测试")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加字段类型配置组件
        try:
            self.field_type_widget = FieldTypeConfigWidget()
            layout.addWidget(self.field_type_widget)
            print("✓ FieldTypeConfigWidget 创建成功")
        except Exception as e:
            print(f"✗ FieldTypeConfigWidget 创建失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("开始测试字段类型配置组件...")
    
    app = QApplication(sys.argv)
    
    try:
        window = TestMainWindow()
        window.show()
        
        print("✓ 测试窗口显示成功")
        print("请在窗口中测试字段类型配置功能")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
