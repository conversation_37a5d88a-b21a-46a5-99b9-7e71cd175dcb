"""
排序性能优化器 - P2级优化
提供排序缓存和优化策略
"""

import time
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
from loguru import logger

from src.core.sort_cache_manager import get_sort_cache_manager


class SortPerformanceOptimizer:
    """
    排序性能优化器
    
    作为数据层和缓存之间的桥梁
    提供透明的性能优化
    """
    
    def __init__(self):
        """初始化优化器"""
        self.logger = logger
        self.cache_manager = get_sort_cache_manager()
        
        # 性能阈值配置
        self.use_cache_threshold = 100  # 超过100行使用缓存
        self.force_db_sort_threshold = 10000  # 超过10000行强制使用数据库排序
        
        self.logger.info("SortPerformanceOptimizer 初始化完成")
    
    def optimize_sort_query(self, table_name: str, data_getter: callable,
                          page: int, page_size: int,
                          sort_columns: List[Dict[str, Any]]) -> Tuple[pd.DataFrame, int, Dict[str, Any]]:
        """
        优化排序查询
        
        Args:
            table_name: 表名
            data_getter: 获取数据的函数
            page: 页码
            page_size: 页大小
            sort_columns: 排序列配置
        
        Returns:
            (数据, 总记录数, 性能统计)
        """
        stats = {
            'cache_hit': False,
            'sort_time': 0,
            'total_time': 0,
            'optimization': None
        }
        
        start_time = time.time()
        
        try:
            # 1. 判断是否需要排序优化
            if not sort_columns or not self._should_use_optimization(table_name):
                # 直接使用原始方法
                data, total = data_getter(table_name, page, page_size, sort_columns)
                stats['optimization'] = 'none'
                stats['total_time'] = time.time() - start_time
                return data, total, stats
            
            # 2. 获取数据总量
            total_count = self._get_total_count(table_name, data_getter)
            
            if total_count == 0:
                stats['optimization'] = 'empty'
                return pd.DataFrame(), 0, stats
            
            # 3. 根据数据量选择优化策略
            if total_count <= self.use_cache_threshold:
                # 小数据量，直接排序
                data, total = data_getter(table_name, page, page_size, sort_columns)
                stats['optimization'] = 'direct'
                
            elif total_count >= self.force_db_sort_threshold:
                # 大数据量，使用数据库排序
                data, total = data_getter(table_name, page, page_size, sort_columns)
                stats['optimization'] = 'database'
                
            else:
                # 中等数据量，使用缓存优化
                data, total, cache_hit = self._sort_with_cache(
                    table_name, data_getter, page, page_size, sort_columns, total_count
                )
                stats['cache_hit'] = cache_hit
                stats['optimization'] = 'cache'
            
            stats['total_time'] = time.time() - start_time
            self._log_performance(table_name, stats)
            
            return data, total, stats
            
        except Exception as e:
            self.logger.error(f"排序优化失败: {e}")
            # 回退到原始方法
            data, total = data_getter(table_name, page, page_size, sort_columns)
            stats['optimization'] = 'fallback'
            stats['total_time'] = time.time() - start_time
            return data, total, stats
    
    def _should_use_optimization(self, table_name: str) -> bool:
        """判断是否应该使用优化"""
        # 可以根据表名、配置等决定
        return True
    
    def _get_total_count(self, table_name: str, data_getter: callable) -> int:
        """获取总记录数"""
        try:
            _, total = data_getter(table_name, 1, 1, None)
            return total
        except:
            return 0
    
    def _sort_with_cache(self, table_name: str, data_getter: callable,
                        page: int, page_size: int,
                        sort_columns: List[Dict[str, Any]],
                        total_count: int) -> Tuple[pd.DataFrame, int, bool]:
        """
        使用缓存的排序
        
        Returns:
            (数据, 总数, 是否命中缓存)
        """
        # 获取全部数据（或合理范围的数据）
        max_rows = min(total_count, 5000)  # 最多缓存5000行
        all_data, _ = data_getter(table_name, 1, max_rows, None)
        
        if isinstance(all_data, pd.DataFrame) and not all_data.empty:
            # 使用缓存管理器排序
            sorted_data, cache_hit = self.cache_manager.get_sorted_data(
                table_name, all_data, sort_columns
            )
            
            # 分页切片
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_data = sorted_data.iloc[start_idx:end_idx]
            
            return page_data, len(sorted_data), cache_hit
        
        # 回退到原始方法
        data, total = data_getter(table_name, page, page_size, sort_columns)
        return data, total, False
    
    def _log_performance(self, table_name: str, stats: Dict[str, Any]):
        """记录性能统计"""
        if stats['total_time'] > 0.5:  # 超过500ms记录
            self.logger.info(
                f"[P2优化] 排序性能: {table_name}, "
                f"策略={stats['optimization']}, "
                f"缓存={stats['cache_hit']}, "
                f"耗时={stats['total_time']:.3f}秒"
            )
    
    def invalidate_table_cache(self, table_name: str):
        """使表缓存失效"""
        self.cache_manager.invalidate_cache(table_name)
        self.logger.info(f"[P2优化] 清除表缓存: {table_name}")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return self.cache_manager.get_statistics()


# 创建全局优化器实例
_optimizer = None

def get_sort_optimizer() -> SortPerformanceOptimizer:
    """获取排序优化器的全局实例"""
    global _optimizer
    if _optimizer is None:
        _optimizer = SortPerformanceOptimizer()
    return _optimizer