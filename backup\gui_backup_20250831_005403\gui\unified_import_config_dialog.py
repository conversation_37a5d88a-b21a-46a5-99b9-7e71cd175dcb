#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据导入 & 字段配置对话框

整合原有的数据导入功能和异动表字段配置功能，提供统一的配置界面。
包含可视化的配置来源指示和冲突检测功能。

创建时间: 2025-01-20
方案: 统一配置界面详细设计（方案3）
"""

import json
import sys
import os
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QSplitter, QTabWidget,
    QGroupBox, QLabel, QPushButton, QComboBox, QCheckBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QTreeWidget,
    QTreeWidgetItem, QTextEdit, QLineEdit, QSpinBox, QFrame,
    QProgressBar, QMessageBox, QFileDialog, QDialogButtonBox,
    QWidget, QFormLayout, QScrollArea, QToolBar, QAction,
    QMenu, QApplication, QStyle
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, QMutex
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.utils.log_config import setup_logger
from src.modules.data_import.excel_importer import ExcelImporter
from src.modules.data_import.data_validator import DataValidator
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

# 导入新的配置管理类
from src.gui.unified_config_manager import ConfigurationManager, ConfigurationSource
from src.gui.unified_visual_indicator import VisualSourceIndicator
from src.gui.unified_conflict_analyzer import ConflictAnalyzer
from src.gui.unified_user_guide_system import get_user_guide_system
from src.gui.unified_feedback_system import get_feedback_manager
from src.gui.unified_performance_optimizer import (
    get_performance_optimizer, get_data_load_optimizer,
    get_ui_response_optimizer, get_stability_manager
)


class UnifiedImportConfigDialog(QDialog):
    """统一数据导入 & 字段配置对话框"""
    
    # 信号定义
    data_imported = pyqtSignal(dict)  # 数据导入完成信号
    config_applied = pyqtSignal(dict)  # 配置应用信号
    conflict_detected = pyqtSignal(list)  # 冲突检测信号
    
    def __init__(self, parent=None, dynamic_table_manager: Optional[DynamicTableManager] = None):
        """
        初始化统一配置对话框
        
        Args:
            parent: 父窗口
            dynamic_table_manager: 动态表管理器
        """
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.dynamic_table_manager = dynamic_table_manager
        
        # 核心组件初始化
        self.config_manager = ConfigurationManager()
        self.visual_indicator = VisualSourceIndicator()
        self.conflict_analyzer = ConflictAnalyzer()
        self.user_guide_system = get_user_guide_system()
        self.feedback_manager = get_feedback_manager()
        self.performance_optimizer = get_performance_optimizer()
        self.data_load_optimizer = get_data_load_optimizer()
        self.ui_response_optimizer = get_ui_response_optimizer()
        self.stability_manager = get_stability_manager()
        
        # 导入相关组件
        self.excel_importer = ExcelImporter()
        self.data_validator = DataValidator()
        self.multi_sheet_importer = MultiSheetImporter(dynamic_table_manager=self.dynamic_table_manager)
        
        # 数据存储
        self.current_file_path = None
        self.excel_data = None
        self.sheet_data = {}  # 存储所有Sheet数据
        self.current_configurations = {}  # 当前配置状态
        self.conflict_reports = []  # 冲突报告
        
        # 界面组件引用
        self.left_panel = None
        self.right_panel = None
        self.tab_widget = None
        self.sheet_tab = None
        self.mapping_tab = None
        self.advanced_tab = None
        self.preview_tab = None
        
        # 初始化界面
        self._init_ui()
        self._connect_signals()
        self._load_user_preferences()
        
        # 执行接口兼容性检查
        self._validate_interface_compatibility()
        
        self.logger.info("🎯 统一配置对话框初始化完成")
    
    def _validate_interface_compatibility(self):
        """验证接口兼容性"""
        compatibility_issues = []
        
        try:
            # 检查ExcelImporter接口
            if not hasattr(self.excel_importer, 'import_data'):
                compatibility_issues.append("ExcelImporter缺少import_data方法")
            if not hasattr(self.excel_importer, 'get_sheet_names'):
                compatibility_issues.append("ExcelImporter缺少get_sheet_names方法")
            
            # 检查ConfigurationManager接口
            if not hasattr(self.config_manager, 'save_configuration_with_source'):
                compatibility_issues.append("ConfigurationManager缺少save_configuration_with_source方法")
            
            # 检查VisualSourceIndicator接口
            if not hasattr(self.visual_indicator, 'get_source_config'):
                compatibility_issues.append("VisualSourceIndicator缺少get_source_config方法")
            
            # 检查关键组件是否成功初始化
            required_components = [
                ('excel_importer', self.excel_importer),
                ('config_manager', self.config_manager),
                ('visual_indicator', self.visual_indicator),
                ('conflict_analyzer', self.conflict_analyzer)
            ]
            
            for name, component in required_components:
                if component is None:
                    compatibility_issues.append(f"组件 {name} 初始化失败")
            
            # 报告兼容性问题
            if compatibility_issues:
                self.logger.warning(f"⚠️ 检测到 {len(compatibility_issues)} 个接口兼容性问题:")
                for issue in compatibility_issues:
                    self.logger.warning(f"  • {issue}")
            else:
                self.logger.info("✅ 接口兼容性检查通过")
                
        except Exception as e:
            self.logger.error(f"❌ 接口兼容性检查失败: {e}")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("统一数据导入 & 字段配置")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 设置窗口标志，启用最大化按钮
        self.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)
        
        # 设置窗口图标和样式
        self._setup_window_style()
        
        # 创建主布局（垂直布局，让内容区域在上，按钮在下）
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧面板
        self.left_panel = self._create_left_panel()
        splitter.addWidget(self.left_panel)
        
        # 创建右侧主面板
        self.right_panel = self._create_right_panel()
        splitter.addWidget(self.right_panel)
        
        # 设置分割器比例（给左侧更多空间，减少右侧固定宽度）
        splitter.setSizes([250, 1150])
        splitter.setStretchFactor(0, 0)  # 左侧面板不拉伸
        splitter.setStretchFactor(1, 1)  # 右侧面板可拉伸
        
        # 创建底部状态栏
        self._create_bottom_panel(main_layout)
        
        self.logger.info("📱 统一配置界面布局创建完成")
    
    def _setup_window_style(self):
        """设置窗口样式"""
        # 设置现代化样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 8px 16px;
                margin-right: 2px;
                border-radius: 4px 4px 0 0;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
                font-weight: bold;
                color: #2980b9;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
    
    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 文件信息区域
        file_group = self._create_file_info_group()
        layout.addWidget(file_group)
        
        # Sheet列表区域
        sheet_group = self._create_sheet_list_group()
        layout.addWidget(sheet_group)
        
        # 配置概览区域
        config_group = self._create_config_overview_group()
        layout.addWidget(config_group)
        
        # 快速操作区域
        quick_group = self._create_quick_actions_group()
        layout.addWidget(quick_group)
        
        layout.addStretch()
        
        return panel
    
    def _create_file_info_group(self) -> QGroupBox:
        """创建文件信息组"""
        group = QGroupBox("📁 文件信息")
        group.setMinimumHeight(120)  # 设置最小高度确保内容不被遮挡
        layout = QVBoxLayout(group)
        layout.setContentsMargins(10, 15, 10, 10)  # 增加上边距
        layout.setSpacing(8)  # 设置合适的间距

        # 文件选择
        file_layout = QHBoxLayout()
        file_layout.setSpacing(8)

        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("点击选择Excel文件...")
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setMinimumHeight(30)  # 设置最小高度

        self.browse_btn = QPushButton("浏览")
        self.browse_btn.setMaximumWidth(80)
        self.browse_btn.setMinimumHeight(30)  # 与输入框高度一致
        self.browse_btn.clicked.connect(self._browse_file)

        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(self.browse_btn)
        layout.addLayout(file_layout)

        # 文件信息显示
        self.file_info_label = QLabel("未选择文件")
        self.file_info_label.setStyleSheet("color: #7f8c8d; font-size: 12px; padding: 2px;")
        self.file_info_label.setWordWrap(True)  # 允许文字换行
        layout.addWidget(self.file_info_label)

        # 表类型检测
        self.table_type_label = QLabel("表类型: 未检测")
        self.table_type_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 2px;")
        self.table_type_label.setWordWrap(True)  # 允许文字换行
        layout.addWidget(self.table_type_label)

        return group
    
    def _create_sheet_list_group(self) -> QGroupBox:
        """创建Sheet列表组"""
        group = QGroupBox("📊 Sheet列表")
        layout = QVBoxLayout(group)
        
        # Sheet列表树形控件
        self.sheet_tree = QTreeWidget()
        self.sheet_tree.setHeaderLabels(["Sheet名称", "状态", "类型"])
        self.sheet_tree.setAlternatingRowColors(True)
        self.sheet_tree.itemClicked.connect(self._on_sheet_selected)
        
        layout.addWidget(self.sheet_tree)
        
        # Sheet操作按钮
        sheet_btn_layout = QHBoxLayout()
        
        self.refresh_sheets_btn = QPushButton("刷新")
        self.refresh_sheets_btn.setMaximumWidth(60)
        self.refresh_sheets_btn.clicked.connect(self._refresh_sheets)
        
        self.toggle_sheet_btn = QPushButton("启用/禁用")
        self.toggle_sheet_btn.setMaximumWidth(80)
        self.toggle_sheet_btn.clicked.connect(self._toggle_sheet_status)
        
        sheet_btn_layout.addWidget(self.refresh_sheets_btn)
        sheet_btn_layout.addWidget(self.toggle_sheet_btn)
        sheet_btn_layout.addStretch()
        
        layout.addLayout(sheet_btn_layout)
        
        return group
    
    def _create_config_overview_group(self) -> QGroupBox:
        """创建配置概览组"""
        group = QGroupBox("⚙️ 配置概览")
        layout = QVBoxLayout(group)
        
        # 配置状态摘要
        self.config_summary_text = QTextEdit()
        self.config_summary_text.setMaximumHeight(120)
        self.config_summary_text.setReadOnly(True)
        self.config_summary_text.setPlaceholderText("配置状态将在此显示...")
        layout.addWidget(self.config_summary_text)
        
        # 快速搜索
        search_layout = QHBoxLayout()
        search_label = QLabel("🔍 搜索:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索字段名...")
        self.search_edit.textChanged.connect(self._on_search_changed)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        layout.addLayout(search_layout)
        
        return group
    
    def _create_quick_actions_group(self) -> QGroupBox:
        """创建快速操作组"""
        group = QGroupBox("📋 快速操作")
        layout = QVBoxLayout(group)
        
        # 最近配置
        self.recent_config_combo = QComboBox()
        self.recent_config_combo.addItem("选择最近配置...")
        layout.addWidget(self.recent_config_combo)
        
        # 快速操作按钮
        self.auto_mapping_btn = QPushButton("🤖 自动映射")
        self.auto_mapping_btn.clicked.connect(self._auto_mapping)
        layout.addWidget(self.auto_mapping_btn)
        
        self.save_template_btn = QPushButton("💾 保存为模板")
        self.save_template_btn.clicked.connect(self._save_as_template)
        layout.addWidget(self.save_template_btn)
        
        self.reset_config_btn = QPushButton("🔄 重置配置")
        self.reset_config_btn.clicked.connect(self._reset_configuration)
        layout.addWidget(self.reset_config_btn)
        
        return group
    
    def _create_right_panel(self) -> QWidget:
        """创建右侧主面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        
        # 创建各个选项卡
        self.sheet_tab = self._create_sheet_management_tab()
        self.mapping_tab = self._create_field_mapping_tab()
        self.advanced_tab = self._create_advanced_config_tab()
        self.preview_tab = self._create_preview_validation_tab()
        
        # 添加选项卡
        self.tab_widget.addTab(self.sheet_tab, "📋 Sheet管理")
        self.tab_widget.addTab(self.mapping_tab, "🔗 字段映射")
        self.tab_widget.addTab(self.advanced_tab, "⚙️ 高级配置")
        self.tab_widget.addTab(self.preview_tab, "👁️ 预览验证")
        
        # 默认选中字段映射选项卡
        self.tab_widget.setCurrentIndex(1)
        
        layout.addWidget(self.tab_widget)
        
        return panel
    
    def _create_sheet_management_tab(self) -> QWidget:
        """创建Sheet管理选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Sheet管理表格
        self.sheet_management_table = QTableWidget()
        self.sheet_management_table.setColumnCount(5)
        self.sheet_management_table.setHorizontalHeaderLabels([
            "Sheet名称", "启用状态", "数据类型", "记录数", "预览"
        ])
        
        # 设置表格属性
        header = self.sheet_management_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.sheet_management_table)
        
        # Sheet管理工具栏
        sheet_toolbar = self._create_sheet_toolbar()
        layout.addWidget(sheet_toolbar)
        
        return widget
    
    def _create_field_mapping_tab(self) -> QWidget:
        """创建字段映射选项卡（核心区域）"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 智能操作工具栏
        mapping_toolbar = self._create_mapping_toolbar()
        layout.addWidget(mapping_toolbar)
        
        # 字段映射表格
        self.field_mapping_table = QTableWidget()
        self.field_mapping_table.setColumnCount(7)
        self.field_mapping_table.setHorizontalHeaderLabels([
            "Excel字段名", "系统字段名", "配置来源", "字段类型", "格式化规则", "必填", "操作"
        ])
        
        # 设置表格属性
        header = self.field_mapping_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        
        # 启用行选择
        self.field_mapping_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.field_mapping_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.field_mapping_table)
        
        return widget
    
    def _create_advanced_config_tab(self) -> QWidget:
        """创建高级配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # 字段类型详细配置组
        field_type_group = self._create_field_type_config_group()
        scroll_layout.addWidget(field_type_group)
        
        # 系统级设置组
        system_config_group = self._create_system_config_group()
        scroll_layout.addWidget(system_config_group)
        
        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_content)
        scroll_area.setWidgetResizable(True)
        
        layout.addWidget(scroll_area)
        
        return widget
    
    def _create_preview_validation_tab(self) -> QWidget:
        """创建预览验证选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        
        # 实时预览区域
        preview_group = self._create_preview_group()
        splitter.addWidget(preview_group)
        
        # 配置验证报告区域
        validation_group = self._create_validation_group()
        splitter.addWidget(validation_group)
        
        splitter.setSizes([400, 200])
        layout.addWidget(splitter)
        
        return widget
    
    def _create_bottom_panel(self, main_layout):
        """创建底部操作面板"""
        bottom_frame = QFrame()
        bottom_frame.setFrameStyle(QFrame.StyledPanel)
        bottom_frame.setMaximumHeight(50)  # 减少高度从80到50
        
        bottom_layout = QHBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        bottom_layout.setSpacing(5)  # 减少间距
        
        # 状态信息区域（更紧凑）
        self.status_label = QLabel("就绪")
        self.status_label.setMinimumWidth(120)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(20)  # 减少进度条高度
        
        # 操作按钮区域（调整按钮大小和间距）
        self.save_config_btn = QPushButton("💾 保存")
        self.save_config_btn.clicked.connect(self._save_configuration)
        self.save_config_btn.setMaximumSize(80, 30)
        
        self.apply_config_btn = QPushButton("🔄 应用")
        self.apply_config_btn.clicked.connect(self._apply_configuration)
        self.apply_config_btn.setMaximumSize(80, 30)
        
        self.reset_btn = QPushButton("❌ 重置")
        self.reset_btn.clicked.connect(self._reset_all)
        self.reset_btn.setMaximumSize(80, 30)
        
        self.help_btn = QPushButton("❓")
        self.help_btn.clicked.connect(self._show_help_dialog)
        self.help_btn.setToolTip("显示使用帮助和引导 (F1)")
        self.help_btn.setMaximumSize(30, 30)
        
        self.feedback_btn = QPushButton("💬")
        self.feedback_btn.clicked.connect(self._show_feedback_dialog)
        self.feedback_btn.setToolTip("提交反馈和建议")
        self.feedback_btn.setMaximumSize(30, 30)
        
        self.import_btn = QPushButton("✅ 确定导入")
        self.import_btn.clicked.connect(self._execute_import)
        self.import_btn.setMaximumSize(120, 30)
        self.import_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                font-size: 12px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        # 布局安排：状态信息 | 进度条 | 按钮组
        bottom_layout.addWidget(self.status_label)
        bottom_layout.addWidget(self.progress_bar, 1)  # 进度条可拉伸
        bottom_layout.addWidget(self.save_config_btn)
        bottom_layout.addWidget(self.apply_config_btn)
        bottom_layout.addWidget(self.reset_btn)
        bottom_layout.addWidget(self.help_btn)
        bottom_layout.addWidget(self.feedback_btn)
        bottom_layout.addStretch()  # 弹性空间
        bottom_layout.addWidget(self.import_btn)
        
        main_layout.addWidget(bottom_frame)
    
    def _create_sheet_toolbar(self) -> QWidget:
        """创建Sheet工具栏"""
        toolbar = QWidget()
        toolbar.setMaximumHeight(80)  # 限制工具栏高度
        main_layout = QVBoxLayout(toolbar)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 第一行：选择操作
        first_row = QHBoxLayout()
        first_row.setSpacing(8)

        self.select_all_sheets_btn = QPushButton("✅ 全选")
        self.select_all_sheets_btn.setMaximumWidth(70)
        self.select_all_sheets_btn.setMaximumHeight(28)
        self.select_all_sheets_btn.clicked.connect(self._select_all_sheets)

        self.deselect_all_sheets_btn = QPushButton("❌ 全不选")
        self.deselect_all_sheets_btn.setMaximumWidth(80)
        self.deselect_all_sheets_btn.setMaximumHeight(28)
        self.deselect_all_sheets_btn.clicked.connect(self._deselect_all_sheets)

        first_row.addWidget(self.select_all_sheets_btn)
        first_row.addWidget(self.deselect_all_sheets_btn)
        first_row.addStretch()

        # 第二行：批量设置数据类型
        second_row = QHBoxLayout()
        second_row.setSpacing(8)

        type_label = QLabel("批量类型:")
        type_label.setMaximumHeight(28)

        self.batch_type_combo = QComboBox()
        self.batch_type_combo.setMaximumWidth(120)
        self.batch_type_combo.setMaximumHeight(28)
        self.batch_type_combo.addItems([
            "basic_info", "salary_detail", "allowance_detail",
            "deduction_detail", "misc_data"
        ])

        self.apply_batch_type_btn = QPushButton("应用")
        self.apply_batch_type_btn.setMaximumWidth(60)
        self.apply_batch_type_btn.setMaximumHeight(28)
        self.apply_batch_type_btn.clicked.connect(self._apply_batch_type)

        second_row.addWidget(type_label)
        second_row.addWidget(self.batch_type_combo)
        second_row.addWidget(self.apply_batch_type_btn)
        second_row.addStretch()

        main_layout.addLayout(first_row)
        main_layout.addLayout(second_row)

        return toolbar
    
    def _create_mapping_toolbar(self) -> QWidget:
        """创建映射工具栏"""
        toolbar = QWidget()
        toolbar.setMaximumHeight(80)  # 限制工具栏高度
        main_layout = QVBoxLayout(toolbar)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 第一行：智能操作和基础编辑
        first_row = QHBoxLayout()
        first_row.setSpacing(8)

        self.smart_auto_mapping_btn = QPushButton("🤖 智能映射")
        self.smart_auto_mapping_btn.setMaximumWidth(100)
        self.smart_auto_mapping_btn.setMaximumHeight(28)
        self.smart_auto_mapping_btn.clicked.connect(self._smart_auto_mapping)

        self.batch_edit_btn = QPushButton("📝 批量编辑")
        self.batch_edit_btn.setMaximumWidth(100)
        self.batch_edit_btn.setMaximumHeight(28)
        self.batch_edit_btn.clicked.connect(self._batch_edit_mappings)

        self.import_mapping_btn = QPushButton("📥 导入")
        self.import_mapping_btn.setMaximumWidth(80)
        self.import_mapping_btn.setMaximumHeight(28)
        self.import_mapping_btn.clicked.connect(self._import_field_mappings)

        self.export_mapping_btn = QPushButton("📤 导出")
        self.export_mapping_btn.setMaximumWidth(80)
        self.export_mapping_btn.setMaximumHeight(28)
        self.export_mapping_btn.clicked.connect(self._export_field_mappings)

        first_row.addWidget(self.smart_auto_mapping_btn)
        first_row.addWidget(self.batch_edit_btn)
        first_row.addWidget(self.import_mapping_btn)
        first_row.addWidget(self.export_mapping_btn)
        first_row.addStretch()

        # 第二行：冲突处理
        second_row = QHBoxLayout()
        second_row.setSpacing(8)

        self.detect_conflicts_btn = QPushButton("🔍 检测冲突")
        self.detect_conflicts_btn.setMaximumWidth(100)
        self.detect_conflicts_btn.setMaximumHeight(28)
        self.detect_conflicts_btn.clicked.connect(self._detect_mapping_conflicts)

        self.resolve_conflicts_btn = QPushButton("🔧 解决冲突")
        self.resolve_conflicts_btn.setMaximumWidth(100)
        self.resolve_conflicts_btn.setMaximumHeight(28)
        self.resolve_conflicts_btn.clicked.connect(self._resolve_mapping_conflicts)
        self.resolve_conflicts_btn.setEnabled(False)

        second_row.addWidget(self.detect_conflicts_btn)
        second_row.addWidget(self.resolve_conflicts_btn)
        second_row.addStretch()

        main_layout.addLayout(first_row)
        main_layout.addLayout(second_row)

        return toolbar
    
    def _create_field_type_config_group(self) -> QGroupBox:
        """创建字段类型配置组"""
        group = QGroupBox("📝 字段类型详细配置")
        layout = QVBoxLayout(group)
        
        # 字段类型定义表格
        self.field_type_table = QTableWidget()
        self.field_type_table.setColumnCount(4)
        self.field_type_table.setHorizontalHeaderLabels([
            "字段类型", "数据类型", "格式化规则", "验证规则"
        ])
        
        # 预定义一些常用的字段类型
        predefined_types = [
            ("salary_float", "float", "千分位,2位小数", "非负数"),
            ("name_string", "string", "去除前后空格", "非空,长度<=50"),
            ("employee_id", "string", "大写", "非空,唯一"),
            ("date_field", "date", "YYYY-MM-DD", "有效日期"),
            ("percentage", "float", "百分比,1位小数", "0-100")
        ]
        
        self.field_type_table.setRowCount(len(predefined_types))
        for row, (field_type, data_type, format_rule, validation) in enumerate(predefined_types):
            self.field_type_table.setItem(row, 0, QTableWidgetItem(field_type))
            self.field_type_table.setItem(row, 1, QTableWidgetItem(data_type))
            self.field_type_table.setItem(row, 2, QTableWidgetItem(format_rule))
            self.field_type_table.setItem(row, 3, QTableWidgetItem(validation))
        
        layout.addWidget(self.field_type_table)
        
        # 字段类型操作按钮
        type_btn_layout = QHBoxLayout()
        
        self.add_type_btn = QPushButton("➕ 添加类型")
        self.add_type_btn.clicked.connect(self._add_field_type)
        
        self.edit_type_btn = QPushButton("✏️ 编辑类型")
        self.edit_type_btn.clicked.connect(self._edit_field_type)
        
        self.delete_type_btn = QPushButton("🗑️ 删除类型")
        self.delete_type_btn.clicked.connect(self._delete_field_type)
        
        type_btn_layout.addWidget(self.add_type_btn)
        type_btn_layout.addWidget(self.edit_type_btn)
        type_btn_layout.addWidget(self.delete_type_btn)
        type_btn_layout.addStretch()
        
        layout.addLayout(type_btn_layout)
        
        return group
    
    def _create_system_config_group(self) -> QGroupBox:
        """创建系统配置组"""
        group = QGroupBox("🛠️ 系统级设置")
        layout = QFormLayout(group)
        
        # 导入策略设置
        self.import_strategy_combo = QComboBox()
        self.import_strategy_combo.addItems([
            "覆盖导入", "增量导入", "合并导入", "跳过重复"
        ])
        layout.addRow("导入策略:", self.import_strategy_combo)
        
        # 错误处理设置
        self.error_handling_combo = QComboBox()
        self.error_handling_combo.addItems([
            "停止导入", "跳过错误继续", "记录错误继续", "忽略所有错误"
        ])
        layout.addRow("错误处理:", self.error_handling_combo)
        
        # 性能配置
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(100, 10000)
        self.batch_size_spin.setValue(1000)
        self.batch_size_spin.setSuffix(" 行")
        layout.addRow("批处理大小:", self.batch_size_spin)
        
        self.parallel_processing_check = QCheckBox("启用并行处理")
        self.parallel_processing_check.setChecked(True)
        layout.addRow("", self.parallel_processing_check)
        
        # 数据验证设置
        self.strict_validation_check = QCheckBox("严格数据验证")
        self.strict_validation_check.setChecked(False)
        layout.addRow("", self.strict_validation_check)
        
        self.auto_data_type_check = QCheckBox("自动数据类型检测")
        self.auto_data_type_check.setChecked(True)
        layout.addRow("", self.auto_data_type_check)
        
        return group
    
    def _create_preview_group(self) -> QGroupBox:
        """创建预览组"""
        group = QGroupBox("📋 实时数据预览")
        layout = QVBoxLayout(group)
        
        # 预览控制
        preview_control_layout = QHBoxLayout()
        
        self.preview_sheet_combo = QComboBox()
        self.preview_sheet_combo.addItem("选择要预览的Sheet...")
        self.preview_sheet_combo.currentTextChanged.connect(self._on_preview_sheet_changed)
        
        self.preview_rows_spin = QSpinBox()
        self.preview_rows_spin.setRange(10, 1000)
        self.preview_rows_spin.setValue(100)
        self.preview_rows_spin.setSuffix(" 行")
        self.preview_rows_spin.valueChanged.connect(self._update_preview)
        
        self.refresh_preview_btn = QPushButton("🔄 刷新预览")
        self.refresh_preview_btn.clicked.connect(self._refresh_preview)
        
        preview_control_layout.addWidget(QLabel("预览Sheet:"))
        preview_control_layout.addWidget(self.preview_sheet_combo)
        preview_control_layout.addWidget(QLabel("显示行数:"))
        preview_control_layout.addWidget(self.preview_rows_spin)
        preview_control_layout.addWidget(self.refresh_preview_btn)
        preview_control_layout.addStretch()
        
        layout.addLayout(preview_control_layout)
        
        # 预览表格
        self.preview_table = QTableWidget()
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.preview_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.preview_table)
        
        return group
    
    def _create_validation_group(self) -> QGroupBox:
        """创建验证组"""
        group = QGroupBox("✅ 配置验证报告")
        layout = QVBoxLayout(group)
        
        # 验证控制
        validation_control_layout = QHBoxLayout()
        
        self.run_validation_btn = QPushButton("🔍 运行验证")
        self.run_validation_btn.clicked.connect(self._run_configuration_validation)
        
        self.auto_validation_check = QCheckBox("自动验证")
        self.auto_validation_check.setChecked(True)
        self.auto_validation_check.toggled.connect(self._toggle_auto_validation)
        
        validation_control_layout.addWidget(self.run_validation_btn)
        validation_control_layout.addWidget(self.auto_validation_check)
        validation_control_layout.addStretch()
        
        layout.addLayout(validation_control_layout)
        
        # 验证结果文本区域
        self.validation_results_text = QTextEdit()
        self.validation_results_text.setMaximumHeight(200)
        self.validation_results_text.setReadOnly(True)
        self.validation_results_text.setPlaceholderText("验证结果将在此显示...")
        
        layout.addWidget(self.validation_results_text)
        
        return group
    
    def _connect_signals(self):
        """连接信号"""
        # 连接配置管理器的信号
        self.config_manager.conflict_detected = self.conflict_detected
        
        # 连接选项卡切换信号
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
        
        # 连接字段映射表格的信号
        self.field_mapping_table.itemChanged.connect(self._on_mapping_item_changed)
        self.field_mapping_table.currentCellChanged.connect(self._on_mapping_cell_changed)
        
        # 连接冲突检测信号
        self.conflict_detected.connect(self._on_conflicts_detected)
        
        self.logger.debug("🔗 信号连接完成")
    
    def _load_user_preferences(self):
        """加载用户偏好设置"""
        try:
            # 加载最近使用的文件
            self._load_recent_files()
            
            # 加载最近使用的配置
            self._load_recent_configurations()
            
            # 加载界面设置
            self._load_ui_preferences()
            
            self.logger.info("✅ 用户偏好设置加载完成")
            
        except Exception as e:
            self.logger.warning(f"⚠️ 加载用户偏好设置失败: {e}")
    
    # 文件操作事件
    def _browse_file(self):
        """浏览文件"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "选择Excel文件",
            "",
            "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )
        
        if file_path:
            self._load_excel_file(file_path)
    
    def _load_excel_file(self, file_path: str):
        """加载Excel文件"""
        try:
            self.current_file_path = file_path
            self.file_path_edit.setText(file_path)
            
            # 更新文件信息
            file_info = f"文件: {os.path.basename(file_path)}"
            self.file_info_label.setText(file_info)
            
            # 检测表类型
            self._detect_table_type()
            
            # 加载Sheet列表
            self._load_sheet_list()
            
            # 更新状态
            self.status_label.setText(f"已加载文件: {os.path.basename(file_path)}")
            
            self.logger.info(f"📁 Excel文件加载成功: {file_path}")
            
        except Exception as e:
            self.logger.error(f"❌ Excel文件加载失败: {e}")
            QMessageBox.critical(self, "错误", f"文件加载失败: {e}")
    
    def _detect_table_type(self):
        """检测表类型"""
        if not self.current_file_path:
            return
        
        # 简单的表类型检测逻辑
        file_name = os.path.basename(self.current_file_path).lower()
        
        if any(keyword in file_name for keyword in ['异动', '变动', '调整', 'change']):
            table_type = "异动表"
            icon = "🔄"
        elif any(keyword in file_name for keyword in ['工资', '薪资', 'salary']):
            table_type = "工资表"
            icon = "💰"
        else:
            table_type = "未知类型"
            icon = "❓"
        
        self.table_type_label.setText(f"表类型: {icon} {table_type}")
        
        # 应用可视化指示器
        source = self._determine_config_source(table_type)
        self.visual_indicator.apply_source_styling(self.table_type_label, source)
    
    def _determine_config_source(self, table_type: str):
        """根据表类型确定配置来源"""
        if table_type == "异动表":
            return ConfigurationSource.USER_CONFIG  # 异动表通常使用用户配置
        elif table_type == "工资表":
            return ConfigurationSource.TABLE_TEMPLATE  # 工资表使用模板配置
        else:
            return ConfigurationSource.SYSTEM_DEFAULT  # 默认配置
    
    def _load_sheet_list(self):
        """加载Sheet列表"""
        if not self.current_file_path:
            return
        
        try:
            # 获取Sheet名称列表
            sheet_names = self.excel_importer.get_sheet_names(self.current_file_path)
            
            # 清空现有项目
            self.sheet_tree.clear()
            self.preview_sheet_combo.clear()
            self.preview_sheet_combo.addItem("选择要预览的Sheet...")
            
            # 添加Sheet项目
            for sheet_name in sheet_names:
                # 添加到树形控件
                item = QTreeWidgetItem([sheet_name, "启用", "未分类"])
                item.setCheckState(0, Qt.Checked)
                self.sheet_tree.addTopLevelItem(item)
                
                # 添加到预览下拉框
                self.preview_sheet_combo.addItem(sheet_name)
            
            # 更新Sheet管理表格
            self._update_sheet_management_table()
            
            self.logger.info(f"📊 加载了 {len(sheet_names)} 个Sheet")
            
        except Exception as e:
            self.logger.error(f"❌ 加载Sheet列表失败: {e}")
    
    def _update_sheet_management_table(self):
        """更新Sheet管理表格"""
        if not self.current_file_path:
            return
        
        try:
            sheet_names = self.excel_importer.get_sheet_names(self.current_file_path)
            self.sheet_management_table.setRowCount(len(sheet_names))
            
            for row, sheet_name in enumerate(sheet_names):
                # Sheet名称
                name_item = QTableWidgetItem(sheet_name)
                self.sheet_management_table.setItem(row, 0, name_item)
                
                # 启用状态复选框
                enable_check = QCheckBox()
                enable_check.setChecked(True)
                self.sheet_management_table.setCellWidget(row, 1, enable_check)
                
                # 数据类型下拉框
                type_combo = QComboBox()
                type_combo.addItems([
                    "basic_info", "salary_detail", "allowance_detail",
                    "deduction_detail", "misc_data"
                ])
                self.sheet_management_table.setCellWidget(row, 2, type_combo)
                
                # 记录数（暂时显示为未知）
                count_item = QTableWidgetItem("未知")
                self.sheet_management_table.setItem(row, 3, count_item)
                
                # 预览按钮
                preview_btn = QPushButton("👁️ 预览")
                preview_btn.clicked.connect(lambda checked, name=sheet_name: self._preview_sheet(name))
                self.sheet_management_table.setCellWidget(row, 4, preview_btn)
            
        except Exception as e:
            self.logger.error(f"❌ 更新Sheet管理表格失败: {e}")
    
    # Sheet操作事件
    def _on_sheet_selected(self, item, column):
        """Sheet选择事件"""
        if item and item.parent() is None:  # 顶级项目
            sheet_name = item.text(0)
            self.logger.debug(f"📊 选择Sheet: {sheet_name}")
            
            # 加载该Sheet的字段映射
            self._load_sheet_field_mappings(sheet_name)
    
    def _refresh_sheets(self):
        """刷新Sheet列表"""
        if self.current_file_path:
            self._load_sheet_list()
            self.status_label.setText("Sheet列表已刷新")
    
    def _toggle_sheet_status(self):
        """切换Sheet状态"""
        current_item = self.sheet_tree.currentItem()
        if current_item:
            current_state = current_item.checkState(0)
            new_state = Qt.Unchecked if current_state == Qt.Checked else Qt.Checked
            current_item.setCheckState(0, new_state)
            
            status = "启用" if new_state == Qt.Checked else "禁用"
            current_item.setText(1, status)
    
    def _select_all_sheets(self):
        """全选Sheet"""
        for i in range(self.sheet_tree.topLevelItemCount()):
            item = self.sheet_tree.topLevelItem(i)
            item.setCheckState(0, Qt.Checked)
            item.setText(1, "启用")
    
    def _deselect_all_sheets(self):
        """全不选Sheet"""
        for i in range(self.sheet_tree.topLevelItemCount()):
            item = self.sheet_tree.topLevelItem(i)
            item.setCheckState(0, Qt.Unchecked)
            item.setText(1, "禁用")
    
    def _apply_batch_type(self):
        """应用批量类型设置"""
        selected_type = self.batch_type_combo.currentText()
        
        # 应用到Sheet管理表格中选中的行
        for row in range(self.sheet_management_table.rowCount()):
            enable_check = self.sheet_management_table.cellWidget(row, 1)
            if enable_check and enable_check.isChecked():
                type_combo = self.sheet_management_table.cellWidget(row, 2)
                if type_combo:
                    type_combo.setCurrentText(selected_type)
        
        self.status_label.setText(f"已批量设置数据类型为: {selected_type}")
    
    def _preview_sheet(self, sheet_name: str):
        """预览指定Sheet"""
        self.preview_sheet_combo.setCurrentText(sheet_name)
        self._on_preview_sheet_changed(sheet_name)
    
    # 搜索和过滤
    def _on_search_changed(self, text: str):
        """搜索文本变化事件"""
        # 在字段映射表格中搜索
        for row in range(self.field_mapping_table.rowCount()):
            should_show = True
            
            if text.strip():
                # 检查每一列是否包含搜索文本
                should_show = False
                for col in range(self.field_mapping_table.columnCount()):
                    item = self.field_mapping_table.item(row, col)
                    if item and text.lower() in item.text().lower():
                        should_show = True
                        break
            
            self.field_mapping_table.setRowHidden(row, not should_show)
    
    # 配置操作事件
    def _auto_mapping(self):
        """自动映射"""
        try:
            # 简单的自动映射逻辑
            self._smart_auto_mapping()
            self.status_label.setText("自动映射完成")
            
        except Exception as e:
            self.logger.error(f"❌ 自动映射失败: {e}")
            QMessageBox.warning(self, "警告", f"自动映射失败: {e}")
    
    def _save_as_template(self):
        """保存为模板"""
        try:
            # 获取当前配置
            current_configs = self._get_current_configurations()
            
            # 保存为模板
            success = self.config_manager.save_configuration_with_source(
                current_configs,
                ConfigurationSource.TABLE_TEMPLATE,
                "用户保存的配置模板"
            )
            
            if success:
                QMessageBox.information(self, "成功", "配置模板保存成功")
                self.status_label.setText("配置已保存为模板")
            else:
                QMessageBox.warning(self, "失败", "配置模板保存失败")
                
        except Exception as e:
            self.logger.error(f"❌ 保存模板失败: {e}")
            QMessageBox.critical(self, "错误", f"保存模板失败: {e}")
    
    def _reset_configuration(self):
        """重置配置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置所有配置吗？这将清除所有自定义配置。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 重置配置管理器
                self.config_manager.reset_configurations()
                
                # 重新加载界面
                self._refresh_all_ui()
                
                self.status_label.setText("配置已重置")
                QMessageBox.information(self, "完成", "配置重置完成")
                
            except Exception as e:
                self.logger.error(f"❌ 重置配置失败: {e}")
                QMessageBox.critical(self, "错误", f"重置配置失败: {e}")
    
    # 占位方法，后续继续实现
    def _load_recent_files(self):
        """加载最近使用的文件"""
        # TODO: 实现最近文件加载
        pass
    
    def _load_recent_configurations(self):
        """加载最近使用的配置"""
        # TODO: 实现最近配置加载
        pass
    
    def _load_ui_preferences(self):
        """加载界面设置"""
        # TODO: 实现界面设置加载
        pass
    
    def _load_sheet_field_mappings(self, sheet_name: str):
        """加载Sheet的字段映射"""
        try:
            if not self.current_file_path or not sheet_name:
                return
            
            # 读取Excel数据，获取列名
            excel_data = self.excel_importer.import_data(self.current_file_path, sheet_name)
            if excel_data is None or excel_data.empty:
                self.logger.warning(f"⚠️ Sheet '{sheet_name}' 数据为空")
                return
            
            # 获取Excel列名
            excel_columns = list(excel_data.columns)
            
            # 清空现有映射表格
            self.field_mapping_table.setRowCount(0)
            
            # 为每个Excel列创建映射行
            self.field_mapping_table.setRowCount(len(excel_columns))
            
            for row, excel_column in enumerate(excel_columns):
                # Excel字段名
                excel_item = QTableWidgetItem(str(excel_column))
                self.field_mapping_table.setItem(row, 0, excel_item)
                
                # 智能推荐系统字段名
                system_field = self._get_recommended_system_field(excel_column)
                system_item = QTableWidgetItem(system_field)
                self.field_mapping_table.setItem(row, 1, system_item)
                
                # 配置来源指示（默认为系统推荐）
                source_config = self.visual_indicator.get_source_config(
                    ConfigurationSource.SYSTEM_DEFAULT
                )
                source_item = QTableWidgetItem(f"{source_config.icon} 系统推荐")
                source_item.setBackground(QColor(source_config.bg_color))
                source_item.setForeground(QColor(source_config.text_color))
                self.field_mapping_table.setItem(row, 2, source_item)
                
                # 字段类型（基于推荐的系统字段确定）
                field_type = self._get_field_type_for_system_field(system_field)
                type_item = QTableWidgetItem(field_type)
                self.field_mapping_table.setItem(row, 3, type_item)
                
                # 格式化规则
                format_rules = self._get_format_rules_for_field_type(field_type)
                format_item = QTableWidgetItem(format_rules)
                self.field_mapping_table.setItem(row, 4, format_item)
                
                # 必填状态
                is_required = self._is_field_required(system_field)
                required_check = QCheckBox()
                required_check.setChecked(is_required)
                self.field_mapping_table.setCellWidget(row, 5, required_check)
                
                # 操作按钮
                action_btn = QPushButton("⚙️")
                action_btn.setMaximumWidth(30)
                action_btn.clicked.connect(lambda checked, r=row: self._edit_field_mapping(r))
                self.field_mapping_table.setCellWidget(row, 6, action_btn)
            
            # 自动调整列宽
            self.field_mapping_table.resizeColumnsToContents()
            
            # 切换到字段映射选项卡
            self.tab_widget.setCurrentIndex(1)
            
            self.logger.info(f"📊 加载了 {len(excel_columns)} 个字段映射 (Sheet: {sheet_name})")
            
        except Exception as e:
            self.logger.error(f"❌ 加载Sheet字段映射失败: {e}")
            QMessageBox.critical(self, "错误", f"加载字段映射失败: {e}")
    
    def _get_recommended_system_field(self, excel_column: str) -> str:
        """获取推荐的系统字段名"""
        excel_column_lower = str(excel_column).lower().strip()
        
        # 字段名映射规则
        field_mappings = {
            # 基本信息字段
            'employee_id': ['工号', '员工编号', '人员编号', '职工号', 'id', 'emp_id'],
            'name': ['姓名', '员工姓名', '人员姓名', '职工姓名', 'name', '名字'],
            'department': ['部门', '科室', '单位', 'dept', 'department'],
            'position': ['职务', '岗位', '职位', 'position', 'job'],
            
            # 工资字段
            'salary_base': ['基本工资', '基础工资', '岗位工资', 'base_salary', 'basic'],
            'salary_performance': ['绩效工资', '效益工资', '奖金', 'performance', 'bonus'],
            'salary_overtime': ['加班费', '超时工资', 'overtime'],
            'salary_subsidy': ['补贴', '津贴', '补助', 'subsidy', 'allowance'],
            
            # 扣除字段
            'deduction_tax': ['个人所得税', '税金', '所得税', 'tax'],
            'deduction_insurance': ['社会保险', '保险费', '社保', 'insurance'],
            'deduction_fund': ['公积金', '住房公积金', 'fund'],
            'deduction_other': ['其他扣除', '扣款', 'deduction'],
            
            # 异动字段
            'change_type': ['异动类型', '变动类型', '调整类型', 'change_type'],
            'change_reason': ['异动原因', '变动原因', '调整原因', 'change_reason'],
            'change_date': ['异动日期', '变动日期', '调整日期', 'change_date'],
            'change_amount': ['异动金额', '变动金额', '调整金额', 'change_amount'],
        }
        
        # 查找最佳匹配
        for system_field, keywords in field_mappings.items():
            for keyword in keywords:
                if keyword in excel_column_lower:
                    return system_field
        
        # 如果没有找到匹配，返回原始列名的清理版本
        return excel_column.replace(' ', '_').lower()
    
    def _get_field_type_for_system_field(self, system_field: str) -> str:
        """根据系统字段获取字段类型"""
        type_mappings = {
            'employee_id': 'employee_id',
            'name': 'name_string', 
            'department': 'name_string',
            'position': 'name_string',
            'salary_base': 'salary_float',
            'salary_performance': 'salary_float',
            'salary_overtime': 'salary_float',
            'salary_subsidy': 'salary_float',
            'deduction_tax': 'salary_float',
            'deduction_insurance': 'salary_float',
            'deduction_fund': 'salary_float',
            'deduction_other': 'salary_float',
            'change_type': 'name_string',
            'change_reason': 'name_string',
            'change_date': 'date_field',
            'change_amount': 'salary_float',
        }
        
        return type_mappings.get(system_field, 'name_string')
    
    def _get_format_rules_for_field_type(self, field_type: str) -> str:
        """根据字段类型获取格式化规则"""
        format_mappings = {
            'salary_float': '千分位,2位小数',
            'name_string': '去除前后空格',
            'employee_id': '大写',
            'date_field': 'YYYY-MM-DD',
            'percentage': '百分比,1位小数'
        }
        
        return format_mappings.get(field_type, '无')
    
    def _is_field_required(self, system_field: str) -> bool:
        """判断字段是否必填"""
        required_fields = {
            'employee_id', 'name', 'salary_base'
        }
        
        return system_field in required_fields
    
    def _edit_field_mapping(self, row: int):
        """编辑字段映射"""
        # TODO: 在后续迭代中实现详细编辑对话框
        excel_field = self.field_mapping_table.item(row, 0).text()
        QMessageBox.information(
            self, "编辑映射", 
            f"编辑字段映射功能正在开发中\n字段: {excel_field}"
        )
    
    def _on_tab_changed(self, index: int):
        """选项卡切换事件"""
        # TODO: 实现选项卡切换处理
        pass
    
    def _on_mapping_item_changed(self, item):
        """映射项目变化事件"""
        # TODO: 实现映射项目变化处理
        pass
    
    def _on_mapping_cell_changed(self, row: int, col: int):
        """映射单元格变化事件"""
        # TODO: 实现映射单元格变化处理
        pass
    
    def _on_conflicts_detected(self, conflicts: list):
        """冲突检测事件"""
        # TODO: 实现冲突检测处理
        pass
    
    def _smart_auto_mapping(self):
        """智能自动映射"""
        try:
            if not self.current_file_path:
                QMessageBox.warning(self, "警告", "请先选择Excel文件")
                return
            
            # 获取当前选中的Sheet
            current_sheet = None
            selected_item = self.sheet_tree.currentItem()
            if selected_item:
                current_sheet = selected_item.text(0)
            else:
                # 如果没有选中，使用第一个启用的Sheet
                for i in range(self.sheet_tree.topLevelItemCount()):
                    item = self.sheet_tree.topLevelItem(i)
                    if item.checkState(0) == Qt.Checked:
                        current_sheet = item.text(0)
                        break
            
            if not current_sheet:
                QMessageBox.warning(self, "警告", "请先选择要映射的Sheet")
                return
            
            # 显示进度指示
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.status_label.setText("正在执行智能自动映射...")
            QApplication.processEvents()
            
            # 执行智能映射
            success = self._perform_intelligent_mapping(current_sheet)
            
            if success:
                self.status_label.setText("智能自动映射完成")
                QMessageBox.information(self, "完成", "智能自动映射完成！")
            else:
                self.status_label.setText("智能映射失败")
                QMessageBox.warning(self, "警告", "智能映射过程中出现问题")
            
        except Exception as e:
            self.logger.error(f"❌ 智能自动映射失败: {e}")
            QMessageBox.critical(self, "错误", f"智能自动映射失败: {e}")
        
        finally:
            self.progress_bar.setVisible(False)
    
    def _perform_intelligent_mapping(self, sheet_name: str) -> bool:
        """执行智能映射"""
        try:
            # 读取Excel数据
            excel_data = self.excel_importer.import_data(self.current_file_path, sheet_name)
            if excel_data is None or excel_data.empty:
                return False
            
            # 获取列名
            columns = list(excel_data.columns)
            self.logger.info(f"🤖 开始智能映射 {len(columns)} 个字段")
            
            # 清空现有映射表格
            self.field_mapping_table.setRowCount(0)
            self.field_mapping_table.setRowCount(len(columns))
            
            # 为每个列进行智能映射
            for row, excel_column in enumerate(columns):
                # 智能推荐系统字段
                system_field = self._get_recommended_system_field(excel_column)
                
                # 基于数据内容优化映射
                if len(excel_data) > 0:
                    sample_data = excel_data[excel_column].dropna().head(5)
                    system_field = self._optimize_mapping_with_data(excel_column, system_field, sample_data)
                
                # 计算置信度
                confidence = self._calculate_mapping_confidence_simple(excel_column, system_field)
                
                # 填充表格
                self._fill_mapping_row(row, excel_column, system_field, confidence)
            
            # 自动调整列宽
            self.field_mapping_table.resizeColumnsToContents()
            
            # 切换到字段映射选项卡
            self.tab_widget.setCurrentIndex(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 执行智能映射失败: {e}")
            return False
    
    def _optimize_mapping_with_data(self, excel_column: str, initial_mapping: str, sample_data) -> str:
        """基于数据内容优化映射"""
        try:
            if sample_data.empty:
                return initial_mapping
            
            # 检查数值数据
            if sample_data.dtype in ['int64', 'float64']:
                max_val = sample_data.max()
                min_val = sample_data.min()
                
                # 工资数据范围判断
                if 1000 <= max_val <= 100000 and min_val >= 0:
                    excel_lower = excel_column.lower()
                    if '基本' in excel_lower or 'base' in excel_lower:
                        return 'salary_base'
                    elif '绩效' in excel_lower or 'bonus' in excel_lower:
                        return 'salary_performance'
                
                # 编号数据判断
                if max_val <= 999999 and min_val >= 1 and sample_data.dtype == 'int64':
                    if '编号' in excel_column or 'id' in excel_column.lower():
                        return 'employee_id'
            
            return initial_mapping
            
        except Exception as e:
            self.logger.debug(f"数据映射优化失败: {e}")
            return initial_mapping
    
    def _calculate_mapping_confidence_simple(self, excel_column: str, system_field: str) -> float:
        """计算简化的映射置信度"""
        confidence = 0.5  # 基础置信度
        excel_lower = excel_column.lower()
        
        # 关键词匹配加分
        keyword_mapping = {
            'employee_id': ['工号', '编号', 'id'],
            'name': ['姓名', '名字', 'name'],
            'salary_base': ['基本工资', '基础工资', 'base'],
            'department': ['部门', '科室', 'dept'],
        }
        
        if system_field in keyword_mapping:
            for keyword in keyword_mapping[system_field]:
                if keyword in excel_lower:
                    confidence += 0.3
                    break
        
        return min(confidence, 1.0)
    
    def _fill_mapping_row(self, row: int, excel_column: str, system_field: str, confidence: float):
        """填充映射表格行"""
        try:
            # Excel字段名
            excel_item = QTableWidgetItem(excel_column)
            self.field_mapping_table.setItem(row, 0, excel_item)
            
            # 系统字段名
            system_item = QTableWidgetItem(system_field)
            self.field_mapping_table.setItem(row, 1, system_item)
            
            # 配置来源（基于置信度）
            if confidence >= 0.8:
                source_config = self.visual_indicator.get_source_config(
                    ConfigurationSource.USER_CONFIG
                )
                source_text = f"{source_config.icon} 智能(高)"
            elif confidence >= 0.6:
                source_config = self.visual_indicator.get_source_config(
                    ConfigurationSource.TABLE_TEMPLATE
                )
                source_text = f"{source_config.icon} 智能(中)"
            else:
                source_config = self.visual_indicator.get_source_config(
                    ConfigurationSource.SYSTEM_DEFAULT
                )
                source_text = f"{source_config.icon} 智能(低)"
            
            source_item = QTableWidgetItem(source_text)
            source_item.setBackground(QColor(source_config.bg_color))
            source_item.setForeground(QColor(source_config.text_color))
            source_item.setToolTip(f"置信度: {confidence:.1%}")
            self.field_mapping_table.setItem(row, 2, source_item)
            
            # 字段类型
            field_type = self._get_field_type_for_system_field(system_field)
            type_item = QTableWidgetItem(field_type)
            self.field_mapping_table.setItem(row, 3, type_item)
            
            # 格式化规则
            format_rules = self._get_format_rules_for_field_type(field_type)
            format_item = QTableWidgetItem(format_rules)
            self.field_mapping_table.setItem(row, 4, format_item)
            
            # 必填状态
            is_required = self._is_field_required(system_field)
            required_check = QCheckBox()
            required_check.setChecked(is_required)
            self.field_mapping_table.setCellWidget(row, 5, required_check)
            
            # 操作按钮
            action_btn = QPushButton("⚙️")
            action_btn.setMaximumWidth(30)
            action_btn.clicked.connect(lambda checked, r=row: self._edit_field_mapping(r))
            self.field_mapping_table.setCellWidget(row, 6, action_btn)
            
        except Exception as e:
            self.logger.error(f"❌ 填充映射行失败: {e}")
    
    def _batch_edit_mappings(self):
        """批量编辑映射"""
        # TODO: 实现批量编辑
        pass
    
    def _import_field_mappings(self):
        """导入字段映射"""
        # TODO: 实现映射导入
        pass
    
    def _export_field_mappings(self):
        """导出字段映射"""
        # TODO: 实现映射导出
        pass
    
    def _detect_mapping_conflicts(self):
        """检测映射冲突"""
        # TODO: 实现冲突检测
        pass
    
    def _resolve_mapping_conflicts(self):
        """解决映射冲突"""
        # TODO: 实现冲突解决
        pass
    
    def _add_field_type(self):
        """添加字段类型"""
        # TODO: 实现字段类型添加
        pass
    
    def _edit_field_type(self):
        """编辑字段类型"""
        # TODO: 实现字段类型编辑
        pass
    
    def _delete_field_type(self):
        """删除字段类型"""
        # TODO: 实现字段类型删除
        pass
    
    def _on_preview_sheet_changed(self, sheet_name: str):
        """预览Sheet变化事件"""
        if sheet_name and sheet_name != "选择要预览的Sheet...":
            self._update_preview_for_sheet(sheet_name)
    
    def _update_preview(self):
        """更新预览"""
        current_sheet = self.preview_sheet_combo.currentText()
        if current_sheet and current_sheet != "选择要预览的Sheet...":
            self._update_preview_for_sheet(current_sheet)
    
    def _refresh_preview(self):
        """刷新预览"""
        self._update_preview()
    
    def _update_preview_for_sheet(self, sheet_name: str):
        """为指定Sheet更新预览"""
        try:
            if not self.current_file_path:
                return
            
            # 显示进度指示
            self.status_label.setText("正在加载预览数据...")
            QApplication.processEvents()
            
            # 读取Excel数据
            excel_data = self.excel_importer.import_data(self.current_file_path, sheet_name)
            if excel_data is None or excel_data.empty:
                self.preview_table.setRowCount(0)
                self.preview_table.setColumnCount(0)
                self.status_label.setText("预览数据为空")
                return
            
            # 获取预览行数
            preview_rows = self.preview_rows_spin.value()
            preview_data = excel_data.head(preview_rows)
            
            # 设置表格大小
            self.preview_table.setRowCount(len(preview_data))
            self.preview_table.setColumnCount(len(preview_data.columns))
            
            # 设置列标题（应用字段映射）
            mapped_headers = []
            for col_name in preview_data.columns:
                mapped_name = self._get_mapped_field_name(col_name)
                mapped_headers.append(f"{col_name}\n→ {mapped_name}")
            
            self.preview_table.setHorizontalHeaderLabels(mapped_headers)
            
            # 填充数据
            for row in range(len(preview_data)):
                for col in range(len(preview_data.columns)):
                    value = preview_data.iloc[row, col]
                    
                    # 处理空值
                    if pd.isna(value):
                        display_value = ""
                    else:
                        # 应用格式化规则
                        display_value = self._format_preview_value(
                            value, preview_data.columns[col]
                        )
                    
                    item = QTableWidgetItem(str(display_value))
                    
                    # 应用样式（基于数据类型）
                    self._apply_preview_cell_style(item, value, preview_data.columns[col])
                    
                    self.preview_table.setItem(row, col, item)
            
            # 调整列宽
            self.preview_table.resizeColumnsToContents()
            
            # 更新状态
            self.status_label.setText(f"预览完成：{len(preview_data)} 行 × {len(preview_data.columns)} 列")
            
            self.logger.info(f"👁️ 预览数据更新完成: {sheet_name} ({len(preview_data)}行)")
            
        except Exception as e:
            self.logger.error(f"❌ 更新预览失败: {e}")
            self.status_label.setText("预览更新失败")
    
    def _get_mapped_field_name(self, excel_field: str) -> str:
        """获取字段的映射名称"""
        try:
            # 从字段映射表格中查找
            for row in range(self.field_mapping_table.rowCount()):
                excel_item = self.field_mapping_table.item(row, 0)
                system_item = self.field_mapping_table.item(row, 1)
                
                if excel_item and system_item and excel_item.text() == excel_field:
                    return system_item.text()
            
            # 如果没有找到，使用智能推荐
            return self._get_recommended_system_field(excel_field)
            
        except Exception as e:
            self.logger.debug(f"获取映射字段名失败: {e}")
            return excel_field
    
    def _format_preview_value(self, value, column_name: str) -> str:
        """格式化预览值"""
        try:
            if pd.isna(value):
                return ""
            
            # 根据字段类型进行格式化
            mapped_field = self._get_mapped_field_name(column_name)
            field_type = self._get_field_type_for_system_field(mapped_field)
            
            if field_type == 'salary_float':
                # 货币格式化
                try:
                    float_value = float(value)
                    return f"{float_value:,.2f}"
                except (ValueError, TypeError):
                    return str(value)
                    
            elif field_type == 'employee_id':
                # 员工编号格式化
                return str(value).strip().upper()
                
            elif field_type == 'name_string':
                # 姓名格式化
                return str(value).strip()
                
            elif field_type == 'date_field':
                # 日期格式化
                return str(value)
            
            else:
                return str(value)
                
        except Exception as e:
            self.logger.debug(f"格式化预览值失败: {e}")
            return str(value) if value is not None else ""
    
    def _apply_preview_cell_style(self, item: QTableWidgetItem, value, column_name: str):
        """应用预览单元格样式"""
        try:
            # 基于数据类型设置样式
            mapped_field = self._get_mapped_field_name(column_name)
            field_type = self._get_field_type_for_system_field(mapped_field)
            
            if field_type == 'salary_float':
                # 货币字段：右对齐，蓝色
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                item.setForeground(QColor("#1976D2"))
                
            elif field_type == 'employee_id':
                # 员工编号：居中，加粗
                item.setTextAlignment(Qt.AlignCenter)
                font = item.font()
                font.setBold(True)
                item.setFont(font)
                
            elif field_type == 'name_string':
                # 姓名：左对齐，正常
                item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                
            elif field_type == 'date_field':
                # 日期：居中，灰色
                item.setTextAlignment(Qt.AlignCenter)
                item.setForeground(QColor("#666666"))
            
            # 空值处理
            if pd.isna(value) or value == "":
                item.setBackground(QColor("#F5F5F5"))
                item.setForeground(QColor("#999999"))
                item.setText("(空)")
                
        except Exception as e:
            self.logger.debug(f"应用预览样式失败: {e}")
    
    def _run_configuration_validation(self):
        """运行配置验证"""
        try:
            self.status_label.setText("正在运行配置验证...")
            QApplication.processEvents()
            
            # 执行验证
            validation_results = self._perform_configuration_validation()
            
            # 显示验证结果
            self._display_validation_results(validation_results)
            
            # 切换到预览验证选项卡
            self.tab_widget.setCurrentIndex(3)
            
            self.status_label.setText("配置验证完成")
            
        except Exception as e:
            self.logger.error(f"❌ 配置验证失败: {e}")
            self.status_label.setText("配置验证失败")
    
    def _toggle_auto_validation(self, enabled: bool):
        """切换自动验证"""
        if enabled:
            self.logger.info("✅ 已启用自动验证")
            if self.field_mapping_table.rowCount() > 0:
                self._run_configuration_validation()
        else:
            self.logger.info("❌ 已禁用自动验证")
    
    def _perform_configuration_validation(self) -> Dict[str, Any]:
        """执行配置验证分析"""
        validation_results = {
            'status': 'success',
            'errors': [],
            'warnings': [],
            'suggestions': [],
            'statistics': {}
        }
        
        try:
            # 基本完整性检查
            if not self.current_file_path:
                validation_results['errors'].append("❌ 未选择Excel文件")
            
            if self.field_mapping_table.rowCount() == 0:
                validation_results['errors'].append("❌ 未配置任何字段映射")
            
            # 检查启用的Sheet
            enabled_sheets = 0
            for i in range(self.sheet_tree.topLevelItemCount()):
                item = self.sheet_tree.topLevelItem(i)
                if item.checkState(0) == Qt.Checked:
                    enabled_sheets += 1
            
            if enabled_sheets == 0:
                validation_results['errors'].append("❌ 未启用任何Sheet")
            
            # 字段映射验证
            self._validate_field_mappings(validation_results)
            
            # 统计信息
            validation_results['statistics'] = {
                'total_mappings': self.field_mapping_table.rowCount(),
                'enabled_sheets': enabled_sheets,
                'error_count': len(validation_results['errors']),
                'warning_count': len(validation_results['warnings'])
            }
            
            # 确定整体状态
            if validation_results['errors']:
                validation_results['status'] = 'error'
            elif validation_results['warnings']:
                validation_results['status'] = 'warning'
            
        except Exception as e:
            validation_results['status'] = 'error'
            validation_results['errors'].append(f"验证过程出错: {e}")
        
        return validation_results
    
    def _validate_field_mappings(self, results: Dict[str, Any]):
        """验证字段映射"""
        try:
            system_fields = set()
            excel_fields = set()
            empty_mappings = 0
            
            for row in range(self.field_mapping_table.rowCount()):
                excel_item = self.field_mapping_table.item(row, 0)
                system_item = self.field_mapping_table.item(row, 1)
                
                if not excel_item or not system_item:
                    empty_mappings += 1
                    continue
                
                excel_field = excel_item.text().strip()
                system_field = system_item.text().strip()
                
                if not excel_field or not system_field:
                    empty_mappings += 1
                    continue
                
                # 检查重复
                if system_field in system_fields:
                    results['warnings'].append(f"⚠️ 系统字段重复映射: {system_field}")
                else:
                    system_fields.add(system_field)
                
                if excel_field in excel_fields:
                    results['warnings'].append(f"⚠️ Excel字段重复: {excel_field}")
                else:
                    excel_fields.add(excel_field)
            
            if empty_mappings > 0:
                results['warnings'].append(f"⚠️ 有 {empty_mappings} 个空的字段映射")
            
            # 检查关键字段
            critical_fields = {'employee_id', 'name'}
            missing_critical = critical_fields - system_fields
            if missing_critical:
                results['errors'].append(f"❌ 缺少关键字段映射: {', '.join(missing_critical)}")
                
        except Exception as e:
            results['errors'].append(f"❌ 字段映射验证失败: {e}")
    
    def _display_validation_results(self, results: Dict[str, Any]):
        """显示验证结果"""
        try:
            report_lines = []
            
            # 状态标题
            status_icons = {'success': '✅', 'warning': '⚠️', 'error': '❌'}
            status_icon = status_icons.get(results['status'], '❓')
            report_lines.append(f"{status_icon} 配置验证报告")
            report_lines.append("=" * 40)
            
            # 统计信息
            if 'statistics' in results:
                stats = results['statistics']
                report_lines.append("📊 配置统计:")
                report_lines.append(f"  • 字段映射总数: {stats.get('total_mappings', 0)}")
                report_lines.append(f"  • 启用Sheet数: {stats.get('enabled_sheets', 0)}")
                report_lines.append(f"  • 错误数量: {stats.get('error_count', 0)}")
                report_lines.append(f"  • 警告数量: {stats.get('warning_count', 0)}")
                report_lines.append("")
            
            # 错误信息
            if results['errors']:
                report_lines.append("🚨 错误:")
                for error in results['errors']:
                    report_lines.append(f"  {error}")
                report_lines.append("")
            
            # 警告信息
            if results['warnings']:
                report_lines.append("⚠️ 警告:")
                for warning in results['warnings']:
                    report_lines.append(f"  {warning}")
                report_lines.append("")
            
            # 总结
            if results['status'] == 'success':
                report_lines.append("🎉 配置验证通过！可以安全地进行数据导入。")
            elif results['status'] == 'warning':
                report_lines.append("⚠️ 配置基本正确，但有一些警告项需要注意。")
            else:
                report_lines.append("❌ 配置存在错误，请修复后再进行数据导入。")
            
            # 显示报告
            report_text = "\n".join(report_lines)
            self.validation_results_text.setText(report_text)
            
            self.logger.info(f"📋 配置验证完成: {results['status']}")
            
        except Exception as e:
            self.logger.error(f"❌ 显示验证结果失败: {e}")
            self.validation_results_text.setText(f"显示验证结果失败: {e}")
    
    def _get_current_configurations(self) -> dict:
        """获取当前配置"""
        # TODO: 实现当前配置获取
        return {}
    
    def _refresh_all_ui(self):
        """刷新所有界面"""
        # TODO: 实现界面刷新
        pass
    
    def _save_configuration(self):
        """保存配置"""
        # TODO: 实现配置保存
        pass
    
    def _apply_configuration(self):
        """应用配置"""
        # TODO: 实现配置应用
        pass
    
    def _reset_all(self):
        """重置所有"""
        # TODO: 实现全部重置
        pass
    
    def _execute_import(self):
        """执行导入"""
        # TODO: 实现数据导入
        pass
    
    def _show_help_dialog(self):
        """显示帮助对话框"""
        try:
            self.user_guide_system.show_help_dialog(self)
            self.feedback_manager.analytics.track_feature_usage("help_dialog")
        except Exception as e:
            self.logger.error(f"❌ 显示帮助对话框失败: {e}")
            QMessageBox.information(self, "帮助", "帮助系统暂时不可用，请查看用户手册。")
    
    def _show_feedback_dialog(self):
        """显示反馈对话框"""
        try:
            self.feedback_manager.show_feedback_dialog(self, "general")
            self.feedback_manager.analytics.track_feature_usage("feedback_dialog")
        except Exception as e:
            self.logger.error(f"❌ 显示反馈对话框失败: {e}")
            QMessageBox.information(self, "反馈", "反馈系统暂时不可用，请稍后再试。")
    
    def show_first_time_guide_if_needed(self):
        """如果需要，显示首次使用引导"""
        try:
            if self.user_guide_system.should_show_first_time_guide():
                self.user_guide_system.show_first_time_guide(self)
        except Exception as e:
            self.logger.error(f"❌ 显示首次引导失败: {e}")
    
    def keyPressEvent(self, event):
        """键盘事件处理"""
        if event.key() == Qt.Key_F1:
            self._show_help_dialog()
        elif event.key() == Qt.Key_F5:
            self._run_configuration_validation()
        elif event.modifiers() & Qt.ControlModifier:
            if event.key() == Qt.Key_A:
                self._smart_auto_mapping()
            elif event.key() == Qt.Key_P:
                self._refresh_preview()
            elif event.key() == Qt.Key_S:
                self._save_configuration()
            elif event.key() == Qt.Key_R:
                self._reset_all()
        else:
            super().keyPressEvent(event)
    
    def showEvent(self, event):
        """显示事件处理"""
        super().showEvent(event)
        
        # 启动性能监控
        try:
            from src.gui.unified_performance_optimizer import get_performance_monitor
            performance_monitor = get_performance_monitor()
            performance_monitor.start_monitoring()
        except Exception as e:
            self.logger.warning(f"⚠️ 启动性能监控失败: {e}")
        
        # 延迟显示首次引导，确保界面完全加载
        QTimer.singleShot(500, self.show_first_time_guide_if_needed)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 记录会话时间
            self.feedback_manager.analytics.track_session_time()
            
            # 保存使用数据
            self.feedback_manager.analytics.save_usage_data()
            
            # 停止性能监控
            from src.gui.unified_performance_optimizer import get_performance_monitor
            performance_monitor = get_performance_monitor()
            performance_monitor.stop_monitoring()
            
            self.logger.info("🏁 统一配置界面会话结束")
            
        except Exception as e:
            self.logger.error(f"❌ 关闭事件处理失败: {e}")
        
        super().closeEvent(event)


if __name__ == "__main__":
    """测试统一配置对话框"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    dialog = UnifiedImportConfigDialog()
    dialog.show()
    
    sys.exit(app.exec_())
