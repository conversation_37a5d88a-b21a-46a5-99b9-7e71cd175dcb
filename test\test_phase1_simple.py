#!/usr/bin/env python3
"""
第一阶段简化测试
仅测试核心组件是否能正常初始化
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)


def test_core_components():
    """测试核心组件"""
    print("测试核心组件...")
    
    # 测试智能映射引擎
    from src.gui.core.smart_mapping_engine import SmartMappingEngine
    mapping_engine = SmartMappingEngine()
    print("✅ 智能映射引擎初始化成功")
    
    # 测试模板管理器
    from src.gui.core.template_manager import TemplateManager
    template_manager = TemplateManager()
    print("✅ 模板管理器初始化成功")
    
    # 测试验证引擎
    from src.gui.core.validation_engine import ValidationEngine
    validation_engine = ValidationEngine()
    print("✅ 验证引擎初始化成功")


def test_import_manager():
    """测试导入管理器"""
    print("\n测试导入管理器...")
    
    from src.gui.unified_data_import_window import UnifiedImportManager
    import_manager = UnifiedImportManager()
    print("✅ 统一导入管理器初始化成功")
    
    # 验证所有组件都已初始化
    assert import_manager.mapping_engine is not None
    assert import_manager.template_manager is not None
    assert import_manager.validation_engine is not None
    assert import_manager.multi_sheet_importer is not None
    print("✅ 所有子组件都已正确初始化")


def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    from src.gui.core.smart_mapping_engine import SmartMappingEngine
    mapping_engine = SmartMappingEngine()
    
    # 测试智能映射
    excel_headers = ["姓名", "工号", "基本工资"]
    mappings = mapping_engine.generate_smart_mapping(excel_headers, "💰 工资表")
    assert len(mappings) == 3
    print("✅ 智能映射功能正常")
    
    # 测试模板获取
    from src.gui.core.template_manager import TemplateManager
    template_manager = TemplateManager()
    templates = template_manager.get_all_templates()
    assert len(templates) >= 3  # 至少有3个内置模板
    print("✅ 模板获取功能正常")


if __name__ == "__main__":
    print("🚀 第一阶段简化测试开始\n")
    
    try:
        test_core_components()
        test_import_manager()
        test_basic_functionality()
        
        print("\n🎉 第一阶段测试全部通过！")
        print("\n📋 第一阶段完成情况:")
        print("✅ UI框架搭建完成")
        print("✅ 核心业务管理类实现完成")
        print("✅ 基础UI组件开发完成") 
        print("✅ 整体布局集成完成")
        print("\n🔄 可以开始第二阶段：核心功能开发")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
