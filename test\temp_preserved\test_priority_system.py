"""
测试字段映射优先级系统
确保用户导入配置拥有最高优先级
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.field_mapping_manager import FieldMappingManager
import json
from datetime import datetime

def test_priority_system():
    """测试优先级系统"""
    print("=" * 60)
    print("测试字段映射优先级系统")
    print("=" * 60)
    
    # 1. 初始化管理器
    manager = FieldMappingManager()
    test_table = "test_salary_2025_01"
    
    # 2. 清空现有配置，从头测试
    print("\n1. 清空现有配置...")
    manager._config = manager._create_default_config()
    
    # 3. 测试默认映射（优先级3）
    print("\n2. 测试默认映射（优先级3）...")
    default_mapping = manager.get_field_mapping(test_table)
    print(f"   默认映射字段数: {len(default_mapping) if default_mapping else 0}")
    if default_mapping and 'employee_id' in default_mapping:
        print(f"   示例: employee_id -> {default_mapping['employee_id']}")
    
    # 4. 设置表特定映射（优先级2）
    print("\n3. 设置表特定映射（优先级2）...")
    manager._config["table_mappings"][test_table] = {
        "field_mappings": {
            "employee_id": "员工编号",  # 修改默认的"工号"
            "employee_name": "员工姓名",  # 修改默认的"姓名"
            "department": "所在部门"  # 修改默认的"部门名称"
        }
    }
    
    table_mapping = manager.get_field_mapping(test_table)
    print(f"   表特定映射字段数: {len(table_mapping) if table_mapping else 0}")
    if table_mapping and 'employee_id' in table_mapping:
        print(f"   示例: employee_id -> {table_mapping['employee_id']}")
        assert table_mapping['employee_id'] == "员工编号", "表特定映射应该覆盖默认映射"
    
    # 5. 保存用户导入配置（优先级1）
    print("\n4. 保存用户导入配置（优先级1）...")
    user_config = {
        'field_mapping': {
            "employee_id": "工号（用户自定义）",  # 用户自定义
            "employee_name": "姓名（用户自定义）",  # 用户自定义
            "department": "部门（用户自定义）",  # 用户自定义
            "custom_field": "自定义字段"  # 用户新增字段
        },
        'is_change_table': True,  # 用户标记为异动表
        'excel_columns': ["工号", "姓名", "部门", "自定义"]
    }
    
    success = manager.save_user_import_config(test_table, user_config)
    print(f"   保存用户配置: {'成功' if success else '失败'}")
    
    # 6. 验证用户配置优先级最高
    print("\n5. 验证用户配置优先级最高...")
    final_mapping = manager.get_field_mapping(test_table)
    print(f"   最终映射字段数: {len(final_mapping) if final_mapping else 0}")
    
    if final_mapping:
        # 验证用户配置覆盖了其他配置
        test_fields = [
            ("employee_id", "工号（用户自定义）"),
            ("employee_name", "姓名（用户自定义）"),
            ("department", "部门（用户自定义）"),
            ("custom_field", "自定义字段")
        ]
        
        print("\n   验证结果:")
        all_pass = True
        for field, expected in test_fields:
            actual = final_mapping.get(field, "未找到")
            is_correct = actual == expected
            status = "PASS" if is_correct else "FAIL"
            print(f"   {status} {field}: {actual} {'==' if is_correct else '!='} {expected}")
            if not is_correct:
                all_pass = False
        
        if all_pass:
            print("\n[SUCCESS] 优先级系统测试通过！用户配置拥有最高优先级")
        else:
            print("\n[FAILED] 优先级系统测试失败！用户配置未能正确覆盖")
    
    # 7. 检查配置文件结构
    print("\n6. 检查配置文件结构...")
    if os.path.exists(manager.config_path):
        with open(manager.config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        if "user_import_configs" in config:
            print("   [OK] user_import_configs 节点存在")
            if test_table in config["user_import_configs"]:
                user_import = config["user_import_configs"][test_table]
                print(f"   [OK] 找到表 {test_table} 的用户配置")
                print(f"     - 是否为异动表: {user_import.get('is_change_table', False)}")
                print(f"     - 配置来源: {user_import.get('source', '未知')}")
                print(f"     - 时间戳: {user_import.get('timestamp', '未知')}")
        else:
            print("   [ERROR] user_import_configs 节点不存在")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_priority_system()