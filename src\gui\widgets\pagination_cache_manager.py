# -*- coding: utf-8 -*-
"""
分页缓存管理器

提供多级缓存功能，包括页面数据缓存、表元信息缓存和预加载机制。
实现LRU淘汰策略和智能内存管理。

主要功能：
- 页面数据缓存：缓存已加载的页面数据
- 表元信息缓存：缓存表的总记录数、列信息等
- 预加载机制：预加载前后页面数据
- LRU淘汰策略：自动清理最少使用的缓存
- 内存监控：监控缓存内存占用，防止内存泄漏
"""

import time
import threading
from typing import Dict, Optional, Tuple, Any
from dataclasses import dataclass, field
from collections import OrderedDict
import pandas as pd
import sys
import gc

from src.utils.log_config import setup_logger


@dataclass
class TableInfo:
    """表元信息"""
    table_name: str
    total_records: int
    columns: list
    last_updated: float = field(default_factory=time.time)
    
    def is_expired(self, ttl_seconds: int = 300) -> bool:
        """检查信息是否过期（默认5分钟TTL）"""
        return time.time() - self.last_updated > ttl_seconds


@dataclass
class CacheEntry:
    """缓存条目"""
    data: pd.DataFrame
    table_name: str
    page: int
    page_size: int
    timestamp: float = field(default_factory=time.time)
    access_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    
    def touch(self):
        """更新访问时间和次数"""
        self.last_accessed = time.time()
        self.access_count += 1
    
    def get_memory_size(self) -> int:
        """估算内存占用大小（字节）"""
        return self.data.memory_usage(deep=True).sum()
    
    def is_expired(self, ttl_seconds: int = 300) -> bool:
        """检查是否过期"""
        return time.time() - self.last_accessed > ttl_seconds


class PaginationCacheManager:
    """
    分页数据缓存管理器
    
    提供高效的页面数据缓存、预加载和内存管理功能。
    """
    
    def __init__(self, max_cache_entries: int = 50, max_memory_mb: int = 50, ttl_seconds: int = 300):
        """
        初始化缓存管理器
        
        Args:
            max_cache_entries: 最大缓存条目数
            max_memory_mb: 最大内存占用（MB）
            ttl_seconds: 缓存过期时间（秒）
        """
        self.logger = setup_logger(__name__)
        
        # 缓存配置
        self.max_cache_entries = max_cache_entries
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.ttl_seconds = ttl_seconds
        
        # 页面数据缓存 - 使用OrderedDict实现LRU
        self._page_cache: OrderedDict[str, CacheEntry] = OrderedDict()
        
        # 表元信息缓存
        self._table_info_cache: Dict[str, TableInfo] = {}
        
        # 预加载缓存
        self._preload_cache: Dict[str, CacheEntry] = {}
        
        # 线程锁
        self._cache_lock = threading.RLock()
        self._info_lock = threading.RLock()
        
        # 缓存统计
        self._hits = 0
        self._misses = 0
        self._evictions = 0
        
        self.logger.info(f"分页缓存管理器初始化完成 - 最大条目数: {max_cache_entries}, 最大内存: {max_memory_mb}MB")
    
    def _generate_cache_key(self, table_name: str, page: int, page_size: int, sort_state: str = "") -> str:
        """生成缓存键，包含排序状态"""
        if sort_state:
            return f"{table_name}:{page}:{page_size}:{sort_state}"
        return f"{table_name}:{page}:{page_size}"
    
    def put_page_data(self, table_name: str, page: int, page_size: int, data: pd.DataFrame, sort_state: str = ""):
        """缓存页面数据"""
        cache_key = self._generate_cache_key(table_name, page, page_size, sort_state)
        
        with self._cache_lock:
            try:
                # 创建缓存条目
                entry = CacheEntry(
                    data=data.copy(),
                    table_name=table_name,
                    page=page,
                    page_size=page_size
                )
                
                # 检查内存限制
                if self._check_memory_limit(entry):
                    # 如果键已存在，先删除旧的
                    if cache_key in self._page_cache:
                        del self._page_cache[cache_key]
                    
                    # 添加新缓存（自动移到末尾，表示最近使用）
                    self._page_cache[cache_key] = entry
                    
                    # 检查并清理缓存
                    self._cleanup_if_needed()
                    
                    self.logger.debug(f"缓存页面数据: {cache_key}, 内存占用: {entry.get_memory_size() / 1024:.1f}KB")
                else:
                    self.logger.warning(f"页面数据过大，跳过缓存: {cache_key}")
                
            except Exception as e:
                self.logger.error(f"缓存页面数据失败: {e}")
    
    def get_page_data(self, table_name: str, page: int, page_size: int, sort_state: str = "") -> Optional[pd.DataFrame]:
        """获取缓存的页面数据"""
        cache_key = self._generate_cache_key(table_name, page, page_size, sort_state)
        
        with self._cache_lock:
            try:
                if cache_key in self._page_cache:
                    entry = self._page_cache[cache_key]
                    
                    # 检查是否过期
                    if entry.is_expired(self.ttl_seconds):
                        del self._page_cache[cache_key]
                        self._misses += 1
                        self.logger.debug(f"缓存已过期: {cache_key}")
                        return None
                    
                    # 更新访问信息（移到末尾表示最近使用）
                    entry.touch()
                    self._page_cache.move_to_end(cache_key)
                    
                    self._hits += 1
                    self.logger.debug(f"缓存命中: {cache_key}")
                    return entry.data.copy()
                else:
                    self._misses += 1
                    self.logger.debug(f"缓存未命中: {cache_key}")
                    return None
                
            except Exception as e:
                self.logger.error(f"获取缓存数据失败: {e}")
                self._misses += 1
                return None
    
    def put_table_info(self, table_name: str, total_records: int, columns: list):
        """缓存表元信息"""
        with self._info_lock:
            try:
                self._table_info_cache[table_name] = TableInfo(
                    table_name=table_name,
                    total_records=total_records,
                    columns=columns.copy()
                )
                self.logger.debug(f"缓存表信息: {table_name}, 记录数: {total_records}")
                
            except Exception as e:
                self.logger.error(f"缓存表信息失败: {e}")
    
    def get_table_info(self, table_name: str) -> Optional[TableInfo]:
        """获取缓存的表元信息"""
        with self._info_lock:
            try:
                if table_name in self._table_info_cache:
                    info = self._table_info_cache[table_name]
                    
                    # 检查是否过期
                    if info.is_expired(self.ttl_seconds):
                        del self._table_info_cache[table_name]
                        self.logger.debug(f"表信息已过期: {table_name}")
                        return None
                    
                    self.logger.debug(f"表信息命中: {table_name}")
                    return info
                else:
                    self.logger.debug(f"表信息未命中: {table_name}")
                    return None
                
            except Exception as e:
                self.logger.error(f"获取表信息失败: {e}")
                return None
    
    def preload_adjacent_pages(self, table_name: str, current_page: int, page_size: int, 
                             total_pages: int, load_func):
        """预加载相邻页面（异步）"""
        try:
            # 确定需要预加载的页面
            preload_pages = []
            
            # 预加载前一页
            if current_page > 1:
                preload_pages.append(current_page - 1)
            
            # 预加载后一页
            if current_page < total_pages:
                preload_pages.append(current_page + 1)
            
            # 异步预加载
            import threading
            for page in preload_pages:
                cache_key = self._generate_cache_key(table_name, page, page_size)
                
                # 检查是否已缓存
                if cache_key not in self._page_cache:
                    thread = threading.Thread(
                        target=self._preload_page,
                        args=(table_name, page, page_size, load_func),
                        daemon=True
                    )
                    thread.start()
                    
        except Exception as e:
            self.logger.error(f"预加载失败: {e}")
    
    def _preload_page(self, table_name: str, page: int, page_size: int, load_func):
        """预加载单个页面"""
        try:
            # 调用加载函数获取数据
            data, _ = load_func(table_name, page, page_size)
            
            if data is not None and not data.empty:
                self.put_page_data(table_name, page, page_size, data)
                self.logger.debug(f"预加载完成: {table_name}第{page}页")
            
        except Exception as e:
            self.logger.error(f"预加载页面失败: {e}")
    
    def _check_memory_limit(self, new_entry: CacheEntry) -> bool:
        """检查添加新条目是否会超过内存限制"""
        try:
            current_memory = self.get_memory_usage()
            new_memory = new_entry.get_memory_size()
            
            return (current_memory + new_memory) <= self.max_memory_bytes
            
        except Exception:
            # 如果计算失败，允许添加
            return True
    
    def _cleanup_if_needed(self):
        """根据需要清理缓存"""
        try:
            # 清理过期条目
            self._cleanup_expired()
            
            # 如果仍然超过限制，执行LRU清理
            while (len(self._page_cache) > self.max_cache_entries or 
                   self.get_memory_usage() > self.max_memory_bytes):
                
                if not self._page_cache:
                    break
                
                # 删除最久未使用的条目（OrderedDict的第一个）
                oldest_key, oldest_entry = self._page_cache.popitem(last=False)
                self._evictions += 1
                self.logger.debug(f"LRU清理缓存: {oldest_key}")
                
        except Exception as e:
            self.logger.error(f"缓存清理失败: {e}")
    
    def _cleanup_expired(self):
        """清理过期的缓存条目"""
        try:
            expired_keys = []
            
            for key, entry in self._page_cache.items():
                if entry.is_expired(self.ttl_seconds):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._page_cache[key]
                self.logger.debug(f"清理过期缓存: {key}")
                
        except Exception as e:
            self.logger.error(f"清理过期缓存失败: {e}")
    
    def get_memory_usage(self) -> int:
        """获取当前缓存内存占用（字节）"""
        try:
            total_memory = 0
            with self._cache_lock:
                for entry in self._page_cache.values():
                    total_memory += entry.get_memory_size()
            return total_memory
            
        except Exception as e:
            self.logger.error(f"计算内存占用失败: {e}")
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            with self._cache_lock:
                total_memory_mb = self.get_memory_usage() / 1024 / 1024
                hit_rate = self._hits / (self._hits + self._misses) if (self._hits + self._misses) > 0 else 0
                
                return {
                    'cache_entries': len(self._page_cache),
                    'max_entries': self.max_cache_entries,
                    'memory_usage_mb': round(total_memory_mb, 2),
                    'max_memory_mb': self.max_memory_bytes / 1024 / 1024,
                    'hits': self._hits,
                    'misses': self._misses,
                    'hit_rate': round(hit_rate * 100, 1),
                    'evictions': self._evictions,
                    'table_info_count': len(self._table_info_cache)
                }
                
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息（兼容性方法）"""
        with self._cache_lock:
            hit_rate = self._hits / (self._hits + self._misses) if (self._hits + self._misses) > 0 else 0
            return {
                'hits': self._hits,
                'misses': self._misses, 
                'hit_rate': hit_rate
            }
    
    def clear_cache(self, table_name: Optional[str] = None):
        """清理缓存"""
        try:
            if table_name:
                # 清理指定表的缓存
                with self._cache_lock:
                    keys_to_remove = [k for k in self._page_cache.keys() if k.startswith(f"{table_name}:")]
                    for key in keys_to_remove:
                        del self._page_cache[key]
                
                with self._info_lock:
                    if table_name in self._table_info_cache:
                        del self._table_info_cache[table_name]
                
                self.logger.info(f"已清理表 {table_name} 的缓存")
            else:
                # 清理所有缓存
                with self._cache_lock:
                    self._page_cache.clear()
                
                with self._info_lock:
                    self._table_info_cache.clear()
                
                # 重置统计
                self._hits = 0
                self._misses = 0
                self._evictions = 0
                
                # 强制垃圾回收
                gc.collect()
                
                self.logger.info("已清理所有缓存")
                
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
    
    def clear_all_cache(self):
        """清空所有缓存 - 兼容性方法"""
        self.clear_cache()
    
    def clear_table_cache(self, table_name: str):
        """清空指定表的缓存 - 便捷方法"""
        self.clear_cache(table_name)

    def clear_table_cache_for_sort(self, table_name: str):
        """排序时清理表的所有缓存（包括不同排序状态的缓存）"""
        try:
            # 清理指定表的所有缓存（不管排序状态）
            with self._cache_lock:
                keys_to_remove = [key for key in self._page_cache.keys() if key.startswith(f"{table_name}:")]
                removed_count = 0
                for key in keys_to_remove:
                    del self._page_cache[key]
                    removed_count += 1

            # 清理表信息缓存
            with self._info_lock:
                if table_name in self._table_info_cache:
                    del self._table_info_cache[table_name]

            self.logger.info(f"🔧 [排序缓存清理] 已清理表 {table_name} 的 {removed_count} 个缓存条目")

        except Exception as e:
            self.logger.error(f"排序缓存清理失败: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.clear_cache()
        except:
            pass 