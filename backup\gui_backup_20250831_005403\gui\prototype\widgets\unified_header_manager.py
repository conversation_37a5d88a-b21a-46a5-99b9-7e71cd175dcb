"""
方案B：统一表头管理器
解决表头累积问题的架构级方案

功能：
1. 单例模式管理所有表头操作
2. 状态机控制表头更新流程
3. 版本控制和缓存机制
4. 观察者模式通知更新

作者：PyQt5架构优化组
创建时间：2025-08-18
"""

import threading
import time
import hashlib
import json
from typing import Dict, List, Optional, Tuple, Any, Callable
from enum import Enum
from dataclasses import dataclass, field
from collections import OrderedDict
from datetime import datetime

from PyQt5.QtCore import QObject, pyqtSignal
from src.utils.log_config import setup_logger


class TableState(Enum):
    """表格状态枚举"""
    IDLE = "idle"                          # 空闲状态
    LOADING = "loading"                    # 加载中
    TABLE_SWITCHING = "table_switching"    # 切换表格
    PAGINATION = "pagination"              # 分页操作
    SORTING = "sorting"                    # 排序操作
    FILTERING = "filtering"                # 过滤操作
    EDITING = "editing"                    # 编辑状态
    ERROR = "error"                        # 错误状态
    RESETTING = "resetting"                # 重置中


class HeaderOperation(Enum):
    """表头操作类型"""
    SET = "set"            # 设置表头
    UPDATE = "update"      # 更新表头
    APPEND = "append"      # 追加表头
    REMOVE = "remove"      # 移除表头
    CLEAR = "clear"        # 清空表头
    RESET = "reset"        # 重置表头


@dataclass
class HeaderSnapshot:
    """表头快照"""
    headers: List[str]
    count: int
    timestamp: float
    state: TableState
    version: int
    hash: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class HeaderUpdateRequest:
    """表头更新请求"""
    operation: HeaderOperation
    headers: Optional[List[str]] = None
    count: Optional[int] = None
    source: str = "unknown"
    priority: int = 0
    force: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)


class HeaderStateManager(QObject):
    """表头状态管理器（状态机）"""
    
    # 信号
    state_changed = pyqtSignal(TableState, TableState)  # 旧状态，新状态
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        self.current_state = TableState.IDLE
        self.previous_state = TableState.IDLE
        self.state_lock = threading.RLock()
        
        # 状态转换规则
        self.valid_transitions = {
            TableState.IDLE: [
                TableState.LOADING,
                TableState.TABLE_SWITCHING,
                TableState.PAGINATION,
                TableState.SORTING,
                TableState.FILTERING,
                TableState.EDITING
            ],
            TableState.LOADING: [
                TableState.IDLE,
                TableState.ERROR,
                TableState.RESETTING
            ],
            TableState.TABLE_SWITCHING: [
                TableState.IDLE,
                TableState.ERROR,
                TableState.LOADING
            ],
            TableState.PAGINATION: [
                TableState.IDLE,
                TableState.ERROR
            ],
            TableState.SORTING: [
                TableState.IDLE,
                TableState.ERROR
            ],
            TableState.FILTERING: [
                TableState.IDLE,
                TableState.ERROR
            ],
            TableState.EDITING: [
                TableState.IDLE,
                TableState.ERROR
            ],
            TableState.ERROR: [
                TableState.RESETTING,
                TableState.IDLE
            ],
            TableState.RESETTING: [
                TableState.IDLE
            ]
        }
        
        # 状态对应的允许操作
        self.allowed_operations = {
            TableState.IDLE: [
                HeaderOperation.SET,
                HeaderOperation.UPDATE,
                HeaderOperation.CLEAR,
                HeaderOperation.RESET
            ],
            TableState.LOADING: [
                HeaderOperation.SET,
                HeaderOperation.RESET
            ],
            TableState.TABLE_SWITCHING: [
                HeaderOperation.SET,
                HeaderOperation.CLEAR,
                HeaderOperation.RESET
            ],
            TableState.PAGINATION: [
                HeaderOperation.UPDATE
            ],
            TableState.SORTING: [],
            TableState.FILTERING: [],
            TableState.EDITING: [],
            TableState.ERROR: [
                HeaderOperation.RESET
            ],
            TableState.RESETTING: [
                HeaderOperation.CLEAR,
                HeaderOperation.SET
            ]
        }
    
    def can_transition_to(self, new_state: TableState) -> bool:
        """检查是否可以转换到新状态"""
        with self.state_lock:
            return new_state in self.valid_transitions.get(self.current_state, [])
    
    def transition_to(self, new_state: TableState) -> bool:
        """转换到新状态"""
        with self.state_lock:
            if not self.can_transition_to(new_state):
                self.logger.warning(
                    f"方案B：无效的状态转换 {self.current_state} -> {new_state}"
                )
                return False
            
            self.previous_state = self.current_state
            self.current_state = new_state
            
            self.logger.info(
                f"方案B：状态转换 {self.previous_state} -> {self.current_state}"
            )
            
            # 发送信号
            self.state_changed.emit(self.previous_state, self.current_state)
            return True
    
    def is_operation_allowed(self, operation: HeaderOperation) -> bool:
        """检查当前状态是否允许该操作"""
        with self.state_lock:
            return operation in self.allowed_operations.get(self.current_state, [])
    
    def get_state(self) -> TableState:
        """获取当前状态"""
        with self.state_lock:
            return self.current_state
    
    def reset_state(self):
        """重置到初始状态"""
        with self.state_lock:
            self.current_state = TableState.IDLE
            self.previous_state = TableState.IDLE


class UnifiedHeaderManager(QObject):
    """
    统一表头管理器（单例模式）
    
    负责：
    1. 集中管理所有表头操作
    2. 防止表头累积
    3. 版本控制和缓存
    4. 状态管理
    5. 观察者通知
    """
    
    # 信号
    headers_updated = pyqtSignal(list)  # 表头更新信号
    headers_reset = pyqtSignal()        # 表头重置信号
    error_occurred = pyqtSignal(str)    # 错误信号
    
    _instance = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        # 防止重复初始化
        if UnifiedHeaderManager._initialized:
            return
        
        super().__init__()
        UnifiedHeaderManager._initialized = True
        self.logger = setup_logger(__name__)
        self._initialized = True
        
        # 状态管理器
        self.state_manager = HeaderStateManager()
        
        # 表头数据
        self.current_headers: List[str] = []
        self.original_headers: List[str] = []
        self.header_version = 0
        
        # 缓存机制（LRU）
        self.cache_size = 10
        self.header_cache: OrderedDict[str, HeaderSnapshot] = OrderedDict()
        
        # 锁机制
        self.update_lock = threading.RLock()
        
        # 配置
        self.max_columns = 50
        self.max_duplicates = 3
        self.min_update_interval = 0.1
        
        # 状态跟踪
        self.last_update_time = 0
        self.update_count = 0
        self.reset_count = 0
        self.error_count = 0
        
        # 观察者列表
        self.observers: List[Callable] = []
        
        # 操作历史
        self.operation_history: List[HeaderUpdateRequest] = []
        self.max_history_size = 100
        
        self.logger.info("方案B：UnifiedHeaderManager初始化完成")
    
    def register_observer(self, callback: Callable):
        """注册观察者"""
        if callback not in self.observers:
            self.observers.append(callback)
            self.logger.debug(f"方案B：注册观察者 {callback.__name__}")
    
    def unregister_observer(self, callback: Callable):
        """注销观察者"""
        if callback in self.observers:
            self.observers.remove(callback)
            self.logger.debug(f"方案B：注销观察者 {callback.__name__}")
    
    def _notify_observers(self, event: str, data: Any = None):
        """通知所有观察者"""
        for observer in self.observers:
            try:
                observer(event, data)
            except Exception as e:
                self.logger.error(f"方案B：通知观察者失败 {observer.__name__}: {e}")
    
    def _calculate_header_hash(self, headers: List[str]) -> str:
        """计算表头哈希值"""
        header_str = "|".join(headers)
        return hashlib.md5(header_str.encode()).hexdigest()
    
    def _validate_headers(self, headers: List[str]) -> Tuple[bool, str]:
        """
        验证表头合法性
        
        Returns:
            (是否合法, 错误信息)
        """
        # 检查列数
        if len(headers) > self.max_columns:
            return False, f"列数超限：{len(headers)} > {self.max_columns}"
        
        # 检查重复
        header_counts = {}
        for h in headers:
            header_counts[h] = header_counts.get(h, 0) + 1
        
        max_dup = max(header_counts.values()) if header_counts else 0
        if max_dup > self.max_duplicates:
            return False, f"表头重复过多：最多重复{max_dup}次"
        
        # 检查空表头
        if not headers:
            return False, "表头列表为空"
        
        return True, ""
    
    def _add_to_cache(self, key: str, snapshot: HeaderSnapshot):
        """添加到缓存（LRU）"""
        # 如果已存在，先删除
        if key in self.header_cache:
            del self.header_cache[key]
        
        # 添加到末尾
        self.header_cache[key] = snapshot
        
        # 限制缓存大小
        if len(self.header_cache) > self.cache_size:
            # 删除最老的
            self.header_cache.popitem(last=False)
    
    def _get_from_cache(self, key: str) -> Optional[HeaderSnapshot]:
        """从缓存获取（更新LRU）"""
        if key in self.header_cache:
            # 移到末尾
            snapshot = self.header_cache.pop(key)
            self.header_cache[key] = snapshot
            return snapshot
        return None
    
    def _record_operation(self, request: HeaderUpdateRequest):
        """记录操作历史"""
        self.operation_history.append(request)
        
        # 限制历史大小
        if len(self.operation_history) > self.max_history_size:
            self.operation_history.pop(0)
    
    def set_headers(
        self,
        headers: List[str],
        source: str = "unknown",
        force: bool = False
    ) -> bool:
        """
        设置表头（主要接口）
        
        Args:
            headers: 表头列表
            source: 来源标识
            force: 是否强制设置
            
        Returns:
            是否成功
        """
        with self.update_lock:
            try:
                # 创建请求
                request = HeaderUpdateRequest(
                    operation=HeaderOperation.SET,
                    headers=headers,
                    count=len(headers),
                    source=source,
                    force=force
                )
                
                # 记录操作
                self._record_operation(request)
                
                # 检查状态
                if not force and not self.state_manager.is_operation_allowed(HeaderOperation.SET):
                    self.logger.warning(
                        f"方案B：当前状态{self.state_manager.get_state()}不允许SET操作"
                    )
                    return False
                
                # 检查更新间隔
                current_time = time.time()
                if not force and (current_time - self.last_update_time) < self.min_update_interval:
                    self.logger.debug("方案B：更新间隔过短，跳过")
                    return False
                
                # 验证表头
                is_valid, error_msg = self._validate_headers(headers)
                if not is_valid:
                    if not force:
                        self.logger.error(f"方案B：表头验证失败：{error_msg}")
                        self.error_count += 1
                        self.error_occurred.emit(error_msg)
                        
                        # 触发重置
                        if self.error_count > 3:
                            self.reset_headers()
                        return False
                
                # 计算哈希
                header_hash = self._calculate_header_hash(headers)
                
                # 检查是否有变化
                current_hash = self._calculate_header_hash(self.current_headers)
                if not force and header_hash == current_hash:
                    self.logger.debug("方案B：表头未变化，跳过更新")
                    return False
                
                # 创建快照
                snapshot = HeaderSnapshot(
                    headers=headers.copy(),
                    count=len(headers),
                    timestamp=current_time,
                    state=self.state_manager.get_state(),
                    version=self.header_version + 1,
                    hash=header_hash,
                    metadata={"source": source}
                )
                
                # 更新表头
                self.current_headers = headers.copy()
                self.header_version += 1
                self.last_update_time = current_time
                self.update_count += 1
                self.error_count = 0  # 重置错误计数
                
                # 添加到缓存
                cache_key = f"{source}_{header_hash[:8]}"
                self._add_to_cache(cache_key, snapshot)
                
                # 发送信号
                self.headers_updated.emit(self.current_headers)
                
                # 通知观察者
                self._notify_observers("headers_set", self.current_headers)
                
                self.logger.info(
                    f"方案B：表头设置成功，版本{self.header_version}，"
                    f"列数{len(headers)}，来源{source}"
                )
                
                return True
                
            except Exception as e:
                self.logger.error(f"方案B：设置表头异常：{e}")
                self.error_count += 1
                self.error_occurred.emit(str(e))
                return False
    
    def update_headers_for_pagination(
        self,
        headers: List[str],
        page: int = 1
    ) -> bool:
        """
        分页时更新表头（特殊处理，防止累积）
        
        Args:
            headers: 表头列表
            page: 页码
            
        Returns:
            是否成功
        """
        with self.update_lock:
            try:
                # 切换到分页状态
                if not self.state_manager.transition_to(TableState.PAGINATION):
                    self.logger.warning("方案B：无法切换到分页状态")
                    return False
                
                # 分页时不应该改变表头结构
                if self.current_headers and headers != self.current_headers:
                    self.logger.warning(
                        f"方案B：分页时检测到表头变化，保持原表头 "
                        f"(当前{len(self.current_headers)}列，新{len(headers)}列)"
                    )
                    # 保持原表头不变
                    headers = self.current_headers
                
                # 更新元数据
                request = HeaderUpdateRequest(
                    operation=HeaderOperation.UPDATE,
                    headers=headers,
                    source=f"pagination_page_{page}",
                    metadata={"page": page}
                )
                self._record_operation(request)
                
                # 切换回空闲状态
                self.state_manager.transition_to(TableState.IDLE)
                
                self.logger.debug(f"方案B：分页更新完成，第{page}页")
                return True
                
            except Exception as e:
                self.logger.error(f"方案B：分页更新异常：{e}")
                self.state_manager.transition_to(TableState.ERROR)
                return False
    
    def reset_headers(self):
        """重置表头到初始状态"""
        with self.update_lock:
            try:
                # 切换到重置状态
                self.state_manager.transition_to(TableState.RESETTING)
                
                self.logger.warning(f"方案B：重置表头，当前版本{self.header_version}")
                
                # 清空当前表头
                self.current_headers = []
                self.header_version = 0
                self.error_count = 0
                self.reset_count += 1
                
                # 尝试从缓存恢复
                if self.original_headers:
                    self.current_headers = self.original_headers.copy()
                    self.logger.info(f"方案B：从原始表头恢复：{len(self.original_headers)}列")
                
                # 发送信号
                self.headers_reset.emit()
                
                # 通知观察者
                self._notify_observers("headers_reset", None)
                
                # 切换回空闲状态
                self.state_manager.transition_to(TableState.IDLE)
                
                self.logger.info(f"方案B：表头重置完成，重置次数{self.reset_count}")
                
            except Exception as e:
                self.logger.error(f"方案B：重置表头异常：{e}")
                self.state_manager.transition_to(TableState.ERROR)
    
    def get_current_headers(self) -> List[str]:
        """获取当前表头"""
        with self.update_lock:
            return self.current_headers.copy()
    
    def get_header_count(self) -> int:
        """获取当前列数"""
        with self.update_lock:
            return len(self.current_headers)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.update_lock:
            return {
                "version": self.header_version,
                "column_count": len(self.current_headers),
                "update_count": self.update_count,
                "reset_count": self.reset_count,
                "error_count": self.error_count,
                "cache_size": len(self.header_cache),
                "current_state": self.state_manager.get_state().value,
                "operation_history_size": len(self.operation_history)
            }
    
    def clear_cache(self):
        """清空缓存"""
        with self.update_lock:
            self.header_cache.clear()
            self.logger.info("方案B：缓存已清空")
    
    def export_state(self) -> Dict[str, Any]:
        """导出当前状态（用于调试）"""
        with self.update_lock:
            return {
                "timestamp": datetime.now().isoformat(),
                "current_headers": self.current_headers,
                "header_version": self.header_version,
                "state": self.state_manager.get_state().value,
                "statistics": self.get_statistics(),
                "cache_keys": list(self.header_cache.keys()),
                "recent_operations": [
                    {
                        "operation": req.operation.value,
                        "source": req.source,
                        "count": req.count
                    }
                    for req in self.operation_history[-10:]
                ]
            }


# 全局单例
_header_manager_instance = None

def get_header_manager() -> UnifiedHeaderManager:
    """获取全局表头管理器实例"""
    global _header_manager_instance
    if _header_manager_instance is None:
        _header_manager_instance = UnifiedHeaderManager()
    return _header_manager_instance