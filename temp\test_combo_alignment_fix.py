#!/usr/bin/env python3
"""
测试字段类型下拉框垂直对齐修复效果
验证下拉框在表格单元格中是否垂直居中显示
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_combo_alignment_fix():
    """测试下拉框垂直对齐修复"""
    print("🔍 测试字段类型下拉框垂直对齐修复...")
    
    try:
        # 检查_setup_table_combo_style方法的更新
        import inspect
        from src.gui.unified_data_import_window import UnifiedMappingConfigWidget
        
        # 验证样式方法存在
        assert hasattr(UnifiedMappingConfigWidget, '_setup_table_combo_style'), "缺少 _setup_table_combo_style 方法"
        print("✅ _setup_table_combo_style 方法存在")
        
        # 检查样式设置方法的内容
        style_source = inspect.getsource(UnifiedMappingConfigWidget._setup_table_combo_style)
        assert "setFixedHeight(30)" in style_source, "缺少固定高度设置"
        assert "margin: 2px" in style_source, "缺少边距设置"
        print("✅ 下拉框固定高度和边距设置正确")
        
        # 检查容器widget的使用
        load_source = inspect.getsource(UnifiedMappingConfigWidget.load_excel_headers)
        assert "container_widget = QWidget()" in load_source, "缺少容器widget创建"
        assert "QVBoxLayout(container_widget)" in load_source, "缺少垂直布局创建"
        assert "setAlignment(Qt.AlignCenter)" in load_source, "缺少居中对齐设置"
        print("✅ 容器widget和垂直居中对齐设置正确")
        
        # 验证数据类型下拉框也使用了容器
        assert "data_container_widget = QWidget()" in load_source, "数据类型下拉框缺少容器widget"
        print("✅ 数据类型下拉框也使用了容器widget")
        
        print("\n🎉 下拉框垂直对齐修复验证通过！")
        print("📋 修复内容：")
        print("   - 下拉框固定高度：30px（适配35px行高）")
        print("   - 容器边距：2px")
        print("   - 垂直对齐：Qt.AlignCenter")
        print("   - 样式边距：margin: 2px")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_alignment_fix_summary():
    """显示对齐修复总结"""
    print("\n" + "="*60)
    print("🔧 字段类型下拉框垂直对齐问题修复总结")
    print("="*60)
    
    print("\n🐛 问题描述：")
    print("   下拉框内容能正常显示，但整体偏下移")
    print("   底部有部分被移出到单元格外面")
    
    print("\n🔍 问题原因：")
    print("   1. 下拉框在单元格中的垂直对齐方式不正确")
    print("   2. 缺少合适的容器来控制下拉框位置")
    print("   3. 高度设置不够精确，导致位置偏移")
    
    print("\n✅ 修复方案：")
    print("   1. 使用固定高度30px替代最小/最大高度设置")
    print("   2. 创建容器widget包装下拉框")
    print("   3. 设置容器垂直居中对齐（Qt.AlignCenter）")
    print("   4. 添加合适的边距（2px）确保不贴边")
    
    print("\n📊 技术细节：")
    print("   - 表格行高：35px")
    print("   - 下拉框高度：30px（固定）")
    print("   - 容器边距：2px")
    print("   - 样式边距：margin: 2px")
    print("   - 对齐方式：Qt.AlignCenter")
    
    print("\n🎯 预期效果：")
    print("   - 下拉框在单元格中垂直居中显示")
    print("   - 上下边距均匀，不超出单元格边界")
    print("   - 视觉效果整齐美观")
    
    print("\n🔄 实现方式：")
    print("   ```python")
    print("   # 创建容器widget确保垂直居中")
    print("   container_widget = QWidget()")
    print("   container_layout = QVBoxLayout(container_widget)")
    print("   container_layout.setContentsMargins(2, 2, 2, 2)")
    print("   container_layout.setAlignment(Qt.AlignCenter)")
    print("   container_layout.addWidget(field_type_combo)")
    print("   ```")

if __name__ == "__main__":
    success = test_combo_alignment_fix()
    show_alignment_fix_summary()
    
    if success:
        print("\n🚀 垂直对齐修复完成，可以重新启动系统测试效果！")
    else:
        print("\n⚠️  修复验证失败，需要检查代码...")
    
    sys.exit(0 if success else 1)
