"""
数据服务 - 处理数据导入、导出、查询等业务逻辑
从主窗口文件中分离
"""

import pandas as pd
from typing import Optional, Dict, Any, List, Tuple
from loguru import logger
from PyQt5.QtCore import QObject, pyqtSignal

from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.core.unified_cache_manager import get_unified_cache_manager
from src.core.unified_state_management import get_unified_state_manager, StateType


class DataService(QObject):
    """
    数据服务类
    
    负责处理所有数据相关的业务逻辑，包括：
    - 数据导入/导出
    - 数据查询和分页
    - 数据缓存管理
    - 数据状态管理
    """
    
    # 信号定义
    data_loaded = pyqtSignal(object)  # 数据加载完成
    data_error = pyqtSignal(str)  # 数据错误
    progress_update = pyqtSignal(int)  # 进度更新
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logger
        
        # 初始化管理器
        self.db_manager = DatabaseManager()
        self.table_manager = DynamicTableManager(self.db_manager)
        self.cache_manager = get_unified_cache_manager()
        self.state_manager = get_unified_state_manager()
        
        self.logger.info("DataService 初始化完成")
    
    def import_data(self, file_path: str, table_name: str, 
                   field_mapping: Optional[Dict[str, str]] = None) -> bool:
        """
        导入数据
        
        Args:
            file_path: 文件路径
            table_name: 表名
            field_mapping: 字段映射
        
        Returns:
            是否导入成功
        """
        try:
            self.logger.info(f"开始导入数据: {file_path} -> {table_name}")
            
            # 读取文件
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            elif file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_path}")
            
            # 应用字段映射
            if field_mapping:
                df = df.rename(columns=field_mapping)
            
            # 保存到数据库
            success = self.table_manager.create_or_update_table(table_name, df)
            
            if success:
                # 清除相关缓存
                self.cache_manager.clear_table_cache(table_name)
                
                # 更新状态
                self.state_manager.update_state(
                    table_name,
                    StateType.DATA,
                    {'imported': True, 'row_count': len(df)},
                    'data_service'
                )
                
                self.logger.info(f"数据导入成功: {len(df)}行")
                self.data_loaded.emit({'table_name': table_name, 'row_count': len(df)})
                return True
            else:
                self.logger.error("数据导入失败")
                self.data_error.emit("数据导入失败")
                return False
                
        except Exception as e:
            self.logger.error(f"导入数据异常: {e}")
            self.data_error.emit(str(e))
            return False
    
    def export_data(self, table_name: str, file_path: str,
                   selected_columns: Optional[List[str]] = None) -> bool:
        """
        导出数据
        
        Args:
            table_name: 表名
            file_path: 导出文件路径
            selected_columns: 选择的列
        
        Returns:
            是否导出成功
        """
        try:
            self.logger.info(f"开始导出数据: {table_name} -> {file_path}")
            
            # 获取数据
            df = self.table_manager.get_dataframe(table_name)
            
            if df is None or df.empty:
                raise ValueError(f"表 {table_name} 没有数据")
            
            # 筛选列
            if selected_columns:
                df = df[selected_columns]
            
            # 导出文件
            if file_path.endswith('.csv'):
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            elif file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False)
            else:
                raise ValueError(f"不支持的导出格式: {file_path}")
            
            self.logger.info(f"数据导出成功: {len(df)}行")
            return True
            
        except Exception as e:
            self.logger.error(f"导出数据异常: {e}")
            self.data_error.emit(str(e))
            return False
    
    def get_page_data(self, table_name: str, page: int = 1, 
                     page_size: int = 100,
                     sort_columns: Optional[List[Dict]] = None) -> Optional[Dict[str, Any]]:
        """
        获取分页数据
        
        Args:
            table_name: 表名
            page: 页码
            page_size: 页大小
            sort_columns: 排序列
        
        Returns:
            分页数据字典
        """
        try:
            # 先尝试从缓存获取
            cached_data = self.cache_manager.get_page_data(
                table_name, page, page_size, sort_columns
            )
            
            if cached_data:
                self.logger.debug(f"从缓存获取分页数据: {table_name} p{page}")
                return cached_data
            
            # 从数据库获取
            if hasattr(self.table_manager, 'get_dataframe_paginated_with_sort'):
                df, total = self.table_manager.get_dataframe_paginated_with_sort(
                    table_name, page, page_size, sort_columns or []
                )
            else:
                df, total = self.table_manager.get_dataframe_paginated(
                    table_name, page, page_size
                )
                
                # 如果有排序需求，在内存中排序
                if sort_columns and not df.empty:
                    df, _ = self.cache_manager.get_sorted_data(
                        table_name, df, sort_columns
                    )
            
            # 构建结果
            result = {
                'data': df,
                'total_records': total,
                'current_page': page,
                'page_size': page_size,
                'table_name': table_name
            }
            
            # 缓存结果
            self.cache_manager.set_page_data(
                table_name, page, page_size, result, sort_columns
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取分页数据失败: {e}")
            return None
    
    def get_table_list(self) -> List[str]:
        """获取所有表名列表"""
        try:
            return self.table_manager.get_all_tables()
        except Exception as e:
            self.logger.error(f"获取表列表失败: {e}")
            return []
    
    def get_table_info(self, table_name: str) -> Optional[Dict[str, Any]]:
        """
        获取表信息
        
        Args:
            table_name: 表名
        
        Returns:
            表信息字典
        """
        try:
            if not self.table_manager.table_exists(table_name):
                return None
            
            # 获取基本信息
            row_count = self.table_manager.get_row_count(table_name)
            columns = self.table_manager.get_table_columns(table_name)
            
            # 获取状态信息
            state = self.state_manager.get_table_state(table_name)
            
            return {
                'name': table_name,
                'row_count': row_count,
                'column_count': len(columns),
                'columns': columns,
                'last_updated': state.last_updated,
                'has_sort': len(state.sort_columns) > 0,
                'current_page': state.current_page,
                'page_size': state.page_size
            }
            
        except Exception as e:
            self.logger.error(f"获取表信息失败: {e}")
            return None
    
    def search_data(self, table_name: str, keyword: str,
                   columns: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """
        搜索数据
        
        Args:
            table_name: 表名
            keyword: 搜索关键词
            columns: 搜索的列
        
        Returns:
            搜索结果DataFrame
        """
        try:
            df = self.table_manager.get_dataframe(table_name)
            
            if df is None or df.empty:
                return pd.DataFrame()
            
            # 确定搜索列
            search_columns = columns if columns else df.columns.tolist()
            
            # 构建搜索条件
            mask = pd.Series([False] * len(df))
            for col in search_columns:
                if col in df.columns:
                    # 转换为字符串并搜索
                    col_str = df[col].astype(str)
                    mask |= col_str.str.contains(keyword, case=False, na=False)
            
            # 返回搜索结果
            return df[mask]
            
        except Exception as e:
            self.logger.error(f"搜索数据失败: {e}")
            return None
    
    def clear_cache(self, table_name: Optional[str] = None):
        """
        清除缓存
        
        Args:
            table_name: 表名，如果为None则清除所有缓存
        """
        if table_name:
            self.cache_manager.clear_table_cache(table_name)
            self.logger.info(f"清除表 {table_name} 的缓存")
        else:
            self.cache_manager.clear_all()
            self.logger.info("清除所有缓存")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据服务统计信息"""
        cache_stats = self.cache_manager.get_statistics()
        state_stats = self.state_manager.get_statistics()
        
        return {
            'cache': cache_stats,
            'state': state_stats,
            'tables': len(self.get_table_list()),
            'db_size': self._get_db_size()
        }
    
    def _get_db_size(self) -> str:
        """获取数据库大小"""
        try:
            import os
            db_path = self.db_manager.db_path
            if os.path.exists(db_path):
                size = os.path.getsize(db_path)
                if size < 1024:
                    return f"{size} B"
                elif size < 1024 * 1024:
                    return f"{size / 1024:.2f} KB"
                else:
                    return f"{size / (1024 * 1024):.2f} MB"
            return "0 B"
        except:
            return "Unknown"