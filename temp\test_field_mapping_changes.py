#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段映射修改后的功能
验证数据库字段列从下拉框改为输入框的修改是否正确
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from gui.unified_data_import_window import UnifiedMappingConfigWidget

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("字段映射修改测试")
        self.setGeometry(100, 100, 1000, 600)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建字段映射组件
        self.mapping_widget = UnifiedMappingConfigWidget()
        layout.addWidget(self.mapping_widget)
        
        # 测试数据
        test_headers = [
            "姓 名",  # 包含空格
            "身份证号\n码",  # 包含换行符
            "基本工资",
            "岗位津贴",
            "绩效工资",
            "扣款项目",
            "实发金额"
        ]
        
        # 加载测试数据
        self.mapping_widget.load_excel_headers(test_headers, "salary_detail")
        
        print("测试数据加载完成")
        print("请检查以下内容：")
        print("1. 数据库字段列是否为输入框（而不是下拉框）")
        print("2. 输入框默认值是否为清理后的Excel列名")
        print("3. 清理规则是否正确（移除空格、换行符等特殊字符）")
        print("4. 双击数据库字段列是否可以正常编辑（显示原有内容）")
        print("5. 编辑时是否可以看到输入的内容")
        
        # 验证清理后的字段名
        print("\n字段名清理测试结果：")
        for header in test_headers:
            cleaned = self.mapping_widget._clean_field_name(header)
            print(f"原字段名: '{header}' -> 清理后: '{cleaned}'")
            
        print("\n编辑功能测试：")
        print("- 请双击任意数据库字段单元格")
        print("- 检查是否显示原有内容")
        print("- 尝试修改内容并按回车确认")
        print("- 检查修改后的内容是否正确保存")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
