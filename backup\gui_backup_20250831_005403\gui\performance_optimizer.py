"""
性能优化器
提供大文件处理、内存优化、异步处理等性能优化功能
"""

import asyncio
import threading
import time
import gc
from typing import Dict, List, Any, Callable, Optional
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, QObject
from PyQt5.QtWidgets import QProgressBar, QApplication

from src.modules.logging.setup_logger import setup_logger


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.memory_threshold_mb = 2048  # 默认2GB内存阈值
        self.gc_timer = QTimer()
        self.gc_timer.timeout.connect(self._force_garbage_collection)
        
    def start_memory_monitoring(self, interval_ms: int = 30000):
        """开始内存监控"""
        # 确保定时器存在
        if not hasattr(self, 'gc_timer') or self.gc_timer is None:
            self.gc_timer = QTimer()
            self.gc_timer.timeout.connect(self._force_garbage_collection)
        
        if not self.gc_timer.isActive():
            self.gc_timer.start(interval_ms)
            self.logger.info(f"内存监控已启动，检查间隔: {interval_ms}ms")
    
    def stop_memory_monitoring(self):
        """停止内存监控"""
        self.gc_timer.stop()
        self.logger.info("内存监控已停止")
    
    def _force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb > self.memory_threshold_mb:
                gc.collect()
                new_memory_mb = process.memory_info().rss / 1024 / 1024
                freed_mb = memory_mb - new_memory_mb
                
                if freed_mb > 0:
                    self.logger.info(f"垃圾回收释放内存: {freed_mb:.1f}MB")
                
        except ImportError:
            # 如果没有psutil，只进行基础垃圾回收
            gc.collect()
        except Exception as e:
            self.logger.error(f"垃圾回收失败: {e}")
    
    def optimize_for_large_file(self, file_size_mb: float):
        """针对大文件进行内存优化"""
        if file_size_mb > 100:  # 100MB以上的文件
            # 降低内存阈值，更频繁的垃圾回收
            self.memory_threshold_mb = max(1024, self.memory_threshold_mb - file_size_mb)
            
            # 重新创建定时器（如果已被删除）
            if not hasattr(self, 'gc_timer') or self.gc_timer is None:
                self.gc_timer = QTimer()
                self.gc_timer.timeout.connect(self._force_garbage_collection)
            
            # 增加垃圾回收频率
            if file_size_mb > 500:  # 500MB以上
                if not self.gc_timer.isActive():
                    self.gc_timer.start(10000)  # 10秒
            else:
                if not self.gc_timer.isActive():
                    self.gc_timer.start(20000)  # 20秒
                
            self.logger.info(f"为大文件({file_size_mb:.1f}MB)优化内存设置")
    
    def get_memory_usage_info(self) -> Dict[str, Any]:
        """获取内存使用信息"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,
                'vms_mb': memory_info.vms / 1024 / 1024,
                'percent': process.memory_percent(),
                'available_mb': psutil.virtual_memory().available / 1024 / 1024
            }
        except ImportError:
            return {'error': 'psutil not available'}
        except Exception as e:
            return {'error': str(e)}


class AsyncDataProcessor(QThread):
    """异步数据处理器"""
    
    progress_updated = pyqtSignal(int, str)  # 进度百分比，状态消息
    data_processed = pyqtSignal(object)      # 处理完成的数据
    error_occurred = pyqtSignal(str)         # 错误信息
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.should_stop = False
        self.data_chunks = []
        self.processor_func = None
        self.batch_size = 1000
        
    def setup_processing(self, data_chunks: List[Any], processor: Callable, batch_size: int = 1000):
        """设置处理参数"""
        self.data_chunks = data_chunks
        self.processor_func = processor
        self.batch_size = batch_size
        self.should_stop = False
    
    def run(self):
        """运行异步处理"""
        try:
            total_chunks = len(self.data_chunks)
            processed_chunks = []
            
            self.progress_updated.emit(0, "开始异步处理...")
            
            for i, chunk in enumerate(self.data_chunks):
                if self.should_stop:
                    self.progress_updated.emit(i * 100 // total_chunks, "处理已取消")
                    return
                
                # 处理数据块
                try:
                    processed_chunk = self.processor_func(chunk)
                    processed_chunks.append(processed_chunk)
                    
                    # 更新进度
                    progress = (i + 1) * 100 // total_chunks
                    self.progress_updated.emit(progress, f"已处理 {i + 1}/{total_chunks} 个数据块")
                    
                    # 批量垃圾回收
                    if (i + 1) % 10 == 0:
                        gc.collect()
                        
                except Exception as chunk_error:
                    self.logger.error(f"处理数据块 {i} 失败: {chunk_error}")
                    self.error_occurred.emit(f"数据块 {i} 处理失败: {chunk_error}")
                    continue
            
            self.progress_updated.emit(100, "异步处理完成")
            self.data_processed.emit(processed_chunks)
            
        except Exception as e:
            self.logger.error(f"异步处理失败: {e}")
            self.error_occurred.emit(f"异步处理失败: {e}")
    
    def stop_processing(self):
        """停止处理"""
        self.should_stop = True
        self.logger.info("异步处理停止请求已发送")


class ChunkedFileReader:
    """分块文件读取器"""
    
    def __init__(self, chunk_size_mb: float = 50):
        self.logger = setup_logger(__name__)
        self.chunk_size_bytes = int(chunk_size_mb * 1024 * 1024)
        
    def read_excel_in_chunks(self, file_path: str, sheet_name: str = None) -> List[Any]:
        """分块读取Excel文件"""
        import pandas as pd
        
        chunks = []
        try:
            # 首先获取文件信息
            excel_file = pd.ExcelFile(file_path)
            
            if sheet_name is None:
                sheet_name = excel_file.sheet_names[0]
            
            # 估算数据行数
            total_rows = self._estimate_excel_rows(file_path, sheet_name)
            
            if total_rows <= 10000:  # 小文件直接读取
                chunk = pd.read_excel(file_path, sheet_name=sheet_name)
                chunks.append(chunk)
                self.logger.info(f"小文件直接读取: {len(chunk)} 行")
            else:
                # 大文件分块读取
                chunk_size = 5000  # 每块5000行
                for start_row in range(0, total_rows, chunk_size):
                    end_row = min(start_row + chunk_size, total_rows)
                    
                    chunk = pd.read_excel(
                        file_path, 
                        sheet_name=sheet_name,
                        skiprows=start_row if start_row > 0 else None,
                        nrows=chunk_size
                    )
                    
                    if not chunk.empty:
                        chunks.append(chunk)
                        self.logger.info(f"读取数据块: 行 {start_row}-{end_row}")
            
            self.logger.info(f"文件分块读取完成: {len(chunks)} 个数据块")
            return chunks
            
        except Exception as e:
            self.logger.error(f"分块读取Excel文件失败: {e}")
            raise
    
    def _estimate_excel_rows(self, file_path: str, sheet_name: str) -> int:
        """估算Excel文件行数"""
        try:
            import pandas as pd
            
            # 先读取少量数据来估算
            sample = pd.read_excel(file_path, sheet_name=sheet_name, nrows=100)
            
            # 基于文件大小估算总行数
            import os
            file_size = os.path.getsize(file_path)
            sample_size = len(sample.to_csv().encode('utf-8'))
            
            if sample_size > 0:
                estimated_rows = (file_size * len(sample)) // sample_size
                return max(estimated_rows, len(sample))
            else:
                return len(sample)
                
        except Exception as e:
            self.logger.warning(f"估算Excel行数失败: {e}")
            return 10000  # 默认估算值


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.metrics = {}
        self.start_times = {}
    
    def start_operation(self, operation_name: str):
        """开始监控操作"""
        self.start_times[operation_name] = time.time()
        self.logger.info(f"开始监控操作: {operation_name}")
    
    def end_operation(self, operation_name: str, additional_info: Dict[str, Any] = None):
        """结束监控操作"""
        if operation_name in self.start_times:
            duration = time.time() - self.start_times[operation_name]
            
            metric = {
                'operation': operation_name,
                'duration_seconds': duration,
                'timestamp': time.time()
            }
            
            if additional_info:
                metric.update(additional_info)
            
            self.metrics[operation_name] = metric
            
            self.logger.info(f"操作完成: {operation_name}, 耗时: {duration:.2f}秒")
            
            # 清理开始时间
            del self.start_times[operation_name]
            
            return metric
        else:
            self.logger.warning(f"未找到操作开始时间: {operation_name}")
            return None
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        total_operations = len(self.metrics)
        
        if total_operations == 0:
            return {'message': '暂无性能数据'}
        
        # 计算总耗时和平均耗时
        total_duration = sum(m['duration_seconds'] for m in self.metrics.values())
        avg_duration = total_duration / total_operations
        
        # 找出最慢的操作
        slowest_op = max(self.metrics.values(), key=lambda x: x['duration_seconds'])
        
        # 生成报告
        report = {
            'total_operations': total_operations,
            'total_duration_seconds': total_duration,
            'average_duration_seconds': avg_duration,
            'slowest_operation': {
                'name': slowest_op['operation'],
                'duration': slowest_op['duration_seconds']
            },
            'operations': list(self.metrics.values())
        }
        
        return report
    
    def clear_metrics(self):
        """清理性能指标"""
        self.metrics.clear()
        self.start_times.clear()
        self.logger.info("性能指标已清理")


class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        
        # 初始化组件
        self.memory_optimizer = MemoryOptimizer()
        self.chunked_reader = ChunkedFileReader()
        self.performance_monitor = PerformanceMonitor()
        
        # 配置参数
        self.config = {
            'enable_memory_optimization': True,
            'enable_async_processing': True,
            'chunk_size_mb': 50,
            'batch_size': 1000,
            'thread_pool_size': 4,
            'memory_threshold_mb': 2048
        }
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=self.config['thread_pool_size'])
        
        self.logger.info("性能优化器初始化完成")
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        
        # 应用配置
        if 'memory_threshold_mb' in new_config:
            self.memory_optimizer.memory_threshold_mb = new_config['memory_threshold_mb']
        
        if 'chunk_size_mb' in new_config:
            self.chunked_reader = ChunkedFileReader(new_config['chunk_size_mb'])
        
        if 'thread_pool_size' in new_config:
            # 重新创建线程池
            self.thread_pool.shutdown(wait=False)
            self.thread_pool = ThreadPoolExecutor(max_workers=new_config['thread_pool_size'])
        
        self.logger.info("性能优化器配置已更新")
    
    def optimize_for_operation(self, operation_type: str, **kwargs):
        """针对特定操作进行优化"""
        if operation_type == "large_file_import":
            file_size_mb = kwargs.get('file_size_mb', 0)
            
            if self.config['enable_memory_optimization']:
                self.memory_optimizer.optimize_for_large_file(file_size_mb)
                self.memory_optimizer.start_memory_monitoring()
            
            # 调整分块大小
            if file_size_mb > 200:  # 200MB以上
                self.chunked_reader.chunk_size_bytes = int(25 * 1024 * 1024)  # 25MB块
            
        elif operation_type == "bulk_processing":
            record_count = kwargs.get('record_count', 0)
            
            # 根据记录数调整批处理大小
            if record_count > 100000:
                self.config['batch_size'] = 500
            elif record_count > 50000:
                self.config['batch_size'] = 1000
            else:
                self.config['batch_size'] = 2000
    
    def create_async_processor(self) -> AsyncDataProcessor:
        """创建异步处理器"""
        processor = AsyncDataProcessor()
        return processor
    
    def read_file_optimized(self, file_path: str, sheet_name: str = None) -> List[Any]:
        """优化的文件读取"""
        operation_name = f"read_file_{file_path.split('/')[-1]}"
        
        self.performance_monitor.start_operation(operation_name)
        
        try:
            # 获取文件大小
            import os
            file_size_mb = os.path.getsize(file_path) / 1024 / 1024
            
            # 针对大文件优化
            self.optimize_for_operation("large_file_import", file_size_mb=file_size_mb)
            
            # 分块读取
            chunks = self.chunked_reader.read_excel_in_chunks(file_path, sheet_name)
            
            # 记录性能
            self.performance_monitor.end_operation(
                operation_name, 
                {'file_size_mb': file_size_mb, 'chunks_count': len(chunks)}
            )
            
            return chunks
            
        except Exception as e:
            self.performance_monitor.end_operation(operation_name, {'error': str(e)})
            raise
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        report = {
            'config': self.config,
            'memory_usage': self.memory_optimizer.get_memory_usage_info(),
            'performance_metrics': self.performance_monitor.get_performance_report()
        }
        
        return report
    
    def cleanup(self):
        """清理资源"""
        try:
            self.memory_optimizer.stop_memory_monitoring()
            self.thread_pool.shutdown(wait=True)
            self.performance_monitor.clear_metrics()
            
            self.logger.info("性能优化器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理性能优化器资源失败: {e}")


# 全局性能优化器实例
_performance_optimizer = None

def get_performance_optimizer() -> PerformanceOptimizer:
    """获取全局性能优化器实例"""
    global _performance_optimizer
    if _performance_optimizer is None:
        _performance_optimizer = PerformanceOptimizer()
    return _performance_optimizer


# 测试用主函数
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 测试性能优化器
    optimizer = get_performance_optimizer()
    
    # 测试内存监控
    optimizer.memory_optimizer.start_memory_monitoring(5000)  # 5秒检查一次
    
    print("性能优化器测试运行中...")
    print("配置:", optimizer.config)
    
    # 模拟一些操作
    optimizer.performance_monitor.start_operation("test_operation")
    time.sleep(2)
    optimizer.performance_monitor.end_operation("test_operation")
    
    print("性能报告:", optimizer.get_optimization_report())
    
    sys.exit(app.exec_())
