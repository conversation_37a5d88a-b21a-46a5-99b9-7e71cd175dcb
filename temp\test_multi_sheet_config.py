#!/usr/bin/env python3
"""
测试多工作表配置保存修复

模拟用户操作：
1. 配置"退休人员工资表"的字段类型
2. 配置"在职人员工资表"的字段类型
3. 配置"离休人员表"的字段类型
4. 验证每个表的配置都能正确保存和重新加载
"""

import sys
import os
import pandas as pd
from pathlib import Path
from unittest.mock import Mock

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger

def simulate_multi_sheet_config_workflow():
    """模拟多工作表配置工作流"""
    logger.info("=== 模拟多工作表配置保存测试 ===")
    
    # 模拟主对话框类
    class MockMainDialog:
        def __init__(self):
            self.change_data_configs = {}  # 新的多工作表配置存储
            self.change_data_config = None  # 兼容性单一配置
            self.logger = logger
        
        def show_status_message(self, message: str, msg_type: str = 'info', duration: int = 3000):
            logger.info(f"Status: {message}")
        
        def sender(self):
            # 模拟信号发送者
            mock_sender = Mock()
            mock_sender.current_sheet_name = self.current_test_sheet
            return mock_sender
        
        def _on_change_data_config_saved(self, config: dict):
            """模拟配置保存处理方法（修复后的版本）"""
            try:
                # 获取当前工作表名称（从config信号的发送者获取）
                sender = self.sender()
                sheet_name = 'unknown'
                if sender and hasattr(sender, 'current_sheet_name'):
                    sheet_name = sender.current_sheet_name
                
                # 初始化按工作表分类的配置字典
                if not hasattr(self, 'change_data_configs'):
                    self.change_data_configs = {}
                
                # 按工作表名称保存配置
                self.change_data_configs[sheet_name] = config
                
                # 兼容性：同时保存到旧的单一变量（保存最后一个配置）
                self.change_data_config = config
                
                self.logger.info(f"异动表配置已保存 (工作表: {sheet_name}): {len(config.get('field_mapping', {}))} 个字段")
                
                # 更新界面提示
                self.show_status_message(f"配置已保存 (工作表: {sheet_name})，将在导入时应用", 'success', 3000)
                
            except Exception as e:
                self.logger.error(f"保存异动表配置失败: {e}")
        
        def get_config_for_sheet(self, sheet_name: str):
            """获取指定工作表的配置"""
            if hasattr(self, 'change_data_configs') and self.change_data_configs:
                return self.change_data_configs.get(sheet_name, None)
            return None
    
    # 创建模拟的主对话框实例
    main_dialog = MockMainDialog()
    
    # 测试数据：不同工作表的配置
    test_configs = {
        "退休人员工资表": {
            'field_types': {
                '序号': 'integer',
                '姓名': 'name_string',
                '基本退休费': 'salary_float',
                '历年调整': 'salary_float',
                '应发工资': 'salary_float'
            },
            'field_mapping': {
                '序号': '序号',
                '姓名': '姓名',
                '基本退休费': '基本退休费',
                '历年调整': '历年调整',
                '应发工资': '应发工资'
            },
            'formatting_rules': {}
        },
        "在职人员工资表": {
            'field_types': {
                '工号': 'employee_id_string',
                '姓名': 'name_string',
                '岗位工资': 'salary_float',
                '薪级工资': 'salary_float',
                '绩效工资': 'salary_float'
            },
            'field_mapping': {
                '工号': '工号',
                '姓名': '姓名',
                '岗位工资': '岗位工资',
                '薪级工资': '薪级工资',
                '绩效工资': '绩效工资'
            },
            'formatting_rules': {}
        },
        "离休人员表": {
            'field_types': {
                '人员代码': 'employee_id_string',
                '姓名': 'name_string',
                '基本离休费': 'salary_float',
                '护理费': 'salary_float',
                '合计': 'salary_float'
            },
            'field_mapping': {
                '人员代码': '人员代码',
                '姓名': '姓名',
                '基本离休费': '基本离休费',
                '护理费': '护理费',
                '合计': '合计'
            },
            'formatting_rules': {}
        }
    }
    
    # 模拟用户逐个配置不同的工作表
    for sheet_name, config in test_configs.items():
        logger.info(f"\n--- 模拟配置工作表: {sheet_name} ---")
        
        # 设置当前测试工作表（模拟用户选择工作表）
        main_dialog.current_test_sheet = sheet_name
        
        # 模拟用户点击"应用"按钮保存配置
        main_dialog._on_change_data_config_saved(config)
        
        # 验证配置是否正确保存
        saved_config = main_dialog.get_config_for_sheet(sheet_name)
        if saved_config:
            if saved_config['field_types'] == config['field_types']:
                logger.info(f"✅ 工作表 '{sheet_name}' 配置保存成功")
            else:
                logger.error(f"❌ 工作表 '{sheet_name}' 配置保存错误")
                return False
        else:
            logger.error(f"❌ 工作表 '{sheet_name}' 配置未找到")
            return False
    
    # 验证所有配置都能正确区分
    logger.info(f"\n--- 验证配置独立性 ---")
    logger.info(f"总共保存了 {len(main_dialog.change_data_configs)} 个工作表的配置")
    
    for sheet_name in test_configs.keys():
        saved_config = main_dialog.get_config_for_sheet(sheet_name)
        if saved_config:
            field_count = len(saved_config.get('field_types', {}))
            logger.info(f"工作表 '{sheet_name}': {field_count} 个字段配置")
        else:
            logger.error(f"❌ 工作表 '{sheet_name}' 配置丢失")
            return False
    
    # 验证配置内容的正确性
    logger.info(f"\n--- 验证配置内容正确性 ---")
    for sheet_name, expected_config in test_configs.items():
        saved_config = main_dialog.get_config_for_sheet(sheet_name)
        
        # 验证字段类型配置
        expected_types = expected_config['field_types']
        saved_types = saved_config.get('field_types', {})
        
        if expected_types == saved_types:
            logger.info(f"✅ 工作表 '{sheet_name}' 字段类型配置正确")
        else:
            logger.error(f"❌ 工作表 '{sheet_name}' 字段类型配置不匹配")
            logger.error(f"期望: {expected_types}")
            logger.error(f"实际: {saved_types}")
            return False
    
    # 验证"最后配置"是否为离休人员表（旧的单一配置变量）
    if main_dialog.change_data_config:
        last_config_types = main_dialog.change_data_config.get('field_types', {})
        expected_last_types = test_configs["离休人员表"]['field_types']
        
        if last_config_types == expected_last_types:
            logger.info("✅ 兼容性配置正确（保存了最后一个配置）")
        else:
            logger.error("❌ 兼容性配置错误")
            return False
    
    logger.info("\n🎉 所有测试通过！多工作表配置修复成功！")
    return True

def main():
    """主函数"""
    success = simulate_multi_sheet_config_workflow()
    
    if success:
        print(">>> 测试结果：多工作表配置修复功能正常工作")
        print(">>> 现在每个工作表的配置都能独立保存和加载")
        return 0
    else:
        print(">>> 测试结果：多工作表配置修复功能存在问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())