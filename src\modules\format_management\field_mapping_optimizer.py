#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
字段映射优化器 - P1级优化
解决字段映射警告和不匹配问题
"""

from typing import Dict, List, Optional, Tuple, Any
from loguru import logger
import json
import os

class FieldMappingOptimizer:
    """
    字段映射优化器
    自动修复和优化字段映射关系
    """
    
    # 常见字段映射（中文 -> 英文）
    COMMON_MAPPINGS = {
        # 基本信息
        '工号': 'employee_id',
        '姓名': 'employee_name',
        '部门': 'department',
        '部门名称': 'department',
        '人员类别': 'employee_type',
        '人员类别代码': 'employee_type_code',
        
        # 工资相关
        '基本工资': 'basic_salary',
        '岗位工资': 'position_salary',
        '薪级工资': 'grade_salary',
        '2025年岗位工资': 'position_salary_2025',
        '2025年薪级工资': 'grade_salary_2025',
        '2025年基础性绩效': 'basic_performance_2025',
        '2025年奖励性绩效预发': 'performance_bonus_2025',
        '应发工资': 'total_salary',
        
        # 津贴补贴
        '津贴': 'allowance',
        '结余津贴': 'balance_allowance',
        '住房补贴': 'housing_allowance',
        '交通补贴': 'transport_allowance',
        '通讯补贴': 'communication_allowance',
        '车补': 'car_allowance',
        '物业补贴': 'property_allowance',
        '补发': 'supplement',
        
        # 扣款项
        '2025公积金': 'provident_fund_2025',
        '代扣代存养老保险': 'pension_insurance',
        '卫生费': 'health_fee',
        '借支': 'advance',
        
        # 时间相关
        '年份': 'year',
        '月份': 'month',
        '创建时间': 'created_at',
        '更新时间': 'updated_at',
        'import_time': 'import_time',
        
        # 系统字段
        '序号': 'sequence_number',
        '自增主键': 'id',
        'data_source': 'data_source'
    }
    
    def __init__(self):
        self.logger = logger
        self._mapping_cache = {}
        self.logger.info("🔧 [P1-优化] 字段映射优化器初始化完成")
    
    def optimize_mappings(self, current_mappings: Dict[str, str], 
                         actual_fields: List[str]) -> Dict[str, str]:
        """
        优化字段映射
        
        Args:
            current_mappings: 当前的字段映射（英文 -> 中文）
            actual_fields: 实际的字段列表（可能是中文或英文）
            
        Returns:
            优化后的字段映射
        """
        optimized = {}
        
        # 创建反向映射（中文 -> 英文）
        reverse_mappings = {v: k for k, v in current_mappings.items()}
        
        # 处理每个实际字段
        for field in actual_fields:
            # 如果字段已经在映射中
            if field in current_mappings:
                # 英文字段，保持原样
                optimized[field] = current_mappings[field]
            elif field in reverse_mappings:
                # 中文字段，使用反向映射
                english_name = reverse_mappings[field]
                optimized[english_name] = field
            elif field in self.COMMON_MAPPINGS:
                # 使用通用映射
                english_name = self.COMMON_MAPPINGS[field]
                optimized[english_name] = field
                self.logger.debug(f"🔧 [P1-优化] 使用通用映射: {field} -> {english_name}")
            else:
                # 尝试智能匹配
                matched = self._smart_match(field, current_mappings)
                if matched:
                    optimized[matched] = field
                else:
                    # 使用原字段名作为key
                    optimized[field] = field
                    self.logger.debug(f"🔧 [P1-优化] 无法映射字段，保持原样: {field}")
        
        return optimized
    
    def _smart_match(self, field: str, mappings: Dict[str, str]) -> Optional[str]:
        """
        智能匹配字段名
        
        Args:
            field: 要匹配的字段名
            mappings: 现有映射
            
        Returns:
            匹配到的英文字段名，或None
        """
        field_lower = field.lower()
        
        # 尝试模糊匹配
        for english, chinese in mappings.items():
            if field_lower in chinese.lower() or chinese.lower() in field_lower:
                return english
        
        # 尝试关键词匹配
        keywords = {
            '工号': 'employee_id',
            '姓名': 'name',
            '部门': 'department',
            '工资': 'salary',
            '津贴': 'allowance',
            '补贴': 'allowance',
            '公积金': 'provident_fund',
            '保险': 'insurance',
            '时间': 'time',
            '日期': 'date'
        }
        
        for keyword, english_base in keywords.items():
            if keyword in field:
                # 查找包含english_base的字段
                for english in mappings.keys():
                    if english_base in english.lower():
                        return english
        
        return None
    
    def validate_mappings(self, mappings: Dict[str, str], 
                         actual_fields: List[str]) -> Dict[str, Any]:
        """
        验证字段映射的完整性
        
        Args:
            mappings: 字段映射（英文 -> 中文）
            actual_fields: 实际字段列表
            
        Returns:
            验证结果
        """
        result = {
            'is_valid': True,
            'missing_mappings': [],
            'unused_mappings': [],
            'suggestions': []
        }
        
        # 获取所有映射的值（中文字段名）
        mapped_chinese_fields = set(mappings.values())
        mapped_english_fields = set(mappings.keys())
        
        # 检查未映射的字段
        for field in actual_fields:
            if field not in mapped_chinese_fields and field not in mapped_english_fields:
                result['missing_mappings'].append(field)
                # 提供建议
                if field in self.COMMON_MAPPINGS:
                    suggestion = f"{field} -> {self.COMMON_MAPPINGS[field]}"
                    result['suggestions'].append(suggestion)
        
        # 检查未使用的映射
        for english, chinese in mappings.items():
            if chinese not in actual_fields and english not in actual_fields:
                result['unused_mappings'].append(english)
        
        # 判断是否有效
        if result['missing_mappings'] or len(result['unused_mappings']) > 10:
            result['is_valid'] = False
        
        # 生成总结
        if result['missing_mappings']:
            result['suggestions'].insert(0, f"发现 {len(result['missing_mappings'])} 个未映射字段")
        if result['unused_mappings']:
            result['suggestions'].append(f"发现 {len(result['unused_mappings'])} 个未使用映射")
        
        return result
    
    def auto_fix_mappings(self, table_type: str, actual_fields: List[str]) -> Dict[str, str]:
        """
        自动修复字段映射
        
        Args:
            table_type: 表类型
            actual_fields: 实际字段列表
            
        Returns:
            修复后的字段映射
        """
        fixed_mappings = {}
        
        for field in actual_fields:
            # 先检查通用映射
            if field in self.COMMON_MAPPINGS:
                english_name = self.COMMON_MAPPINGS[field]
                fixed_mappings[english_name] = field
            else:
                # 检查是否是英文字段
                if self._is_english_field(field):
                    # 尝试找到对应的中文名
                    chinese_name = self._find_chinese_name(field)
                    fixed_mappings[field] = chinese_name or field
                else:
                    # 中文字段，生成英文名
                    english_name = self._generate_english_name(field)
                    fixed_mappings[english_name] = field
        
        self.logger.info(f"🔧 [P1-优化] 自动修复完成: {table_type}，映射 {len(fixed_mappings)} 个字段")
        return fixed_mappings
    
    def _is_english_field(self, field: str) -> bool:
        """判断是否是英文字段名"""
        try:
            field.encode('ascii')
            return True
        except UnicodeEncodeError:
            return False
    
    def _find_chinese_name(self, english_field: str) -> Optional[str]:
        """根据英文字段名查找中文名"""
        # 反向查找
        for chinese, english in self.COMMON_MAPPINGS.items():
            if english == english_field:
                return chinese
        return None
    
    def _generate_english_name(self, chinese_field: str) -> str:
        """为中文字段生成英文名"""
        # 简单的拼音或关键词替换
        # 这里使用简单的规则
        if '工号' in chinese_field:
            return 'employee_id_custom'
        elif '姓名' in chinese_field:
            return 'name_custom'
        elif '工资' in chinese_field:
            return 'salary_custom'
        else:
            # 使用field_加序号的形式
            return f"field_{hash(chinese_field) % 10000}"

# 单例实例
_optimizer_instance: Optional[FieldMappingOptimizer] = None

def get_field_mapping_optimizer() -> FieldMappingOptimizer:
    """获取字段映射优化器单例"""
    global _optimizer_instance
    if _optimizer_instance is None:
        _optimizer_instance = FieldMappingOptimizer()
    return _optimizer_instance