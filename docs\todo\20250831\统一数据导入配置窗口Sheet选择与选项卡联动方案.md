# 统一数据导入配置窗口 - Sheet选择与选项卡联动分析方案

**日期**: 2025-08-30  
**版本**: v1.0  
**状态**: 待实施

## 📋 需求概述

在"统一数据导入配置"窗口中，需要实现以下功能联动：

1. **Sheet选择联动**: 点击左侧Sheet列表中某个表时，右侧显示对应数据
2. **选项卡联动**: 根据当前选中的选项卡显示不同类型的信息：
   - 🔗 **字段映射选项卡**: 显示Sheet的字段映射相关信息
   - 👁️ **预览验证选项卡**: 显示Sheet的预览验证相关信息
   - 🎨 **格式化规则选项卡**: 显示Sheet的格式化规则相关信息

## 🔍 现状分析

### 核心组件架构
基于 `src/gui/unified_data_import_window.py` 的分析：

```
UnifiedDataImportWindow (主窗口)
├── EnhancedSheetManagementWidget (左侧Sheet管理)
└── QTabWidget (右侧选项卡容器)
    ├── UnifiedMappingConfigWidget (字段映射)
    ├── DataProcessingWidget (数据处理)
    └── PreviewValidationWidget (预览验证)
```

### 现有信号机制
```python
# 已实现的信号连接
self.sheet_management_widget.sheet_selection_changed.connect(self._on_sheet_selection_changed)
self.sheet_management_widget.sheet_preview_requested.connect(self._on_sheet_preview_requested)
self.mapping_tab.mapping_changed.connect(self._on_mapping_changed)
```

### 关键问题识别

1. **缺少选项卡切换监听**: 未监听`QTabWidget.currentChanged`信号
2. **数据显示不同步**: Sheet选择和选项卡状态间缺乏联动
3. **数据缓存缺失**: 每次切换重新加载数据，性能低下
4. **状态管理混乱**: 没有统一的状态管理机制

## 💡 解决方案设计

### 方案架构: 事件驱动 + 智能缓存 + 状态同步

#### 1. 增强事件驱动机制

**在主窗口添加选项卡切换监听**:
```python
def _connect_signals(self):
    # 现有连接...
    
    # 新增：选项卡切换监听
    self.config_details_widget.currentChanged.connect(self._on_tab_changed)
```

**扩展Sheet选择变化处理**:
```python
def _on_sheet_selection_changed(self, selected_sheets: List[Dict]):
    """Sheet选择变化处理 - 增强版"""
    self.logger.info(f"Sheet选择变化: {len(selected_sheets)} 个选中")
    
    if not selected_sheets:
        self.status_updated.emit("未选择工作表")
        self._clear_all_tab_content()
        return
    
    # 更新当前选中的Sheet缓存
    self.current_selected_sheets = selected_sheets
    
    # 根据当前选项卡更新对应显示
    current_tab_index = self.config_details_widget.currentIndex()
    self._sync_tab_content_for_sheets(selected_sheets, current_tab_index)
    
    # 如果只选择了一个Sheet，自动加载其字段到映射配置
    if len(selected_sheets) == 1:
        sheet = selected_sheets[0]
        self._cache_sheet_data(sheet['name'])
```

**新增选项卡切换处理**:
```python
def _on_tab_changed(self, tab_index: int):
    """选项卡切换处理"""
    self.logger.info(f"选项卡切换到: {tab_index}")
    
    # 获取当前选中的Sheet
    selected_sheets = getattr(self, 'current_selected_sheets', [])
    
    if selected_sheets:
        self._sync_tab_content_for_sheets(selected_sheets, tab_index)
    else:
        self._show_empty_tab_content(tab_index)
```

#### 2. 核心同步更新机制

**统一同步方法**:
```python
def _sync_tab_content_for_sheets(self, selected_sheets: List[Dict], tab_index: int):
    """根据选中的sheet和当前选项卡同步更新显示内容"""
    if not selected_sheets:
        self._clear_tab_content(tab_index)
        return
    
    self.status_updated.emit(f"正在加载 {len(selected_sheets)} 个工作表的数据...")
    
    try:
        if tab_index == 0:  # 字段映射选项卡
            self._update_mapping_tab_for_sheets(selected_sheets)
        elif tab_index == 1:  # 数据处理选项卡  
            self._update_processing_tab_for_sheets(selected_sheets)
        elif tab_index == 2:  # 预览验证选项卡
            self._update_preview_tab_for_sheets(selected_sheets)
            
        self.status_updated.emit("✅ 数据加载完成")
        
    except Exception as e:
        self.status_updated.emit(f"❌ 数据加载失败: {e}")
        self.logger.error(f"同步选项卡内容失败: {e}")
```

**分选项卡处理**:
```python
def _update_mapping_tab_for_sheets(self, selected_sheets: List[Dict]):
    """更新字段映射选项卡"""
    if len(selected_sheets) == 1:
        # 单Sheet处理
        sheet = selected_sheets[0]
        headers = self._get_cached_or_load_headers(sheet['name'])
        mapping_data = self._get_cached_or_generate_mapping(sheet['name'], headers)
        
        self.mapping_tab.load_single_sheet_mapping(sheet['name'], headers, mapping_data)
    else:
        # 多Sheet处理 - 显示兼容性分析
        compatibility_data = self._analyze_sheets_compatibility(selected_sheets)
        self.mapping_tab.load_multi_sheet_mapping(selected_sheets, compatibility_data)

def _update_preview_tab_for_sheets(self, selected_sheets: List[Dict]):
    """更新预览验证选项卡"""
    preview_data = []
    
    for sheet in selected_sheets:
        sheet_name = sheet['name']
        data = self._get_cached_or_load_preview(sheet_name)
        preview_data.append({
            'sheet_name': sheet_name,
            'data': data,
            'row_count': len(data) if data else 0
        })
    
    self.preview_tab.load_sheets_preview(preview_data)

def _update_processing_tab_for_sheets(self, selected_sheets: List[Dict]):
    """更新数据处理选项卡"""
    # 分析所有选中Sheet的数据特征
    processing_config = self._analyze_processing_requirements(selected_sheets)
    self.processing_tab.load_processing_config(selected_sheets, processing_config)
```

#### 3. 智能数据缓存机制

**缓存结构设计**:
```python
def _init_data_cache(self):
    """初始化数据缓存"""
    self.sheet_data_cache = {
        # 'sheet_name': {
        #     'headers': [...],           # 字段头信息
        #     'mapping_data': {...},      # 字段映射数据
        #     'preview_data': [...],      # 预览数据（前10行）
        #     'processing_hints': {...},  # 数据处理建议
        #     'last_updated': datetime,   # 最后更新时间
        #     'is_dirty': False          # 数据是否需要更新
        # }
    }
```

**智能缓存管理**:
```python
def _get_cached_or_load_headers(self, sheet_name: str):
    """获取或加载Sheet表头（带缓存）"""
    if sheet_name not in self.sheet_data_cache:
        self.sheet_data_cache[sheet_name] = {}
    
    cache = self.sheet_data_cache[sheet_name]
    
    if 'headers' not in cache or cache.get('is_dirty', True):
        try:
            # 使用现有的加载逻辑
            df = self.import_manager.excel_importer.import_data(
                self.current_file_path, sheet_name, max_rows=1
            )
            
            if df is not None and not df.empty:
                cache['headers'] = list(df.columns)
                cache['last_updated'] = datetime.now()
                cache['is_dirty'] = False
            else:
                cache['headers'] = []
                
        except Exception as e:
            self.logger.error(f"加载Sheet头失败: {sheet_name} - {e}")
            cache['headers'] = []
    
    return cache.get('headers', [])

def _get_cached_or_load_preview(self, sheet_name: str, max_rows: int = 10):
    """获取或加载Sheet预览数据（带缓存）"""
    if sheet_name not in self.sheet_data_cache:
        self.sheet_data_cache[sheet_name] = {}
    
    cache = self.sheet_data_cache[sheet_name]
    
    if 'preview_data' not in cache or cache.get('is_dirty', True):
        try:
            df = self.import_manager.excel_importer.import_data(
                self.current_file_path, sheet_name, max_rows=max_rows
            )
            
            if df is not None and not df.empty:
                cache['preview_data'] = df.to_dict('records')
                cache['last_updated'] = datetime.now()
                cache['is_dirty'] = False
            else:
                cache['preview_data'] = []
                
        except Exception as e:
            self.logger.error(f"加载Sheet预览失败: {sheet_name} - {e}")
            cache['preview_data'] = []
    
    return cache.get('preview_data', [])

def _cache_sheet_data(self, sheet_name: str):
    """预缓存Sheet的所有数据"""
    # 预加载表头
    self._get_cached_or_load_headers(sheet_name)
    
    # 预加载预览数据
    self._get_cached_or_load_preview(sheet_name)
    
    # 预生成映射建议（如果有映射引擎）
    if hasattr(self.import_manager, 'mapping_engine'):
        headers = self.sheet_data_cache[sheet_name].get('headers', [])
        if headers:
            mapping_suggestions = self.import_manager.mapping_engine.generate_mapping_suggestions(
                headers, self.current_table_type
            )
            self.sheet_data_cache[sheet_name]['mapping_suggestions'] = mapping_suggestions
```

#### 4. 状态管理优化

**清理方法**:
```python
def _clear_all_tab_content(self):
    """清空所有选项卡内容"""
    self.mapping_tab.clear()
    self.processing_tab.clear()
    self.preview_tab.clear()

def _clear_tab_content(self, tab_index: int):
    """清空指定选项卡内容"""
    if tab_index == 0:
        self.mapping_tab.clear()
    elif tab_index == 1:
        self.processing_tab.clear()
    elif tab_index == 2:
        self.preview_tab.clear()

def _show_empty_tab_content(self, tab_index: int):
    """显示空内容提示"""
    empty_message = "请先选择要配置的工作表"
    
    if tab_index == 0:
        self.mapping_tab.show_empty_message(empty_message)
    elif tab_index == 1:
        self.processing_tab.show_empty_message(empty_message)
    elif tab_index == 2:
        self.preview_tab.show_empty_message(empty_message)
```

## 🔧 实施计划

### 第一阶段：基础联动功能
- [ ] 实现选项卡切换监听
- [ ] 建立Sheet选择与选项卡显示的基本联动
- [ ] 测试单Sheet选择的基本显示

### 第二阶段：数据缓存优化
- [ ] 实现Sheet数据缓存机制
- [ ] 优化数据加载性能
- [ ] 添加缓存失效和刷新机制

### 第三阶段：多Sheet支持
- [ ] 实现多Sheet选择时的兼容性分析
- [ ] 优化多Sheet数据显示界面
- [ ] 添加批量操作功能

### 第四阶段：用户体验增强
- [ ] 添加加载状态指示器
- [ ] 实现数据预加载策略
- [ ] 添加错误处理和恢复机制

## ⚠️ 注意事项

1. **数据一致性**: 确保缓存数据与实际文件数据一致
2. **内存管理**: 合理控制缓存大小，避免内存溢出
3. **异常处理**: 完善异常处理机制，保证系统稳定性
4. **用户反馈**: 及时提供操作反馈和状态提示

## 📝 技术要点

### 关键信号连接
```python
# 选项卡切换
self.config_details_widget.currentChanged.connect(self._on_tab_changed)

# Sheet选择变化（增强）
self.sheet_management_widget.sheet_selection_changed.connect(self._on_sheet_selection_changed_enhanced)

# 缓存失效通知
self.import_manager.file_changed.connect(self._invalidate_cache)
```

### 性能优化策略
- **延迟加载**: 只在需要时加载数据
- **增量更新**: 只更新变化的部分
- **后台预加载**: 预测用户操作，提前加载数据
- **内存限制**: 设置缓存大小上限，自动清理过期数据

### 错误处理机制
- **数据加载失败**: 显示友好错误信息，提供重试选项
- **格式不兼容**: 智能检测并提供格式转换建议
- **网络异常**: 本地缓存降级处理

---

**文档作者**: Cascade AI  
**最后更新**: 2025-08-30 21:05  
**审核状态**: 待审核
