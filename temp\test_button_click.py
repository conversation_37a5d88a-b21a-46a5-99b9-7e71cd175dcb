#!/usr/bin/env python3
"""
测试按钮点击功能
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.gui.unified_data_import_window import UnifiedDataImportWindow
from src.utils.log_config import setup_logger

def test_button_clicks():
    """测试按钮点击功能"""
    logger = setup_logger(__name__)
    
    app = QApplication([])
    
    try:
        # 创建窗口
        window = UnifiedDataImportWindow()
        window.show()
        
        def test_clicks():
            logger.info("开始测试按钮点击...")
            
            # 测试1: 全选按钮
            try:
                logger.info("测试全选按钮点击...")
                window.sheet_management_widget.select_all_btn.click()
                logger.info("✅ 全选按钮点击成功")
            except Exception as e:
                logger.error(f"❌ 全选按钮点击失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试2: 智能映射按钮
            try:
                logger.info("测试智能映射按钮点击...")
                window.mapping_tab.smart_mapping_btn.click()
                logger.info("✅ 智能映射按钮点击成功")
            except Exception as e:
                logger.error(f"❌ 智能映射按钮点击失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试3: 验证配置按钮
            try:
                logger.info("测试验证配置按钮点击...")
                window.mapping_tab.validate_btn.click()
                logger.info("✅ 验证配置按钮点击成功")
            except Exception as e:
                logger.error(f"❌ 验证配置按钮点击失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试4: 刷新按钮
            try:
                logger.info("测试刷新按钮点击...")
                window.sheet_management_widget.refresh_btn.click()
                logger.info("✅ 刷新按钮点击成功")
            except Exception as e:
                logger.error(f"❌ 刷新按钮点击失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 测试5: 预览按钮
            try:
                logger.info("测试预览按钮点击...")
                window.sheet_management_widget.preview_btn.click()
                logger.info("✅ 预览按钮点击成功")
            except Exception as e:
                logger.error(f"❌ 预览按钮点击失败: {e}")
                import traceback
                traceback.print_exc()
            
            logger.info("按钮点击测试完成")
            
            # 2秒后关闭
            QTimer.singleShot(2000, app.quit)
        
        # 1秒后开始测试
        QTimer.singleShot(1000, test_clicks)
        
        app.exec_()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_button_clicks()
