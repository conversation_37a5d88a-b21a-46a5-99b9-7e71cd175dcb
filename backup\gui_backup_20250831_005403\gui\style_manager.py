#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一样式管理器 - StyleManager

实现单例模式的样式管理系统，提供全局、窗口、组件级样式应用接口。
基于现有的modern_style.py Material Design样式系统构建。

主要功能:
1. 单例模式的样式管理器
2. 全局应用程序样式应用
3. 窗口级样式应用
4. 组件级样式应用
5. 响应式样式支持
6. 主题切换支持
7. 样式缓存优化

创建时间: 2025-07-29
Author: StyleManager Implementation Team
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QPushButton, QTableWidget, QLineEdit, QHeaderView
from PyQt5.QtCore import Qt, QObject, pyqtSignal
from PyQt5.QtGui import QFont

from src.gui.modern_style import (
    MaterialDesignPalette, 
    ModernStyleSheet, 
    ResponsiveLayout, 
    ModernShadowEffects
)
from src.utils.log_config import get_module_logger


class StyleManager(QObject):
    """统一样式管理器 - 单例模式"""
    
    _instance: Optional['StyleManager'] = None
    _current_theme: str = "light"
    _screen_size_category: str = "medium"
    
    # 信号定义
    theme_changed = pyqtSignal(str)
    responsive_updated = pyqtSignal(str)
    
    def __init__(self):
        """初始化样式管理器"""
        if StyleManager._instance is not None:
            raise Exception("StyleManager是单例类，请使用get_instance()方法获取实例")
        
        super().__init__()
        self.logger = get_module_logger(__name__)
        
        # 初始化样式系统组件
        self.modern_stylesheet = ModernStyleSheet()
        self.palette = MaterialDesignPalette()
        self.responsive_layout = ResponsiveLayout()
        self.shadow_effects = ModernShadowEffects()
        
        # 样式缓存
        self._style_cache: Dict[str, str] = {}
        self._compiled_styles: Dict[str, str] = {}
        
        self.logger.info("StyleManager初始化完成")
        
    @classmethod
    def get_instance(cls) -> 'StyleManager':
        """单例模式获取样式管理器实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    def reset_instance(cls):
        """重置单例实例（主要用于测试）"""
        cls._instance = None
        
    def apply_global_style(self, app: QApplication) -> bool:
        """
        应用全局样式到应用程序
        
        Args:
            app: QApplication实例
            
        Returns:
            bool: 应用成功返回True
        """
        try:
            self.logger.info("开始应用全局Material Design样式...")
            
            # 获取完整的样式表
            complete_style = self.modern_stylesheet.get_complete_style()
            
            # 应用全局字体
            font = QFont("Microsoft YaHei", 9)
            app.setFont(font)
            
            # 应用样式表
            app.setStyleSheet(complete_style)
            
            self.logger.info("全局样式应用成功")
            return True
            
        except Exception as e:
            self.logger.error(f"全局样式应用失败: {e}")
            return False
    
    def apply_window_style(self, window: QMainWindow) -> bool:
        """
        应用窗口级样式
        
        Args:
            window: QMainWindow实例
            
        Returns:
            bool: 应用成功返回True
        """
        try:
            self.logger.info("开始应用窗口级样式...")
            
            # 应用主窗口样式
            main_window_style = self.modern_stylesheet.get_main_window_style()
            current_style = window.styleSheet()
            
            # 合并样式
            merged_style = self._merge_styles(current_style, main_window_style)
            window.setStyleSheet(merged_style)
            
            # 设置窗口属性
            window.setMinimumSize(1024, 768)
            
            self.logger.info("窗口级样式应用成功")
            return True
            
        except Exception as e:
            self.logger.error(f"窗口级样式应用失败: {e}")
            return False
    
    def apply_component_style(self, component: QWidget, style_type: str) -> bool:
        """
        应用组件级样式
        
        Args:
            component: 要应用样式的组件
            style_type: 样式类型 (table, button_primary, input等)
            
        Returns:
            bool: 应用成功返回True
        """
        try:
            # 获取缓存的样式
            style_content = self._get_cached_style(style_type)
            
            if style_content:
                current_style = component.styleSheet()
                merged_style = self._merge_styles(current_style, style_content)
                component.setStyleSheet(merged_style)
                
                self.logger.debug(f"组件样式应用成功: {style_type}")
                return True
            else:
                self.logger.warning(f"未找到样式类型: {style_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"组件样式应用失败 [{style_type}]: {e}")
            return False
    
    def set_theme(self, theme: str) -> bool:
        """
        切换主题
        
        Args:
            theme: 主题名称 ('light' 或 'dark')
            
        Returns:
            bool: 切换成功返回True
        """
        try:
            if theme not in ['light', 'dark']:
                self.logger.warning(f"不支持的主题: {theme}")
                return False
                
            if theme == self._current_theme:
                self.logger.info(f"主题已经是 {theme}，无需切换")
                return True
            
            self.logger.info(f"切换主题: {self._current_theme} -> {theme}")
            
            old_theme = self._current_theme
            self._current_theme = theme
            
            # 清除样式缓存
            self._clear_cache()
            
            # 发出主题变更信号
            self.theme_changed.emit(theme)
            
            self.logger.info(f"主题切换成功: {theme}")
            return True
            
        except Exception as e:
            self.logger.error(f"主题切换失败: {e}")
            self._current_theme = old_theme  # 回滚
            return False
    
    def update_responsive_style(self, width: int, height: int) -> bool:
        """
        根据屏幕尺寸更新响应式样式
        
        Args:
            width: 窗口宽度
            height: 窗口高度
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            # 检测屏幕尺寸类别
            new_category = self.responsive_layout.get_size_category(width)
            
            if new_category != self._screen_size_category:
                self.logger.info(f"响应式样式更新: {self._screen_size_category} -> {new_category}")
                
                old_category = self._screen_size_category
                self._screen_size_category = new_category
                
                # 清除相关缓存
                self._clear_responsive_cache()
                
                # 发出响应式更新信号
                self.responsive_updated.emit(new_category)
                
                self.logger.info(f"响应式样式更新成功: {new_category}")
                return True
            else:
                self.logger.debug(f"屏幕尺寸类别未变化: {new_category}")
                return True
                
        except Exception as e:
            self.logger.error(f"响应式样式更新失败: {e}")
            return False
    
    def get_current_theme(self) -> str:
        """获取当前主题"""
        return self._current_theme
    
    def get_screen_size_category(self) -> str:
        """获取当前屏幕尺寸类别"""
        return self._screen_size_category
    
    def _get_cached_style(self, style_key: str, **params) -> str:
        """
        获取缓存的样式
        
        Args:
            style_key: 样式键名
            **params: 额外参数
            
        Returns:
            str: 样式内容
        """
        # 生成缓存键
        cache_key = f"{style_key}_{self._current_theme}_{self._screen_size_category}"
        if params:
            cache_key += f"_{hash(frozenset(params.items()))}"
        
        if cache_key not in self._style_cache:
            style = self._compile_style(style_key, **params)
            self._style_cache[cache_key] = style
        
        return self._style_cache[cache_key]
    
    def _compile_style(self, style_key: str, **params) -> str:
        """
        编译动态样式
        
        Args:
            style_key: 样式键名
            **params: 参数
            
        Returns:
            str: 编译后的样式
        """
        try:
            # 根据样式键名获取对应的样式方法
            style_methods = {
                'main_window': self.modern_stylesheet.get_main_window_style,
                'header': self.modern_stylesheet.get_header_style,
                'sidebar': self.modern_stylesheet.get_sidebar_style,
                'table': self.modern_stylesheet.get_table_style,
                'table_header': self.modern_stylesheet.get_table_style,  # 表头使用表格样式
                'button_primary': self.modern_stylesheet.get_button_style,
                'button_secondary': lambda: self.modern_stylesheet.get_button_style(),
                'button_success': lambda: self.modern_stylesheet.get_button_style(),
                'button_warning': lambda: self.modern_stylesheet.get_button_style(),
                'button_error': lambda: self.modern_stylesheet.get_button_style(),
                'input': self.modern_stylesheet.get_input_style,
                'card': self.modern_stylesheet.get_card_style,
                'status_bar': self.modern_stylesheet.get_status_bar_style,
            }
            
            if style_key in style_methods:
                base_style = style_methods[style_key]()
                
                # 应用响应式调整
                responsive_adjustments = self._get_responsive_adjustments()
                if responsive_adjustments:
                    base_style = self._apply_responsive_adjustments(base_style, responsive_adjustments)
                
                return base_style
            else:
                self.logger.warning(f"未找到样式方法: {style_key}")
                return ""
                
        except Exception as e:
            self.logger.error(f"样式编译失败 [{style_key}]: {e}")
            return ""
    
    def _merge_styles(self, base_style: str, custom_style: str) -> str:
        """
        合并基础样式和自定义样式
        
        Args:
            base_style: 基础样式
            custom_style: 自定义样式
            
        Returns:
            str: 合并后的样式
        """
        if not base_style:
            return custom_style
        if not custom_style:
            return base_style
            
        return f"{base_style}\n{custom_style}"
    
    def _get_responsive_adjustments(self) -> Dict[str, Any]:
        """
        获取响应式调整参数
        
        Returns:
            Dict: 调整参数
        """
        adjustments = {
            'small': {
                'sidebar_width': self.responsive_layout.get_sidebar_width('small'),
                'font_size': self.responsive_layout.get_font_size('small'),
                'button_padding': '6px 12px'
            },
            'medium': {
                'sidebar_width': self.responsive_layout.get_sidebar_width('medium'),
                'font_size': self.responsive_layout.get_font_size('medium'),
                'button_padding': '8px 16px'
            },
            'large': {
                'sidebar_width': self.responsive_layout.get_sidebar_width('large'),
                'font_size': self.responsive_layout.get_font_size('large'),
                'button_padding': '10px 20px'
            }
        }
        
        return adjustments.get(self._screen_size_category, adjustments['medium'])
    
    def _apply_responsive_adjustments(self, base_style: str, adjustments: Dict[str, Any]) -> str:
        """
        应用响应式调整到样式
        
        Args:
            base_style: 基础样式
            adjustments: 调整参数
            
        Returns:
            str: 调整后的样式
        """
        # 这里可以实现具体的样式参数替换逻辑
        # 暂时返回原样式
        return base_style
    
    def _clear_cache(self):
        """清除所有样式缓存"""
        self._style_cache.clear()
        self._compiled_styles.clear()
        self.logger.debug("样式缓存已清除")
    
    def _clear_responsive_cache(self):
        """清除响应式相关的样式缓存"""
        keys_to_remove = []
        for key in self._style_cache.keys():
            if self._screen_size_category not in key:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self._style_cache[key]
            
        self.logger.debug("响应式样式缓存已清除")
    
    def enable_hot_reload(self, enable: bool = True):
        """
        启用样式热重载（仅开发环境）
        
        Args:
            enable: 是否启用热重载
        """
        if enable and os.getenv('APP_ENV') == 'development':
            try:
                self._setup_file_watcher()
                self.logger.info("样式热重载已启用")
            except Exception as e:
                self.logger.error(f"样式热重载启用失败: {e}")
        else:
            self.logger.info("样式热重载未启用（非开发环境）")
    
    def _setup_file_watcher(self):
        """设置文件监听器"""
        try:
            from PyQt5.QtCore import QFileSystemWatcher
            
            watcher = QFileSystemWatcher()
            watcher.fileChanged.connect(self._on_style_file_changed)
            
            # 监听modern_style.py文件
            style_file_path = str(Path(__file__).parent / "modern_style.py")
            watcher.addPath(style_file_path)
            
            self.logger.info(f"文件监听器设置完成: {style_file_path}")
            
        except ImportError as e:
            self.logger.warning(f"无法导入QFileSystemWatcher: {e}")
        except Exception as e:
            self.logger.error(f"文件监听器设置失败: {e}")
    
    def _on_style_file_changed(self, path: str):
        """样式文件变化时重新加载"""
        try:
            self.logger.info(f"检测到样式文件变化: {path}")
            self._clear_cache()
            
            # 重新导入样式模块
            import importlib
            from src.gui import modern_style
            importlib.reload(modern_style)
            
            # 重新初始化样式组件
            self.modern_stylesheet = modern_style.ModernStyleSheet()
            self.palette = modern_style.MaterialDesignPalette()
            
            self.logger.info("样式系统热重载完成")
            
        except Exception as e:
            self.logger.error(f"样式热重载失败: {e}")
    
    def get_style_info(self) -> Dict[str, Any]:
        """
        获取样式系统信息
        
        Returns:
            Dict: 样式系统信息
        """
        return {
            'current_theme': self._current_theme,
            'screen_size_category': self._screen_size_category,
            'cache_size': len(self._style_cache),
            'available_themes': ['light', 'dark'],
            'available_style_types': [
                'main_window', 'header', 'sidebar', 'table',
                'button_primary', 'button_secondary', 'button_success',
                'button_warning', 'button_error', 'input', 'card', 'status_bar'
            ]
        }