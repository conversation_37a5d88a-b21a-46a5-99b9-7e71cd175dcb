#!/usr/bin/env python3
"""
测试用户配置独立文件保存功能

验证功能：
1. "另存配置"按钮保存到独立文件
2. 用户配置目录结构正确
3. 配置文件格式正确
4. 配置加载功能正常
5. 界面区分显示系统和用户配置
"""

import sys
import os
import pandas as pd
from pathlib import Path
from datetime import datetime
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.modules.data_import.change_data_config_manager import ChangeDataConfigManager


def test_user_config_save():
    """测试用户配置保存功能"""
    logger.info("=== 测试用户配置独立文件保存 ===")
    
    # 1. 初始化配置管理器
    config_manager = ChangeDataConfigManager()
    
    # 2. 模拟一个用户配置
    test_config = {
        'field_types': {
            '序号': 'integer',
            '姓名': 'name_string',
            '部门': 'text_string',
            '基本工资': 'salary_float',
            '津贴': 'salary_float',
            '应发工资': 'salary_float'
        },
        'field_mapping': {
            '序号': '序号',
            '姓名': '姓名',
            '部门': '部门',
            '基本工资': '基本工资',
            '津贴': '津贴',
            '应发工资': '应发工资'
        },
        'formatting_rules': {
            'salary_float': {
                '小数位数': '2',
                '千位分隔符': '是'
            }
        }
    }
    
    # 3. 模拟用户另存配置的过程
    config_name = "测试用户配置"
    
    # 清理文件名
    def sanitize_filename(filename: str) -> str:
        import re
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename.strip())
        sanitized = re.sub(r'_+', '_', sanitized)
        sanitized = sanitized.strip('_')
        return sanitized if sanitized else 'unnamed_config'
    
    clean_name = sanitize_filename(config_name)
    
    # 4. 创建完整的配置结构并保存到独立文件
    user_config_dir = config_manager.config_dir / 'user_configs'
    config_file_path = user_config_dir / f"{clean_name}.json"
    
    config_with_meta = {
        'name': clean_name,
        'description': f"用户配置 - {len(test_config.get('field_mapping', {}))} 个字段",
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat(),
        'version': '1.0',
        'type': 'user_config',
        'sheet_name': '在职人员工资表',
        'data': test_config
    }
    
    with open(config_file_path, 'w', encoding='utf-8') as f:
        json.dump(config_with_meta, f, ensure_ascii=False, indent=2)
    
    logger.info(f"✅ 用户配置已保存到: {config_file_path}")
    
    # 5. 测试配置管理器的用户配置方法
    logger.info("\n--- 测试配置管理器方法 ---")
    
    # 列出用户配置
    user_configs = config_manager.list_user_configs()
    logger.info(f"找到 {len(user_configs)} 个用户配置:")
    for config in user_configs:
        logger.info(f"  - {config['name']}: {config['description']}")
    
    # 加载用户配置
    loaded_config = config_manager.load_user_config(clean_name)
    if loaded_config:
        logger.info(f"✅ 用户配置加载成功，包含 {len(loaded_config.get('field_mapping', {}))} 个字段")
    else:
        logger.error("❌ 用户配置加载失败")
        return False
    
    # 6. 验证文件结构
    logger.info("\n--- 验证文件结构 ---")
    config_dir = config_manager.config_dir
    
    logger.info(f"配置根目录: {config_dir}")
    logger.info(f"统一配置文件: {config_dir / 'configurations.json'}")
    logger.info(f"用户配置目录: {config_dir / 'user_configs'}")
    logger.info(f"模板配置目录: {config_dir / 'templates'}")
    
    # 检查目录是否存在
    if (config_dir / 'user_configs').exists():
        logger.info("✅ 用户配置目录存在")
        user_config_files = list((config_dir / 'user_configs').glob('*.json'))
        logger.info(f"用户配置文件数量: {len(user_config_files)}")
        for file in user_config_files:
            logger.info(f"  - {file.name}")
    else:
        logger.error("❌ 用户配置目录不存在")
        return False
    
    # 7. 对比系统配置和用户配置
    logger.info("\n--- 对比系统配置和用户配置 ---")
    
    system_configs = config_manager.list_configs()
    logger.info(f"系统配置数量: {len(system_configs)}")
    logger.info(f"用户配置数量: {len(user_configs)}")
    
    # 显示配置文件大小对比
    system_config_file = config_dir / 'configurations.json'
    if system_config_file.exists():
        system_size = system_config_file.stat().st_size
        logger.info(f"系统配置文件大小: {system_size} 字节")
    
    total_user_size = sum(f.stat().st_size for f in (config_dir / 'user_configs').glob('*.json'))
    logger.info(f"用户配置文件总大小: {total_user_size} 字节")
    
    # 8. 测试配置内容验证
    logger.info("\n--- 验证配置内容 ---")
    
    # 验证加载的配置与原始配置是否一致
    if loaded_config:
        original_field_types = test_config['field_types']
        loaded_field_types = loaded_config.get('field_types', {})
        
        if original_field_types == loaded_field_types:
            logger.info("✅ 字段类型配置保存/加载一致")
        else:
            logger.error("❌ 字段类型配置不一致")
            return False
        
        original_field_mapping = test_config['field_mapping']
        loaded_field_mapping = loaded_config.get('field_mapping', {})
        
        if original_field_mapping == loaded_field_mapping:
            logger.info("✅ 字段映射配置保存/加载一致")
        else:
            logger.error("❌ 字段映射配置不一致")
            return False
    
    logger.info("\n🎉 所有测试通过！用户配置独立文件保存功能正常工作！")
    return True


def show_config_structure():
    """显示配置文件结构"""
    logger.info("\n=== 配置文件结构展示 ===")
    
    config_manager = ChangeDataConfigManager()
    config_dir = config_manager.config_dir
    
    print(f"\n配置文件结构:")
    print(f"state/change_data_configs/")
    print(f"├── configurations.json        # 系统统一配置文件")
    print(f"├── user_configs/             # 用户独立配置目录")
    print(f"│   ├── 测试用户配置.json")
    print(f"│   └── ...")
    print(f"└── templates/               # 模板配置目录")
    print(f"    └── ...")
    
    # 实际文件列表
    print(f"\n实际文件:")
    if config_dir.exists():
        for item in config_dir.iterdir():
            if item.is_file():
                size = item.stat().st_size
                print(f"  FILE {item.name} ({size} 字节)")
            elif item.is_dir():
                files = list(item.glob('*.json'))
                print(f"  DIR {item.name}/ ({len(files)} 个文件)")
                for file in files[:3]:  # 最多显示3个
                    size = file.stat().st_size
                    print(f"    └── {file.name} ({size} 字节)")
                if len(files) > 3:
                    print(f"    └── ... (还有 {len(files) - 3} 个文件)")


def main():
    """主函数"""
    try:
        # 运行测试
        success = test_user_config_save()
        
        # 显示结构
        show_config_structure()
        
        if success:
            print(f"\n>>> 测试结果：用户配置独立文件保存功能正常")
            print(f">>> '另存配置'按钮现在将配置保存到独立文件")
            print(f">>> 系统和用户配置完全分离管理")
            return 0
        else:
            print(f"\n>>> 测试结果：用户配置功能存在问题")
            return 1
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())