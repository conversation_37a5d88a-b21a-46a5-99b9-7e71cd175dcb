#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新架构多列排序管理器

与新架构兼容的多列排序功能实现，支持：
- 多个字段同时排序（最多3个字段）
- 升序、降序、取消排序的完整循环
- 排序优先级管理
- 与新架构的表格组件完全兼容

创建时间: 2025-07-18
作者: 月度工资异动处理系统开发团队
"""

import time
from typing import List, Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass, field

from PyQt5.QtCore import Qt, QObject, pyqtSignal
from PyQt5.QtWidgets import QTableWidget, QHeaderView

try:
    from src.utils.log_config import setup_logger
    from src.utils.logging_utils import bind_context, log_throttle, log_sample, redact
except ImportError:
    import logging
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger


class SortOrder(Enum):
    """排序顺序枚举"""
    NONE = "none"           # 无排序
    ASCENDING = "ascending"  # 升序
    DESCENDING = "descending"  # 降序


@dataclass
class SortColumn:
    """排序列信息"""
    column_index: int
    column_name: str
    order: SortOrder
    priority: int = 0  # 排序优先级，数值越小优先级越高
    timestamp: float = field(default_factory=time.time)  # 添加时间戳


class NewArchitectureColumnSortManager(QObject):
    """
    新架构多列排序管理器
    
    专为新架构设计的多列排序功能，特点：
    - 与新架构的自定义排序循环完全兼容
    - 支持多列排序（最多3列）
    - 完整的排序循环：无排序 → 升序 → 降序 → 无排序
    - 排序优先级管理
    - 事件驱动架构
    """
    
    # 信号定义
    sort_state_changed = pyqtSignal(list)  # 排序状态变化时发出
    sort_request = pyqtSignal(str, str, str)  # 请求排序时发出 (table_name, column_name, sort_state)
    sort_cleared = pyqtSignal()  # 清除排序时发出
    
    def __init__(self, parent_table: QTableWidget, max_sort_columns: int = 3):
        """
        初始化多列排序管理器
        
        Args:
            parent_table: 关联的表格组件
            max_sort_columns: 最大排序列数
        """
        super().__init__()
        
        base_logger = setup_logger(__name__)
        # 绑定组件级上下文
        self.logger = bind_context(base_logger, component="ColumnSortManager")
        self.parent_table = parent_table
        self.max_sort_columns = max_sort_columns
        
        # 排序状态
        self.sort_columns: List[SortColumn] = []
        self.sort_history: List[List[SortColumn]] = []  # 排序历史
        self.max_history_size = 10
        
        # 🔧 [新架构] 统一字段映射管理 - 动态加载而非硬编码
        self.field_mapping = {}
        self.current_table_name = ""
        
        # 🔧 [新架构] 从统一配置文件加载字段映射
        self._load_field_mappings_from_config()
        
        self.logger.info(f"🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: {max_sort_columns}")
    
    def handle_header_click(self, logical_index: int, column_name: str) -> bool:
        """
        处理表头点击事件，实现多列排序逻辑
        
        Args:
            logical_index: 列索引
            column_name: 列名
            
        Returns:
            bool: 是否处理成功
        """
        try:
            # 获取当前列的排序状态
            current_sort_column = self._find_sort_column(logical_index)
            
            if current_sort_column:
                # 如果列已经在排序中，更新其排序顺序
                next_order = self._get_next_sort_order(current_sort_column.order)
                
                if next_order == SortOrder.NONE:
                    # 移除排序
                    self._remove_sort_column(logical_index)
                    if log_throttle('multi-sort', 0.5):
                        self.logger.info(f"🆕 [多列排序] 移除列{logical_index}({column_name})的排序")
                else:
                    # 更新排序顺序
                    current_sort_column.order = next_order
                    current_sort_column.timestamp = time.time()
                    if log_throttle('multi-sort', 0.5):
                        self.logger.info(f"🆕 [多列排序] 更新列{logical_index}({column_name})排序: {next_order.value}")
            else:
                # 新增排序列
                self._add_sort_column(logical_index, column_name, SortOrder.ASCENDING)
                if log_throttle('multi-sort', 0.5):
                    self.logger.info(f"🆕 [多列排序] 新增列{logical_index}({column_name})排序: 升序")
            
            # 记录历史
            self._add_to_history()
            
            # 发出排序状态变化信号
            self.sort_state_changed.emit(self.sort_columns.copy())
            
            # 发出排序请求信号
            if self.sort_columns:
                primary_column = self.sort_columns[0]  # 优先级最高的列
                db_column_name = self.field_mapping.get(primary_column.column_name, primary_column.column_name)
                self.sort_request.emit(
                    self._get_table_name(),
                    db_column_name,
                    primary_column.order.value
                )
            else:
                self.sort_cleared.emit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"🆕 [多列排序] 处理表头点击失败: {e}")
            return False
    
    def _find_sort_column(self, logical_index: int) -> Optional[SortColumn]:
        """查找指定列的排序信息"""
        for sort_col in self.sort_columns:
            if sort_col.column_index == logical_index:
                return sort_col
        return None
    
    def _get_next_sort_order(self, current_order: SortOrder) -> SortOrder:
        """获取下一个排序顺序"""
        # 🔧 [用户需求修复] 恢复完整三态循环：无排序 → 升序 → 降序 → 无排序
        order_cycle = {
            SortOrder.NONE: SortOrder.ASCENDING,
            SortOrder.ASCENDING: SortOrder.DESCENDING,
            SortOrder.DESCENDING: SortOrder.NONE  # 🔧 修复：降序后回到无排序状态
        }
        return order_cycle.get(current_order, SortOrder.ASCENDING)
    
    def _add_sort_column(self, logical_index: int, column_name: str, order: SortOrder):
        """添加排序列"""
        # 检查是否已达到最大排序列数
        if len(self.sort_columns) >= self.max_sort_columns:
            # 移除最旧的排序列
            oldest_column = min(self.sort_columns, key=lambda x: x.timestamp)
            self.sort_columns.remove(oldest_column)
            self.logger.info(f"🆕 [多列排序] 达到最大排序列数，移除最旧的列{oldest_column.column_index}")
        
        # 添加新的排序列
        new_sort_column = SortColumn(
            column_index=logical_index,
            column_name=column_name,
            order=order,
            priority=len(self.sort_columns)
        )
        self.sort_columns.append(new_sort_column)
        
        # 重新分配优先级
        self._reassign_priorities()
    
    def _remove_sort_column(self, logical_index: int):
        """移除排序列"""
        self.sort_columns = [col for col in self.sort_columns if col.column_index != logical_index]
        self._reassign_priorities()
    
    def _reassign_priorities(self):
        """重新分配优先级"""
        # 按时间戳排序，最新的优先级最高
        self.sort_columns.sort(key=lambda x: x.timestamp, reverse=True)
        for i, col in enumerate(self.sort_columns):
            col.priority = i
    
    def _add_to_history(self):
        """添加到排序历史"""
        current_state = [
            SortColumn(
                column_index=col.column_index,
                column_name=col.column_name,
                order=col.order,
                priority=col.priority,
                timestamp=col.timestamp
            )
            for col in self.sort_columns
        ]
        
        self.sort_history.append(current_state)
        
        # 限制历史记录大小
        if len(self.sort_history) > self.max_history_size:
            self.sort_history = self.sort_history[-self.max_history_size:]
    
    def _get_table_name(self) -> str:
        """获取表名"""
        try:
            # 尝试从父表格获取表名
            if hasattr(self.parent_table, 'current_table_name'):
                return self.parent_table.current_table_name
            
            # 尝试从主窗口获取
            main_window = self._get_main_window()
            if main_window and hasattr(main_window, 'current_table_name'):
                return main_window.current_table_name
            
            return "unknown_table"
        except Exception as e:
            self.logger.error(f"获取表名失败: {e}")
            return "unknown_table"
    
    def _get_main_window(self):
        """获取主窗口引用"""
        try:
            widget = self.parent_table
            while widget:
                if hasattr(widget, 'dynamic_table_manager'):
                    return widget
                widget = widget.parent()
            return None
        except Exception:
            return None
    
    def clear_all_sorts(self):
        """清除所有排序"""
        if self.sort_columns:
            self.sort_columns.clear()
            self.sort_state_changed.emit([])
            self.sort_cleared.emit()
            self.logger.info("🆕 [多列排序] 已清除所有排序")
    
    def get_sort_info(self) -> Dict[str, Any]:
        """获取排序信息"""
        return {
            'sort_columns': [
                {
                    'column_index': col.column_index,
                    'column_name': col.column_name,
                    'order': col.order.value,
                    'priority': col.priority,
                    'db_column_name': self.field_mapping.get(col.column_name, col.column_name)
                }
                for col in sorted(self.sort_columns, key=lambda x: x.priority)
            ],
            'total_columns': len(self.sort_columns),
            'max_columns': self.max_sort_columns
        }
    
    def get_sort_description(self) -> str:
        """获取排序描述"""
        if not self.sort_columns:
            return "无排序"
        
        descriptions = []
        sorted_columns = sorted(self.sort_columns, key=lambda x: x.priority)
        
        for i, col in enumerate(sorted_columns):
            order_text = "升序" if col.order == SortOrder.ASCENDING else "降序"
            priority_text = f"({i+1})" if len(sorted_columns) > 1 else ""
            descriptions.append(f"{col.column_name}{priority_text}: {order_text}")
        
        return "; ".join(descriptions)
    
    def update_sort_indicators(self):
        """更新排序指示器显示"""
        try:
            header = self.parent_table.horizontalHeader()
            
            # 清除所有排序指示器
            header.setSortIndicator(-1, Qt.AscendingOrder)
            
            # 只显示优先级最高的排序指示器
            if self.sort_columns:
                primary_column = min(self.sort_columns, key=lambda x: x.priority)
                
                # 🔧 [P1-紧急修复] 根据列名重新查找正确的列索引
                actual_column_index = self._find_column_index_by_name(primary_column.column_name)
                if actual_column_index >= 0:
                    qt_order = Qt.AscendingOrder if primary_column.order == SortOrder.ASCENDING else Qt.DescendingOrder
                    header.setSortIndicator(actual_column_index, qt_order)
                    
                    self.logger.debug(f"🔧 [P1-紧急修复] 更新排序指示器: 列名={primary_column.column_name}, 实际索引={actual_column_index}, {primary_column.order.value}")
                else:
                    self.logger.warning(f"🔧 [P1-紧急修复] 无法找到列名 '{primary_column.column_name}' 的索引，清除排序指示器")
            
        except Exception as e:
            self.logger.error(f"🆕 [多列排序] 更新排序指示器失败: {e}")
    
    def _find_column_index_by_name(self, column_name: str) -> int:
        """
        🔧 [P1-紧急修复] 根据列名查找当前表格中的实际列索引
        
        Args:
            column_name: 列名
            
        Returns:
            列索引，未找到返回-1
        """
        try:
            for col in range(self.parent_table.columnCount()):
                header_item = self.parent_table.horizontalHeaderItem(col)
                if header_item and header_item.text() == column_name:
                    return col
            return -1
        except Exception as e:
            self.logger.error(f"🔧 [P1-紧急修复] 查找列索引失败: {e}")
            return -1
    
    def _load_field_mappings_from_config(self):
        """🔧 [新架构] 从统一配置文件加载字段映射"""
        try:
            import json
            from pathlib import Path
            
            # 读取统一字段映射配置
            config_path = Path(__file__).parent.parent.parent.parent.parent / "state" / "data" / "field_mappings.json"
            
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 从所有表格模板中构建完整的字段映射
                all_mappings = {}
                if 'field_templates' in config_data:
                    for template_name, mappings in config_data['field_templates'].items():
                        # 创建反向映射：中文名 -> 数据库字段名
                        for db_field, chinese_name in mappings.items():
                            all_mappings[chinese_name] = db_field
                
                self.field_mapping = all_mappings
                if log_throttle('multi-sort-mapping', 2.0):
                    self.logger.info(f"🔧 [新架构] 成功加载 {len(all_mappings)} 个字段映射")
                
            else:
                if log_throttle('multi-sort-mapping', 2.0):
                    self.logger.warning(f"🔧 [新架构] 字段映射配置文件不存在: {config_path}")
                self._load_fallback_mappings()
                
        except Exception as e:
            self.logger.error(f"🔧 [新架构] 加载字段映射失败: {e}")
            self._load_fallback_mappings()
    
    def _load_fallback_mappings(self):
        """🔧 [新架构] 加载后备字段映射"""
        self.field_mapping = {
            # 通用字段
            "序号": "sequence_number",
            "工号": "employee_id", 
            "人员代码": "employee_id",
            "姓名": "employee_name",
            "部门名称": "department",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",
            
            # 在职人员字段
            "2025年岗位工资": "position_salary_2025",
            "2025年薪级工资": "grade_salary_2025",
            "2025年校龄工资": "seniority_salary_2025",  # A岗职工特有
            "津贴": "allowance",
            "结余津贴": "balance_allowance",
            "2025年基础性绩效": "basic_performance_2025",
            "卫生费": "health_fee",
            "2025年生活补贴": "living_allowance_2025",  # A岗职工特有
            "交通补贴": "transport_allowance",
            "物业补贴": "property_allowance",
            "住房补贴": "housing_allowance",
            "车补": "car_allowance",
            "通讯补贴": "communication_allowance",
            "2025年奖励性绩效预发": "performance_bonus_2025",
            "补发": "supplement",
            "借支": "advance",
            "应发工资": "total_salary",
            "2025公积金": "provident_fund_2025",
            "保险扣款": "insurance_deduction",
            "代扣代存养老保险": "pension_insurance",
            
            # 离退休人员字段
            "基本离休费": "basic_retirement_salary",
            "基本退休费": "basic_retirement_salary",
            "生活补贴": "living_allowance",
            "离休补贴": "retirement_allowance",
            "护理费": "nursing_fee",
            "增发一次性生活补贴": "one_time_living_allowance",
            "合计": "total",
            "离退休生活补贴": "retirement_living_allowance",
            "增资预付": "salary_advance",
            "2016待遇调整": "adjustment_2016",
            "2017待遇调整": "adjustment_2017", 
            "2018待遇调整": "adjustment_2018",
            "2019待遇调整": "adjustment_2019",
            "2020待遇调整": "adjustment_2020",
            "2021待遇调整": "adjustment_2021",
            "2022待遇调整": "adjustment_2022",
            "2023待遇调整": "adjustment_2023",
            "公积": "provident_fund",
            "备注": "remarks"
        }
        self.logger.info(f"🔧 [新架构] 加载后备字段映射 {len(self.field_mapping)} 个")
    
    def update_table_context(self, table_name: str):
        """🔧 [新架构] 更新表格上下文"""
        try:
            if table_name != self.current_table_name:
                self.current_table_name = table_name
                self.logger.info(f"🔧 [新架构] 排序管理器切换到表格: {table_name}")
                
                # 🔧 [关键修复] 重新加载适合当前表格的字段映射
                self._reload_field_mapping_for_table(table_name)
                
                # 清理当前排序状态
                self.clear_all_sorts()
                
        except Exception as e:
            self.logger.error(f"🔧 [新架构] 更新表格上下文失败: {e}")
    
    def _reload_field_mapping_for_table(self, table_name: str):
        """🔧 [新架构] 为特定表格重新加载字段映射"""
        try:
            import json
            from pathlib import Path
            
            # 读取统一字段映射配置
            config_path = Path(__file__).parent.parent.parent.parent.parent / "state" / "data" / "field_mappings.json"
            
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 优先从特定表格配置获取
                table_mappings = config_data.get('table_mappings', {})
                if table_name in table_mappings:
                    field_mappings = table_mappings[table_name].get('field_mappings', {})
                    if field_mappings:
                        # 创建反向映射：中文名 -> 数据库字段名（排序管理器需要的方向）
                        self.field_mapping = {v: k for k, v in field_mappings.items()}
                        self.logger.info(f"🔧 [新架构] 为表格 {table_name} 重新加载 {len(self.field_mapping)} 个字段映射")
                        return
                
                # 如果没有特定表格配置，使用通用模板
                self._load_field_mappings_from_config()
                
        except Exception as e:
            self.logger.error(f"🔧 [新架构] 为表格重新加载字段映射失败: {e}")
            # 保持原有映射


# 模块导出
__all__ = [
    "NewArchitectureColumnSortManager",
    "SortOrder",
    "SortColumn"
]