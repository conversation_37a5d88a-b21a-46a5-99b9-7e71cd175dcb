#!/usr/bin/env python3
"""
验证'unknown'工作表名称问题的修复效果

专门测试在current_sheet_name为'unknown'情况下，
新的_collect_all_configured_sheets方法是否能正确处理
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger

def test_unknown_sheet_name_fix():
    """测试'unknown'工作表名称的修复"""
    logger.info("=== 测试'unknown'工作表名称修复 ===")
    
    class TestDialog:
        def __init__(self):
            # 模拟用户初次打开对话框的情况
            self.current_sheet_name = 'unknown'  # 这是问题的根源
            self.all_sheets_configs = {}  # 空的，用户还没切换过
            
            # 模拟实际的工作表数据
            self.all_sheets_data = {
                'A岗职工': {'序号': 1, '工号': '001001', '姓名': '张三', '基本工资': 5000.0},
                '退休人员工资表': {'序号': 1, '姓名': '李四', '基本退休费': 3000.0},
                '离休人员表': {'人员代码': '001002', '姓名': '王五', '基本离休费': 3500.0}
            }
            
            # 模拟父窗口的配置
            self.mock_parent_configs = {
                'A岗职工': {
                    'field_mapping': {'序号': '序号', '工号': '工号', '姓名': '姓名', '基本工资': '基本工资'},
                    'field_types': {'序号': 'integer', '工号': 'employee_id_string', '姓名': 'name_string', '基本工资': 'salary_float'}
                },
                '退休人员工资表': {
                    'field_mapping': {'序号': '序号', '姓名': '姓名', '基本退休费': '基本退休费'},
                    'field_types': {'序号': 'integer', '姓名': 'name_string', '基本退休费': 'salary_float'}
                },
                '离休人员表': {
                    'field_mapping': {'人员代码': '人员代码', '姓名': '姓名', '基本离休费': '基本离休费'},
                    'field_types': {'人员代码': 'employee_id_string', '姓名': 'name_string', '基本离休费': 'salary_float'}
                }
            }
        
        def get_current_configuration(self):
            """模拟获取当前配置"""
            # 如果是'unknown'，返回第一个工作表的配置
            if self.current_sheet_name == 'unknown' and self.all_sheets_data:
                first_sheet_name = list(self.all_sheets_data.keys())[0]
                return self.mock_parent_configs.get(first_sheet_name, {})
            return self.mock_parent_configs.get(self.current_sheet_name, {})
        
        def parent(self):
            """模拟父窗口"""
            class MockParent:
                def __init__(self, configs):
                    self.change_data_configs = configs
            return MockParent(self.mock_parent_configs)
        
        def _collect_all_configured_sheets(self):
            """修复后的配置收集方法"""
            logger.info("开始执行修复后的配置收集...")
            configs_to_save = {}
            
            try:
                # 1. 修复unknown工作表名称问题
                actual_sheet_name = self.current_sheet_name
                if self.current_sheet_name == 'unknown' and self.all_sheets_data:
                    actual_sheet_name = list(self.all_sheets_data.keys())[0]
                    logger.info(f"修复工作表名称：从 'unknown' 更正为 '{actual_sheet_name}'")
                
                # 2. 收集当前工作表配置
                if hasattr(self, 'current_sheet_name') and actual_sheet_name:
                    try:
                        current_config = self.get_current_configuration()
                        if current_config and current_config.get('field_mapping'):
                            configs_to_save[actual_sheet_name] = current_config
                            logger.info(f"收集到当前工作表 '{actual_sheet_name}' 配置：{len(current_config.get('field_mapping', {}))} 个字段")
                    except Exception as e:
                        logger.warning(f"获取当前工作表配置时出错: {e}")
                
                # 3. 从all_sheets_configs收集其他配置
                for sheet_name, config in self.all_sheets_configs.items():
                    if config and config.get('field_mapping'):
                        if sheet_name not in configs_to_save:
                            configs_to_save[sheet_name] = config
                            logger.info(f"从all_sheets_configs收集到工作表 '{sheet_name}' 配置：{len(config.get('field_mapping', {}))} 个字段")
                
                # 4. 从父窗口收集配置
                try:
                    parent = self.parent()
                    if parent and hasattr(parent, 'change_data_configs') and parent.change_data_configs:
                        for sheet_name, config in parent.change_data_configs.items():
                            if config and config.get('field_mapping'):
                                if sheet_name not in configs_to_save:
                                    configs_to_save[sheet_name] = config
                                    logger.info(f"从父窗口收集到工作表 '{sheet_name}' 配置：{len(config.get('field_mapping', {}))} 个字段")
                except Exception as e:
                    logger.warning(f"从父窗口获取配置时出错: {e}")
                
                logger.info(f"配置收集完成，共找到 {len(configs_to_save)} 个已配置的工作表")
                return configs_to_save
                
            except Exception as e:
                logger.error(f"配置收集过程中发生错误: {e}")
                return {}
        
        def old_buggy_logic(self):
            """旧的有问题的逻辑"""
            logger.info("--- 旧逻辑（有问题的）---")
            
            # 只会保存all_sheets_configs中的内容
            if not self.all_sheets_configs:
                logger.error("旧逻辑失败：没有找到配置（因为用户没切换表）")
                return {}
            
            # 如果current_sheet_name是'unknown'，条件判断会阻止保存
            if self.current_sheet_name == 'unknown':
                logger.error("旧逻辑失败：当前工作表名称为'unknown'，被条件判断阻止")
                return {}
            
            return self.all_sheets_configs
    
    # 执行测试
    dialog = TestDialog()
    
    logger.info("\\n--- 对比测试 ---")
    logger.info(f"初始状态：current_sheet_name='{dialog.current_sheet_name}'")
    logger.info(f"初始状态：all_sheets_configs为空={not dialog.all_sheets_configs}")
    logger.info(f"父窗口有配置数量：{len(dialog.mock_parent_configs)}")
    
    # 测试旧逻辑
    old_result = dialog.old_buggy_logic()
    
    # 测试新逻辑
    new_result = dialog._collect_all_configured_sheets()
    
    logger.info("\\n--- 结果对比 ---")
    logger.info(f"旧逻辑结果：{len(old_result)} 个工作表 - {list(old_result.keys())}")
    logger.info(f"新逻辑结果：{len(new_result)} 个工作表 - {list(new_result.keys())}")
    
    # 验证修复效果
    expected_sheets = {'A岗职工', '退休人员工资表', '离休人员表'}
    old_success = expected_sheets.issubset(set(old_result.keys()))
    new_success = expected_sheets.issubset(set(new_result.keys()))
    
    logger.info(f"\\n--- 修复验证 ---")
    logger.info(f"用户期望保存的工作表：{expected_sheets}")
    logger.info(f"旧逻辑是否满足期望：{old_success}")
    logger.info(f"新逻辑是否满足期望：{new_success}")
    
    # 特别验证'unknown'问题
    has_unknown_in_new = 'unknown' in new_result
    first_sheet_saved = 'A岗职工' in new_result
    
    logger.info(f"新逻辑是否还有'unknown'工作表：{has_unknown_in_new}")
    logger.info(f"第一个工作表是否正确保存：{first_sheet_saved}")
    
    return not old_success and new_success and not has_unknown_in_new and first_sheet_saved

def main():
    """主函数"""
    try:
        success = test_unknown_sheet_name_fix()
        
        print(f"\\n>>> 'unknown'工作表名称修复验证: {'成功' if success else '失败'}")
        if success:
            print(">>> 修复完全成功！")
            print(">>> - 'unknown'工作表名称问题已解决")
            print(">>> - 用户无需切换表就能保存所有配置")
            print(">>> - 第一个工作表能正确保存为实际名称")
        else:
            print(">>> 修复仍有问题，需要进一步调试")
            
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())