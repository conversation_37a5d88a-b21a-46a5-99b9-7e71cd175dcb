#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表头配置缓存模块
优化表头设置性能，减少重复的表头配置和信号连接
"""

import hashlib
from typing import Dict, Any, Optional, List
from src.utils.log_config import setup_logger


class HeaderConfigCache:
    """表头配置缓存
    
    缓存表头配置信息，减少重复的表头设置操作
    """
    
    def __init__(self):
        """初始化表头配置缓存"""
        self.logger = setup_logger(__name__ + ".HeaderConfigCache")
        
        # 表头配置缓存
        self.header_cache: Dict[str, Dict[str, Any]] = {}
        
        # 排序指示器缓存
        self.sort_indicator_cache: Dict[str, Dict[int, str]] = {}
        
        # 列宽缓存
        self.column_width_cache: Dict[str, Dict[int, int]] = {}
        
        self.logger.info("表头配置缓存初始化完成")
    
    def _generate_columns_hash(self, columns: List[str]) -> str:
        """生成列配置的哈希值
        
        Args:
            columns: 列名列表
            
        Returns:
            列配置的哈希值
        """
        columns_str = "|".join(sorted(columns))
        return hashlib.md5(columns_str.encode()).hexdigest()
    
    def get_cached_header_config(self, columns: List[str]) -> Optional[Dict[str, Any]]:
        """获取缓存的表头配置
        
        Args:
            columns: 列名列表
            
        Returns:
            缓存的表头配置或None
        """
        columns_hash = self._generate_columns_hash(columns)
        
        if columns_hash in self.header_cache:
            self.logger.debug(f"表头配置缓存命中: {len(columns)}列")
            return self.header_cache[columns_hash].copy()
        else:
            self.logger.debug(f"表头配置缓存未命中: {len(columns)}列")
            return None
    
    def cache_header_config(self, columns: List[str], config: Dict[str, Any]) -> None:
        """缓存表头配置
        
        Args:
            columns: 列名列表
            config: 表头配置字典
        """
        columns_hash = self._generate_columns_hash(columns)
        self.header_cache[columns_hash] = config.copy()
        self.logger.debug(f"表头配置已缓存: {len(columns)}列")
    
    def get_cached_sort_indicators(self, table_name: str) -> Optional[Dict[int, str]]:
        """获取缓存的排序指示器状态
        
        Args:
            table_name: 表名
            
        Returns:
            排序指示器状态字典或None
        """
        if table_name in self.sort_indicator_cache:
            self.logger.debug(f"排序指示器缓存命中: {table_name}")
            return self.sort_indicator_cache[table_name].copy()
        else:
            self.logger.debug(f"排序指示器缓存未命中: {table_name}")
            return None
    
    def cache_sort_indicators(self, table_name: str, indicators: Dict[int, str]) -> None:
        """缓存排序指示器状态
        
        Args:
            table_name: 表名
            indicators: 排序指示器状态字典 {列索引: 排序状态}
        """
        self.sort_indicator_cache[table_name] = indicators.copy()
        self.logger.debug(f"排序指示器已缓存: {table_name}")
    
    def update_sort_indicator(self, table_name: str, column_index: int, sort_state: str) -> None:
        """更新单个排序指示器
        
        Args:
            table_name: 表名
            column_index: 列索引
            sort_state: 排序状态 ('none', 'ascending', 'descending')
        """
        if table_name not in self.sort_indicator_cache:
            self.sort_indicator_cache[table_name] = {}
        
        self.sort_indicator_cache[table_name][column_index] = sort_state
        self.logger.debug(f"排序指示器已更新: {table_name} 列{column_index} -> {sort_state}")
    
    def get_cached_column_widths(self, table_name: str) -> Optional[Dict[int, int]]:
        """获取缓存的列宽配置
        
        Args:
            table_name: 表名
            
        Returns:
            列宽配置字典或None
        """
        if table_name in self.column_width_cache:
            self.logger.debug(f"列宽缓存命中: {table_name}")
            return self.column_width_cache[table_name].copy()
        else:
            self.logger.debug(f"列宽缓存未命中: {table_name}")
            return None
    
    def cache_column_widths(self, table_name: str, widths: Dict[int, int]) -> None:
        """缓存列宽配置
        
        Args:
            table_name: 表名
            widths: 列宽配置字典 {列索引: 宽度}
        """
        self.column_width_cache[table_name] = widths.copy()
        self.logger.debug(f"列宽已缓存: {table_name}")
    
    def invalidate_table_cache(self, table_name: str) -> None:
        """使指定表的所有缓存失效
        
        Args:
            table_name: 表名
        """
        removed_count = 0
        
        # 移除排序指示器缓存
        if table_name in self.sort_indicator_cache:
            del self.sort_indicator_cache[table_name]
            removed_count += 1
        
        # 移除列宽缓存
        if table_name in self.column_width_cache:
            del self.column_width_cache[table_name]
            removed_count += 1
        
        if removed_count > 0:
            self.logger.info(f"表 {table_name} 的表头缓存已失效，移除 {removed_count} 个条目")
    
    def clear_all_cache(self) -> None:
        """清空所有缓存"""
        header_count = len(self.header_cache)
        sort_count = len(self.sort_indicator_cache)
        width_count = len(self.column_width_cache)
        
        self.header_cache.clear()
        self.sort_indicator_cache.clear()
        self.column_width_cache.clear()
        
        total_count = header_count + sort_count + width_count
        self.logger.info(f"已清空所有表头缓存，共移除 {total_count} 个条目")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            包含缓存统计信息的字典
        """
        return {
            'header_configs': len(self.header_cache),
            'sort_indicators': len(self.sort_indicator_cache),
            'column_widths': len(self.column_width_cache),
            'total_entries': len(self.header_cache) + len(self.sort_indicator_cache) + len(self.column_width_cache)
        }
    
    def log_cache_stats(self) -> None:
        """记录缓存统计信息到日志"""
        stats = self.get_cache_stats()
        self.logger.info(f"🔧 [表头缓存] 缓存统计: "
                        f"表头配置={stats['header_configs']}, "
                        f"排序指示器={stats['sort_indicators']}, "
                        f"列宽={stats['column_widths']}, "
                        f"总计={stats['total_entries']}")


class FieldMappingCache:
    """字段映射缓存
    
    缓存字段映射结果，减少重复的映射计算
    """
    
    def __init__(self):
        """初始化字段映射缓存"""
        self.logger = setup_logger(__name__ + ".FieldMappingCache")
        
        # 字段映射缓存 {表名: 映射结果}
        self.mapping_cache: Dict[str, Dict[str, Any]] = {}
        
        self.logger.info("字段映射缓存初始化完成")
    
    def get_cached_mapping(self, table_name: str) -> Optional[Dict[str, Any]]:
        """获取缓存的字段映射
        
        Args:
            table_name: 表名
            
        Returns:
            缓存的字段映射或None
        """
        if table_name in self.mapping_cache:
            self.logger.debug(f"字段映射缓存命中: {table_name}")
            return self.mapping_cache[table_name].copy()
        else:
            self.logger.debug(f"字段映射缓存未命中: {table_name}")
            return None
    
    def cache_mapping(self, table_name: str, mapping: Dict[str, Any]) -> None:
        """缓存字段映射
        
        Args:
            table_name: 表名
            mapping: 字段映射字典
        """
        self.mapping_cache[table_name] = mapping.copy()
        self.logger.debug(f"字段映射已缓存: {table_name}")
    
    def invalidate_mapping(self, table_name: str) -> bool:
        """使指定表的字段映射缓存失效
        
        Args:
            table_name: 表名
            
        Returns:
            是否成功移除缓存
        """
        if table_name in self.mapping_cache:
            del self.mapping_cache[table_name]
            self.logger.debug(f"字段映射缓存已失效: {table_name}")
            return True
        return False
    
    def clear_cache(self) -> None:
        """清空所有字段映射缓存"""
        cache_size = len(self.mapping_cache)
        self.mapping_cache.clear()
        self.logger.info(f"已清空所有字段映射缓存，共移除 {cache_size} 个条目")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            包含缓存统计信息的字典
        """
        return {
            'mapping_entries': len(self.mapping_cache)
        }
    
    def log_cache_stats(self) -> None:
        """记录缓存统计信息到日志"""
        stats = self.get_cache_stats()
        self.logger.info(f"🔧 [字段映射缓存] 缓存统计: 映射条目={stats['mapping_entries']}")
