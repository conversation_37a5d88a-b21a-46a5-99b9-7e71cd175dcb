#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置缓存管理器

提供配置数据的缓存机制，提升配置加载性能
"""

import json
import hashlib
import time
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from loguru import logger


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str                                    # 缓存键
    data: Any                                   # 缓存数据
    created_at: datetime                        # 创建时间
    last_accessed: datetime                     # 最后访问时间
    access_count: int = 0                       # 访问次数
    file_hash: str = ""                         # 文件哈希值
    file_mtime: float = 0.0                     # 文件修改时间
    ttl: Optional[timedelta] = None             # 生存时间
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据


class ConfigCacheManager:
    """配置缓存管理器"""
    
    def __init__(self, cache_dir: Optional[Path] = None, max_size: int = 100, default_ttl: int = 3600):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录
            max_size: 最大缓存条目数
            default_ttl: 默认生存时间（秒）
        """
        self.cache_dir = cache_dir or Path("cache/config_cache")
        self.max_size = max_size
        self.default_ttl = timedelta(seconds=default_ttl)
        
        # 内存缓存
        self.memory_cache: Dict[str, CacheEntry] = {}
        
        # 统计信息
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "disk_reads": 0,
            "disk_writes": 0
        }
        
        # 确保缓存目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载持久化缓存索引
        self._load_cache_index()
        
        logger.info(f"配置缓存管理器初始化完成: {self.cache_dir}, 最大条目数: {max_size}")
    
    def get(self, key: str, file_path: Optional[Path] = None) -> Optional[Any]:
        """
        获取缓存数据
        
        Args:
            key: 缓存键
            file_path: 关联的文件路径（用于验证缓存有效性）
            
        Returns:
            缓存的数据，如果不存在或已过期返回None
        """
        # 检查内存缓存
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            
            # 检查是否过期
            if self._is_expired(entry):
                self._remove_from_memory(key)
                self.stats["misses"] += 1
                return None
            
            # 检查文件是否已修改
            if file_path and not self._is_file_valid(entry, file_path):
                self._remove_from_memory(key)
                self._remove_from_disk(key)
                self.stats["misses"] += 1
                return None
            
            # 更新访问信息
            entry.last_accessed = datetime.now()
            entry.access_count += 1
            
            self.stats["hits"] += 1
            logger.debug(f"缓存命中: {key}")
            return entry.data
        
        # 尝试从磁盘加载
        disk_entry = self._load_from_disk(key)
        if disk_entry:
            # 检查是否过期
            if self._is_expired(disk_entry):
                self._remove_from_disk(key)
                self.stats["misses"] += 1
                return None
            
            # 检查文件是否已修改
            if file_path and not self._is_file_valid(disk_entry, file_path):
                self._remove_from_disk(key)
                self.stats["misses"] += 1
                return None
            
            # 加载到内存缓存
            disk_entry.last_accessed = datetime.now()
            disk_entry.access_count += 1
            self._add_to_memory(key, disk_entry)
            
            self.stats["hits"] += 1
            logger.debug(f"磁盘缓存命中: {key}")
            return disk_entry.data
        
        self.stats["misses"] += 1
        return None
    
    def put(self, key: str, data: Any, file_path: Optional[Path] = None, 
            ttl: Optional[timedelta] = None, metadata: Optional[Dict[str, Any]] = None):
        """
        存储数据到缓存
        
        Args:
            key: 缓存键
            data: 要缓存的数据
            file_path: 关联的文件路径
            ttl: 生存时间
            metadata: 元数据
        """
        now = datetime.now()
        
        # 计算文件哈希和修改时间
        file_hash = ""
        file_mtime = 0.0
        if file_path and file_path.exists():
            file_hash = self._calculate_file_hash(file_path)
            file_mtime = file_path.stat().st_mtime
        
        # 创建缓存条目
        entry = CacheEntry(
            key=key,
            data=data,
            created_at=now,
            last_accessed=now,
            access_count=1,
            file_hash=file_hash,
            file_mtime=file_mtime,
            ttl=ttl or self.default_ttl,
            metadata=metadata or {}
        )
        
        # 添加到内存缓存
        self._add_to_memory(key, entry)
        
        # 保存到磁盘
        self._save_to_disk(key, entry)
        
        logger.debug(f"缓存已存储: {key}")
    
    def remove(self, key: str):
        """移除缓存条目"""
        self._remove_from_memory(key)
        self._remove_from_disk(key)
        logger.debug(f"缓存已移除: {key}")
    
    def clear(self):
        """清空所有缓存"""
        self.memory_cache.clear()
        
        # 清空磁盘缓存
        for cache_file in self.cache_dir.glob("*.cache"):
            cache_file.unlink()
        
        # 重置统计信息
        self.stats = {key: 0 for key in self.stats}
        
        logger.info("所有缓存已清空")
    
    def cleanup(self):
        """清理过期和无效的缓存"""
        removed_count = 0
        
        # 清理内存缓存
        expired_keys = []
        for key, entry in self.memory_cache.items():
            if self._is_expired(entry):
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_from_memory(key)
            self._remove_from_disk(key)
            removed_count += 1
        
        # 清理磁盘缓存
        for cache_file in self.cache_dir.glob("*.cache"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                created_at = datetime.fromisoformat(cache_data['created_at'])
                ttl_seconds = cache_data.get('ttl_seconds', self.default_ttl.total_seconds())
                
                if datetime.now() - created_at > timedelta(seconds=ttl_seconds):
                    cache_file.unlink()
                    removed_count += 1
                    
            except Exception as e:
                logger.warning(f"清理缓存文件失败: {cache_file} - {e}")
                cache_file.unlink()
                removed_count += 1
        
        logger.info(f"缓存清理完成，移除了 {removed_count} 个过期条目")
        return removed_count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.stats,
            "memory_entries": len(self.memory_cache),
            "disk_entries": len(list(self.cache_dir.glob("*.cache"))),
            "hit_rate": f"{hit_rate:.2f}%",
            "cache_dir": str(self.cache_dir)
        }
    
    def _add_to_memory(self, key: str, entry: CacheEntry):
        """添加到内存缓存"""
        # 检查是否需要清理空间
        if len(self.memory_cache) >= self.max_size:
            self._evict_lru()
        
        self.memory_cache[key] = entry
    
    def _remove_from_memory(self, key: str):
        """从内存缓存移除"""
        if key in self.memory_cache:
            del self.memory_cache[key]
    
    def _evict_lru(self):
        """清理最少使用的缓存条目"""
        if not self.memory_cache:
            return
        
        # 找到最少使用的条目
        lru_key = min(
            self.memory_cache.keys(),
            key=lambda k: (self.memory_cache[k].access_count, self.memory_cache[k].last_accessed)
        )
        
        self._remove_from_memory(lru_key)
        self.stats["evictions"] += 1
        logger.debug(f"LRU清理: {lru_key}")
    
    def _save_to_disk(self, key: str, entry: CacheEntry):
        """保存到磁盘"""
        try:
            cache_file = self.cache_dir / f"{self._safe_filename(key)}.cache"
            
            cache_data = {
                "key": entry.key,
                "data": entry.data,
                "created_at": entry.created_at.isoformat(),
                "last_accessed": entry.last_accessed.isoformat(),
                "access_count": entry.access_count,
                "file_hash": entry.file_hash,
                "file_mtime": entry.file_mtime,
                "ttl_seconds": entry.ttl.total_seconds() if entry.ttl else None,
                "metadata": entry.metadata
            }
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            self.stats["disk_writes"] += 1
            
        except Exception as e:
            logger.error(f"保存缓存到磁盘失败: {key} - {e}")
    
    def _load_from_disk(self, key: str) -> Optional[CacheEntry]:
        """从磁盘加载"""
        try:
            cache_file = self.cache_dir / f"{self._safe_filename(key)}.cache"
            
            if not cache_file.exists():
                return None
            
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            ttl = None
            if cache_data.get('ttl_seconds'):
                ttl = timedelta(seconds=cache_data['ttl_seconds'])
            
            entry = CacheEntry(
                key=cache_data['key'],
                data=cache_data['data'],
                created_at=datetime.fromisoformat(cache_data['created_at']),
                last_accessed=datetime.fromisoformat(cache_data['last_accessed']),
                access_count=cache_data['access_count'],
                file_hash=cache_data.get('file_hash', ''),
                file_mtime=cache_data.get('file_mtime', 0.0),
                ttl=ttl,
                metadata=cache_data.get('metadata', {})
            )
            
            self.stats["disk_reads"] += 1
            return entry
            
        except Exception as e:
            logger.error(f"从磁盘加载缓存失败: {key} - {e}")
            return None
    
    def _remove_from_disk(self, key: str):
        """从磁盘移除"""
        try:
            cache_file = self.cache_dir / f"{self._safe_filename(key)}.cache"
            if cache_file.exists():
                cache_file.unlink()
        except Exception as e:
            logger.error(f"从磁盘移除缓存失败: {key} - {e}")
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """检查缓存条目是否过期"""
        if not entry.ttl:
            return False
        return datetime.now() - entry.created_at > entry.ttl
    
    def _is_file_valid(self, entry: CacheEntry, file_path: Path) -> bool:
        """检查关联文件是否有效"""
        if not file_path.exists():
            return False
        
        current_mtime = file_path.stat().st_mtime
        if abs(current_mtime - entry.file_mtime) > 1:  # 允许1秒误差
            return False
        
        if entry.file_hash:
            current_hash = self._calculate_file_hash(file_path)
            return current_hash == entry.file_hash
        
        return True
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def _safe_filename(self, key: str) -> str:
        """生成安全的文件名"""
        # 使用哈希值作为文件名，避免特殊字符问题
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    
    def _load_cache_index(self):
        """加载缓存索引"""
        # 这里可以实现更复杂的索引机制
        pass


# 全局缓存管理器实例
_global_cache_manager = None

def get_cache_manager() -> ConfigCacheManager:
    """获取全局缓存管理器实例"""
    global _global_cache_manager
    if _global_cache_manager is None:
        _global_cache_manager = ConfigCacheManager()
    return _global_cache_manager


# 导出的类和函数
__all__ = [
    "CacheEntry",
    "ConfigCacheManager",
    "get_cache_manager"
]
