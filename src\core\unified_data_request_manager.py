"""
统一数据请求管理器 - 架构重构核心组件

实现统一的数据请求处理管道，解决数据流架构混乱问题。
整合排序、分页、字段映射等功能到统一接口。

主要功能：
1. 统一数据请求接口
2. 处理排序状态
3. 处理字段映射
4. 执行数据库查询
5. 保存状态管理

架构重构目标：
- 解决排序和分页使用不同数据处理路径的问题
- 统一数据验证和错误处理
- 提供可预测的数据响应格式
"""

from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
import logging
from datetime import datetime

from src.utils.log_config import setup_logger
from src.utils.logging_utils import log_throttle, bind_context, log_sample, redact
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config import ConfigManager
from src.core.unified_mapping_service import get_unified_mapping_service


class RequestType(Enum):
    """数据请求类型"""
    INITIAL_LOAD = "initial_load"      # 初始加载
    PAGE_CHANGE = "page_change"        # 分页切换
    SORT_CHANGE = "sort_change"        # 排序变更
    FILTER_CHANGE = "filter_change"    # 过滤变更
    REFRESH = "refresh"                # 刷新数据


@dataclass
class DataRequest:
    """数据请求对象"""
    table_name: str
    request_type: RequestType = RequestType.INITIAL_LOAD
    
    # 分页参数
    page: int = 1
    page_size: int = 50
    
    # 排序参数
    sort_columns: Optional[List[Dict[str, Any]]] = None
    
    # 过滤参数
    filters: Optional[Dict[str, Any]] = None
    
    # 字段选择
    selected_fields: Optional[List[str]] = None
    
    # 状态保持选项
    preserve_page: bool = True
    preserve_sort: bool = True
    preserve_filters: bool = True
    
    # 请求元数据
    request_id: str = field(default_factory=lambda: f"req_{datetime.now().timestamp()}")
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class DataResponse:
    """数据响应对象"""
    success: bool
    data: Optional[pd.DataFrame] = None
    
    # 分页信息
    total_records: int = 0
    current_page: int = 1
    page_size: int = 50
    total_pages: int = 0
    
    # 元数据
    table_name: str = ""
    loaded_fields: int = 0
    processing_time_ms: float = 0
    
    # 错误信息
    error: Optional[str] = None
    error_code: Optional[str] = None
    
    # 状态信息
    sort_applied: bool = False
    filters_applied: bool = False
    field_mapping_applied: bool = False
    
    # 🔧 [P1性能修复] 缓存状态标识，用于优化加载指示器显示
    cache_hit: bool = False
    
    # 响应元数据
    response_id: str = field(default_factory=lambda: f"resp_{datetime.now().timestamp()}")
    timestamp: datetime = field(default_factory=datetime.now)


class FieldMappingService:
    """字段映射服务 - 现在委托给UnifiedMappingService"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.logger = setup_logger("FieldMappingService")
        # 使用统一映射服务
        self.unified_service = get_unified_mapping_service()
    
    def get_field_mapping(self, table_name: str) -> Dict[str, str]:
        """获取表的字段映射 - 委托给统一服务"""
        return self.unified_service.get_field_mapping(table_name)
    
    def _load_field_mapping(self, table_name: str):
        """从配置加载字段映射"""
        try:
            # 从配置文件加载字段映射
            import json
            import os
            
            config_path = "state/data/field_mappings.json"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    mappings_data = json.load(f)
                    
                    # 获取表名对应的映射（支持旧格式和新格式）
                    if "table_mappings" in mappings_data and table_name in mappings_data["table_mappings"]:
                        # 新格式：table_mappings -> table_name -> field_mappings
                        field_mappings = mappings_data["table_mappings"][table_name].get("field_mappings", {})
                        self._field_mappings_cache[table_name] = field_mappings
                        self.logger.debug(f"已加载表 {table_name} 的字段映射: {len(field_mappings)} 个字段")
                    else:
                        # 尝试直接查找（兼容旧格式）
                        if table_name in mappings_data:
                            self._field_mappings_cache[table_name] = mappings_data[table_name]
                            self.logger.debug(f"已加载表 {table_name} 的字段映射（旧格式）: {len(mappings_data[table_name])} 个字段")
                        else:
                            self._field_mappings_cache[table_name] = {}
                            self.logger.debug(f"表 {table_name} 无字段映射配置")
            else:
                self.logger.warning(f"字段映射配置文件不存在: {config_path}")
                self._field_mappings_cache[table_name] = {}
                
        except Exception as e:
            self.logger.error(f"加载表 {table_name} 字段映射失败: {e}")
            self._field_mappings_cache[table_name] = {}
    
    def apply_field_mapping(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """应用字段映射到DataFrame"""
        if df is None or df.empty:
            return df
        
        try:
            field_mapping = self.get_field_mapping(table_name)
            if not field_mapping:
                self.logger.debug(f"表 {table_name} 无字段映射，返回原始数据")
                return df
            
            # 创建安全的字段映射，避免重复列名
            safe_mapping = {}
            existing_columns = set(df.columns)
            
            for old_name, new_name in field_mapping.items():
                # 只有当原列存在且目标列名不存在时才映射
                if old_name in existing_columns:
                    # 如果目标列名已存在（包括已经被映射的），跳过此映射
                    if new_name not in existing_columns and new_name not in safe_mapping.values():
                        safe_mapping[old_name] = new_name
                        # 将新列名加入已存在集合，防止后续重复
                        existing_columns.add(new_name)
                    else:
                        self.logger.debug(f"跳过重复映射: {old_name} -> {new_name}（目标列已存在）")
            
            # 应用安全的字段映射
            if safe_mapping:
                renamed_df = df.rename(columns=safe_mapping)
                self.logger.debug(f"已为表 {table_name} 应用字段映射: {len(safe_mapping)} 个字段")
                
                # 验证没有重复列
                if len(renamed_df.columns) != len(set(renamed_df.columns)):
                    duplicates = [col for col in renamed_df.columns if list(renamed_df.columns).count(col) > 1]
                    self.logger.error(f"字段映射后发现重复列: {set(duplicates)}")
                    
                return renamed_df
            else:
                self.logger.debug(f"表 {table_name} 无有效字段映射")
                return df
            
        except Exception as e:
            self.logger.error(f"应用字段映射失败: {e}")
            return df


class UnifiedDataRequestManager:
    """统一的数据请求处理管道"""
    
    def __init__(self, 
                 db_manager: DynamicTableManager,
                 config_manager: ConfigManager):
        self.db_manager = db_manager
        self.config_manager = config_manager
        base_logger = setup_logger("UnifiedDataRequestManager")
        self.logger = bind_context(base_logger, component="UDRM")
        
        # 初始化服务
        self.field_mapping_service = FieldMappingService(config_manager)
        
        # 请求历史（用于调试和监控）
        self._request_history: List[DataRequest] = []
        self._response_history: List[DataResponse] = []
        
        self.logger.info("统一数据请求管理器初始化完成")
    
    def request_table_data(self, request: DataRequest) -> DataResponse:
        """
        统一的数据请求接口
        
        Args:
            request: 数据请求对象
            
        Returns:
            DataResponse: 数据响应对象
        """
        start_time = datetime.now()
        ctx_logger = bind_context(
            self.logger,
            table=request.table_name,
            request_type=(request.request_type.value if hasattr(request.request_type, 'value') else str(request.request_type)),
            request_id=request.request_id,
        )
        if log_throttle(f"udrm-start:{request.table_name}", 1.5):
            ctx_logger.info("开始处理数据请求")
        
        try:
            # 1. 验证请求参数
            validation_result = self._validate_request(request)
            if not validation_result["valid"]:
                return self._create_error_response(
                    request, "请求参数验证失败", validation_result["error"]
                )
            
            # 2. 处理排序状态
            sort_params = self._process_sort_state(request)
            
            # 3. 处理字段映射
            field_mapping = self._process_field_mapping(request)
            
            # 4. 执行数据库查询
            raw_data, total_records = self._execute_database_query(request, sort_params)
            
            if raw_data is None:
                return self._create_error_response(
                    request, "数据库查询失败", "查询返回空结果"
                )
            
            # 5. 应用字段映射
            mapped_data = self._apply_field_mapping(raw_data, field_mapping, request.table_name)
            
            # 6. 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # 7. 创建成功响应
            response = DataResponse(
                success=True,
                data=mapped_data,
                total_records=total_records,
                current_page=request.page,
                page_size=request.page_size,
                total_pages=max(1, (total_records + request.page_size - 1) // request.page_size),
                table_name=request.table_name,
                loaded_fields=len(mapped_data.columns) if mapped_data is not None and not mapped_data.empty else 0,
                processing_time_ms=processing_time,
                sort_applied=bool(sort_params),
                field_mapping_applied=bool(field_mapping),
                filters_applied=bool(request.filters)
            )
            
            # 8. 保存请求状态（异步处理，不影响响应）
            self._save_request_state(request, sort_params, field_mapping)
            
            if log_throttle(f"udrm-done:{request.table_name}", 1.5):
                ctx_logger.info(f"数据请求处理完成: 字段={response.loaded_fields}, 行数={len(mapped_data)}, 耗时={processing_time:.1f}ms")
            
            # 保存到历史记录
            self._add_to_history(request, response)
            
            return response
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            ctx_logger.error(f"数据请求处理失败: {e}", exc_info=True)
            
            error_response = self._create_error_response(
                request, f"处理失败: {str(e)}", "PROCESSING_ERROR"
            )
            error_response.processing_time_ms = processing_time
            
            self._add_to_history(request, error_response)
            return error_response
    
    def _find_similar_table_names(self, target_table_name: str) -> List[str]:
        """
        查找与目标表名相似的表名
        
        🔧 [P0修复] 严格限制表类型匹配，避免工资表和异动表混淆
        
        Args:
            target_table_name: 目标表名
            
        Returns:
            相似表名列表
        """
        try:
            # 🔧 [P0修复] 首先判断目标表的类型
            target_table_type = None
            if 'salary_data' in target_table_name:
                target_table_type = 'salary_data'
            elif 'change_data' in target_table_name:
                target_table_type = 'change_data'
            
            # 获取所有表名
            all_tables = self.db_manager.get_table_list()
            if not all_tables:
                return []
            
            # 提取表名
            table_names = [table.get('name', '') for table in all_tables if table.get('name')]
            
            # 🔧 [P0修复] 如果能识别表类型，只在同类型表中查找
            if target_table_type:
                table_names = [name for name in table_names if target_table_type in name]
                if not table_names:
                    self.logger.warning(f"🔧 [P0修复] 没有找到类型为 {target_table_type} 的表，不进行跨类型匹配")
                    return []  # 不允许跨类型匹配
            
            # 解析目标表名的组成部分
            import re
            target_parts = re.split(r'[_-]', target_table_name.lower())
            
            # 计算相似度并排序
            similar_tables = []
            for table_name in table_names:
                table_parts = re.split(r'[_-]', table_name.lower())
                
                # 计算共同部分的数量
                common_parts = len(set(target_parts) & set(table_parts))
                if common_parts >= 3:  # 至少有3个共同部分才认为是相似的
                    similar_tables.append((table_name, common_parts))
            
            # 按相似度排序，返回表名
            similar_tables.sort(key=lambda x: x[1], reverse=True)
            return [table[0] for table in similar_tables[:3]]  # 返回最相似的3个表
            
        except Exception as e:
            self.logger.error(f"查找相似表名失败: {e}")
            return []
    
    def _validate_request(self, request: DataRequest) -> Dict[str, Any]:
        """🔧 [P2修复] 验证请求参数（改进错误处理和表名映射）"""
        try:
            # 检查表名
            if not request.table_name:
                self.logger.error(f"🔧 [P0修复] 请求验证失败：表名为空")
                return {"valid": False, "error": "表名不能为空"}

            # 🔧 [P3修复] 处理特殊表名映射（优化日志频率）
            original_table_name = request.table_name
            if request.table_name == "default_table":
                # default_table是占位符，需要映射到实际表
                from src.utils.logging_utils import log_throttle
                if log_throttle('default-table-mapping', 10.0):  # 10秒内只显示一次
                    self.logger.info(f"🔧 [P3修复] 检测到占位符表名 'default_table'，尝试映射到实际表")

                # 尝试获取第一个可用的工资数据表
                try:
                    available_tables = self._get_available_salary_tables()
                    if available_tables:
                        request.table_name = available_tables[0]
                        if log_throttle('table-mapping-success', 5.0):  # 5秒内只显示一次
                            self.logger.info(f"🔧 [P3修复] 自动映射 'default_table' -> '{request.table_name}'")
                    else:
                        return {"valid": False, "error": "没有可用的工资数据表"}
                except Exception as e:
                    self.logger.error(f"🔧 [P3修复] 获取可用表列表失败: {e}")
                    return {"valid": False, "error": f"无法获取可用表列表: {str(e)}"}

            # 🔧 [P2修复] 安全的表存在性检查
            try:
                table_exists = self.db_manager.table_exists(request.table_name)
            except Exception as e:
                self.logger.error(f"🔧 [P2修复] 检查表存在性时出错: {e}")
                return {"valid": False, "error": f"检查表存在性失败: {str(e)}"}

            if not table_exists:
                # 🔧 [P2修复] 尝试查找相似的表名
                try:
                    similar_tables = self._find_similar_table_names(request.table_name)
                    if similar_tables:
                        self.logger.warning(f"🔧 [P2修复] 表 {request.table_name} 不存在，但找到相似表: {similar_tables}")
                        # 使用第一个找到的相似表
                        request.table_name = similar_tables[0]
                        self.logger.info(f"🔧 [P2修复] 自动使用相似表名: {request.table_name}")
                    else:
                        return {"valid": False, "error": f"表 {request.table_name} 不存在，且未找到相似表"}
                except Exception as e:
                    self.logger.error(f"🔧 [P2修复] 查找相似表名时出错: {e}")
                    return {"valid": False, "error": f"表 {request.table_name} 不存在"}
            
            # 检查分页参数
            if request.page < 1:
                self.logger.error(f"🔧 [P0修复] 请求验证失败：页码={request.page}，必须大于0")
                return {"valid": False, "error": "页码必须大于0"}
            
            if request.page_size < 1 or request.page_size > 1000:
                self.logger.error(f"🔧 [P0修复] 请求验证失败：每页大小={request.page_size}，必须在1-1000之间")
                return {"valid": False, "error": "每页大小必须在1-1000之间"}
            
            # 检查排序参数 (兼容 column_name 和 column 两种格式)
            if request.sort_columns:
                for i, sort_col in enumerate(request.sort_columns):
                    if not isinstance(sort_col, dict):
                        self.logger.error(f"🔧 [P0修复] 排序验证失败：第{i}个排序列参数不是字典，类型={type(sort_col)}")
                        return {"valid": False, "error": "排序列参数格式错误"}
                    # 兼容两种格式：column_name 或 column
                    if "column_name" not in sort_col and "column" not in sort_col:
                        self.logger.error(f"🔧 [P0修复] 排序验证失败：第{i}个排序列缺少列名，keys={sort_col.keys()}")
                        return {"valid": False, "error": "排序列缺少列名（column_name 或 column）"}
            
            return {"valid": True, "error": None}
            
        except Exception as e:
            self.logger.error(f"🔧 [P2修复] 请求验证失败: {e}")
            return {"valid": False, "error": f"验证过程出错: {str(e)}"}
    
    def _process_sort_state(self, request: DataRequest) -> List[Dict[str, Any]]:
        """处理排序状态"""
        try:
            if not request.sort_columns:
                return []
            
            processed_sort = []
            for sort_col in request.sort_columns:
                # 兼容两种格式：column_name 或 column
                column_name = sort_col.get("column_name") or sort_col.get("column")
                if not column_name:
                    continue
                
                # 🔧 [排序修复] 尝试反向映射界面列名到数据库列名
                db_column_name = self._reverse_map_column_name(request.table_name, column_name)
                
                # 验证列是否存在
                if not self.db_manager._column_exists_in_table(request.table_name, db_column_name):
                    self.logger.warning(f"排序列 {column_name} (映射为 {db_column_name}) 在表 {request.table_name} 中不存在，跳过")
                    continue
                
                # 标准化排序顺序格式
                order = sort_col.get("order", "ascending").lower()
                if order in ["asc", "ascending"]:
                    order = "ascending"
                elif order in ["desc", "descending"]:
                    order = "descending"
                else:
                    order = "ascending"  # 默认升序
                
                processed_sort.append({
                    "column_name": db_column_name,  # 🔧 [排序修复] 使用数据库列名
                    "order": order,
                    "priority": sort_col.get("priority", 0)
                })
            # 采样输出一次标准化后的排序参数
            if log_sample("udrm-sort", 5):
                self.logger.debug(f"标准化排序参数: {processed_sort}")
            return processed_sort
            
        except Exception as e:
            self.logger.error(f"处理排序状态失败: {e}")
            return []
    
    def _reverse_map_column_name(self, table_name: str, display_column_name: str) -> str:
        """
        🔧 [P2修复] 双向映射列名（英文<->中文）

        Args:
            table_name: 表名
            display_column_name: 输入的列名（可能是英文或中文）

        Returns:
            str: 数据库中实际存在的列名
        """
        try:
            # 🔧 [P2修复] 首先检查原始列名是否直接存在
            if self.db_manager._column_exists_in_table(table_name, display_column_name):
                self.logger.debug(f"🔧 [P2修复] 列名直接存在: {display_column_name}")
                return display_column_name

            # 🔧 [P2修复] 双向映射表（英文<->中文）
            bidirectional_mapping = {
                # 英文 -> 中文
                "grade_salary_2025": "2025年薪级工资",
                "position_salary_2025": "2025年岗位工资",
                "employee_id": "工号",
                "employee_name": "姓名",
                "department": "部门名称",
                "employee_type": "人员类别",
                "employee_type_code": "人员类别代码",
                "allowance": "津贴",
                "balance_allowance": "结余津贴",
                "basic_performance_2025": "2025年基础性绩效",
                "health_fee": "卫生费",
                "transport_allowance": "交通补贴",
                "property_allowance": "物业补贴",
                "communication_allowance": "通讯补贴",
                "performance_bonus_2025": "2025年奖励性绩效预发",
                "provident_fund_2025": "2025公积金",
                "housing_allowance": "住房补贴",
                "car_allowance": "车补",
                "supplement": "补发",
                "advance": "借支",
                "total_salary": "应发工资",
                "pension_insurance": "代扣代存养老保险",
                "seniority_salary_2025": "2025年校龄工资",
                # 中文 -> 英文
                "2025年薪级工资": "grade_salary_2025",
                "2025年岗位工资": "position_salary_2025",
                "工号": "employee_id",
                "姓名": "employee_name",
                "部门名称": "department",
                "人员类别": "employee_type",
                "人员类别代码": "employee_type_code",
                "津贴": "allowance",
                "结余津贴": "balance_allowance",
                "2025年基础性绩效": "basic_performance_2025",
                "卫生费": "health_fee",
                "交通补贴": "transport_allowance",
                "物业补贴": "property_allowance",
                "通讯补贴": "communication_allowance",
                "2025年奖励性绩效预发": "performance_bonus_2025",
                "2025公积金": "provident_fund_2025",
                "住房补贴": "housing_allowance",
                "车补": "car_allowance",
                "补发": "supplement",
                "借支": "advance",
                "应发工资": "total_salary",
                "代扣代存养老保险": "pension_insurance",
                "2025年校龄工资": "seniority_salary_2025"
            }

            # 🔧 [P2修复] 尝试映射
            if display_column_name in bidirectional_mapping:
                mapped_name = bidirectional_mapping[display_column_name]
                # 验证映射后的列名是否存在
                if self.db_manager._column_exists_in_table(table_name, mapped_name):
                    self.logger.info(f"🔧 [P2修复] 列名映射成功: {display_column_name} -> {mapped_name}")
                    return mapped_name
                else:
                    self.logger.debug(f"🔧 [P2修复] 映射后的列名不存在: {mapped_name}")

            # 🔧 [P2修复] 如果直接映射失败，尝试模糊匹配
            fuzzy_match = self._fuzzy_match_column_name(table_name, display_column_name)
            if fuzzy_match:
                self.logger.info(f"🔧 [P2修复] 模糊匹配成功: {display_column_name} -> {fuzzy_match}")
                return fuzzy_match

            # 如果没有找到映射，返回原始列名
            self.logger.warning(f"🔧 [P2修复] 未找到列名映射: {display_column_name}，使用原始列名")
            return display_column_name

        except Exception as e:
            self.logger.error(f"🔧 [P2修复] 列名映射失败: {e}")
            return display_column_name

    def _fuzzy_match_column_name(self, table_name: str, column_name: str) -> Optional[str]:
        """
        🔧 [P2修复] 模糊匹配列名

        Args:
            table_name: 表名
            column_name: 要匹配的列名

        Returns:
            匹配的列名，如果找不到则返回None
        """
        try:
            # 获取表中所有列名
            # 🔧 [P0-CRITICAL修复] 使用 get_table_info 而不是不存在的 get_table_columns
            table_info = self.db_manager.get_table_info(table_name)
            all_columns = [col['name'] for col in table_info] if table_info else []
            if not all_columns:
                return None

            # 1. 包含匹配
            for col in all_columns:
                if column_name in col or col in column_name:
                    return col

            # 2. 关键词匹配
            keywords = {
                'grade': ['薪级', '级别'],
                'position': ['岗位', '职位'],
                'salary': ['工资', '薪'],
                'allowance': ['津贴', '补贴'],
                'employee': ['员工', '职工'],
                'department': ['部门'],
                'bonus': ['奖金', '奖励'],
                'deduction': ['扣款', '扣除']
            }

            for eng_key, chn_keys in keywords.items():
                if eng_key in column_name.lower():
                    for chn_key in chn_keys:
                        for col in all_columns:
                            if chn_key in col:
                                return col

                # 反向匹配
                for chn_key in chn_keys:
                    if chn_key in column_name:
                        for col in all_columns:
                            if eng_key in col.lower():
                                return col

            return None

        except Exception as e:
            self.logger.error(f"🔧 [P2修复] 模糊匹配失败: {e}")
            return None

    def _process_field_mapping(self, request: DataRequest) -> Dict[str, str]:
        """处理字段映射"""
        try:
            return self.field_mapping_service.get_field_mapping(request.table_name)
        except Exception as e:
            self.logger.error(f"处理字段映射失败: {e}")
            return {}
    
    def _execute_database_query(self, request: DataRequest, sort_params: List[Dict[str, Any]]) -> Tuple[Optional[pd.DataFrame], int]:
        """执行数据库查询"""
        try:
            # 使用动态表管理器的分页排序查询方法
            df, total_records = self.db_manager.get_dataframe_paginated_with_sort(
                table_name=request.table_name,
                page=request.page,
                page_size=request.page_size,
                sort_columns=sort_params
            )
            
            return df, total_records
            
        except Exception as e:
            self.logger.error(f"数据库查询失败: {e}")
            return None, 0
    
    def _apply_field_mapping(self, df: pd.DataFrame, field_mapping: Dict[str, str], table_name: str) -> pd.DataFrame:
        """应用字段映射"""
        try:
            return self.field_mapping_service.apply_field_mapping(df, table_name)
        except Exception as e:
            self.logger.error(f"应用字段映射失败: {e}")
            return df
    
    def _save_request_state(self, request: DataRequest, sort_params: List[Dict[str, Any]], field_mapping: Dict[str, str]):
        """保存请求状态（异步处理）"""
        try:
            # 这里可以保存到状态管理器或配置文件
            # 暂时只记录日志
            self.logger.debug(f"保存请求状态: {request.table_name}, 排序: {len(sort_params)}, 映射: {len(field_mapping)}")
        except Exception as e:
            self.logger.error(f"保存请求状态失败: {e}")
    
    def _create_error_response(self, request: DataRequest, error_msg: str, error_code: str) -> DataResponse:
        """创建错误响应"""
        return DataResponse(
            success=False,
            table_name=request.table_name,
            current_page=request.page,
            page_size=request.page_size,
            error=error_msg,
            error_code=error_code
        )
    
    def _add_to_history(self, request: DataRequest, response: DataResponse):
        """添加到历史记录"""
        try:
            self._request_history.append(request)
            self._response_history.append(response)

            # 限制历史记录数量
            max_history = 100
            if len(self._request_history) > max_history:
                self._request_history = self._request_history[-max_history:]
                self._response_history = self._response_history[-max_history:]
        except Exception as e:
            self.logger.error(f"添加历史记录失败: {e}")

    def _get_available_salary_tables(self) -> List[str]:
        """🔧 [P2修复] 获取可用的工资数据表列表"""
        try:
            # 尝试从数据库管理器获取表列表
            if hasattr(self.db_manager, 'get_table_list'):
                tables = self.db_manager.get_table_list(table_type='salary_data')
                if tables:
                    table_names = [table.get('name', '') for table in tables if table.get('name')]
                    # 过滤掉空名称和default_table
                    valid_tables = [name for name in table_names if name and name != 'default_table']
                    if valid_tables:
                        self.logger.info(f"🔧 [P2修复] 找到 {len(valid_tables)} 个可用工资数据表")
                        return valid_tables

            # 如果上述方法失败，尝试其他方式获取表列表
            if hasattr(self.db_manager, 'get_all_tables'):
                all_tables = self.db_manager.get_all_tables()
                # 筛选工资数据表
                salary_tables = [table for table in all_tables if 'salary_data' in table and table != 'default_table']
                if salary_tables:
                    self.logger.info(f"🔧 [P2修复] 通过备用方法找到 {len(salary_tables)} 个工资数据表")
                    return salary_tables

            self.logger.warning("🔧 [P2修复] 未找到任何可用的工资数据表")
            return []

        except Exception as e:
            self.logger.error(f"🔧 [P2修复] 获取可用工资数据表失败: {e}")
            return []
                
        except Exception as e:
            self.logger.error(f"添加历史记录失败: {e}")
    
    def get_request_statistics(self) -> Dict[str, Any]:
        """获取请求统计信息"""
        try:
            total_requests = len(self._request_history)
            successful_requests = sum(1 for resp in self._response_history if resp.success)
            
            if total_requests > 0:
                success_rate = successful_requests / total_requests
                avg_processing_time = sum(resp.processing_time_ms for resp in self._response_history) / total_requests
            else:
                success_rate = 0
                avg_processing_time = 0
            
            return {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "success_rate": success_rate,
                "average_processing_time_ms": avg_processing_time,
                "last_request_time": self._request_history[-1].timestamp if self._request_history else None
            }
            
        except Exception as e:
            self.logger.error(f"获取请求统计失败: {e}")
            return {"error": str(e)} 