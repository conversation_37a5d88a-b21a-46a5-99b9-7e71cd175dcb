"""
数据流一致性验证器 - 薪资管理系统核心修复模块

该模块负责确保数据在各个处理环节的一致性，防止排序空白列等问题。

主要功能:
- 数据与表头一致性验证
- 格式配置完整性检查
- 字段映射验证
- 数据流修复机制
- 性能监控和日志记录

技术特性:
- 多层验证策略
- 自动修复机制
- 降级处理
- 详细的错误报告

作者: 薪资管理系统修复团队
创建时间: 2025-08-01
版本: 1.0.0
"""

import time
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

from src.utils.log_config import setup_logger


class ValidationLevel(Enum):
    """验证级别"""
    STRICT = "strict"      # 严格验证，发现问题立即报错
    MODERATE = "moderate"  # 中等验证，尝试修复
    LENIENT = "lenient"    # 宽松验证，记录警告但继续


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    issues: List[str]
    fixes_applied: List[str]
    data: Optional[List[Dict[str, Any]]] = None
    headers: Optional[List[str]] = None
    performance_ms: float = 0.0


@dataclass
class DataFlowMetrics:
    """数据流指标"""
    original_columns: int
    final_columns: int
    data_rows: int
    validation_time_ms: float
    fixes_count: int


class DataFlowValidator:
    """
    数据流一致性验证器
    
    负责验证和修复数据在各个处理环节的一致性问题
    """
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.MODERATE):
        """
        初始化验证器

        Args:
            validation_level: 验证级别
        """
        self.logger = setup_logger("DataFlowValidator")
        self.validation_level = validation_level
        self.metrics_history = []

        # 🔧 [P0-紧急修复] 添加验证跳过机制
        self.skip_validation_contexts = {
            'normal_table_display',  # 正常表格显示
            'pagination_update',     # 分页更新
            'sort_operation'         # 排序操作
        }

        self.logger.info(f"🔧 [数据验证器] 初始化完成，验证级别: {validation_level.value}")
    
    def validate_data_consistency(
        self, 
        data: List[Dict[str, Any]], 
        headers: List[str], 
        table_type: str = None,
        context: str = "unknown"
    ) -> ValidationResult:
        """
        验证数据一致性
        
        Args:
            data: 数据列表
            headers: 表头列表
            table_type: 表类型
            context: 验证上下文（用于日志）
            
        Returns:
            验证结果
        """
        start_time = time.time()
        issues = []
        fixes_applied = []
        
        try:
            self.logger.debug(f"🔧 [数据验证] 开始验证 - 上下文: {context}")

            # 🔧 [P0-紧急修复] 检查是否需要跳过验证
            if self._should_skip_validation(context, data, headers):
                self.logger.debug(f"🔧 [P0-紧急修复] 跳过验证 - 上下文: {context}")
                return ValidationResult(
                    is_valid=True,
                    issues=[],
                    fixes_applied=[],
                    data=data,
                    headers=headers,
                    performance_ms=0.0
                )

            # 1. 基础数据验证
            basic_issues = self._validate_basic_data(data, headers)
            issues.extend(basic_issues)
            
            # 2. 列数一致性验证
            column_issues, fixed_data, fixed_headers = self._validate_column_consistency(data, headers)
            issues.extend(column_issues)
            
            if fixed_data is not None:
                data = fixed_data
                fixes_applied.append("列数一致性修复")
            
            if fixed_headers is not None:
                headers = fixed_headers
                fixes_applied.append("表头一致性修复")
            
            # 3. 字段类型验证
            type_issues = self._validate_field_types(data, headers)
            issues.extend(type_issues)
            
            # 4. 表类型特定验证
            if table_type:
                type_specific_issues = self._validate_table_type_specific(data, headers, table_type)
                issues.extend(type_specific_issues)
            
            # 5. 记录性能指标
            validation_time = (time.time() - start_time) * 1000
            metrics = DataFlowMetrics(
                original_columns=len(headers),
                final_columns=len(headers),
                data_rows=len(data) if data else 0,
                validation_time_ms=validation_time,
                fixes_count=len(fixes_applied)
            )
            self.metrics_history.append(metrics)
            
            # 6. 生成验证结果
            is_valid = len(issues) == 0 or self.validation_level == ValidationLevel.LENIENT
            
            if issues:
                self.logger.warning(f"🔧 [数据验证] 发现{len(issues)}个问题: {issues}")
            if fixes_applied:
                self.logger.info(f"🔧 [数据验证] 应用{len(fixes_applied)}个修复: {fixes_applied}")
            
            self.logger.debug(f"🔧 [数据验证] 验证完成 - 耗时: {validation_time:.2f}ms")
            
            return ValidationResult(
                is_valid=is_valid,
                issues=issues,
                fixes_applied=fixes_applied,
                data=data,
                headers=headers,
                performance_ms=validation_time
            )
            
        except Exception as e:
            self.logger.error(f"🔧 [数据验证] 验证过程异常: {e}")
            return ValidationResult(
                is_valid=False,
                issues=[f"验证异常: {str(e)}"],
                fixes_applied=[],
                data=data,
                headers=headers,
                performance_ms=(time.time() - start_time) * 1000
            )

    def _should_skip_validation(self, context: str, data: List[Dict[str, Any]], headers: List[str]) -> bool:
        """
        🔧 [P0-紧急修复] 判断是否应该跳过验证

        Args:
            context: 验证上下文
            data: 数据
            headers: 表头

        Returns:
            是否跳过验证
        """
        try:
            # 1. 检查上下文是否在跳过列表中
            for skip_context in self.skip_validation_contexts:
                if skip_context in context:
                    return True

            # 2. 检查数据规模是否正常（小规模数据通常不需要验证）
            if data and headers:
                data_size = len(data)
                header_size = len(headers)

                # 小规模数据且表头数量正常，跳过验证
                if data_size < 1000 and header_size < 50:
                    return True

                # 表头数量在正常范围内，跳过验证
                if 5 <= header_size <= 100:
                    return True

            # 3. 检查是否是重复验证（短时间内相同上下文）
            # 这里可以添加缓存机制，暂时简化

            return False

        except Exception as e:
            self.logger.error(f"🔧 [P0-紧急修复] 跳过验证判断异常: {e}")
            return False  # 异常时不跳过，保持安全

    def _validate_basic_data(self, data: List[Dict[str, Any]], headers: List[str]) -> List[str]:
        """基础数据验证"""
        issues = []
        
        if not headers:
            issues.append("表头列表为空")
        
        if not data:
            issues.append("数据列表为空")
            return issues
        
        if not isinstance(data, list):
            issues.append("数据不是列表类型")
        
        if not isinstance(headers, list):
            issues.append("表头不是列表类型")
        
        return issues
    
    def _validate_column_consistency(
        self, 
        data: List[Dict[str, Any]], 
        headers: List[str]
    ) -> Tuple[List[str], Optional[List[Dict[str, Any]]], Optional[List[str]]]:
        """验证列数一致性 - P1级增强版"""
        issues = []
        fixed_data = None
        fixed_headers = None
        
        # 🔧 [P0-紧急修复] 调整验证阈值，减少过度干预
        if len(headers) > 200:  # 提高阈值从100到200
            issues.append(f"检测到异常表头数量: {len(headers)}个（超过200）")
            # 🔧 [P0-紧急修复] 改为警告记录，不强制截断
            self.logger.warning(f"🔧 [P0-紧急修复] 表头数量异常: {len(headers)}个，但不进行强制截断")
            # 注释掉强制修复逻辑
            # fixed_headers = headers[:50]  # 保留前50个
            # self.logger.error(f"🔧 [P1优化] 检测到异常表头数量: {len(headers)}，已截断至50个")
            # headers = fixed_headers

        # 🔧 [P0-紧急修复] 调整表头重复检测阈值
        header_counts = {}
        for header in headers:
            header_counts[header] = header_counts.get(header, 0) + 1

        max_duplicates = max(header_counts.values()) if header_counts else 0
        if max_duplicates > 10:  # 提高阈值从5到10
            issues.append(f"严重表头重复（最多{max_duplicates}次）")
            # 🔧 [P0-紧急修复] 改为警告记录，不强制去重
            self.logger.warning(f"🔧 [P0-紧急修复] 检测到严重表头重复（最多{max_duplicates}次），但不进行强制去重")
            # 注释掉强制修复逻辑
            # unique_headers = []
            # seen = set()
            # for h in headers:
            #     if h not in seen:
            #         unique_headers.append(h)
            #         seen.add(h)
            # fixed_headers = unique_headers
            # self.logger.error(f"🔧 [P1优化] 检测到严重表头重复（最多{max_duplicates}次），已去重")
            # headers = fixed_headers
        
        if not data or not headers:
            return issues, fixed_data, fixed_headers
        
        # 检查数据行的列数一致性
        data_columns = len(data[0]) if isinstance(data[0], dict) else 0
        header_columns = len(headers)
        
        if data_columns != header_columns:
            issues.append(f"列数不匹配: 数据{data_columns}列, 表头{header_columns}列")

            # 🔧 [P0-紧急修复] 调整修复策略，减少过度干预
            # 只有在差异很大时才进行修复，小差异只记录警告
            column_diff = abs(data_columns - header_columns)
            if column_diff > 5 and self.validation_level in [ValidationLevel.MODERATE, ValidationLevel.LENIENT]:
                self.logger.info(f"🔧 [P0-紧急修复] 列数差异较大({column_diff})，尝试修复")
                fixed_data, fixed_headers = self._fix_column_mismatch(data, headers)
            else:
                self.logger.warning(f"🔧 [P0-紧急修复] 列数差异较小({column_diff})，仅记录警告不修复")
        
        return issues, fixed_data, fixed_headers
    
    def _fix_column_mismatch(
        self, 
        data: List[Dict[str, Any]], 
        headers: List[str]
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """修复列数不匹配问题"""
        try:
            data_columns = len(data[0]) if data and isinstance(data[0], dict) else 0
            header_columns = len(headers)
            
            if data_columns > header_columns:
                # 数据列多，截断数据或补充表头
                self.logger.info(f"🔧 [列数修复] 数据列过多，从{data_columns}调整到{header_columns}")
                fixed_data = []
                for row in data:
                    if isinstance(row, dict):
                        fixed_row = {key: row.get(key, '') for key in headers if key in row}
                        fixed_data.append(fixed_row)
                    else:
                        fixed_data.append(row)
                return fixed_data, headers
                
            elif header_columns > data_columns:
                # 表头多，截断表头或补充数据
                self.logger.info(f"🔧 [列数修复] 表头过多，从{header_columns}调整到{data_columns}")
                if data_columns > 0 and isinstance(data[0], dict):
                    actual_headers = list(data[0].keys())
                    fixed_headers = actual_headers[:header_columns]
                    return data, fixed_headers
                else:
                    fixed_headers = headers[:data_columns]
                    return data, fixed_headers
            
            return data, headers
            
        except Exception as e:
            self.logger.error(f"🔧 [列数修复] 修复失败: {e}")
            return data, headers
    
    def _validate_field_types(self, data: List[Dict[str, Any]], headers: List[str]) -> List[str]:
        """验证字段类型 - P1级增强版"""
        issues = []
        
        if not data or not headers:
            return issues
        
        # 检查数据类型一致性
        try:
            # 🔧 [P1优化] 增强验证：检查数据行长度一致性
            row_lengths = set()
            for i, row in enumerate(data[:10]):  # 检查前10行
                if not isinstance(row, dict):
                    issues.append(f"第{i+1}行数据不是字典类型")
                    continue
                
                row_lengths.add(len(row))
                
                # 🔧 [P1优化] 检查关键字段是否存在
                key_fields = ['工号', '人员代码', 'employee_id', 'id']
                has_key = any(field in row for field in key_fields)
                if i == 0 and not has_key:
                    issues.append("数据缺少关键标识字段（工号/人员代码/ID）")
                
                # 检查必需字段
                missing_fields = []
                for header in headers[:10]:  # 只检查前10个表头，避免性能问题
                    if header not in row:
                        missing_fields.append(header)
                
                if missing_fields and i < 3:  # 只报告前3行的缺失字段
                    issues.append(f"第{i+1}行缺少字段: {missing_fields[:5]}")  # 最多显示5个
            
            # 🔧 [P1优化] 检查行长度一致性
            if len(row_lengths) > 1:
                issues.append(f"数据行字段数量不一致: {sorted(row_lengths)}")
                self.logger.warning(f"🔧 [P1优化] 检测到数据行长度不一致: {row_lengths}")
        
        except Exception as e:
            issues.append(f"字段类型验证异常: {str(e)}")
        
        return issues
    
    def _validate_table_type_specific(
        self, 
        data: List[Dict[str, Any]], 
        headers: List[str], 
        table_type: str
    ) -> List[str]:
        """表类型特定验证"""
        issues = []
        
        # 定义各表类型的必需字段
        required_fields = {
            'active_employees': ['工号', '姓名', '部门名称'],
            'a_grade_employees': ['工号', '姓名', '部门名称'],
            'retired_employees': ['人员代码', '姓名', '部门名称'],
            'pension_employees': ['人员代码', '姓名', '部门名称']
        }
        
        table_required = required_fields.get(table_type, [])
        
        for field in table_required:
            if field not in headers:
                issues.append(f"表类型{table_type}缺少必需字段: {field}")
        
        return issues
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {"message": "暂无性能数据"}
        
        recent_metrics = self.metrics_history[-10:]  # 最近10次
        
        avg_time = sum(m.validation_time_ms for m in recent_metrics) / len(recent_metrics)
        total_fixes = sum(m.fixes_count for m in recent_metrics)
        
        return {
            "average_validation_time_ms": round(avg_time, 2),
            "total_validations": len(self.metrics_history),
            "recent_fixes_count": total_fixes,
            "validation_level": self.validation_level.value
        }
