{"templates": [{"id": "builtin_standard_salary", "name": "标准工资表", "description": "适用于标准格式的工资表，第1行为表头，第2行开始为数据", "category": "builtin", "tags": ["工资表", "标准格式"], "config_data": {"header_row": 1, "data_start_row": 2, "has_header": true, "auto_detect_header": true, "remove_summary_rows": true, "summary_keywords": ["合计", "小计", "总计", "汇总"], "skip_empty_rows": true, "trim_whitespace": true, "normalize_numbers": true, "handle_merged_cells": true, "is_enabled": true}, "created_time": "2025-08-31T20:32:43.493939", "modified_time": "2025-08-31T20:33:10.080581", "usage_count": 2, "is_favorite": false, "author": "system", "version": "1.0"}, {"id": "builtin_with_title", "name": "带标题的数据表", "description": "第1行为标题，第2行为表头，第3行开始为数据", "category": "builtin", "tags": ["带标题", "数据表"], "config_data": {"header_row": 2, "data_start_row": 3, "has_header": true, "auto_detect_header": true, "remove_summary_rows": true, "summary_keywords": ["合计", "小计", "总计", "汇总"], "skip_empty_rows": true, "trim_whitespace": true, "normalize_numbers": true, "handle_merged_cells": true, "is_enabled": true}, "created_time": "2025-08-31T20:32:43.493939", "modified_time": "2025-08-31T21:45:49.405048", "usage_count": 1, "is_favorite": false, "author": "system", "version": "1.0"}, {"id": "builtin_summary_sheet", "name": "汇总表（不导入）", "description": "汇总表模板，默认不导入数据", "category": "builtin", "tags": ["汇总表", "不导入"], "config_data": {"is_enabled": false, "remove_summary_rows": true, "summary_keywords": ["合计", "小计", "总计", "汇总", "统计"], "notes": "汇总表，不导入数据"}, "created_time": "2025-08-31T20:32:43.493939", "modified_time": "2025-08-31T20:32:43.493939", "usage_count": 0, "is_favorite": false, "author": "system", "version": "1.0"}, {"id": "builtin_documentation", "name": "说明文档（不导入）", "description": "说明文档模板，默认不导入", "category": "builtin", "tags": ["说明文档", "不导入"], "config_data": {"is_enabled": false, "notes": "说明文档，不导入数据"}, "created_time": "2025-08-31T20:32:43.493939", "modified_time": "2025-08-31T20:32:43.493939", "usage_count": 0, "is_favorite": false, "author": "system", "version": "1.0"}, {"id": "a1494e21-a3d2-468a-a17f-38504984b686", "name": "自定义工资表模板 (已更新) (已更新)", "description": "这是一个更新后的模板描述", "category": "user", "tags": ["工资表", "自定义", "第3行表头"], "config_data": {"header_row": 3, "data_start_row": 4, "has_header": true, "remove_summary_rows": true, "summary_keywords": ["总计", "合计"], "skip_empty_rows": true, "is_enabled": true}, "created_time": "2025-08-31T20:32:43.498296", "modified_time": "2025-08-31T20:33:10.145433", "usage_count": 0, "is_favorite": false, "author": "user", "version": "1.0"}, {"id": "7b481af4-c158-4047-888b-c026c7fc1d15", "name": "测试模板", "description": "从测试工资表创建的模板", "category": "user", "tags": ["测试", "工资表"], "config_data": {"header_row": 1, "data_start_row": 2, "skip_empty_rows": true, "has_header": true, "auto_detect_header": true, "remove_summary_rows": true, "summary_keywords": ["合计", "小计", "总计", "汇总"], "trim_whitespace": true, "normalize_numbers": true, "handle_merged_cells": true, "fill_empty_values": false, "field_mappings": {}, "field_types": {}, "required_fields": [], "validation_rules": {}, "is_enabled": true, "notes": ""}, "created_time": "2025-08-31T20:32:43.513829", "modified_time": "2025-08-31T20:32:43.513829", "usage_count": 0, "is_favorite": false, "author": "user", "version": "1.0"}, {"id": "afaafc29-b2ab-49f5-aa98-0f488a9b800c", "name": "标准工资表", "description": "适用于标准格式的工资表，第1行为表头，第2行开始为数据", "category": "imported", "tags": ["工资表", "标准格式"], "config_data": {"header_row": 1, "data_start_row": 2, "has_header": true, "auto_detect_header": true, "remove_summary_rows": true, "summary_keywords": ["合计", "小计", "总计", "汇总"], "skip_empty_rows": true, "trim_whitespace": true, "normalize_numbers": true, "handle_merged_cells": true, "is_enabled": true}, "created_time": "2025-08-31T20:32:43.493939", "modified_time": "2025-08-31T20:32:43.524651", "usage_count": 1, "is_favorite": true, "author": "system", "version": "1.0"}, {"id": "3075032a-e039-4988-ac28-2505b1c9e678", "name": "自定义工资表模板", "description": "第3行为表头，第4行开始为数据的工资表", "category": "user", "tags": ["工资表", "自定义", "第3行表头"], "config_data": {"header_row": 3, "data_start_row": 4, "has_header": true, "remove_summary_rows": true, "summary_keywords": ["总计", "合计"], "skip_empty_rows": true, "is_enabled": true}, "created_time": "2025-08-31T20:33:09.992728", "modified_time": "2025-08-31T20:33:09.992728", "usage_count": 0, "is_favorite": false, "author": "user", "version": "1.0"}, {"id": "6b2ecf23-9983-4e64-a1ed-02edc106adb2", "name": "测试模板", "description": "从测试工资表创建的模板", "category": "user", "tags": ["测试", "工资表"], "config_data": {"header_row": 1, "data_start_row": 2, "skip_empty_rows": true, "has_header": true, "auto_detect_header": true, "remove_summary_rows": true, "summary_keywords": ["合计", "小计", "总计", "汇总"], "trim_whitespace": true, "normalize_numbers": true, "handle_merged_cells": true, "fill_empty_values": false, "field_mappings": {}, "field_types": {}, "required_fields": [], "validation_rules": {}, "is_enabled": true, "notes": ""}, "created_time": "2025-08-31T20:33:10.073761", "modified_time": "2025-08-31T20:33:10.073761", "usage_count": 0, "is_favorite": false, "author": "user", "version": "1.0"}, {"id": "8c385b5e-b62e-4bbd-8eb3-352261624eb5", "name": "标准工资表", "description": "适用于标准格式的工资表，第1行为表头，第2行开始为数据", "category": "imported", "tags": ["工资表", "标准格式"], "config_data": {"header_row": 1, "data_start_row": 2, "has_header": true, "auto_detect_header": true, "remove_summary_rows": true, "summary_keywords": ["合计", "小计", "总计", "汇总"], "skip_empty_rows": true, "trim_whitespace": true, "normalize_numbers": true, "handle_merged_cells": true, "is_enabled": true}, "created_time": "2025-08-31T20:32:43.493939", "modified_time": "2025-08-31T20:33:10.080581", "usage_count": 2, "is_favorite": false, "author": "system", "version": "1.0"}], "saved_time": "2025-09-01T14:17:02.504752"}