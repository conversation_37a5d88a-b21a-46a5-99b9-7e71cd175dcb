#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI组件包

包含各种自定义的GUI组件和控件。
"""

# 只导入当前包中的组件，避免循环导入
from .target_selection_widget import TargetSelectionWidget, NewCategoryDialog

# 从widgets.py导入所有需要的组件
try:
    from ..widgets import DataTableWidget, ChangeDetectionWidget, ReportPreviewWidget
except ImportError:
    # 如果导入失败，定义占位符类
    from PyQt5.QtWidgets import QTableWidget, QWidget
    
    class DataTableWidget(QTableWidget):
        pass
    
    class ChangeDetectionWidget(QWidget):
        pass
    
    class ReportPreviewWidget(QWidget):
        pass

__all__ = [
    # 新组件
    'TargetSelectionWidget',
    'NewCategoryDialog',
    # 数据表格组件
    'DataTableWidget',
    # 异动检测组件
    'ChangeDetectionWidget',
    # 报告预览组件
    'ReportPreviewWidget'
] 