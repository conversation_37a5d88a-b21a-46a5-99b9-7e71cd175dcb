"""
运行时优化器 - P2级优化
提供运行时性能优化功能
"""

import asyncio
import functools
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Any, Optional, List
from queue import Queue, Empty
from loguru import logger

from src.core.performance_profiler import profile_function


class BatchProcessor:
    """
    批处理器
    
    将多个小操作合并为批量操作
    """
    
    def __init__(self, batch_size: int = 100, timeout: float = 0.1):
        """
        初始化批处理器
        
        Args:
            batch_size: 批大小
            timeout: 超时时间（秒）
        """
        self.batch_size = batch_size
        self.timeout = timeout
        self.queue = Queue()
        self.logger = logger
        
        # 启动处理线程
        self._running = True
        self._thread = threading.Thread(target=self._process_loop, daemon=True)
        self._thread.start()
    
    def add(self, item: Any, callback: Callable):
        """
        添加项目到批处理队列
        
        Args:
            item: 要处理的项目
            callback: 处理回调
        """
        self.queue.put((item, callback))
    
    def _process_loop(self):
        """处理循环"""
        batch = []
        callbacks = []
        
        while self._running:
            try:
                # 获取项目
                item, callback = self.queue.get(timeout=self.timeout)
                batch.append(item)
                callbacks.append(callback)
                
                # 检查是否达到批大小
                if len(batch) >= self.batch_size:
                    self._process_batch(batch, callbacks)
                    batch = []
                    callbacks = []
                    
            except Empty:
                # 超时，处理当前批次
                if batch:
                    self._process_batch(batch, callbacks)
                    batch = []
                    callbacks = []
    
    def _process_batch(self, batch: List[Any], callbacks: List[Callable]):
        """
        处理批次
        
        Args:
            batch: 批次项目
            callbacks: 回调列表
        """
        try:
            self.logger.debug(f"处理批次: {len(batch)}项")
            
            # 批量处理
            results = self._batch_operation(batch)
            
            # 执行回调
            for callback, result in zip(callbacks, results):
                if callback:
                    callback(result)
                    
        except Exception as e:
            self.logger.error(f"批处理失败: {e}")
    
    def _batch_operation(self, batch: List[Any]) -> List[Any]:
        """
        批量操作（由子类实现）
        
        Args:
            batch: 批次数据
        
        Returns:
            结果列表
        """
        return batch
    
    def stop(self):
        """停止处理器"""
        self._running = False
        if self._thread.is_alive():
            self._thread.join(timeout=1)


class DataBatchProcessor(BatchProcessor):
    """数据批处理器"""
    
    def __init__(self, operation: Callable, batch_size: int = 100):
        """
        初始化数据批处理器
        
        Args:
            operation: 批处理操作函数
            batch_size: 批大小
        """
        super().__init__(batch_size)
        self.operation = operation
    
    def _batch_operation(self, batch: List[Any]) -> List[Any]:
        """执行批量数据操作"""
        return self.operation(batch)


class ThreadPoolManager:
    """
    线程池管理器
    
    优化线程使用
    """
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.logger = logger
        
        # 创建线程池
        import os
        cpu_count = os.cpu_count() or 4
        self._executor = ThreadPoolExecutor(max_workers=min(cpu_count * 2, 8))
        
        self.logger.info(f"线程池初始化: {self._executor._max_workers}个工作线程")
    
    @profile_function
    def submit(self, fn: Callable, *args, **kwargs):
        """
        提交任务到线程池
        
        Args:
            fn: 函数
            *args: 位置参数
            **kwargs: 关键字参数
        
        Returns:
            Future对象
        """
        return self._executor.submit(fn, *args, **kwargs)
    
    def map(self, fn: Callable, *iterables, timeout=None):
        """
        并行映射操作
        
        Args:
            fn: 函数
            *iterables: 可迭代对象
            timeout: 超时时间
        
        Returns:
            结果迭代器
        """
        return self._executor.map(fn, *iterables, timeout=timeout)
    
    def shutdown(self, wait=True):
        """关闭线程池"""
        self._executor.shutdown(wait=wait)


class MemoryOptimizer:
    """
    内存优化器
    
    管理内存使用
    """
    
    def __init__(self):
        self.logger = logger
        self._object_pool = {}
        self._weak_refs = {}
    
    def create_object_pool(self, class_type: type, size: int = 10):
        """
        创建对象池
        
        Args:
            class_type: 类类型
            size: 池大小
        """
        pool_name = class_type.__name__
        if pool_name not in self._object_pool:
            self._object_pool[pool_name] = {
                'class': class_type,
                'available': [],
                'in_use': set(),
                'max_size': size
            }
            
            # 预创建对象
            for _ in range(size):
                obj = class_type()
                self._object_pool[pool_name]['available'].append(obj)
            
            self.logger.debug(f"创建对象池: {pool_name}, 大小={size}")
    
    def acquire_object(self, class_type: type) -> Any:
        """
        从对象池获取对象
        
        Args:
            class_type: 类类型
        
        Returns:
            对象实例
        """
        pool_name = class_type.__name__
        
        if pool_name not in self._object_pool:
            # 没有池，直接创建
            return class_type()
        
        pool = self._object_pool[pool_name]
        
        if pool['available']:
            # 从池中获取
            obj = pool['available'].pop()
            pool['in_use'].add(obj)
            return obj
        elif len(pool['in_use']) < pool['max_size']:
            # 创建新对象
            obj = pool['class']()
            pool['in_use'].add(obj)
            return obj
        else:
            # 池已满，直接创建
            self.logger.warning(f"对象池已满: {pool_name}")
            return class_type()
    
    def release_object(self, obj: Any):
        """
        释放对象回池
        
        Args:
            obj: 对象实例
        """
        pool_name = obj.__class__.__name__
        
        if pool_name in self._object_pool:
            pool = self._object_pool[pool_name]
            
            if obj in pool['in_use']:
                pool['in_use'].remove(obj)
                
                # 重置对象状态（如果有reset方法）
                if hasattr(obj, 'reset'):
                    obj.reset()
                
                pool['available'].append(obj)
    
    def optimize_memory(self):
        """执行内存优化"""
        import gc
        
        # 强制垃圾回收
        collected = gc.collect()
        
        # 清理对象池中未使用的对象
        for pool_name, pool in self._object_pool.items():
            while len(pool['available']) > pool['max_size'] // 2:
                pool['available'].pop()
        
        self.logger.debug(f"内存优化: 回收{collected}个对象")
        
        return collected


class QueryOptimizer:
    """
    查询优化器
    
    优化数据查询性能
    """
    
    def __init__(self):
        self.logger = logger
        self._query_cache = {}
        self._index_cache = {}
    
    def create_index(self, data: Any, column: str):
        """
        创建索引
        
        Args:
            data: 数据（如DataFrame）
            column: 索引列
        """
        import pandas as pd
        
        if isinstance(data, pd.DataFrame):
            # 创建索引
            index = data.set_index(column)
            
            # 缓存索引
            cache_key = f"{id(data)}_{column}"
            self._index_cache[cache_key] = index
            
            self.logger.debug(f"创建索引: {column}")
            return index
        
        return data
    
    @profile_function
    def optimized_query(self, data: Any, condition: Callable) -> Any:
        """
        优化的查询
        
        Args:
            data: 数据
            condition: 查询条件
        
        Returns:
            查询结果
        """
        import pandas as pd
        
        if isinstance(data, pd.DataFrame):
            # 使用向量化操作
            if len(data) > 1000:
                # 大数据集，使用query方法
                try:
                    return data.query(condition)
                except:
                    pass
            
            # 标准过滤
            return data[condition(data)]
        
        # 其他数据类型
        return [item for item in data if condition(item)]


def debounce(wait: float):
    """
    防抖装饰器
    
    Args:
        wait: 等待时间（秒）
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        func._timer = None
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            def call_func():
                func._timer = None
                return func(*args, **kwargs)
            
            # 取消之前的调用
            if func._timer is not None:
                func._timer.cancel()
            
            # 设置新的延迟调用
            func._timer = threading.Timer(wait, call_func)
            func._timer.start()
        
        return wrapper
    
    return decorator


def throttle(wait: float):
    """
    节流装饰器
    
    Args:
        wait: 最小间隔时间（秒）
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        func._last_call = 0
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import time
            now = time.time()
            
            if now - func._last_call >= wait:
                func._last_call = now
                return func(*args, **kwargs)
        
        return wrapper
    
    return decorator


# 全局实例
_thread_pool_manager = None
_memory_optimizer = None
_query_optimizer = None

def get_thread_pool_manager() -> ThreadPoolManager:
    """获取线程池管理器"""
    global _thread_pool_manager
    if _thread_pool_manager is None:
        _thread_pool_manager = ThreadPoolManager()
    return _thread_pool_manager

def get_memory_optimizer() -> MemoryOptimizer:
    """获取内存优化器"""
    global _memory_optimizer
    if _memory_optimizer is None:
        _memory_optimizer = MemoryOptimizer()
    return _memory_optimizer

def get_query_optimizer() -> QueryOptimizer:
    """获取查询优化器"""
    global _query_optimizer
    if _query_optimizer is None:
        _query_optimizer = QueryOptimizer()
    return _query_optimizer