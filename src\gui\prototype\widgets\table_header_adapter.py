"""
方案B：表格组件适配器
将现有表格组件与UnifiedHeaderManager集成

功能：
1. 拦截所有setColumnCount调用
2. 通过UnifiedHeaderManager管理表头
3. 监听状态变化并响应
4. 提供向后兼容接口

作者：PyQt5架构优化组
创建时间：2025-08-18
"""

from typing import List, Optional, Any
from PyQt5.QtWidgets import QTableWidget
from PyQt5.QtCore import QObject

from src.gui.prototype.widgets.unified_header_manager import (
    get_header_manager,
    UnifiedHeaderManager,
    TableState
)
from src.utils.log_config import setup_logger


class TableHeaderAdapter:
    """
    表格表头适配器
    将表格组件的表头操作委托给UnifiedHeaderManager
    """
    
    def __init__(self, table_widget: QTableWidget):
        """
        初始化适配器
        
        Args:
            table_widget: 要适配的表格组件
        """
        self.logger = setup_logger(__name__)
        self.table = table_widget
        self.header_manager = get_header_manager()
        
        # 保存原始方法
        self._original_setColumnCount = self.table.setColumnCount
        self._original_setHorizontalHeaderLabels = self.table.setHorizontalHeaderLabels
        
        # 替换方法
        self._patch_methods()
        
        # 连接信号
        self._connect_signals()
        
        # 当前状态
        self.current_table_name = ""
        self.is_pagination_mode = False
        self.current_page = 1
        
        self.logger.info("方案B：TableHeaderAdapter初始化完成")
    
    def _patch_methods(self):
        """替换表格组件的方法"""
        # 替换setColumnCount
        self.table.setColumnCount = self._managed_setColumnCount
        
        # 替换setHorizontalHeaderLabels
        self.table.setHorizontalHeaderLabels = self._managed_setHorizontalHeaderLabels
        
        self.logger.debug("方案B：表格方法已替换")
    
    def _connect_signals(self):
        """连接信号"""
        # 监听表头更新
        self.header_manager.headers_updated.connect(self._on_headers_updated)
        
        # 监听表头重置
        self.header_manager.headers_reset.connect(self._on_headers_reset)
        
        # 监听错误
        self.header_manager.error_occurred.connect(self._on_error_occurred)
        
        # 监听状态变化
        self.header_manager.state_manager.state_changed.connect(self._on_state_changed)
        
        self.logger.debug("方案B：信号已连接")
    
    def _managed_setColumnCount(self, count: int):
        """
        受管理的setColumnCount
        
        Args:
            count: 列数
        """
        try:
            # 获取当前表头
            current_headers = []
            for i in range(self.table.columnCount()):
                item = self.table.horizontalHeaderItem(i)
                if item:
                    current_headers.append(item.text())
                else:
                    current_headers.append(f"列{i+1}")
            
            # 调整表头列表长度
            if count > len(current_headers):
                # 增加列
                for i in range(len(current_headers), count):
                    current_headers.append(f"列{i+1}")
            elif count < len(current_headers):
                # 减少列
                current_headers = current_headers[:count]
            
            # 确定来源
            source = "pagination" if self.is_pagination_mode else "table_switch"
            if self.current_table_name:
                source = f"{source}_{self.current_table_name}"
            
            # 通过管理器设置表头
            if self.is_pagination_mode:
                # 分页模式特殊处理
                success = self.header_manager.update_headers_for_pagination(
                    current_headers,
                    self.current_page
                )
            else:
                # 普通设置
                success = self.header_manager.set_headers(
                    current_headers,
                    source=source
                )
            
            if success:
                # 调用原始方法
                self._original_setColumnCount(count)
                self.logger.debug(f"方案B：列数设置成功：{count}")
            else:
                self.logger.warning(f"方案B：列数设置被拒绝：{count}")
                
        except Exception as e:
            self.logger.error(f"方案B：设置列数异常：{e}")
            # 降级到原始方法
            self._original_setColumnCount(count)
    
    def _managed_setHorizontalHeaderLabels(self, labels: List[str]):
        """
        受管理的setHorizontalHeaderLabels
        
        Args:
            labels: 表头标签列表
        """
        try:
            # 确定来源
            source = "set_labels"
            if self.current_table_name:
                source = f"{source}_{self.current_table_name}"
            
            # 通过管理器设置表头
            success = self.header_manager.set_headers(
                labels,
                source=source
            )
            
            if success:
                # 先确保列数正确
                if self.table.columnCount() != len(labels):
                    self._original_setColumnCount(len(labels))
                
                # 设置表头标签
                self.table.setHorizontalHeaderLabels(labels)
                self.logger.debug(f"方案B：表头标签设置成功：{len(labels)}列")
            else:
                self.logger.warning(f"方案B：表头标签设置被拒绝")
                
        except Exception as e:
            self.logger.error(f"方案B：设置表头标签异常：{e}")
            # 降级到原始方法
            self.table.setHorizontalHeaderLabels(labels)
    
    def _on_headers_updated(self, headers: List[str]):
        """
        处理表头更新信号
        
        Args:
            headers: 新的表头列表
        """
        try:
            # 直接更新表格（避免递归）
            if self.table.columnCount() != len(headers):
                self._original_setColumnCount(len(headers))
            
            for i, header in enumerate(headers):
                self.table.setHorizontalHeaderItem(i, header)
            
            self.logger.debug(f"方案B：表头已更新：{len(headers)}列")
            
        except Exception as e:
            self.logger.error(f"方案B：处理表头更新信号异常：{e}")
    
    def _on_headers_reset(self):
        """处理表头重置信号"""
        try:
            # 清空表格
            self.table.clear()
            self._original_setColumnCount(0)
            self.table.setRowCount(0)
            
            self.logger.info("方案B：表头已重置")
            
        except Exception as e:
            self.logger.error(f"方案B：处理表头重置信号异常：{e}")
    
    def _on_error_occurred(self, error_msg: str):
        """
        处理错误信号
        
        Args:
            error_msg: 错误信息
        """
        self.logger.error(f"方案B：表头管理器错误：{error_msg}")
    
    def _on_state_changed(self, old_state: TableState, new_state: TableState):
        """
        处理状态变化信号
        
        Args:
            old_state: 旧状态
            new_state: 新状态
        """
        self.logger.debug(f"方案B：状态变化 {old_state.value} -> {new_state.value}")
        
        # 根据状态调整行为
        if new_state == TableState.PAGINATION:
            self.is_pagination_mode = True
        elif new_state == TableState.IDLE:
            self.is_pagination_mode = False
    
    def set_table_context(
        self,
        table_name: str = "",
        is_pagination: bool = False,
        page: int = 1
    ):
        """
        设置表格上下文
        
        Args:
            table_name: 表名
            is_pagination: 是否分页模式
            page: 页码
        """
        self.current_table_name = table_name
        self.is_pagination_mode = is_pagination
        self.current_page = page
        
        # 更新状态
        if is_pagination:
            self.header_manager.state_manager.transition_to(TableState.PAGINATION)
        else:
            self.header_manager.state_manager.transition_to(TableState.TABLE_SWITCHING)
    
    def get_statistics(self) -> dict:
        """获取统计信息"""
        return self.header_manager.get_statistics()
    
    def reset(self):
        """重置适配器"""
        self.header_manager.reset_headers()
        self.current_table_name = ""
        self.is_pagination_mode = False
        self.current_page = 1
    
    def restore_original_methods(self):
        """恢复原始方法（用于卸载适配器）"""
        self.table.setColumnCount = self._original_setColumnCount
        self.table.setHorizontalHeaderLabels = self._original_setHorizontalHeaderLabels
        
        # 断开信号
        try:
            self.header_manager.headers_updated.disconnect(self._on_headers_updated)
            self.header_manager.headers_reset.disconnect(self._on_headers_reset)
            self.header_manager.error_occurred.disconnect(self._on_error_occurred)
            self.header_manager.state_manager.state_changed.disconnect(self._on_state_changed)
        except:
            pass
        
        self.logger.info("方案B：原始方法已恢复")


def apply_header_management(table_widget: QTableWidget) -> TableHeaderAdapter:
    """
    应用表头管理到表格组件
    
    Args:
        table_widget: 表格组件
        
    Returns:
        适配器实例
    """
    adapter = TableHeaderAdapter(table_widget)
    
    # 保存适配器引用到表格组件
    table_widget._header_adapter = adapter
    
    return adapter


def remove_header_management(table_widget: QTableWidget):
    """
    移除表头管理
    
    Args:
        table_widget: 表格组件
    """
    if hasattr(table_widget, '_header_adapter'):
        adapter = table_widget._header_adapter
        adapter.restore_original_methods()
        delattr(table_widget, '_header_adapter')