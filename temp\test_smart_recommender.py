#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能配置推荐功能测试脚本
"""

import sys
import os
import pandas as pd
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.smart_config_recommender import SmartConfigRecommender
from src.modules.data_import.sheet_config_manager import SheetConfigManager

def test_name_based_recommendation():
    """测试基于名称的推荐"""
    print("=== 测试基于Sheet名称的智能推荐 ===")
    
    recommender = SmartConfigRecommender()
    
    test_cases = [
        "2024年1月工资表",
        "员工信息汇总",
        "数据说明",
        "工资模板",
        "2024年度统计",
        "备注信息"
    ]
    
    for sheet_name in test_cases:
        print(f"\n📊 Sheet: {sheet_name}")
        recommendation = recommender.recommend_config(sheet_name)
        
        print(f"  置信度: {recommendation.confidence:.2f}")
        print(f"  是否启用: {recommendation.config.is_enabled}")
        print(f"  表头行: {recommendation.config.header_row}")
        print(f"  数据起始行: {recommendation.config.data_start_row}")
        print(f"  移除汇总行: {recommendation.config.remove_summary_rows}")
        print(f"  推荐理由: {', '.join(recommendation.reasons)}")
        
        if recommendation.warnings:
            print(f"  ⚠️  警告: {', '.join(recommendation.warnings)}")
        
        if recommendation.suggestions:
            print(f"  💡 建议: {', '.join(recommendation.suggestions)}")

def test_content_based_recommendation():
    """测试基于内容的推荐"""
    print("\n=== 测试基于Sheet内容的智能推荐 ===")
    
    recommender = SmartConfigRecommender()
    
    # 创建测试数据
    test_data_cases = [
        {
            'name': '标准工资表',
            'data': pd.DataFrame({
                '姓名': ['张三', '李四', '王五', '合计'],
                '工号': ['001', '002', '003', ''],
                '基本工资': [5000, 6000, 5500, 16500],
                '绩效工资': [1000, 1200, 800, 3000]
            })
        },
        {
            'name': '带标题的数据表',
            'data': pd.DataFrame({
                '2024年1月工资表': ['', '', '', ''],
                'Unnamed: 1': ['', '', '', ''],
                'Unnamed: 2': ['姓名', '张三', '李四', '王五'],
                'Unnamed: 3': ['工资', '5000', '6000', '5500']
            })
        },
        {
            'name': '空值较多的表',
            'data': pd.DataFrame({
                '姓名': ['张三', None, '王五', None],
                '部门': [None, '技术部', None, '销售部'],
                '工资': [5000, None, 5500, None]
            })
        }
    ]
    
    for case in test_data_cases:
        print(f"\n📊 测试案例: {case['name']}")
        print("数据预览:")
        print(case['data'].head())
        
        recommendation = recommender.recommend_config(
            sheet_name=case['name'],
            sample_data=case['data']
        )
        
        print(f"\n推荐结果:")
        print(f"  置信度: {recommendation.confidence:.2f}")
        print(f"  表头行: {recommendation.config.header_row}")
        print(f"  数据起始行: {recommendation.config.data_start_row}")
        print(f"  移除汇总行: {recommendation.config.remove_summary_rows}")
        print(f"  跳过空行: {recommendation.config.skip_empty_rows}")
        print(f"  推荐理由: {', '.join(recommendation.reasons)}")
        
        if recommendation.warnings:
            print(f"  ⚠️  警告: {', '.join(recommendation.warnings)}")
        
        if recommendation.suggestions:
            print(f"  💡 建议: {', '.join(recommendation.suggestions)}")

def test_sheet_config_manager_integration():
    """测试与Sheet配置管理器的集成"""
    print("\n=== 测试Sheet配置管理器集成 ===")
    
    manager = SheetConfigManager()
    
    test_sheets = [
        "2024年1月",
        "员工汇总",
        "数据说明"
    ]
    
    for sheet_name in test_sheets:
        print(f"\n📊 Sheet: {sheet_name}")
        
        # 获取推荐摘要
        summary = manager.get_recommendation_summary(sheet_name)
        print(f"推荐摘要: {summary}")
        
        # 应用智能推荐
        success = manager.apply_smart_recommendation(sheet_name)
        print(f"应用推荐: {'成功' if success else '失败'}")
        
        # 获取配置
        config = manager.get_or_create_config(sheet_name)
        print(f"最终配置: 启用={config.is_enabled}, 表头行={config.header_row}, 数据起始行={config.data_start_row}")

def main():
    """主函数"""
    print("🚀 智能配置推荐功能测试")
    print("=" * 50)
    
    try:
        # 测试基于名称的推荐
        test_name_based_recommendation()
        
        # 测试基于内容的推荐
        test_content_based_recommendation()
        
        # 测试集成功能
        test_sheet_config_manager_integration()
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
