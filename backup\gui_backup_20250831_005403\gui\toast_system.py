"""
Toast消息系统 - Toast Message System

实现现代化的非侵入式消息通知系统，支持多种消息类型和自动消失。

主要功能:
1. 多种消息类型 (Success/Warning/Error/Info)
2. 自动堆叠显示
3. 自定义显示时长
4. 滑入滑出动画
5. 手动关闭功能
"""

from typing import Optional, List
from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QHBoxLayout, QVBoxLayout,
    QGraphicsOpacityEffect, QFrame, QApplication
)
from PyQt5.QtCore import (
    Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect,
    pyqtSignal, QParallelAnimationGroup, QSequentialAnimationGroup
)
from PyQt5.QtGui import QFont, QIcon

from .modern_style import MaterialDesignPalette


class ToastMessage(QFrame):
    """单个Toast消息组件"""
    
    # 信号定义
    close_requested = pyqtSignal()
    
    def __init__(self, message: str, message_type: str = "info", 
                 duration: int = 3000, parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        self.message = message
        self.message_type = message_type
        self.duration = duration
        
        # 设置基本属性
        self.setFixedHeight(60)
        self.setMinimumWidth(300)
        self.setMaximumWidth(400)
        
        # 初始化UI
        self._init_ui()
        
        # 设置样式
        self._set_style()
        
        # 设置自动关闭定时器
        if duration > 0:
            self.auto_close_timer = QTimer()
            self.auto_close_timer.setSingleShot(True)
            self.auto_close_timer.timeout.connect(self.close_requested.emit)
            self.auto_close_timer.start(duration)
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(12)
        
        # 图标标签
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(24, 24)
        self.icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.icon_label)
        
        # 消息标签
        self.message_label = QLabel(self.message)
        self.message_label.setWordWrap(True)
        font = QFont()
        font.setPointSize(9)
        self.message_label.setFont(font)
        layout.addWidget(self.message_label, 1)
        
        # 关闭按钮
        self.close_button = QPushButton("×")
        self.close_button.setFixedSize(20, 20)
        self.close_button.clicked.connect(self.close_requested.emit)
        layout.addWidget(self.close_button)
    
    def _set_style(self):
        """设置样式"""
        # 根据消息类型设置颜色
        colors = self._get_type_colors()
        
        # 设置图标
        self._set_icon()
        
        # 设置样式表
        self.setStyleSheet(f"""
        ToastMessage {{
            background-color: {colors['background']};
            border: 1px solid {colors['border']};
            border-radius: 8px;
            border-left: 4px solid {colors['accent']};
        }}
        
        QLabel {{
            color: {colors['text']};
            background: transparent;
            border: none;
        }}
        
        QPushButton {{
            background-color: transparent;
            border: none;
            color: {colors['text']};
            font-size: 14px;
            font-weight: bold;
            border-radius: 10px;
        }}
        
        QPushButton:hover {{
            background-color: {colors['button_hover']};
        }}
        """)
    
    def _get_type_colors(self) -> dict:
        """获取消息类型对应的颜色"""
        if self.message_type == "success":
            return {
                'background': '#F1F8E9',
                'border': '#C8E6C9',
                'accent': MaterialDesignPalette.STATUS['success'],
                'text': '#2E7D32',
                'button_hover': 'rgba(76, 175, 80, 0.1)'
            }
        elif self.message_type == "warning":
            return {
                'background': '#FFF8E1',
                'border': '#FFE0B2',
                'accent': MaterialDesignPalette.STATUS['warning'],
                'text': '#E65100',
                'button_hover': 'rgba(255, 152, 0, 0.1)'
            }
        elif self.message_type == "error":
            return {
                'background': '#FFEBEE',
                'border': '#FFCDD2',
                'accent': MaterialDesignPalette.STATUS['error'],
                'text': '#C62828',
                'button_hover': 'rgba(244, 67, 54, 0.1)'
            }
        else:  # info
            return {
                'background': '#E3F2FD',
                'border': '#BBDEFB',
                'accent': MaterialDesignPalette.STATUS['info'],
                'text': '#1565C0',
                'button_hover': 'rgba(33, 150, 243, 0.1)'
            }
    
    def _set_icon(self):
        """设置图标"""
        # 这里可以设置实际的图标，现在使用文本符号
        icons = {
            'success': '✓',
            'warning': '⚠',
            'error': '✕',
            'info': 'ℹ'
        }
        
        self.icon_label.setText(icons.get(self.message_type, 'ℹ'))
        self.icon_label.setAlignment(Qt.AlignCenter)
        
        # 设置图标样式
        colors = self._get_type_colors()
        self.icon_label.setStyleSheet(f"""
        QLabel {{
            color: {colors['accent']};
            font-weight: bold;
            font-size: 16px;
        }}
        """)


class ToastContainer(QWidget):
    """Toast消息容器"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        # 设置窗口属性
        self.setWindowFlags(
            Qt.FramelessWindowHint | 
            Qt.WindowStaysOnTopHint | 
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_ShowWithoutActivating)
        
        # 初始化布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 20, 20, 20)
        self.layout.setSpacing(10)
        self.layout.addStretch()  # 从底部开始堆叠
        
        # Toast消息列表
        self.toast_messages: List[ToastMessage] = []
        
        # 设置初始位置和大小
        self._update_position()
    
    def add_toast(self, message: str, message_type: str = "info", duration: int = 3000):
        """添加Toast消息"""
        # 创建Toast消息
        toast = ToastMessage(message, message_type, duration, self)
        toast.close_requested.connect(lambda: self._remove_toast(toast))
        
        # 添加到布局
        self.layout.insertWidget(self.layout.count() - 1, toast)
        self.toast_messages.append(toast)
        
        # 显示容器
        self.show()
        
        # 更新位置和大小
        self._update_geometry()
        
        # 播放进入动画
        self._animate_toast_in(toast)
    
    def _remove_toast(self, toast: ToastMessage):
        """移除Toast消息"""
        if toast in self.toast_messages:
            # 播放退出动画
            self._animate_toast_out(toast)
    
    def _animate_toast_in(self, toast: ToastMessage):
        """Toast进入动画"""
        # 设置初始状态
        toast.setFixedHeight(0)
        toast.setMaximumHeight(0)
        
        # 创建高度动画
        self.height_animation = QPropertyAnimation(toast, b"maximumHeight")
        self.height_animation.setDuration(300)
        self.height_animation.setStartValue(0)
        self.height_animation.setEndValue(60)
        self.height_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 创建透明度动画
        self.opacity_effect = QGraphicsOpacityEffect()
        toast.setGraphicsEffect(self.opacity_effect)
        
        self.opacity_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.opacity_animation.setDuration(300)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 并行执行动画
        self.animation_group = QParallelAnimationGroup()
        self.animation_group.addAnimation(self.height_animation)
        self.animation_group.addAnimation(self.opacity_animation)
        
        # 动画完成后恢复高度
        self.animation_group.finished.connect(lambda: toast.setFixedHeight(60))
        
        self.animation_group.start()
    
    def _animate_toast_out(self, toast: ToastMessage):
        """Toast退出动画"""
        # 创建透明度动画
        if hasattr(toast, 'graphicsEffect') and toast.graphicsEffect():
            opacity_effect = toast.graphicsEffect()
        else:
            opacity_effect = QGraphicsOpacityEffect()
            toast.setGraphicsEffect(opacity_effect)
        
        self.out_opacity_animation = QPropertyAnimation(opacity_effect, b"opacity")
        self.out_opacity_animation.setDuration(200)
        self.out_opacity_animation.setStartValue(1.0)
        self.out_opacity_animation.setEndValue(0.0)
        self.out_opacity_animation.setEasingCurve(QEasingCurve.InCubic)
        
        # 创建高度动画
        self.out_height_animation = QPropertyAnimation(toast, b"maximumHeight")
        self.out_height_animation.setDuration(200)
        self.out_height_animation.setStartValue(60)
        self.out_height_animation.setEndValue(0)
        self.out_height_animation.setEasingCurve(QEasingCurve.InCubic)
        
        # 顺序执行动画
        self.out_animation_group = QSequentialAnimationGroup()
        self.out_animation_group.addAnimation(self.out_opacity_animation)
        self.out_animation_group.addAnimation(self.out_height_animation)
        
        # 动画完成后移除Toast
        self.out_animation_group.finished.connect(lambda: self._finalize_remove_toast(toast))
        
        self.out_animation_group.start()
    
    def _finalize_remove_toast(self, toast: ToastMessage):
        """最终移除Toast"""
        if toast in self.toast_messages:
            self.toast_messages.remove(toast)
            self.layout.removeWidget(toast)
            toast.deleteLater()
            
            # 更新几何形状
            self._update_geometry()
            
            # 如果没有消息了，隐藏容器
            if not self.toast_messages:
                self.hide()
    
    def _update_geometry(self):
        """更新几何形状"""
        if not self.toast_messages:
            return
        
        # 计算所需高度
        total_height = 40  # 上下边距
        total_height += len(self.toast_messages) * 60  # 每个Toast的高度
        total_height += (len(self.toast_messages) - 1) * 10  # 间距
        
        # 设置大小
        self.setFixedSize(420, total_height)
        
        # 更新位置
        self._update_position()
    
    def _update_position(self):
        """更新位置到屏幕右上角"""
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.right() - self.width() - 20
            y = parent_rect.top() + 80  # 留出菜单栏空间
            self.move(x, y)


class ToastManager:
    """Toast管理器 - 单例模式"""
    
    _instance = None
    _container = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def set_parent(self, parent: QWidget):
        """设置父窗口"""
        if self._container is None:
            self._container = ToastContainer(parent)
    
    def show_success(self, message: str, duration: int = 3000):
        """显示成功消息"""
        if self._container:
            self._container.add_toast(message, "success", duration)
    
    def show_warning(self, message: str, duration: int = 5000):
        """显示警告消息"""
        if self._container:
            self._container.add_toast(message, "warning", duration)
    
    def show_error(self, message: str, duration: int = 0):
        """显示错误消息 (默认不自动关闭)"""
        if self._container:
            self._container.add_toast(message, "error", duration)
    
    def show_info(self, message: str, duration: int = 4000):
        """显示信息消息"""
        if self._container:
            self._container.add_toast(message, "info", duration)
    
    def show_toast(self, message: str, message_type: str = "info", duration: int = 3000):
        """显示自定义Toast消息"""
        if self._container:
            self._container.add_toast(message, message_type, duration)


# 全局Toast管理器实例
toast_manager = ToastManager() 