#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置版本管理功能测试脚本
"""

import sys
import os
import time
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_version_manager import ConfigVersionManager, VersionType
from src.modules.data_import.sheet_config_manager import SheetConfigManager

def test_version_manager():
    """测试版本管理器"""
    print("=== 测试配置版本管理器 ===")
    
    manager = ConfigVersionManager()
    
    # 1. 创建初始版本
    print("\n📋 创建初始版本:")
    config_data_v1 = {
        'header_row': 1,
        'data_start_row': 2,
        'is_enabled': True,
        'remove_summary_rows': False
    }
    
    version_id_v1 = manager.create_version(
        sheet_name="测试工资表",
        config_data=config_data_v1,
        version_type=VersionType.MANUAL,
        description="初始配置版本",
        tags=["initial"],
        created_by="user"
    )
    
    print(f"  创建版本1: {version_id_v1}")
    
    # 2. 创建第二个版本
    print("\n📋 创建第二个版本:")
    config_data_v2 = {
        'header_row': 1,
        'data_start_row': 2,
        'is_enabled': True,
        'remove_summary_rows': True,  # 修改了这个字段
        'summary_keywords': ['合计', '小计']  # 新增字段
    }
    
    version_id_v2 = manager.create_version(
        sheet_name="测试工资表",
        config_data=config_data_v2,
        version_type=VersionType.AUTO,
        description="启用汇总行移除",
        created_by="system"
    )
    
    print(f"  创建版本2: {version_id_v2}")
    
    # 3. 查看版本历史
    print("\n📜 版本历史:")
    history = manager.get_version_history("测试工资表")
    for version in history:
        print(f"  v{version.version_number}: {version.description} "
              f"({version.version_type.value}, {version.created_by})")
        print(f"    创建时间: {version.created_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"    变更数: {len(version.changes)}")
        if version.changes:
            for change in version.changes:
                print(f"      • {change.change_type.value}: {change.field_name}")
        print(f"    当前版本: {'是' if version.is_current else '否'}")
        print()
    
    # 4. 版本比较
    print("\n🔍 版本比较:")
    comparison = manager.compare_versions("测试工资表", version_id_v1, version_id_v2)
    print(f"  版本1 vs 版本2:")
    print(f"  差异数量: {comparison['differences_count']}")
    print(f"  是否相同: {comparison['is_identical']}")
    
    if comparison['differences']:
        print(f"  具体差异:")
        for diff in comparison['differences']:
            print(f"    • {diff['field']}: {diff['version1_value']} -> {diff['version2_value']} ({diff['change_type']})")
    
    # 5. 创建里程碑
    print("\n🏆 创建里程碑:")
    milestone_id = manager.create_milestone(
        sheet_name="测试工资表",
        description="功能完成里程碑",
        tags=["milestone", "feature-complete"]
    )
    print(f"  里程碑ID: {milestone_id}")
    
    # 6. 版本恢复
    print("\n🔄 版本恢复:")
    print(f"  恢复到版本1: {version_id_v1}")
    success, backup_id = manager.restore_version("测试工资表", version_id_v1)
    print(f"  恢复结果: {'成功' if success else '失败'}")
    if backup_id:
        print(f"  备份版本ID: {backup_id}")
    
    # 7. 查看当前版本
    print("\n📍 当前版本:")
    current_version = manager.get_current_version("测试工资表")
    if current_version:
        print(f"  当前版本: v{current_version.version_number}")
        print(f"  描述: {current_version.description}")
        print(f"  配置: {current_version.config_data}")
    
    # 8. 版本统计
    print("\n📊 版本统计:")
    stats = manager.get_version_statistics("测试工资表")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    return manager

def test_sheet_manager_integration():
    """测试与Sheet配置管理器的集成"""
    print("\n=== 测试Sheet配置管理器集成 ===")
    
    manager = SheetConfigManager()
    
    # 1. 创建初始配置
    print("\n📋 创建初始配置:")
    sheet_name = "集成测试表"
    
    success = manager.update_config(sheet_name,
                                   header_row=1,
                                   data_start_row=2,
                                   is_enabled=True)
    print(f"  创建初始配置: {'成功' if success else '失败'}")
    
    # 2. 修改配置（应该自动创建版本）
    print("\n📝 修改配置:")
    success = manager.update_config(sheet_name,
                                   remove_summary_rows=True,
                                   summary_keywords=['合计', '小计', '总计'])
    print(f"  修改配置: {'成功' if success else '失败'}")
    
    # 3. 再次修改配置
    print("\n📝 再次修改配置:")
    success = manager.update_config(sheet_name,
                                   skip_empty_rows=True,
                                   trim_whitespace=True,
                                   normalize_numbers=True)
    print(f"  再次修改配置: {'成功' if success else '失败'}")
    
    # 4. 查看版本历史
    print("\n📜 查看版本历史:")
    history = manager.get_config_version_history(sheet_name)
    print(f"  版本数量: {len(history)}")
    for i, version in enumerate(history[:3]):  # 显示前3个版本
        print(f"  {i+1}. v{version.version_number}: {version.description}")
        print(f"     时间: {version.created_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"     变更: {len(version.changes)}个")
    
    # 5. 创建里程碑
    print("\n🏆 创建里程碑:")
    milestone_id = manager.create_config_milestone(
        sheet_name=sheet_name,
        description="配置优化完成",
        tags=["optimization", "stable"]
    )
    print(f"  里程碑ID: {milestone_id}")
    
    # 6. 查看里程碑
    print("\n🏆 查看里程碑:")
    milestones = manager.get_config_milestones(sheet_name)
    print(f"  里程碑数量: {len(milestones)}")
    for milestone in milestones:
        print(f"  • {milestone.description} (v{milestone.version_number})")
    
    # 7. 版本恢复测试
    print("\n🔄 版本恢复测试:")
    if len(history) >= 2:
        # 恢复到第二个版本
        second_version = history[1]
        print(f"  恢复到版本: v{second_version.version_number}")
        success = manager.restore_config_version(sheet_name, second_version.version_id)
        print(f"  恢复结果: {'成功' if success else '失败'}")
        
        # 查看恢复后的配置
        current_config = manager.get_or_create_config(sheet_name)
        print(f"  恢复后配置:")
        print(f"    移除汇总行: {current_config.remove_summary_rows}")
        print(f"    跳过空行: {current_config.skip_empty_rows}")
    
    # 8. 审计跟踪
    print("\n🔍 审计跟踪:")
    audit = manager.get_config_audit_trail(sheet_name)
    print(f"  总版本数: {audit.get('total_versions', 0)}")
    print(f"  总变更数: {audit.get('total_changes', 0)}")
    print(f"  变更类型: {audit.get('change_types', {})}")
    
    # 显示最近的几个变更
    trail = audit.get('audit_trail', [])
    if trail:
        print(f"  最近变更:")
        for entry in trail[:3]:  # 显示最近3个
            print(f"    v{entry['version_number']}: {entry['description']}")
            print(f"      时间: {entry['created_time']}")
            print(f"      变更: {entry['changes_count']}个")
    
    # 9. 全局版本统计
    print("\n📊 全局版本统计:")
    global_stats = manager.get_version_statistics()
    for key, value in global_stats.items():
        print(f"  {key}: {value}")

def test_version_cleanup():
    """测试版本清理功能"""
    print("\n=== 测试版本清理功能 ===")
    
    manager = ConfigVersionManager()
    manager.max_versions_per_sheet = 5  # 设置最大版本数为5
    
    sheet_name = "清理测试表"
    
    # 创建多个版本
    print("\n📋 创建多个版本:")
    for i in range(8):
        config_data = {
            'header_row': 1,
            'data_start_row': 2,
            'test_field': f'value_{i}',
            'version_number': i + 1
        }
        
        version_id = manager.create_version(
            sheet_name=sheet_name,
            config_data=config_data,
            version_type=VersionType.AUTO,
            description=f"测试版本 {i + 1}"
        )
        print(f"  创建版本 {i + 1}: {version_id}")
    
    # 查看清理后的版本
    print("\n📜 清理后的版本:")
    history = manager.get_version_history(sheet_name)
    print(f"  保留版本数: {len(history)}")
    for version in history:
        print(f"  v{version.version_number}: {version.description}")
    
    # 创建里程碑版本（应该被保留）
    print("\n🏆 创建里程碑版本:")
    milestone_id = manager.create_milestone(
        sheet_name=sheet_name,
        description="重要里程碑",
        tags=["important"]
    )
    print(f"  里程碑ID: {milestone_id}")
    
    # 再创建几个版本，测试里程碑是否被保留
    for i in range(3):
        config_data = {
            'header_row': 1,
            'data_start_row': 2,
            'test_field': f'after_milestone_{i}',
            'version_number': i + 10
        }
        
        manager.create_version(
            sheet_name=sheet_name,
            config_data=config_data,
            version_type=VersionType.AUTO,
            description=f"里程碑后版本 {i + 1}"
        )
    
    # 查看最终版本
    print("\n📜 最终版本列表:")
    final_history = manager.get_version_history(sheet_name)
    print(f"  最终版本数: {len(final_history)}")
    for version in final_history:
        milestone_mark = " 🏆" if version.version_type == VersionType.MILESTONE else ""
        print(f"  v{version.version_number}: {version.description}{milestone_mark}")

def main():
    """主函数"""
    print("🚀 配置版本管理功能测试")
    print("=" * 50)
    
    try:
        # 测试版本管理器
        manager = test_version_manager()
        
        # 测试集成功能
        test_sheet_manager_integration()
        
        # 测试版本清理
        test_version_cleanup()
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
