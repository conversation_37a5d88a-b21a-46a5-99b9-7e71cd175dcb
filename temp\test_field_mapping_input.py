#!/usr/bin/env python3
"""
测试字段映射输入框功能
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication, QTableWidgetItem
from PyQt5.QtCore import QTimer
from src.gui.unified_data_import_window import UnifiedDataImportWindow
from src.utils.log_config import setup_logger

def test_field_mapping_input():
    """测试字段映射输入框功能"""
    logger = setup_logger(__name__)
    
    app = QApplication([])
    
    try:
        # 创建窗口
        window = UnifiedDataImportWindow()
        window.show()
        
        def test_mapping_functionality():
            logger.info("开始测试字段映射输入框功能...")
            
            # 模拟Excel字段头数据（包含各种需要清理的字段名）
            test_headers = [
                "姓名",           # 正常中文字段
                "人员 代码",      # 包含空格
                "基本\n工资",     # 包含换行符
                "津贴\t补助",     # 包含制表符
                "年龄(岁)",       # 包含特殊字符
                "2020年基础工资", # 数字开头
                "Employee ID",    # 英文字段
                "  工资  \n",     # 复杂空白字符
                "@#特殊$字符%",   # 大量特殊字符
                "",               # 空字段
            ]
            
            # 测试字段映射组件
            mapping_tab = window.mapping_tab
            
            # 调用load_excel_headers方法
            logger.info(f"加载测试字段头: {len(test_headers)} 个字段")
            mapping_tab.load_excel_headers(test_headers, "salary_table")
            
            # 检查映射表格
            mapping_table = mapping_tab.mapping_table
            row_count = mapping_table.rowCount()
            
            logger.info(f"映射表格行数: {row_count}")
            
            if row_count == len(test_headers):
                logger.info("✅ 表格行数正确")
                
                # 检查每一行的内容
                for row in range(row_count):
                    # 检查Excel列名（第0列）
                    excel_item = mapping_table.item(row, 0)
                    if excel_item:
                        excel_name = excel_item.text()
                        original_name = test_headers[row]
                        
                        # 检查数据库字段名（第1列）
                        db_item = mapping_table.item(row, 1)
                        if db_item:
                            db_name = db_item.text()
                            tooltip = db_item.toolTip()
                            
                            logger.info(f"行 {row}:")
                            logger.info(f"  Excel列名: '{original_name}' -> 显示: '{excel_name}'")
                            logger.info(f"  数据库字段: '{db_name}'")
                            logger.info(f"  提示信息: {tooltip}")
                            
                            # 验证字段名清理效果
                            if original_name == "姓名":
                                assert db_name == "姓名", f"期望'姓名'，实际'{db_name}'"
                            elif original_name == "人员 代码":
                                assert db_name == "人员_代码", f"期望'人员_代码'，实际'{db_name}'"
                            elif original_name == "基本\n工资":
                                assert db_name == "基本_工资", f"期望'基本_工资'，实际'{db_name}'"
                            elif original_name == "津贴\t补助":
                                assert db_name == "津贴_补助", f"期望'津贴_补助'，实际'{db_name}'"
                            elif original_name == "年龄(岁)":
                                assert db_name == "年龄_岁", f"期望'年龄_岁'，实际'{db_name}'"
                            elif original_name == "2020年基础工资":
                                assert db_name == "field_2020年基础工资", f"期望'field_2020年基础工资'，实际'{db_name}'"
                            elif original_name == "Employee ID":
                                assert db_name == "Employee_ID", f"期望'Employee_ID'，实际'{db_name}'"
                            elif original_name == "  工资  \n":
                                assert db_name == "工资", f"期望'工资'，实际'{db_name}'"
                            elif original_name == "@#特殊$字符%":
                                assert db_name == "特殊_字符", f"期望'特殊_字符'，实际'{db_name}'"
                            elif original_name == "":
                                assert db_name == "field_name", f"期望'field_name'，实际'{db_name}'"
                            
                            logger.info(f"  ✅ 字段名清理正确")
                        else:
                            logger.error(f"  ❌ 行 {row} 数据库字段为空")
                    else:
                        logger.error(f"❌ 行 {row} Excel列名为空")
                
                # 测试字段编辑功能
                logger.info("\n测试字段编辑功能...")
                
                # 修改第一行的数据库字段名
                first_db_item = mapping_table.item(0, 1)
                if first_db_item:
                    original_text = first_db_item.text()
                    new_text = "custom_name"
                    first_db_item.setText(new_text)
                    
                    # 验证修改是否生效
                    updated_text = first_db_item.text()
                    if updated_text == new_text:
                        logger.info(f"✅ 字段编辑成功: '{original_text}' -> '{new_text}'")
                    else:
                        logger.error(f"❌ 字段编辑失败: 期望'{new_text}'，实际'{updated_text}'")
                
                logger.info("✅ 字段映射输入框功能测试完成")
                
            else:
                logger.error(f"❌ 表格行数不正确: 期望{len(test_headers)}，实际{row_count}")
            
            # 3秒后关闭
            QTimer.singleShot(3000, app.quit)
        
        # 2秒后开始测试
        QTimer.singleShot(2000, test_mapping_functionality)
        
        app.exec_()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_field_mapping_input()
