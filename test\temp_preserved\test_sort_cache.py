"""
测试排序缓存性能
验证P2优化效果
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
import time
from src.core.sort_cache_manager import get_sort_cache_manager

def create_test_data(rows=1000):
    """创建测试数据"""
    np.random.seed(42)
    return pd.DataFrame({
        'employee_id': [f'EMP{i:04d}' for i in range(rows)],
        'employee_name': [f'Name_{i}' for i in range(rows)],
        'department': np.random.choice(['研发部', '销售部', '人事部', '财务部'], rows),
        'total_salary': np.random.uniform(3000, 20000, rows).round(2),
        'performance_score': np.random.uniform(60, 100, rows).round(1)
    })

def test_sort_cache():
    """测试排序缓存"""
    print("=" * 60)
    print("测试排序缓存性能")
    print("=" * 60)
    
    manager = get_sort_cache_manager()
    
    # 1. 清空缓存
    print("\n1. 清空缓存...")
    manager.invalidate_cache()
    
    # 2. 创建测试数据
    print("\n2. 创建测试数据...")
    test_data = create_test_data(1000)
    print(f"   数据行数: {len(test_data)}")
    print(f"   数据列数: {len(test_data.columns)}")
    
    # 3. 测试单列排序
    print("\n3. 测试单列排序...")
    
    sort_columns = [
        {'column_name': 'total_salary', 'sort_order': 'desc'}
    ]
    
    # 第一次排序（缓存未命中）
    start = time.time()
    sorted_data1, cache_hit1 = manager.get_sorted_data('test_table', test_data, sort_columns)
    time1 = time.time() - start
    
    print(f"   第一次排序: 耗时={time1:.4f}秒, 缓存命中={cache_hit1}")
    
    # 第二次排序（缓存命中）
    start = time.time()
    sorted_data2, cache_hit2 = manager.get_sorted_data('test_table', test_data, sort_columns)
    time2 = time.time() - start
    
    print(f"   第二次排序: 耗时={time2:.4f}秒, 缓存命中={cache_hit2}")
    
    if cache_hit2 and time2 < time1:
        speedup = time1 / time2 if time2 > 0 else float('inf')
        print(f"   [PASS] 缓存加速: {speedup:.1f}倍")
    else:
        print("   [FAIL] 缓存未生效")
    
    # 验证排序结果正确性
    if sorted_data1.iloc[0]['total_salary'] >= sorted_data1.iloc[-1]['total_salary']:
        print("   [PASS] 排序结果正确（降序）")
    else:
        print("   [FAIL] 排序结果错误")
    
    # 4. 测试多列排序
    print("\n4. 测试多列排序...")
    
    sort_columns = [
        {'column_name': 'department', 'sort_order': 'asc'},
        {'column_name': 'total_salary', 'sort_order': 'desc'}
    ]
    
    start = time.time()
    sorted_data3, cache_hit3 = manager.get_sorted_data('test_table', test_data, sort_columns)
    time3 = time.time() - start
    
    print(f"   多列排序: 耗时={time3:.4f}秒, 缓存命中={cache_hit3}")
    
    # 5. 测试大数据量
    print("\n5. 测试大数据量...")
    
    large_data = create_test_data(5000)
    
    # 第一次排序
    start = time.time()
    sorted_large1, _ = manager.get_sorted_data('large_table', large_data, sort_columns)
    time_large1 = time.time() - start
    
    # 第二次排序（缓存）
    start = time.time()
    sorted_large2, cache_hit_large = manager.get_sorted_data('large_table', large_data, sort_columns)
    time_large2 = time.time() - start
    
    print(f"   5000行数据:")
    print(f"   - 首次排序: {time_large1:.4f}秒")
    print(f"   - 缓存排序: {time_large2:.4f}秒")
    
    if cache_hit_large:
        speedup = time_large1 / time_large2 if time_large2 > 0 else float('inf')
        print(f"   - 加速比: {speedup:.1f}倍")
    
    # 6. 测试缓存失效
    print("\n6. 测试缓存失效...")
    
    # 修改数据
    modified_data = test_data.copy()
    modified_data.loc[0, 'total_salary'] = 99999
    
    # 应该检测到数据变化，不使用缓存
    sorted_modified, cache_hit_modified = manager.get_sorted_data('test_table', modified_data, sort_columns)
    
    if not cache_hit_modified:
        print("   [PASS] 数据变化后缓存失效")
    else:
        print("   [FAIL] 未检测到数据变化")
    
    # 7. 获取缓存统计
    print("\n7. 缓存统计...")
    stats = manager.get_statistics()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # 8. 测试缓存优化
    print("\n8. 测试缓存优化...")
    manager.optimize_cache()
    
    stats_after = manager.get_statistics()
    print(f"   优化后缓存条目: {stats_after['cache_entries']}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_sort_cache()