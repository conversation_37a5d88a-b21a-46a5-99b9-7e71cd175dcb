"""
依赖注入容器 - P3级架构规范化
提供自动依赖解析和注入功能
"""

from typing import Dict, Any, Type, Optional, Callable, List, get_type_hints
import inspect
from dataclasses import dataclass
from enum import Enum
from loguru import logger


class InjectScope(Enum):
    """注入作用域"""
    SINGLETON = "singleton"    # 单例
    TRANSIENT = "transient"    # 瞬态
    REQUEST = "request"        # 请求级


@dataclass
class Dependency:
    """依赖描述"""
    interface: Type
    implementation: Type
    scope: InjectScope
    factory: Optional[Callable] = None
    instance: Optional[Any] = None
    dependencies: List[Type] = None


class DependencyContainer:
    """
    依赖注入容器
    
    提供自动依赖解析、循环依赖检测和生命周期管理
    """
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.logger = logger
        self._dependencies: Dict[Type, Dependency] = {}
        self._request_scope: Dict[Type, Any] = {}
        self._resolving: set = set()  # 用于检测循环依赖
        
        self.logger.info("DependencyContainer 初始化完成")
    
    def register(
        self,
        interface: Type,
        implementation: Optional[Type] = None,
        scope: InjectScope = InjectScope.SINGLETON,
        factory: Optional[Callable] = None
    ):
        """
        注册依赖
        
        Args:
            interface: 接口类型
            implementation: 实现类型
            scope: 作用域
            factory: 工厂函数
        """
        if not implementation and not factory:
            implementation = interface
        
        # 分析依赖
        dependencies = self._analyze_dependencies(implementation or factory)
        
        dep = Dependency(
            interface=interface,
            implementation=implementation,
            scope=scope,
            factory=factory,
            dependencies=dependencies
        )
        
        self._dependencies[interface] = dep
        self.logger.debug(f"注册依赖: {interface.__name__} -> {implementation.__name__ if implementation else 'factory'}")
    
    def register_singleton_instance(self, interface: Type, instance: Any):
        """
        注册单例实例
        
        Args:
            interface: 接口类型
            instance: 实例对象
        """
        dep = Dependency(
            interface=interface,
            implementation=type(instance),
            scope=InjectScope.SINGLETON,
            instance=instance
        )
        
        self._dependencies[interface] = dep
        self.logger.debug(f"注册单例实例: {interface.__name__}")
    
    def resolve(self, interface: Type) -> Any:
        """
        解析依赖
        
        Args:
            interface: 接口类型
        
        Returns:
            实例对象
        
        Raises:
            ValueError: 依赖未注册或循环依赖
        """
        if interface not in self._dependencies:
            raise ValueError(f"依赖未注册: {interface.__name__}")
        
        # 检测循环依赖
        if interface in self._resolving:
            raise ValueError(f"检测到循环依赖: {interface.__name__}")
        
        self._resolving.add(interface)
        
        try:
            dep = self._dependencies[interface]
            
            if dep.scope == InjectScope.SINGLETON:
                return self._get_singleton(dep)
            elif dep.scope == InjectScope.REQUEST:
                return self._get_request_scoped(dep)
            else:  # TRANSIENT
                return self._create_instance(dep)
        finally:
            self._resolving.discard(interface)
    
    def _get_singleton(self, dep: Dependency) -> Any:
        """获取单例实例"""
        if dep.instance is None:
            dep.instance = self._create_instance(dep)
        return dep.instance
    
    def _get_request_scoped(self, dep: Dependency) -> Any:
        """获取请求作用域实例"""
        if dep.interface not in self._request_scope:
            self._request_scope[dep.interface] = self._create_instance(dep)
        return self._request_scope[dep.interface]
    
    def _create_instance(self, dep: Dependency) -> Any:
        """创建实例"""
        try:
            if dep.factory:
                # 使用工厂创建
                return self._invoke_factory(dep.factory, dep.dependencies)
            elif dep.implementation:
                # 使用构造函数创建
                return self._construct_instance(dep.implementation, dep.dependencies)
            else:
                raise ValueError(f"无法创建实例: {dep.interface.__name__}")
                
        except Exception as e:
            self.logger.error(f"创建实例失败: {dep.interface.__name__} - {e}")
            raise
    
    def _construct_instance(self, cls: Type, dependencies: List[Type]) -> Any:
        """构造实例"""
        # 解析构造函数参数
        sig = inspect.signature(cls.__init__)
        kwargs = {}
        
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
            
            # 获取参数类型注解
            param_type = param.annotation
            
            if param_type != inspect.Parameter.empty:
                # 尝试解析依赖
                if param_type in self._dependencies:
                    kwargs[param_name] = self.resolve(param_type)
                elif param.default != inspect.Parameter.empty:
                    # 使用默认值
                    kwargs[param_name] = param.default
                else:
                    self.logger.warning(f"无法解析参数: {cls.__name__}.{param_name}: {param_type}")
        
        return cls(**kwargs)
    
    def _invoke_factory(self, factory: Callable, dependencies: List[Type]) -> Any:
        """调用工厂函数"""
        sig = inspect.signature(factory)
        kwargs = {}
        
        for param_name, param in sig.parameters.items():
            param_type = param.annotation
            
            if param_type != inspect.Parameter.empty and param_type in self._dependencies:
                kwargs[param_name] = self.resolve(param_type)
        
        return factory(**kwargs)
    
    def _analyze_dependencies(self, target: Optional[Type]) -> List[Type]:
        """分析依赖关系"""
        if not target:
            return []
        
        dependencies = []
        
        # 分析构造函数
        if inspect.isclass(target):
            sig = inspect.signature(target.__init__)
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue
                
                param_type = param.annotation
                if param_type != inspect.Parameter.empty:
                    dependencies.append(param_type)
        
        # 分析工厂函数
        elif callable(target):
            sig = inspect.signature(target)
            for param_name, param in sig.parameters.items():
                param_type = param.annotation
                if param_type != inspect.Parameter.empty:
                    dependencies.append(param_type)
        
        return dependencies
    
    def clear_request_scope(self):
        """清除请求作用域"""
        self._request_scope.clear()
        self.logger.debug("清除请求作用域")
    
    def get_dependency_graph(self) -> Dict[str, List[str]]:
        """
        获取依赖关系图
        
        Returns:
            依赖关系字典
        """
        graph = {}
        
        for interface, dep in self._dependencies.items():
            deps = []
            if dep.dependencies:
                deps = [d.__name__ for d in dep.dependencies]
            graph[interface.__name__] = deps
        
        return graph
    
    def validate_dependencies(self) -> List[str]:
        """
        验证依赖关系
        
        Returns:
            问题列表
        """
        issues = []
        
        for interface, dep in self._dependencies.items():
            if dep.dependencies:
                for required_dep in dep.dependencies:
                    if required_dep not in self._dependencies:
                        issues.append(f"{interface.__name__} 需要 {required_dep.__name__}，但未注册")
        
        # 检测循环依赖
        visited = set()
        rec_stack = set()
        
        def has_cycle(node: Type) -> bool:
            visited.add(node)
            rec_stack.add(node)
            
            if node in self._dependencies:
                dep = self._dependencies[node]
                if dep.dependencies:
                    for neighbor in dep.dependencies:
                        if neighbor not in visited:
                            if has_cycle(neighbor):
                                return True
                        elif neighbor in rec_stack:
                            issues.append(f"循环依赖: {node.__name__} <-> {neighbor.__name__}")
                            return True
            
            rec_stack.remove(node)
            return False
        
        for interface in self._dependencies:
            if interface not in visited:
                has_cycle(interface)
        
        return issues


# 依赖注入装饰器
def injectable(scope: InjectScope = InjectScope.SINGLETON):
    """
    可注入装饰器
    
    Args:
        scope: 注入作用域
    """
    def decorator(cls):
        # 自动注册到容器
        container = get_dependency_container()
        container.register(cls, cls, scope)
        return cls
    
    return decorator


def inject(interface: Type):
    """
    注入装饰器
    
    Args:
        interface: 要注入的接口类型
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 解析依赖
            container = get_dependency_container()
            instance = container.resolve(interface)
            
            # 注入到参数
            return func(*args, instance, **kwargs)
        
        return wrapper
    return decorator


class AutoWired:
    """自动装配属性描述符"""
    
    def __init__(self, interface: Type):
        self.interface = interface
        self._instance = None
    
    def __get__(self, obj, objtype=None):
        if self._instance is None:
            container = get_dependency_container()
            self._instance = container.resolve(self.interface)
        return self._instance


# 全局容器实例
_dependency_container = None

def get_dependency_container() -> DependencyContainer:
    """获取依赖容器实例"""
    global _dependency_container
    if _dependency_container is None:
        _dependency_container = DependencyContainer()
    return _dependency_container


# 便捷函数
def register_dependency(
    interface: Type,
    implementation: Optional[Type] = None,
    scope: InjectScope = InjectScope.SINGLETON
):
    """注册依赖的便捷函数"""
    container = get_dependency_container()
    container.register(interface, implementation, scope)


def resolve_dependency(interface: Type) -> Any:
    """解析依赖的便捷函数"""
    container = get_dependency_container()
    return container.resolve(interface)