"""
显示列Schema对齐器

职责：在数据渲染前，保证数据记录与表头列集合严格一致，避免降级渲染与高频UI修复。

功能边界：
- 不做字段映射与值格式化，仅对齐列集合与顺序
- 仅在最终落地前进行对齐，复用既有的映射/格式/偏好结果

使用位置：
- 在表格组件设置数据前（如 VirtualizedExpandableTable._set_data_impl）调用
"""

from __future__ import annotations

from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

from src.utils.log_config import setup_logger


@dataclass
class DisplaySchemaDecision:
    table_name: str
    headers: List[str]
    # 未来可扩展：reverse_mapping / missing_columns 等


class DisplaySchemaResolver:
    """轻量级显示列Schema对齐器（无副作用）"""

    def __init__(self):
        self._logger = setup_logger(__name__)
        # 简单缓存：key=(table_name, tuple(headers))
        self._last_decision_key: Optional[Tuple[str, Tuple[str, ...]]] = None
        self._last_decision: Optional[DisplaySchemaDecision] = None

    def decide(self, table_name: str, headers: List[str]) -> DisplaySchemaDecision:
        key = (table_name or "", tuple(headers or []))
        if self._last_decision_key == key and self._last_decision is not None:
            return self._last_decision

        decision = DisplaySchemaDecision(table_name=table_name or "", headers=list(headers or []))
        self._last_decision_key = key
        self._last_decision = decision
        return decision

    def align_records_and_headers(
        self,
        data_records: List[Dict[str, object]],
        headers: List[str],
        table_name: str = "",
    ) -> Tuple[List[Dict[str, object]], List[str]]:
        """
        对齐数据记录与表头：
        - 确保每条记录包含所有表头列（缺失列补空字符串）
        - 丢弃记录中多余列（仅保留表头列）
        - 返回与 headers 同步后的新 records 与（可能清洗后的）headers
        """
        try:
            headers = list(headers or [])
            if not headers:
                return data_records, headers

            # 决策（当前仅回传原有表头，保留扩展点）
            _ = self.decide(table_name, headers)

            # 方案E：对 header 做轻度规范化，去换行/多余空白
            normalized_headers = [str(h).replace('\n', '').strip() for h in headers]
            aligned: List[Dict[str, object]] = []
            for rec in data_records or []:
                # 仅保留目标表头列，并确保顺序
                new_rec = {h: rec.get(h, "") for h in normalized_headers}
                aligned.append(new_rec)

            # 若传入记录为None或空，仍返回 headers 以保证列结构
            return aligned, normalized_headers

        except Exception as e:
            # 出现异常不影响主流程，按原样返回
            self._logger.warning(f"DisplaySchemaResolver 对齐失败，按原数据返回: {e}")
            return data_records, headers


_resolver_singleton: Optional[DisplaySchemaResolver] = None


def get_display_schema_resolver() -> DisplaySchemaResolver:
    global _resolver_singleton
    if _resolver_singleton is None:
        _resolver_singleton = DisplaySchemaResolver()
    return _resolver_singleton


