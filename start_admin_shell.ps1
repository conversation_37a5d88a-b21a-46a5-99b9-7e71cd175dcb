﻿Write-Host "=== Salary Changes System Admin Environment ===" -ForegroundColor Green
Set-Location 'C:\test\salary_changes\salary_changes'
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host "Project files:" -ForegroundColor Yellow
Get-ChildItem | Format-Table Name, Length, LastWriteTime
Write-Host "清理 logs 目录..." -ForegroundColor Yellow
if (Test-Path "logs") { Remove-Item -Path "logs" -Recurse -Force; Write-Host "已删除 logs 目录" -ForegroundColor Green } else { Write-Host "logs 目录不存在" -ForegroundColor Yellow }
Write-Host "清理 state 目录..." -ForegroundColor Yellow
if (Test-Path "state") { Remove-Item -Path "state" -Recurse -Force; Write-Host "已删除 state 目录" -ForegroundColor Green } else { Write-Host "state 目录不存在" -ForegroundColor Yellow }
Write-Host "清理 data\db 目录..." -ForegroundColor Yellow
if (Test-Path "data\db") { Remove-Item -Path "data\db" -Recurse -Force; Write-Host "已删除 data\db 目录" -ForegroundColor Green } else { Write-Host "data\db 目录不存在" -ForegroundColor Yellow }
Write-Host "
Starting application..." -ForegroundColor Green
Write-Host "清理 src 目录下的 __pycache__..." -ForegroundColor Yellow
Get-ChildItem -Path "src" -Directory -Recurse -Filter "__pycache__" | ForEach-Object { Remove-Item -Path $_.FullName -Recurse -Force; Write-Host "已删除: $($_.FullName)" }
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
if (Test-Path 'E:\project\case\salary_changes\.venv\Scripts\Activate.ps1') { & 'E:\project\case\salary_changes\.venv\Scripts\Activate.ps1' } else { Write-Host "未找到激活脚本：E:\project\case\salary_changes\.venv\Scripts\Activate.ps1，已跳过激活。" -ForegroundColor Yellow }
Write-Host "Running: python main.py" -ForegroundColor Yellow
python main.py
Write-Host "
Application exited. This window will stay open. Type 'exit' to close." -ForegroundColor Cyan
